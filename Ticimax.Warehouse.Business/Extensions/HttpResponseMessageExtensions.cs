using System;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;

namespace Ticimax.Warehouse.Business.Extensions
{
    public static class HttpResponseMessageExtensions
    {
        public static async Task HandleKnownExceptions(
            this HttpResponseMessage response,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            if (response.StatusCode == HttpStatusCode.BadRequest)
                throw await JsonSerializerWrapper.Deserialize<BusinessException>(response.Content, cancellationToken);

            if (response.StatusCode == HttpStatusCode.Unauthorized)
                throw new UnauthorizedAccessException();

            if (response.StatusCode == HttpStatusCode.Forbidden)
                throw await JsonSerializerWrapper.Deserialize<ForbiddenAccessException>(response.Content, cancellationToken);

            if (response.StatusCode == HttpStatusCode.NotFound)
            {
                string json = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrEmpty(json))
                    throw new NotFoundException();

                throw JsonSerializer.Deserialize<NotFoundException>(json) ?? new NotFoundException();
            }

            if (response.StatusCode == HttpStatusCode.Conflict)
                throw new OptimisticLockException();
        }
    }
}