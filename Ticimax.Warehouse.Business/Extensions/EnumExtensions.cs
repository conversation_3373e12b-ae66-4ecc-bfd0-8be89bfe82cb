using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;

namespace Ticimax.Warehouse.Business.Extensions
{
    public static class EnumExtensions
    {
        public static string GetStringValue<T>(int type) where T:Enum
        {
            foreach (T item in Enum.GetValues(typeof(T)))
            {
                if (Convert.ToInt32(item) == type)
                {
                    FieldInfo field = typeof(T).GetField(item.ToString());
                    if (field != null && Attribute.GetCustomAttribute(field, typeof(StringValueAttribute)) is StringValueAttribute attribute)
                    {
                        return attribute.Value;
                    }
                }
            }
            return "";
        }
    }
}
