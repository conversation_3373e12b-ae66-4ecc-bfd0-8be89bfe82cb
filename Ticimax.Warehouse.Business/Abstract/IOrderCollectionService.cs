using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Models.Responses.OrderCollection;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IOrderCollectionService
    {
        Task<DataResult<OrderCollectionSet>> GetCollectionSet(GetOrderCollectionSetDto request, CancellationToken cancellationToken);

        Task<DataResult<List<OrderCollectionSet>>> GetList(GetOrderCollectionSetDto request, CancellationToken cancellationToken);

        Task<DataResult<List<OrderCollectionSet>>> GetPendingMergeSet(OrderCollectionGetPendingMergeSetDto request, CancellationToken cancellationToken);

        Task<DataResult<List<OrderCollectionSet>>> GetPendingCollectSet(OrderCollectionGetPendingCollectSetDto request, CancellationToken cancellationToken);

        /// <summary>
        /// 'depo_dagitilan_urun' tablosundan Manuel, Otomatik sipariş tipine göre 'siparis' ya da 'depo_siparis' tablosundan siparişleri çeker.
        /// </summary>
        /// <param name="filter">OrderCollectionSetFilter</param>
        /// <returns>List<OrderCollectionOrderTypeDto></returns>
        Task<DataResult<List<Order>>> GetMySetOrder(OrderCollectionSetFilter filter, PagingDto paging = null, CancellationToken cancellationToken = default);

        Task<DataResult<int>> ProductCountInSet(OrderCollectionProductCountInSetDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> MissingProductCountInSet(OrderCollectionMissingProductCountInSetDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> CollectedProductCountInSet(OrderCollectionCollectedProductCountInSetDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> OrderCountInParcel(OrderCollectionOrderCountInParcelDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> OrderAssignParcel(OrderCollectionOrderAssignParcelDto request, CancellationToken cancellationToken);

        Task OrderAssignTable(OrderCollectionOrderAssignTableDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> OrderParcelPreparation(OrderCollectionOrderParcelPreparationDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> SetAssignTable(OrderCollectionSetAssignTableDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> UpdateMissingProduct(OrderCollectionUpdateMissingProductDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> SetQualityControlCompleated(OrderCollectionSetQualityControlCompleatedDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> SetQualityControlCompletedByID(OrderCollectionSetQualityControlCompleatedByIDDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> DeleteByID(int ID, CancellationToken cancellationToken);

        Task DeleteByOrderProductIdForOneRow(int orderProductId, CancellationToken cancellationToken);

        Task<ErrorResponse> DeleteByPreparedID(int userID, CancellationToken cancellationToken);

        Task<ErrorResponse> SetPreparingStatusCollectionProduct(SetPreparingStatusCollectionProductDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> OrderDistribute(CancellationToken cancellationToken);

        Task<DataResult<List<OrderCollectionPageDto>>> OrderCollectionPage(SetGetListFilter filter, PagingDto paging = null, CancellationToken cancellationToken = default);

        Task<DataResult<List<SetDetailDto>>> SetDetail(SetDetailFilter filter, CancellationToken cancellationToken);

        Task<DataResult<List<SetDetailEditDto>>> SetDetailEdit(SetDetailEditFilter filter, CancellationToken cancellationToken);

        Task DeleteSet(SetDeleteDto request,CancellationToken cancellationToken);

        Task<ErrorResponse> AssignAlreadyHaveSet(SetAssignDto request, CancellationToken cancellationToken);
        
        Task SetAssign(string setNo, int userId, CancellationToken cancellationToken);

        Task<DataResult<List<OrderInTheSetDto>>> OrdersInTheSet(OrderInTheSetFilter filter, CancellationToken cancellationToken);

        Task<ErrorResponse> SetAssignByShelf(List<int> IDs, List<int> ShelfIds, int preparedID, CancellationToken cancellationToken);

        Task<ErrorResponse> SetUpdateTableID(SetUpdateTableDto request, CancellationToken cancellationToken);

        Task<DataResult<List<DistributionProductCheckerDto>>> CheckOrderCollectionSet(string setNo, CancellationToken cancellationToken);

        Task<ErrorResponse> DeleteByOrderID(int orderID, CancellationToken cancellationToken);

        Task<ErrorResponse> AddWithSetNo(AddWithSetNoDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> SetMissingToFoundAddProduct(OrderProduct orderCollectionProduct, CancellationToken cancellationToken);

        Task<ErrorResponse> SetUpdateCarID(string setNo, int carID, CancellationToken cancellationToken);

        Task<ErrorResponse> SetUndoInTheCar(int carID, string setNo, CancellationToken cancellationToken);

        Task<ErrorResponse> MissingPieceToOccunciesPiece(MissingPieceToOccunciesPieceDto entity, CancellationToken cancellationToken);

        Task<Pageable<OrderCollectionMinifyOrder>> GetOrderCollectionMinifyOrdersAsync(string setNo, PagingDto paging, CancellationToken cancellationToken);

        Task<Pageable<OrderCollectionMinifyShelf>> GetOrderCollectionShelfsAsync(string setNo, PagingDto paging, CancellationToken cancellationToken);

        Task<DataResult<OrderCollectionPageDto>> GetUserSet(CancellationToken cancellationToken);
        Task DeleteByOrderProductIdMissingProductForOneRow(int orderProductId, CancellationToken cancellationToken);
        Task<bool> GetStoreChangeConfirmation(StoreChangeConfirmationFilter filter, CancellationToken cancellationToken);
        Task<UserCarControlDto> GetUserCarAlert(CancellationToken cancellationToken);
        Task<ErrorResponse> SetUpdatePreparedID(string setNo, int preparedId, CancellationToken cancellationToken);
    }
}