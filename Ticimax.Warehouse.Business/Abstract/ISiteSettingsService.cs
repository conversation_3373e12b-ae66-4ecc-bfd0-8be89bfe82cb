using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.BusinessEntities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface ISiteSettingsService
    {
        Task<WarehouseSettings> GetSettings(int warehouseId, int storeId, CancellationToken cancellationToken);

        Task<ErrorResponse> UpdateWarehouseSettings(int warehouseId, int storeId, SetWarehouseSettingsDto request, CancellationToken cancellationToken);

        Task<WarehouseSettingsDto> GetWarehouseSettings(int warehouseId, int storeId, CancellationToken cancellationToken);

        Task<WarehouseSettingsDto> ReturnDefaultSettings(int warehouseId, int storeId, CancellationToken cancellationToken);

        Task<BLDepoAyar> GetBLWarehouseSettings(int warehouseId, int storeId, CancellationToken cancellationToken);
    }
}