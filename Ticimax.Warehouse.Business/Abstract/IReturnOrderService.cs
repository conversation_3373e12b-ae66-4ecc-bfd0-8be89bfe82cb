using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IReturnOrderService
    {
        Task<DataResult<ReturnOrderGetOrderResponseDto>> GetOrder(string barcode, CancellationToken cancellationToken);
        Task Completed(int id, ReturnOrderCompleatedDto request, CancellationToken cancellationToken);
        Task<List<ReturnOrderProductRequest>> GetReturnRequest(int orderId, CancellationToken cancellationToken);
    }
}