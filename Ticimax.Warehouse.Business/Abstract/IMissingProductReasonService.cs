using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IMissingProductReasonService
    {
        Task<DataResult<int>> GetCount(MissingProductReasonGetListDto request, CancellationToken cancellationToken);
        Task<DataResult<List<MissingProductReason>>> GetList(MissingProductReasonGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default);
        Task<ErrorResponse> Add(MissingProductReasonAddDto requset, CancellationToken cancellationToken);
        Task<ErrorResponse> Update(MissingProductReasonUpdateDto requset, CancellationToken cancellationToken);
        Task<ErrorResponse> Delete(MissingProductReasonDeleteDto requset, CancellationToken cancellationToken);
    }
}