using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Models.Requests;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Models.Responses;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Movement;

namespace Ticimax.Warehouse.Business.Abstract
{
#nullable enable
    public interface IWarehouseProductTransferService
    {
        Task<WarehouseProductTransferFileAggregate> GetById(Guid id, CancellationToken cancellationToken);
        Task<WarehouseProductTransferFileAggregate> CreateFile(CreateWarehouseProductTransferRequest request, CancellationToken cancellationToken);
        Task<WarehouseProductTransferFileAggregate> UpdateFile(Guid id, UpdateWarehouseProductTransferRequest request, CancellationToken cancellationToken);
        Task UpdateFileStatus(Guid id, string status, CancellationToken cancellationToken);
        Task<Pageable<WarehouseProductTransferFileAggregate>> Get(string? name, string? fileNo, string? status, int? createdUserId, string? productIds, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<int> Count(string? name, string? fileNo, string? status, int? createdUserId, string? productIds, CancellationToken cancellationToken);
        Task<Pageable<ProductDetailedWarehouseProductTransferResponse>> GetItems(Guid fileId, int? shelfId, int? productId, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<Pageable<ProductDetailedWarehouseProductTransferResponse>> GetGroupped(Guid fileId, int? productId, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<List<WarehouseProductTransferMovementAggregate>> GetMovements(Guid fileId, CancellationToken cancellationToken);
        Task CreateMovements(Guid fileId, string message, CancellationToken cancellationToken);
        Task Pick(Guid fileId, int shelfId, int productId, double quantity, bool isMissing, CancellationToken cancellationToken);
        Task Check(Guid fileId, int productId, double quantity, bool isBulkProductCompleted, Guid? fileProductId, CancellationToken cancellationToken);
        Task PickCompleted(Guid id, CancellationToken cancellationToken);
        Task CheckCompleted(Guid id, CancellationToken cancellationToken);
        Task<PickShelfsWarehouseProductTransferResponse> GetPickShelfs(Guid id, CancellationToken cancellationToken);
        Task<PickProductsWarehouseProductTransferResponse> GetPickProducts(Guid id, int shelfId, CancellationToken cancellationToken);
        Task<CheckProductsWarehouseProductTransferResponse> GetCheckProducts(Guid id, CancellationToken cancellationToken);
        Task<List<NotPickProductsWarehouseProductTransferResponse>> GetNonPickProducts(CancellationToken cancellationToken);
        Task FileCompleted(WarehouseTransferBulkCompletedRequest request, CancellationToken cancellationToken);
    }
}