using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.Review.Request;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface INotificationService
    {
        Task<Pageable<NotificationDto>> GetNotifications(FilterNotificationRequest request, CancellationToken cancellationToken);
        Task CreateNotification(CreateNotificationRequest request, CancellationToken cancellationToken);
        Task<bool> UpdateNotification(Guid id, UpdateNotificationRequest request, CancellationToken cancellationToken);
        Task<bool> ConfirmNotification(Guid id, CancellationToken cancellationToken);
        Task<bool> ReadNotification(Guid id, CancellationToken cancellationToken);
        Task<bool> ReadAllNotification(AllReadNotificationRequest request, CancellationToken cancellationToken);
        Task<bool> DeleteNotification(Guid id, CancellationToken cancellationToken);
    }
}
