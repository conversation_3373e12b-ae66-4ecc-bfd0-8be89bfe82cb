using System;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Models;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Request;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IExcelJobService
    {
        Task<ExcelJobItem> CreateExcelJob(CreateExcelJobRequest request, CancellationToken cancellationToken);
        Task<Pageable<ExcelJobRowItem>> GetExcelJobRows(Guid jobId, bool? failedRows, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<Pageable<ExcelJobItem>> ListExcelJobs(string type, int? userId, string? status, int pageSize, int pageIndex, CancellationToken cancellationToken);
    }
}