using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface ICancelReturnReasonService
    {
        Task<DataResult<List<CancelReturnReason>>> GetList(CancelReturnReasonGetListDto request, WarehouseOperationRulesPaging paging = null, CancellationToken cancellationToken = default);
    }
}