using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IDynamicFormDataService
    {
        Task<DataResult<List<DynamicFormData>>> GetList(DynamicFormDataGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default);

        Task<DataResult<int>> GetCount(DynamicFormDataGetListDto request, CancellationToken cancellationToken);
    }
}