using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface ICategoryService
    {
        Task<DataResult<List<Category>>> GetList(CategoryGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default);
        Task<DataResult<List<Category>>> GetAllChildAndParentCategories(int categoryId, PagingDto paging = null, CancellationToken cancellationToken = default);
    }
}