using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseCarProductService
    {
        Task Add(WarehouseCarProductAddDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> Update(WarehouseCarProductUpdateDto request, CancellationToken cancellationToken);

        Task<DataResult<List<WarehouseCarProduct>>> GetList(WarehouseCarProductGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default);

        Task<ErrorResponse> Delete(WarehouseCarProductDeleteDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> DeleteCarAllProduct(WarehouseCarProductDeleteCarAllProductDto request, CancellationToken cancellationToken);

        Task Reduce(WarehouseCarProductReduceDto request,CancellationToken cancellationToken);

        Task<DataResult<int>> GetCount(WarehouseCarProductGetListDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> ApprovalUpdate(int id,WarehouseCarProductSettingsUpdateDto request, CancellationToken cancellationToken);
        Task<List<WarehouseCarMissingProductsDto>> GetListMissingProducts(WarehouseCarProductGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default);

    }
}