using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Models.Requests.LatestFeature;
using Ticimax.Warehouse.Entities.Models.Responses.LatestFeature;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface ILatestFeatureService
    {
        Task<Pageable<LatestFeatureModel>> GetLatestFeatures(FilterLatestFeatureRequest request, CancellationToken cancellationToken);
    }
}
