using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.ShelfCountingItem.Models.Request;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.Item;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.Item.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IShelfCountingItemService
    {
        Task<Pageable<ShelfCountingItemAggregate>> Get(Guid? fileId, int? shelfId, int? productId, string? status,bool? pickedProduct, int pageSize, int pageIndex, CancellationToken cancellationToken);

        Task<Pageable<ShelfCountingItemDetailedDto>> GetDetailed(Guid? fileId, int? shelfId, int? productId, string? status,bool? pickedProduct, int pageSize, int pageIndex, CancellationToken cancellationToken);

        Task<ShelfCountingItemAggregate> GetById(Guid id, CancellationToken cancellationToken);

        Task<ShelfCountingItemDetailedDto> Create(CreateShelfCountingItemRequest request, CancellationToken cancellationToken);

        Task Pick(Guid id, double quantity, CancellationToken cancellationToken);
        
        Task Revert(Guid id, double quantity, CancellationToken cancellationToken);

        Task Delete(Guid id, CancellationToken cancellationToken);

        Task ResetByShelfIdAndProductId(int shelfId, int productId, CancellationToken cancellationToken);

        Task<List<ShelfCountingItemAggregate>> CreateByFile(CreateForFileShelfCountingItemRequest request, CancellationToken cancellationToken);

        Task<(double, double)> CompletedByFile(Guid fileId, bool changeEcommerce, CancellationToken cancellationToken);

        Task DeleteByFile(Guid id, CancellationToken cancellationToken);

        Task<Pageable<ShelfCountingItemGroupedByShelfDto>> GetGroupedByShelf(Guid? fileId, int differentType, int pageSize, int pageIndex, CancellationToken cancellationToken);

        Task ShelfComplete(Guid fileId, int shelfId, CancellationToken cancellationToken);
        Task<Pageable<ShelfCountingItemGroupedByProductDto>> GetGroupedByProduct(ShelfCountingGrouppedByProductRequest request, CancellationToken cancellationToken);

    }
}