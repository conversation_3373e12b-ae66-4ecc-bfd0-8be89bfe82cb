using System;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.ShelfCountingFile.Models.Request;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.File;

namespace Ticimax.Warehouse.Business.Abstract
{
#nullable enable
    public interface IShelfCountingFilesService
    {
        Task<Pageable<ShelfCountingFileAggregate>> Get(string? name, string? fileNo, string? shelfId, string? status, int differentType, int pageSize, int pageIndex, CancellationToken cancellationToken);

        Task<ShelfCountingFileAggregate> GetById(Guid id, CancellationToken cancellationToken);

        Task<ShelfCountingFileAggregate> Create(CreateShelfCountingFileRequest request, CancellationToken cancellationToken);

        Task<ShelfCountingFileAggregate> Update(Guid id, UpdateShelfCountingFileRequest request, CancellationToken cancellationToken);

        Task Completed(Guid id, CompleteShelfCountingFileRequest request,CancellationToken cancellationToken);

        Task Delete(Guid id, CancellationToken cancellationToken);
    }
}