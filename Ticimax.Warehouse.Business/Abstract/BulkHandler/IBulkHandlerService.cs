using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Request;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Response;

namespace Ticimax.Warehouse.Business.Abstract.BulkHandler
{
    public interface IBulkHandlerService
    {
        Task<ExcelImportResponse> ShelfExcelImport(ShelfImportRequest request, CancellationToken cancellationToken);
        
        Task<ExcelImportResponse> ShelfProductExcelImport(ShelfProductImportRequest request, CancellationToken cancellationToken);

        Task<ExcelImportResponse> SupplierExcelImport(SupplierImportRequest request, CancellationToken cancellationToken);

        Task<ExcelImportResponse> WarehouseCustomerExcelImport(WarehouseCustomerImportRequest request, CancellationToken cancellationToken);

        Task<ExcelImportResponse> WarehouseTableExcelImport(WarehouseTableImportRequest request, CancellationToken cancellationToken);

        Task<ExcelImportResponse> WarehouseCarExcelImport(WarehouseCarImportRequest request, CancellationToken cancellationToken);
        Task SuccessCallBack(int userId, CancellationToken cancellationToken);
        Task FailedCallBack(CancellationToken cancellationToken);
    }
}