using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface ILabelProductService
    {
        Task<ErrorResponse> LabelPrinted(LabelProductPrintedDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> LabelProductCheck(LabelProductCheckDto request, CancellationToken cancellationToken);
    }
}