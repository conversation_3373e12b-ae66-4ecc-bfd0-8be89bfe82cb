using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface ITableOrderCombineService
    {
        Task<DataResult<WarehouseTableOrderCombineDto>> OrderCombine(CancellationToken cancellationToken);

        Task TableSelection(string barcode, CancellationToken cancellationToken);

        Task<DataResult<TableOrderCombineTableProductAssignResponseDto>> TableProductAssign(TableOrderCombineTableProductAssignDto request, CancellationToken cancellationToken);

        Task MissingBoxAssign(int tableId, int parcelId, MissingBoxAssignRequestDto request, CancellationToken cancellationToken);
        Task OrderCombineCompleted(OrderCombineCompletedDto request, CancellationToken cancellationToken);
    }
}