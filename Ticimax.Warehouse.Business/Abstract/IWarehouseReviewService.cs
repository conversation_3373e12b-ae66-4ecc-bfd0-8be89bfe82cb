using System;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.WarehouseChatHub.Models;
using Ticimax.Warehouse.Entities.Models.Requests.WarehouseReview;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseReviewService
    {
        Task Create(CreateWarehouseReviewRequest request, CancellationToken cancellationToken);
        Task<Nextable<WarehouseChatResponse>> GetWarehouseChatHistory(string domainName, Guid connectionId, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task WarehouseChatRead(WarehouseChatReadRequest request, CancellationToken cancellationToken);
        Task<WarehouseChatUserUnDeliveredCountResponse> GetUserChatUnReadCount(CancellationToken cancellationToken);
        Task<Nextable<WarehouseChatGrouppedResponse>> GetWarehouseChatGroupped(string domainName, int receiverId, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<CreateWarehouseChatConnectionResponse> CreateWarehouseChatConnection(CreateWarehouseChatConnectionRequest request, CancellationToken cancellationToken);
    }
}