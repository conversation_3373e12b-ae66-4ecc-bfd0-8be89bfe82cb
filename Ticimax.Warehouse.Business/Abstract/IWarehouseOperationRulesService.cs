using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseOperationRulesService
    {
        Task<DataResult<List<WarehouseOperationRules>>> GetList(WarehouseOperationRulesGetListDto request, WarehouseOperationRulesPaging paging = null, CancellationToken cancellationToken = default);

        Task Add(WarehouseOperationRulesAddDto request, CancellationToken cancellationToken);

        Task Update(int id, WarehouseOperationRulesUpdateDto request, CancellationToken cancellationToken);

        Task Delete(int id, CancellationToken cancellationToken);

        Task<int> GetCount(WarehouseOperationRulesFilter filter, CancellationToken cancellationToken);
        Task<bool> SingleItemOrderPackagingRulesControl(OrderProduct readProduct, CancellationToken cancellationToken);
        Task<bool> OrderPackagingRulesControl(Order order, CancellationToken cancellationToken);
        Task<DataResult<List<CancelReturnReason>>> GetCancelReturnReason(CancelReturnReasonGetListDto request, CancelReturnReasonPaging paging = null, CancellationToken cancellationToken = default);
    }
}