using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseAuthGroupUserService
    {
        Task<DataResult<List<WarehouseAuthGroupUser>>> GetList(WarehouseAuthGroupUserFilter request, CancellationToken cancellationToken);

        Task<DataResult<WarehouseAuthGroupUser>> GetAuthGroupUser(WarehouseAuthGroupUserFilter request, CancellationToken cancellationToken);

        Task<ErrorResponse> Add(WarehouseAuthGroupUser request, string version, CancellationToken cancellationToken);

        Task<ErrorResponse> Delete(WarehouseAuthGroupUser request, CancellationToken cancellationToken);
    }
}