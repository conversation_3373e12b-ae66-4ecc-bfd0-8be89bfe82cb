using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseAuthGroupService
    {
        Task<DataResult<List<WarehouseAuthGroup>>> GetList(CancellationToken cancellationToken);

        Task<ErrorResponse> Add(WarehouseAuthGroupAddDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> Delete(WarehouseAuthGroupDeleteDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> Update(WarehouseAuthGroupUpdateDto request, CancellationToken cancellationToken);

        Task<int> AddReturnId(WarehouseAuthGroupAddDto request, CancellationToken cancellationToken);
    }
}