using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface ISupportPanelService
    {
        Task<List<CustomerUserCountsDto>> GetCustomerUserCountsReport(string domainName, bool filterTestDomain, CancellationToken cancellationToken);
        Task<CustomerCountReportDto> GetCustomerCountReport(string domainName, bool filterTestDomain, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<bool> ChangeCustomerStatus(int id, bool status, CancellationToken cancellationToken);
        Task<int> GetUserCount(bool filterTestDomain, CancellationToken cancellationToken);
        Task<int> GetCustomerCount(bool filterTestDomain, CancellationToken cancellationToken);
    }
}
