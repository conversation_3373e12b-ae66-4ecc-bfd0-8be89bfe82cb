using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseTableService
    {
        Task<DataResult<List<WarehouseTable>>> GetListAsync(WarehouseTableGetListDto request, CancellationToken cancellationToken, PagingDto paging = null);

        Task<ErrorResponse> UpdateAsync(WarehouseTableUpdateDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> DeleteAsync(WarehouseTableDeleteDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> AddAsync(WarehouseTableAddDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> GetCountAsync(WarehouseTableGetListDto request, CancellationToken cancellationToken);

        Task<DataResult<List<WarehouseTableProduct>>> GetProductsAsync(WarehouseTableGetProductDto request, CancellationToken cancellationToken);
        Task<DataResult<List<WarehouseTable>>> GetMissingTables(CancellationToken cancellationToken, PagingDto paging = null);
    }
}