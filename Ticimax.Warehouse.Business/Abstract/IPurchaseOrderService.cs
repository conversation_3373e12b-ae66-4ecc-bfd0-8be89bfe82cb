using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.Inbound.Models;
using Ticimax.Warehouse.Business.Concrete.Inbound.Models.Request;
using Ticimax.Warehouse.Business.Concrete.Inbound.Models.Response;
using Ticimax.Warehouse.Business.Concrete.Report.PurchaseOrder.Response;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IPurchaseOrderService
    {
        Task<PurchaseOrderModel> GetPurchaseOrder(Guid purchaseOrderId, CancellationToken cancellationToken);
        Task<Pageable<PurchaseOrderModel>> FilterPurchaseOrders(FilterPurchaseOrdersRequest request, CancellationToken cancellationToken);
        Task<PurchaseOrderModel> CreatePurchaseOrder(CreatePurchaseOrderRequest request, CancellationToken cancellationToken);
        Task<PurchaseOrderModel> UpdatePurchaseOrder(UpdatePurchaseOrderRequest request, CancellationToken cancellationToken);
        Task<bool> DeletePurchaseOrder(Guid purchaseOrderId, CancellationToken cancellationToken);
        Task<PurchaseOrderPickProductScreenResponse> PickScreen(Guid purchaseOrderId, CancellationToken cancellationToken);
        Task Complete(Guid purchaseOrderId, CancellationToken cancellationToken);

        Task<Pageable<PurchaseOrderProductModel>> FilterPurchaseOrderProducts(Guid purchaseOrderId, FilterPurchaseOrderProductsRequest request, CancellationToken cancellationToken);
        Task<GetPurchaseOrderProductReportResponse> GetPurchaseOrderProductReport(List<int> productIds, CancellationToken cancellationToken);
        Task<PurchaseOrderProductModel> PickPurchaseOrderProduct(Guid purchaseOrderId, string productBarcode, PickPurchaseOrderProductRequest request, CancellationToken cancellationToken);
        Task TransferPurchaseOrderProduct(Guid purchaseOrderId, EmptyToStockShelfDto request, CancellationToken cancellationToken);
        Task<PurchaseOrderProductModel> CreatePurchaseOrderProduct(Guid purchaseOrderId, int productId,string barcode, CreatePurchaseOrderProductsRequest request, CancellationToken cancellationToken);
        Task<PurchaseOrderProductModel> UpdatePurchaseOrderProduct(Guid purchaseOrderId, Guid purchaseOrderProductId, UpdatePurchaseOrderProductRequest request, CancellationToken cancellationToken);
        Task CreatePurchaseOrderBulkProducts(Guid purchaseOrderId, CreatePurchaseOrderBulkProductsRequest request, CancellationToken cancellationToken);
        Task DeletePurchaseOrderProduct(Guid purchaseOrderId, Guid purchaseOrderProductId, CancellationToken cancellationToken);

        Task<PurchaseOrderMovementModel> CreateMovement(Guid purchaseOrderId, CreatePurchaseOrderMovementRequest request, CancellationToken cancellationToken);
        Task<Pageable<PurchaseOrderMovementModel>> GetMovements(Guid purchaseOrderId, int pageSize = 20, int pageIndex = 1, CancellationToken cancellationToken = default);

        Task<Pageable<FilterPurchaseOrderHistoryReportResponse>> FilterPurchaseOrderHistoryReport(PurchaseOrderHistoryReportRequest filter, CancellationToken cancellationToken);
        Task<Pageable<FilterPurchaseOrderProductReportResponse>> FilterPurchaseOrderProductReport(PurchaseOrderProductReportRequest filter, CancellationToken cancellationToken);
        Task<PurchaseOrderModel> PurchaseOrderProductsBulk(CreatePurchaseOrderProductsBulkRequest request, CancellationToken cancellationToken);
        Task PurchaseOrderUpdateStatusBulk(UpdatePurchaseOrderStatusBulkRequest request, CancellationToken cancellationToken);

    }
}