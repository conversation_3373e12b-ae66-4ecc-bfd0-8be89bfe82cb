using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IInvoiceService
    {
        Task<DataResult<List<Entities.Concrete.Invoice>>> GetList(InvoiceGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default);

        Task<DataResult<InvoiceCreateResponseDto>> CreateInvoice(CreateInvoiceRequestDto request, CancellationToken cancellationToken);

        Task<DataResult<InvoiceCreateResponseDto>> ReCreateInvoice(ReOrderInvoicePrintDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> GetCount(InvoiceGetListDto request, CancellationToken cancellationToken);
    }
}