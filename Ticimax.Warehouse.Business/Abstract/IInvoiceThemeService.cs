using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Invoice.DTO.Models;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IInvoiceThemeService
    {
        Task<DataResult<List<InvoiceTheme>>> GetList(InvoiceThemeGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default);

        Task<ErrorResponse> Update(int id, InvoiceThemeUpdateDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> Add(InvoiceThemeAddDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> Delete(int id, CancellationToken cancellationToken);

        Task<ErrorResponse> RenderTemplate(InvoiceThemeRenderTemplateDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> GetCount(InvoiceThemeGetListDto request, CancellationToken cancellationToken);

        Task<List<DesignItem>> GetItems(string type, CancellationToken cancellationToken);
    }
}