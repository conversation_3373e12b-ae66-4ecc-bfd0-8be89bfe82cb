using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.WarehouseAuth.Models;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseAuthGroupDefinitionService
    {
        Task<DataResult<List<WarehouseAuthGroupDefinition>>> GetList(WarehouseAuthGroupDefinitionGetListDto request, string version, CancellationToken cancellationToken);

        Task<ErrorResponse> AddList(WarehouseAuthGroupDefinitionAddListDto request, CancellationToken cancellationToken);

        Task<DataResult<List<WarehouseAuthGroupDefinitionDefaultAuthDto>>> GetDefaultAuth(string version, CancellationToken cancellationToken);

        Task<ErrorResponse> ReturnDefaultAuth(List<int> authIDs, string version, CancellationToken cancellationToken);

        Task<List<WarehouseAuthGroupDefinition>> VersionAuthControl(List<WarehouseAuthGroupDefinition> authGroupDefinitions, CancellationToken cancellationToken);

        Task<DataResult<List<WarehouseAuthGroupDefinitionInformationDto>>> GetAuthDefinitionInformation(CancellationToken cancellationToken);
        Task<List<WarehouseAuthModel>> GetWarehouseAuths(CancellationToken cancellationToken);
        Task SyncWarehouseAuth(WarehouseAuthModel model, CancellationToken cancellationToken);
    }
}