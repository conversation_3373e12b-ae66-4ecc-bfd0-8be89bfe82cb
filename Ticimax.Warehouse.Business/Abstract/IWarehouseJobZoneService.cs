using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Request;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Response;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseJobZoneService
    {
        Task<WarehouseJobZoneAggregate> GetJobById(Guid jobId, CancellationToken cancellationToken);
        Task<Pageable<WarehouseJobZoneAggregate>> FilterJobs(FilterWarehouseJobFilter filter, CancellationToken cancellationToken);
        Task CreateWarehouseJob(CreateWarehouseJobRequest request, CancellationToken cancellationToken);
        Task<bool> DeleteWarehouseJob(Guid id, CancellationToken cancellationToken);
        Task UpdateWarehouseJob(Guid id, UpdateWarehouseJobRequest request, CancellationToken cancellationToken);
        Task<List<FlowTypesResponse>> GetFlowType(CancellationToken cancellationToken);
        Task<List<ConditionTypesResponse>> GetConditionTypes(CancellationToken cancellationToken);
        Task<List<ConditionOperatorsResponse>> GetConditionOperators(CancellationToken cancellationToken);
        Task<List<ConditionConjunctionsResponse>> GetConditionConjunctions(CancellationToken cancellationToken);
    }
}