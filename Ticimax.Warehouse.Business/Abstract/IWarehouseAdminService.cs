using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models;
using Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models.Reponse;
using Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models.Request;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseAdminService
    {
        Task CompareAdd(List<string> domainNames, CancellationToken cancellationToken);
    }
}
