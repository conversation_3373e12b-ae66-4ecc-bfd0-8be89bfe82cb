using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.OrderWaiting.Models.Request;
using Ticimax.Warehouse.Entities.Concrete.OrderWaiting;

namespace Ticimax.Warehouse.Business.Abstract
{
    #nullable enable
    public interface IWaitingOrderService
    {
        Task<WaitingOrderAggregate> GetByOrderId(int orderId, CancellationToken cancellationToken);
        Task<Pageable<WaitingOrderAggregate>> Get(int? orderId, string? status, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<WaitingOrderAggregate> Create(int orderId, WaitingOrderRequest request, CancellationToken cancellationToken);
        Task Completed(int id, CancellationToken cancellationToken);
    }
}