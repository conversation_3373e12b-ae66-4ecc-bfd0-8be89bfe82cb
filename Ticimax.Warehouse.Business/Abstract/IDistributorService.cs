using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IDistributorService
    {
        Task<DataResult<List<Core.Order.Entities.Concrete.Order>>> GetOrderList(DistributorGetOrderListDto request, Paging paging, CancellationToken cancellationToken);

        Task<ErrorResponse> AssignOrder(DistributorAssignOrderDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> MissingProductAssign(DistributorMissingProductAssignDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> OrderCompleated(DistributorOrderCompleatedDto request, CancellationToken cancellationToken);
    }
}