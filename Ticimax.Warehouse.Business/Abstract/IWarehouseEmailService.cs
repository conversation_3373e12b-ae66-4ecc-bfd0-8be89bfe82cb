using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Ticimax.Warehouse.Business.Abstract
{
    public interface IWarehouseEmailService
    {
        Task SendMissingOrderPickProblemMail(List<string> tos, string url, string ip, string setNo, int orderId, double sumDistributionAmount, double sumOrderProductsAmount, CancellationToken cancellationToken);
        Task SendSafetyStockMail(List<string> tos, int id, string barcode, string target, double totalStock, CancellationToken cancellationToken);
        Task SendResetPasswordEmail(List<string> tos, string link, CancellationToken cancellationToken);
    }
}
