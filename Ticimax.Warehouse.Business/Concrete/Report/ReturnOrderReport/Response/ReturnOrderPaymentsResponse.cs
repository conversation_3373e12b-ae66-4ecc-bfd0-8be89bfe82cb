using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderReport.ValueObjects;

namespace Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderReport.Response
{
    public class ReturnOrderPaymentsResponse
    {
        public Guid Id { get; set; }
        public int OrderId { get; set; }

        public double ReturnPayDoorAmount { get; set; }

        public double ReturnFixedCargoRefundAmount { get; set; }
        public double ReturnCargoAmount { get; set; }
        public double TotalReturnAmount { get; set; }
        public double ReturnReduceShippingCostAmountForReport { get; set; }
        public double ReturnOrderProductPiece { get; set; }
        public int ReturnPaymentType { get; set; }
        public string ReturnPaymentTypeStr { get; set; }
        public int MemberId { get; set; }
        public string MemberName { get; set; }
        public double ReturnBankCommissionAmount { get; set; }
        public long CreatedDate { get; set; }
        public long OrderDate { get; set; }
        public DateTime CreatedDateConvert { get; set; }
        public string Currency { get; set; }
        public List<ReturnOrderPaymentsInfo> ReturnOrderPaymentsInfo { get; set; }
    }
}
