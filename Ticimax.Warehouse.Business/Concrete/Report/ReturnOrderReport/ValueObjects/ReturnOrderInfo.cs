using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderReport.ValueObjects
{
    public class ReturnOrderInfo
    {
        public ReturnOrderInfo(double returnPayDoorAmount, double fixedCargoRefundAmount, double returnCargoAmount, int returnPaymentType, double totalReturnAmount,
            double returnReduceShippingCostAmountForReport, long createdDate, 
            List<ReturnOrderPaymentsInfo> returnOrderPaymentsInfo, double returnOrderProductPiece,
            int memberId, string memberName, double returnBankCommissionAmount)
        {
            ReturnPayDoorAmount = returnPayDoorAmount;
            ReturnFixedCargoRefundAmount = fixedCargoRefundAmount;
            ReturnCargoAmount = returnCargoAmount;
            TotalReturnAmount = totalReturnAmount;
            ReturnPaymentType = returnPaymentType;
            CreatedDate = createdDate;
            ReturnReduceShippingCostAmountForReport = returnReduceShippingCostAmountForReport;
            ReturnOrderPaymentsInfo = returnOrderPaymentsInfo;
            ReturnOrderProductPiece = returnOrderProductPiece;
            MemberId = memberId;
            MemberName = memberName;
            ReturnBankCommissionAmount = returnBankCommissionAmount;
        }
        public double ReturnPayDoorAmount { get; set; }
        public double ReturnFixedCargoRefundAmount { get; set; }
        public double ReturnCargoAmount { get; set; }
        public double TotalReturnAmount { get; set; }
        public double ReturnReduceShippingCostAmountForReport { get; set; }
        public double ReturnOrderProductPiece { get; set; }
        public int ReturnPaymentType { get; set; }
        public long CreatedDate { get; set; }
        public List<ReturnOrderPaymentsInfo> ReturnOrderPaymentsInfo { get; set; }
        public int MemberId { get; set; }
        public string MemberName { get; set; }
        public double ReturnBankCommissionAmount { get; set; }
    }
    public class ReturnOrderPaymentsInfo
    {
        public int PaymentTypeId { get; set; }
        public double PaymentAmount { get; set; }
    }
}
