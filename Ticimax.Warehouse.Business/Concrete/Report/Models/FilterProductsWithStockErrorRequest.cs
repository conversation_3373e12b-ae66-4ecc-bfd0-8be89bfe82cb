namespace Ticimax.Warehouse.Business.Concrete.Report.Models
{
    public class FilterProductsWithStockErrorRequest
    {
        public int ProductId { get; set; }
        public string ProductIds { get; set; }
        public double? MinWebStock { get; set; }
        public double? MaxWebStock { get; set; }
        public double? MinShelfStock { get; set; }
        public double? MaxShelfStock { get; set; }
        public double? MinConsigmentStock { get; set; }
        public double? MaxConsigmentStock { get; set; }
        public bool? OnlyWrong { get; set; }
    }
}