using System;

namespace Ticimax.Warehouse.Business.Concrete.Report.ShippedOrder.Models.Response
{
    public class ShippedOrderResponse
    {
        public string DomainName { get; set; }

        public int OrderId { get; set; }

        public int PacketId { get; set; }

        public string Code { get; set; }

        public string CargoCompany { get; set; }

        public string CustomerName { get; set; }

        public int ProductCount { get; set; }
        public long OrderDate { get; set; }
        public string OrderSource { get; set; }
        public double CargoAmount { get; set; }
        public double TotalAmount { get; set; }
        public double QualityControlDesi { get; set; }

        public long CreatedDate { get; set; }
        public DateTime CreatedDateConvert { get; set; }
        public bool IsDelivery { get; set; }
    }
}