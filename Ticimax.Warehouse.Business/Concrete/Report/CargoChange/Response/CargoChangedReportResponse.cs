using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderReport.ValueObjects;

namespace Ticimax.Warehouse.Business.Concrete.Report.CargoChange.Response
{
    public class CargoChangedReportResponse
    {
        public Guid Id { get; set; }
        public string DomainName { get; set; }
        public int WarehouseId { get; set; }
        public int StoreId { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; }
        public int OrderId { get; set; }

        public int OldCargoIntegrationId { get; set; }
        public string OldCargoIntegrationName { get; set; }
        public string OldCargoCode { get; set; }
        public string NewCargoCode { get; set; }
        public int OldCargoCompanyId { get; set; }
        public string OldCargoCompanyName { get; set; }
        public int NewCargoIntegrationId { get; set; }
        public string NewCargoIntegrationName { get; set; }
        public int NewCargoCompanyId { get; set; }
        public string NewCargoCompanyName { get; set; }
        public long CreatedDate { get; set; }
        public DateTime CreatedDateConvert { get; set; }
    }
}
