using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Ticimax.Warehouse.Business.Concrete.Report.ProductMovementStockControl
{
    public class ProductMovementStockControlResponse
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public double WebStock { get; set; }
        public double ShelfStock { get; set; }
        public double ConsigmentStock { get; set; }
        public double OldWebStock { get; set; }
        public double OldShelfStock { get; set; }
        public double OldConsigmentStock { get; set; }
        public double Piece { get; set; }
        public string Type { get; set; }
        public string ObjectId { get; set; }
        public long CreatedDate { get; set; }
    }
}
