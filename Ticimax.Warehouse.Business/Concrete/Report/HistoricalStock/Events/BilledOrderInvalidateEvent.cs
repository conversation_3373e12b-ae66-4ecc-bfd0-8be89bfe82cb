using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Business.Concrete.Report.HistoricalStock.Events
{
    public class BilledOrderInvalidateEvent : BaseDomainEvent
    {
        public BilledOrderInvalidateEvent(string domainName, int orderId, int personId, string personName, int personTableId, string personTable, double desi, string ınvoiceNo, int warehouseId, int storeId, long createdDate)
        {
            DomainName = domainName;
            OrderId = orderId;
            PersonId = personId;
            PersonName = personName;
            PersonTableId = personTableId;
            PersonTable = personTable;
            Desi = desi;
            InvoiceNo = ınvoiceNo;
            WarehouseId = warehouseId;
            StoreId = storeId;
            CreatedDate = createdDate;
        }

        public string DomainName { get; set; }

        public int OrderId { get; set; }

        public int PersonId { get; set; }

        public string PersonName { get; set; }

        public int PersonTableId { get; set; }

        public string PersonTable { get; set; }

        public double Desi { get; set; }

        public string InvoiceNo { get; set; }

        public int WarehouseId { get; set; }

        public int StoreId { get; set; }

        public long CreatedDate { get; set; }
    }
}