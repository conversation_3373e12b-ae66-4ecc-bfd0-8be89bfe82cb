namespace Ticimax.Warehouse.Business.Concrete.Report.ProductExtraction.ValueObjects
{
    public class ProductExtractionProductInfo
    {
        public ProductExtractionProductInfo()
        {
        }

        public ProductExtractionProductInfo(int ıd, string name, string barcode, string stockCode, double piece, string supplierDefinition, int supplierId, bool addStockWebSite)
        {
            Id = ıd;
            Name = name;
            Barcode = barcode;
            StockCode = stockCode;
            Piece = piece;
            SupplierDefinition = supplierDefinition;
            SupplierId = supplierId;
            AddStockWebSite = addStockWebSite;
        }

        public int Id { get; set; }

        public string Name { get; set; }

        public string Barcode { get; set; }

        public string StockCode { get; set; }

        public double Piece { get; set; }
        public string SupplierDefinition{ get; set; }
        public int SupplierId{ get; set; }
        public bool AddStockWebSite { get; set; }
    }
}