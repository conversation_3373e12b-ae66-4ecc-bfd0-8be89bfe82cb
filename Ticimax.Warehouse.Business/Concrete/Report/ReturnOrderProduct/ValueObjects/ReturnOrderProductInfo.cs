namespace Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderProduct.ValueObjects
{
    public class ReturnOrderProductInfo
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string Unit { get; set; }

        public double Quantity { get; set; }

        public double Amount { get; set; }
        public int ReturnReasonID { get; set; }
        public string ReturnReasonDefinition { get; set; }
        public int CategoryID { get; set; }
        public string CategoryName { get; set; }
        public bool? IsBeforeDelivery { get; set; }
        public string IsBeforeDeliveryStr { get; set; }
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string AdditionalOptions { get; set; }
    }
}