using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Business.Concrete.Report.ProductSearch.Events
{
    public class ProductSearchEvent : BaseDomainEvent
    {
        public ProductSearchEvent(string domainName, int warehouseId, int storeId, int userId,  int productId, string productName,
            string productBarcode, string productStockCode)
        {
            DomainName = domainName;
            WarehouseId = warehouseId;
            StoreId = storeId;
            UserId = userId;
            ProductId = productId;
            ProductName = productName;
            ProductBarcode = productBarcode;
            ProductStockCode = productStockCode;
        }

        public string DomainName { get; set; }
        public int WarehouseId { get; set; }
        public int StoreId { get; set; }
        public int UserId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductBarcode { get; set; }
        public string ProductStockCode { get; set; }
    }
}
