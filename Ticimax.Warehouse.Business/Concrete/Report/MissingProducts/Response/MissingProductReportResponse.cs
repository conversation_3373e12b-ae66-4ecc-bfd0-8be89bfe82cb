using System;

namespace Ticimax.Warehouse.Business.Concrete.Report.MissingProducts.Response
{
    public class MissingProductReportResponse
    {
        public Guid Id { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public int OrderId { get; set; }
        public int OrderProductId { get; set; }
        public int ShelfId { get; set; }
        public string ShelfName { get; set; }
        public double Piece { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string StockCode { get; set; }
        public string Barcode { get; set; }
        public long MissingDate { get; set; }
        public DateTime MissingDateConvert { get; set; }
        public int? ApprovalStatus { get; set; }
        public long? ApprovalDate { get; set; }
        public DateTime? ApprovalDateConvert { get; set; }
        public int? ApprovalUserId { get; set; }
        public string? ApprovalUserName { get; set; }
        public bool? IsFound { get; set; }
        public string FoundShelfName { get; set; }
        public int? FoundShelfId { get; set; }
        public double? FoundPiece { get; set; }
        public long? CompletedDate { get; set; }
        public DateTime? CompletedDateConvert { get; set; }
    }
}
