using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.Report.MissingProducts.Events
{
    public class MissingProductCompletedEventPayload : BaseDomainEvent
    {
        public MissingProductCompletedEventPayload(int orderProductId, bool isMissingProduct, int shelfId, double piece, long completedDate)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            OrderProductId = orderProductId;
            IsMissingProduct = isMissingProduct;
            ShelfId = shelfId;
            Piece = piece;
            CompletedDate = completedDate;
        }
        public int OrderProductId { get; set; }
        public int ShelfId { get; set; }
        public double Piece { get; set; }
        public string DomainName { get; set; }
        public bool IsMissingProduct { get; set; }
        public long CompletedDate { get; set; }
    }
}