using System.Collections.Generic;

namespace Ticimax.Warehouse.Business.Concrete.Report.MissingProducts.Request
{
    public class MissingProductReportRequest
    {
        public int PageSize { get; set; } = 20;
        public int PageIndex { get; set; } = 1;
        public bool? IsFound { get; set; }
        public string? ProductIds { get; set; }
        public int? UserId { get; set; }
        public int? OrderId { get; set; }
        public int? ApprovalStatus { get; set; }
        public long? FoundDate { get; set; }

        public long? StartDate { get; set; }
        public long? EndDate { get; set; }
    }
}
