using System;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.Report.TableMovements.Events
{
    public class TableMovementsCompletedEvent : BaseDomainEvent
    {
        public TableMovementsCompletedEvent(int tableId, string tableName, string process, string message)

        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            TableId = tableId;
            TableName = tableName;
            PersonId = WebSiteInfo.User.Value.ID;
            PersonName = WebSiteInfo.User.Value.Username;
            Process = process;
            Message = message;
            WarehouseId = WebSiteInfo.User.Value.WarehouseID;
            StoreId = WebSiteInfo.User.Value.StoreID;
            CreatedDate = DateTime.Now.ToTimestamp();
        }

        public string DomainName { get; set; }
        public int TableId { get; set; }
        public string TableName { get; set; }
        public int PersonId { get; set; }
        public string PersonName { get; set; }
        public string Process { get; set; }
        public string Message { get; set; }
        public int WarehouseId { get; set; }
        public int StoreId { get; set; }
        public long CreatedDate { get; set; }
    }
}
