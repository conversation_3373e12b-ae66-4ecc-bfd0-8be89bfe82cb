using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Business.Concrete.Report.PickedProduct.Events
{
    public class InvalidatePickedProductEvent : BaseDomainEvent
    {
        public InvalidatePickedProductEvent(string domainName, int warehouseId, int storeId, int userId, string username, int productId, string productName,
            double quantity, int shelfId, string shelfName, string stockCode, long createdDate)
        {
            DomainName = domainName;
            WarehouseId = warehouseId;
            StoreId = storeId;
            UserId = userId;
            Username = username;
            ProductId = productId;
            ProductName = productName;
            Quantity = quantity;
            ShelfId = shelfId;
            ShelfName = shelfName;
            StockCode = stockCode;
            CreatedDate = createdDate;
        }

        public string DomainName { get; set; }
        public int WarehouseId { get; set; }
        public int StoreId { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public double Quantity { get; set; }
        public int ShelfId { get; set; }
        public string ShelfName { get; set; }
        public string StockCode { get; set; }
        public long CreatedDate { get; set; }
    }
}
