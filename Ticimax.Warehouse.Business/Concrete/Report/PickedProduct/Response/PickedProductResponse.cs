using System;

namespace Ticimax.Warehouse.Business.Concrete.Report.PickedProduct.Response
{
    public class PickedProductResponse
    {
        public Guid Id { get; set; }
        public string DomainName { get; set; }
        public int WarehouseId { get; set; }
        public int StoreId { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductBarcode { get; set; }
        public double Quantity { get; set; }
        public int ShelfId { get; set; }
        public string ShelfName { get; set; }
        public string StockCode { get; set; }
        public long CreatedDate { get; set; }
        public DateTime CreatedDateConvert { get; set; }
        public long LastModifiedDate { get; set; }
        public long Version { get; set; }
    }
}
