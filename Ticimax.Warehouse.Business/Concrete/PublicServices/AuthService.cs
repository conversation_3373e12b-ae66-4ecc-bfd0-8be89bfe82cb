using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using Ticimax.Core.CrossCuttingConcerns.Caching;
using Ticimax.Core.Exceptions;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Abstract.PublicServices;
using Ticimax.Warehouse.Entities.Dtos.LoginControlDto;
using Ticimax.Warehouse.Entities.Dtos.PublicDtos.AppSettingsDto;

namespace Ticimax.Warehouse.Business.Concrete.PublicServices
{
    public class AuthService : IAuthService
    {
        private readonly IAppSettingsService _appSettingsService;
        private readonly ISecurityControlService _securityControlService;
        private readonly HttpContext _context;
        private readonly ICacheManager _cache;

        public AuthService(IAppSettingsService appSettingsService, ISecurityControlService securityControlService, IHttpContextAccessor accessor, ICacheManager cache)
        {
            _appSettingsService = appSettingsService;
            _securityControlService = securityControlService;
            _context = accessor.HttpContext;
            _cache = cache;
        }

        public async Task<JwtTokenResultDto> AuthAsync(GetTokenDto entity, CancellationToken cancellationToken)
        {
            var appSettings = await _appSettingsService.GetAsync(new GetAppSettingsDto { SecretKey = entity.SecretKey, AccessKey = entity.AccessKey });
            if (appSettings == null)
            {
                var security = (await _securityControlService.Get(new SecurityControlDto { PhysicalAddress = _context.Connection.RemoteIpAddress.ToString() }, cancellationToken)).Model;
                if (security != null)
                {
                    if (security.NumberOfAttempts > 5)
                        throw new BusinessException("YasaklananErisim");
                    else
                    {
                        security.NumberOfAttempts += 1;
                        security.DateOfUpdate = DateTime.Now.ToString("dd.MM.yyyy");
                        await _securityControlService.Update(security, cancellationToken);

                        throw new BusinessException("ErisimBilgileriHatali");
                    }
                }
                else
                {
                    await _securityControlService.Add(new SecurityControlDto { PhysicalAddress = _context.Connection.RemoteIpAddress.ToString(), DateOfUpdate = DateTime.Now.ToString("dd.MM.yyyy"), NumberOfAttempts = 1 }, cancellationToken);
                    throw new BusinessException("ErisimBilgileriHatali");
                }
            }

            return GenerateJwtToken(appSettings, cancellationToken);
        }

        private JwtTokenResultDto GenerateJwtToken(GetAppSettingsDto appSettings, CancellationToken cancellationToken)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Expires = DateTime.UtcNow.AddHours(1),
                Claims = new Dictionary<string, object> { { "SecretKey", appSettings.SecretKey }, { "AccessKey", appSettings.AccessKey }, { "Target", appSettings.Target } },
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(Encoding.ASCII.GetBytes(appSettings.SecretKey)), SecurityAlgorithms.HmacSha256Signature),
                Subject = new ClaimsIdentity(new List<Claim> { new Claim("SecretKey", appSettings.SecretKey), new Claim("AccessKey", appSettings.AccessKey), new Claim("Target", appSettings.TargetID.ToString()) })
            };

            SecurityToken securityToken = tokenHandler.CreateToken(tokenDescriptor);
            string token = tokenHandler.WriteToken(securityToken);
            _cache.Add(token, appSettings, 3600);
            return new JwtTokenResultDto { Token = token, Expire = DateTime.UtcNow.AddHours(1) };
        }
    }
}