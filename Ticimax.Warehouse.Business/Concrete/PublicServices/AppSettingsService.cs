using System.Threading.Tasks;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract.PublicServices;
using Ticimax.Warehouse.DataAccessLayer.Abstract.PostgreSQL;
using Ticimax.Warehouse.Entities.Dtos.PublicDtos.AppSettingsDto;

namespace Ticimax.Warehouse.Business.Concrete.PublicServices
{
    public class AppSettingsService : BaseService, IAppSettingsService
    {
        private readonly IAppSettingsDal _appSettingsDal;

        public AppSettingsService(IAppSettingsDal appSettingsDal)
        {
            _appSettingsDal = appSettingsDal;
        }

        public async Task<GetAppSettingsDto> GetAsync(GetAppSettingsDto entity)
        {
            var appSettings = await _appSettingsDal.GetAsync(entity.ToEntity());
            if (appSettings == null)
                throw new NotFoundException();
            return appSettings.ToEntity();
        }
    }
}