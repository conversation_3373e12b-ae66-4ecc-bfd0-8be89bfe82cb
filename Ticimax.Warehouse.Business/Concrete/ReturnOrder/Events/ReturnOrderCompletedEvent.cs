using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderReport.ValueObjects;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.ReturnOrder.Events
{
    public class ReturnOrderCompletedEvent : BaseDomainEvent
    {
        public ReturnOrderCompletedEvent(int orderId, int orderProductId, long orderDate, ReturnOrderCompletedProduct product, ReturnOrderInfo returnOrderInfo)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            OrderId = orderId;
            OrderProductId = orderProductId;
            Product = product;
            OrderDate = orderDate;
            WarehouseId = WebSiteInfo.User.Value.WarehouseID;
            StoreId = WebSiteInfo.User.Value.StoreID;
            UserId = WebSiteInfo.User.Value.ID;
            Username = WebSiteInfo.User.Value.Name;
            ReturnOrderInfo = returnOrderInfo;
            Year = DateTime.Now.Year;
            Month = DateTime.Now.Month;
        }

        public string DomainName { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }

        public int OrderId { get; set; }

        public int OrderProductId { get; set; }

        public ReturnOrderCompletedProduct Product { get; set; }
        public long OrderDate { get; set; }
        public int WarehouseId { get; set; }
        public int StoreId { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public ReturnOrderInfo ReturnOrderInfo { get; set; }

    }

    public class ReturnOrderCompletedProduct
    {
        public ReturnOrderCompletedProduct(int id, string name, string unit, double quantity, double amount, int returnReasonID, int categoryId,
            bool isBeforeDelivery, int supplierId, string supplierName, string additionalOptions)
        {
            Id = id;
            Name = name;
            Unit = unit;
            Quantity = quantity;
            Amount = amount;
            ReturnReasonID = returnReasonID;
            CategoryID = categoryId;
            IsBeforeDelivery = isBeforeDelivery;
            SupplierId = supplierId;
            SupplierName = supplierName;
            AdditionalOptions = additionalOptions;
        }
        
        public int Id { get; set; }

        public string Name { get; set; }

        public string Unit { get; set; }

        public double Quantity { get; set; }

        public double Amount { get; set; }
        public int ReturnReasonID { get; set; }
        public int CategoryID { get; set; }
        public bool IsBeforeDelivery { get; set; }
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string AdditionalOptions { get; set; }
    }
}