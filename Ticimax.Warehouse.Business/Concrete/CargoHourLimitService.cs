using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class CargoHourLimitService : BaseService, ICargoHourLimitService
    {
        private readonly ICargoHourLimitDal _cargoHourLimitDal;
        private readonly IOrderService _orderService;
        private readonly ILogger<CargoHourLimitService> _logger;
        private readonly IOrderMovementService _orderMovementService;

        public CargoHourLimitService(ICargoHourLimitDal cargoHourLimitDal, IOrderService orderService, ILogger<CargoHourLimitService> logger, IOrderMovementService orderMovementService)
        {
            _cargoHourLimitDal = cargoHourLimitDal;
            _orderService = orderService;
            _logger = logger;
            _orderMovementService = orderMovementService;
        }

        public async Task<DataResult<List<CargoHourLimitWeekDto>>> GetCargoHour(CargoHourLimitGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<List<CargoHourLimitWeekDto>> response = new DataResult<List<CargoHourLimitWeekDto>>();

            response.Model = (await _cargoHourLimitDal.GetCargoHourAsync(request.ToFilter(), cancellationToken)).ToList();

            return response;
        }

        public async Task<DataResult<int>> GetCount(CargoHourLimitGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _cargoHourLimitDal.GetCountAsync(request.ToFilter(), cancellationToken);

            return response;
        }

        public async Task<DataResult<List<CargoHourLimitWeekDto>>> GetDeliveryHour(CargoHourGetDeliveryHourDto request, CancellationToken cancellationToken)
        {
            var order = (await _orderService.GetList(
                new Core.Order.Entities.Dtos.OrderGetListDto
                {
                    OrderID = request.OrderID,
                    CargoType = WebSiteInfo.User.Value.CargoType
                }, null, cancellationToken)).Model.FirstOrDefault();

            return await GetCargoHour(
                new CargoHourLimitGetListDto
                {
                    Date = DateTime.Now,
                    DistrictID = order.DeliveryAddress.IlceID,
                    SemtID = order.DeliveryAddress.SemtID,
                    CargoID = order.CargoCompanyID,
                    Day = 5
                }, cancellationToken);
        }

        public async Task<DataResult<List<CargoHourLimit>>> GetList(CargoHourLimitGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<CargoHourLimit>> response = new DataResult<List<CargoHourLimit>>();

            response.Model = (await _cargoHourLimitDal.GetListAsync(request.ToFilter(), paging != null ? new CargoHourLimitPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();
            if (request.isGetCount)
                response.Count = (await GetCount(request, cancellationToken)).Model;

            return response;
        }

        public async Task<ErrorResponse> SetDeliveryHour(CargoHourSetDeliveryHourDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var cargoHour = (await GetList(new CargoHourLimitGetListDto { ID = request.CargoHourID }, null, cancellationToken)).Model.FirstOrDefault();
            await _orderService.UpdateOrderDeliveryHour(
                new Core.Order.Entities.Dtos.UpdateOrderDeliveryHourDto
                {
                    CargoHourID = cargoHour.ID,
                    Date = DateTime.ParseExact(request.DateStr, "dd-MM-yyyy", System.Globalization.CultureInfo.InvariantCulture),
                    DeliveryHour = cargoHour.HourRange,
                    OrderID = request.OrderID
                }, cancellationToken);

            await _orderService.SetPackagingStatus(
                new Core.Order.Entities.Dtos.OrderSetPackagingStatusDto
                {
                    IDs = new List<int> { request.OrderID },
                    Status = PackageStatus.Beklemede
                }, cancellationToken);

            _logger.LogTrace(JsonSerializerWrapper.Serialize(new TiciWmsLogDto
            {
                Date = DateTime.Now,
                Details = request.OrderID + "numaralı siparişin teslimat tarihini" + request.DateStr + " " + cargoHour.HourRange + "olarak günceledi",
                Process = "TESLIMATSAATI",
                LogType = "DEPOURUNTOPLAMA",
                PersonID = WebSiteInfo.User.Value.ID
            }));

            await _orderMovementService.AddAsync(
                new Core.Order.Entities.Dtos.OrderMovementAddDto
                {
                    AgentID = WebSiteInfo.User.Value.ID,
                    isSystem = true,
                    Message = "Siparişin teslimat tarihini" + request.DateStr + " " + cargoHour.HourRange + "olarak güncelledim.",
                    Name = WebSiteInfo.User.Value.Name,
                    OrderID = request.OrderID
                }, cancellationToken);

            return response;
        }
    }
}