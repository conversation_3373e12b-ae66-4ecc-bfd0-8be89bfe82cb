using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.PurchaseOrder.Events
{
    public class PurchaseOrderProductEventPayload : BaseDomainEvent
    {
        public PurchaseOrderProductEventPayload(Guid purchaseOrderId, PurchaseOrderProductEventModel product, PurchaseOrderShelfEventModel shelf,
            double piece, double occurencesPiece, double price, long occurrencesDate, int preparedId, double quantityToBeAddressed, 
            bool pickingCompleted, string orderLineId, long createdDate, long lastModifiedDate)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            UserId = WebSiteInfo.User.Value.ID;
            PurchaseOrderId = purchaseOrderId;
            Product = product;
            Shelf = shelf;
            Piece = piece;
            OccurrencesPiece = occurencesPiece;
            Price = price;
            OccurrencesDate = occurrencesDate;
            PreparedId = preparedId;
            QuantityToBeAddressed = quantityToBeAddressed;
            PickingCompleted = pickingCompleted;
            OrderLineId = orderLineId;
            CreatedDate = createdDate;
            LastModifiedDate = lastModifiedDate;
        }
        public string DomainName { get; set; }
        public int UserId { get; set; }
        public Guid PurchaseOrderId { get; set; }
        public PurchaseOrderProductEventModel Product { get; set; }
        public PurchaseOrderShelfEventModel Shelf { get; set; }
        public double Piece { get; set; }
        public double OccurrencesPiece { get; set; }
        public double Price { get; set; }
        public long OccurrencesDate { get; set; }
        public int PreparedId { get; set; }
        public double QuantityToBeAddressed { get; set; }
        public bool PickingCompleted { get; set; }
        public string OrderLineId { get; set; }
        public long CreatedDate { get; set; }
        public long LastModifiedDate { get; set; }
    }

    public class PurchaseOrderProductEventModel
    {
        public PurchaseOrderProductEventModel(int id, string name, string image, string barcode)
        {
            Id = id;
            Name = name;
            Image = image;
            Barcode = barcode;
        }
        public int Id { get; set; }
        public string Name { get; set; }
        public string Image { get; set; }
        public string Barcode { get; set; }
    }

    public class PurchaseOrderShelfEventModel
    {
        public PurchaseOrderShelfEventModel(int id, int parentId, string definition, string code, string barcode,
            int warehouseId, int storeId, int rank, bool isMissingProductShelf, long addingDate, bool isEmptyShelf, bool isOpenForSale)
        {
            Id = id;
            ParentId = parentId;
            Definition = definition;
            Code = code;
            Barcode = barcode;
            WarehouseId = warehouseId;
            StoreId = storeId;
            Rank = rank;
            IsMissingProductShelf = isMissingProductShelf;
            AddingDate = addingDate;
            IsEmptyShelf = isEmptyShelf;
            IsOpenForSale = isOpenForSale;
        }

        public int Id { get; set; }
        public int ParentId { get; set; }
        public string Definition { get; set; }
        public string Code { get; set; }
        public string Barcode { get; set; }
        public int WarehouseId { get; set; }
        public int StoreId { get; set; }
        public int Rank { get; set; }
        public bool IsMissingProductShelf { get; set; }
        public long AddingDate { get; set; }
        public bool IsEmptyShelf { get; set; }
        public bool IsOpenForSale { get; set; }
    }
}
