using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Core.Order.Business.Concrete.PickingProducts.Events
{
    public class PickingProductSelectedEventPayload : BaseDomainEvent
    {
        public PickingProductSelectedEventPayload(string type, int orderId)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            UserId = WebSiteInfo.User.Value.ID;
            Type = type;
            OrderId = orderId;
        }

        public string DomainName { get; set; }
        public int UserId { get; set; }
        public string Type { get; set; }
        public int OrderId { get; set; }
    }
}
