using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.StoreAgents.Events
{
    public class StoreAgentEventPayload : BaseDomainEvent
    {
        public StoreAgentEventPayload(Entities.Concrete.StoreAgent agent)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            UserId = WebSiteInfo.User.Value.ID;
            Name = agent.Name;
            LastName = agent.LastName;
            Active = agent.Active;
            TypeID = agent.TypeID;
            Image = agent.Image;
            Username = agent.Username;
            Password = agent.Password;
            StoreID = agent.StoreID;
            WarehouseID = agent.WarehouseID;
            Telephone = agent.Telephone;
            InternalNumber = agent.InternalNumber;
            OrderCancelLimit = agent.OrderCancelLimit;
            AuthId = agent.AuthId;
            CustomPrinter = agent.CustomPrinter;
        }

        public string DomainName { get; set; }
        public int UserId { get; set; }
        public string Name { get; set; }
        public string LastName { get; set; }
        public bool Active { get; set; }
        public int TypeID { get; set; }
        public string Image { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public int StoreID { get; set; }
        public int WarehouseID { get; set; }
        public string Telephone { get; set; }
        public string InternalNumber { get; set; }
        public int OrderCancelLimit { get; set; } = 0;
        public int AuthId { get; set; }
        public bool CustomPrinter { get; set; }
    }
}
