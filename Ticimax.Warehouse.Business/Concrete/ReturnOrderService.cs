using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Report.Enum;
using Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderReport.ValueObjects;
using Ticimax.Warehouse.Business.Concrete.ReturnOrder.Enums;
using Ticimax.Warehouse.Business.Concrete.ReturnOrder.Events;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Static;
using static iText.IO.Util.IntHashtable;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class ReturnOrderService : BaseService, IReturnOrderService
    {
        private readonly IOrderService _orderService;
        private readonly IOrderProductService _orderProductService;
        private readonly IOrderProductReturnCauseService _orderProductReturnCauseService;
        private readonly IOrderPaymentService _orderPaymentService;
        private readonly IOrderProductStatusService _orderProductStatusService;
        private readonly ITicimaxWarehouseService _ticimaxWarehouseService;
        private readonly IShelfService _shelfService;
        private readonly IShelfProductService _shelfProductService;
        private readonly IReturnPaymentService _returnPaymentService;
        private readonly ICustomerServicesManager _customerServicesManager;
        private readonly IMemberBalanceService _memberBalanceService;
        private readonly IOrderMovementService _orderMovementService;
        private readonly ILogger<ReturnOrderService> _logger;
        private readonly IOrderCampaignService _orderCampaignService;
        private readonly IReturnOrderRequestService _returnOrderRequestService;
        private readonly IProductMovementService _productMovementService;
        private readonly IBalanceService _balanceService;

        public ReturnOrderService(IOrderService orderService
            , IOrderPaymentService orderPaymentService
            , IOrderProductReturnCauseService orderProductReturnCauseService
            , IOrderProductService orderProductService
            , IOrderProductStatusService orderProductStatusService
            , ITicimaxWarehouseService ticimaxWarehouseService
            , IShelfService shelfService
            , IShelfProductService shelfProductService
            , IReturnPaymentService returnPaymentService
            , ICustomerServicesManager customerServicesManager
            , IMemberBalanceService memberBalanceService
            , IOrderMovementService orderMovementService
            , ILogger<ReturnOrderService> logger
            , IOrderCampaignService orderCampaignService
            , IReturnOrderRequestService returnOrderRequestService
            , IProductMovementService productMovementService
            , IBalanceService balanceService)
        {
            _orderService = orderService;
            _orderProductReturnCauseService = orderProductReturnCauseService;
            _orderProductService = orderProductService;
            _orderPaymentService = orderPaymentService;
            _orderProductStatusService = orderProductStatusService;
            _ticimaxWarehouseService = ticimaxWarehouseService;
            _shelfService = shelfService;
            _shelfProductService = shelfProductService;
            _returnPaymentService = returnPaymentService;
            _customerServicesManager = customerServicesManager;
            _memberBalanceService = memberBalanceService;
            _orderMovementService = orderMovementService;
            _logger = logger;
            _orderCampaignService = orderCampaignService;
            _returnOrderRequestService = returnOrderRequestService;
            _productMovementService = productMovementService;
            _balanceService = balanceService;
        }

        public async Task Completed(int id, ReturnOrderCompleatedDto request, CancellationToken cancellationToken)
        {
            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = id }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
                throw new NotFoundException("ORDER_IS_NOT_FOUND", new KeyValuePair<string, string>("Id", id.ToString()));

            if (request.DeliveryBeforeReturn &&
                !WebSiteInfo.User.Value.Settings.IadeAyar.PartialReturnOnUndeliveredOrder &&
                order.Status == (int)OrderStatus.TeslimEdildi)
                throw new BusinessException("CHECK_ORDER_STATUS");

            order.Products = new List<OrderProduct>();
            order.Payments = (await _orderPaymentService.GetList(new OrderPaymentGetListDto { OrderID = id }, cancellationToken)).Model;

            var returnCausies = (await _orderProductReturnCauseService.GetList(new OrderProductReturnCauseGetListDto { Active = 1 }, null, cancellationToken)).Model;
            var productStatus = (await _orderProductStatusService.GetList(new OrderProductStatusServiceGetListDto { OrderProductStatusID = 0, Active = 1, Operation = 2 }, cancellationToken)).Model.FirstOrDefault();
            if (WebSiteInfo.User.Value.Settings.IadeAyar.OrderProductCanceledStatus != 0)
            {
                productStatus = (await _orderProductStatusService.GetList(new OrderProductStatusServiceGetListDto
                { OrderProductStatusID = WebSiteInfo.User.Value.Settings.IadeAyar.OrderProductCanceledStatus },
                                cancellationToken)).Model.FirstOrDefault();
            }

            var defaultReturnCause = returnCausies.FirstOrDefault(x => x.Operation == (WebSiteInfo.User.Value.Settings.IadeAyar.StokEkle ? 1 : 2));
            var products = (await _orderProductService.GetProductList(new Core.Order.Entities.Filters.OrderProductFilter { OrderID = id, Status = -1, ImageAddress = WebSiteInfo.User.Value.ImagePath }, cancellationToken)).Model;

            if (request.DeliveryBeforeReturn && !WebSiteInfo.User.Value.Settings.IadeAyar.PartialReturnOnUndeliveredOrder)
            {
                if (defaultReturnCause == null)
                    throw new NotFoundException("DEFAULT_RETURN_REASON_COULD_NOT_BE_FOUND_TO_ADD_STOCK");
                products.ForEach(x =>
                {
                    x.ReturnPiece = x.Piece;
                    x.Status = productStatus.ID;
                    x.ReturnReasonID = defaultReturnCause.ID;
                });

                request.Products = products;
            }

            request.Products = request.Products.GroupBy(x => x.OrderProductID).Select(x => x.FirstOrDefault()).ToList();
            foreach (var product in request.Products)
            {
                var dbProduct = products.FirstOrDefault(x => x.OrderProductID == product.OrderProductID);
                if (dbProduct == null)
                    throw new NotFoundException("ORDER_PRODUCT_ID_IS_NOT_FOUND", new KeyValuePair<string, string>("orderProductId", product.OrderProductID.ToString()));

                dbProduct.ReturnPiece = product.ReturnPiece;
                dbProduct.Status = productStatus.ID;
                dbProduct.ReturnReasonID = product.ReturnReasonID > 0 ? product.ReturnReasonID : defaultReturnCause.ID;
                order.Products.Add(dbProduct);
            }

            SetOrderProductStatusRequest returnRequest = new SetOrderProductStatusRequest
            {
                OrderId = order.ID,
                Products = order.Products.Select(x =>
                    new OrderProductStatus
                    {
                        OrderLineId = x.OrderProductID,
                        Process = productStatus.Operation,
                        Quantity = x.ReturnPiece,
                        StatusId = productStatus.ID,
                        ReturnCauseId = x.ReturnReasonID
                    }).ToList()
            };

            await ReturnWarehouseCheck(order.Products, cancellationToken);

            var returnResponse = await _ticimaxWarehouseService.SetOrderProductStatus(returnRequest, cancellationToken);
            if (returnResponse.IsError)
                throw new BusinessException("SERVICE_NOT_READY",
                    new KeyValuePair<string, string>("exMessage", returnResponse.ErrorMessage),
                    new KeyValuePair<string, string>("model", returnResponse.Model != null ? returnResponse.Model.ToJsonSerialize() : ""));

            double totalReturnAmount = 0;
            double totalReturnAmountForReport = 0;
            double returnReduceShippingCostAmountForReport = 0;
            double returnPayDoorAmount = 0;
            double returnFixedAmount = 0;
            double returnCargoAmount = 0;
            double returnBankCommissionAmount = 0;
            List<ReturnOrderPaymentsInfo> returnOrderPaymentsInfo = new List<ReturnOrderPaymentsInfo>();
            if (!request.DeliveryBeforeReturn || (request.DeliveryBeforeReturn && order.PaymentTypeID != 2 && order.PaymentTypeID != 3))
            {
                if (WebSiteInfo.User.Value.Settings.IadeAyar.IadeListesineEkle)
                {
                    var approvedPayments = order.Payments.Where(x => x.Approved == 1 && x.Amount > 0).ToList();
                    returnOrderPaymentsInfo = approvedPayments.Select(x => new ReturnOrderPaymentsInfo { PaymentTypeId = x.PaymentType, PaymentAmount = x.Amount }).ToList();

                    double totalDiscount = order.GiftVoucherAmount + approvedPayments.Sum(x => x.PaymentDiscount);
                    double totalProductAmount = products.Sum(x => x.KDVIncludeAmount * x.Piece);
                    if (totalDiscount > 0)
                    {
                        double productDiscountRate = totalDiscount / totalProductAmount;
                        foreach (var product in products)
                        {
                            product.Amount -= product.Amount * productDiscountRate;
                            product.KDVAmount = product.Amount * (product.KDVRate / 100f);
                        }
                    }

                    List<int> prodCartControl = new List<int>();
                    foreach (var product in request.Products)
                    {
                        var orderProduct = products.FirstOrDefault(x => x.Barcode == product.Barcode && !prodCartControl.Contains(x.ProductID));
                        if (orderProduct != null)
                        {
                            prodCartControl.Add(orderProduct.ProductID);
                            product.Amount = orderProduct.Amount;
                            product.KDVAmount = orderProduct.KDVAmount;
                            totalReturnAmount += product.KDVIncludeAmount * product.ReturnPiece;
                        }
                    }

                    if (WebSiteInfo.User.Value.Settings.IadeAyar.RefundBankCommission
                                    && request.RefundBankCommission)
                    {
                        totalReturnAmount += approvedPayments.Sum(x => x.BankCommission);
                        returnBankCommissionAmount = approvedPayments.Sum(x => x.BankCommission);
                        order.AdditionalInfo.BankCommissionReturn = true;
                    }

                    if (request.CargoReturn && !order.AdditionalInfo.CargoReturn)
                    {
                        totalReturnAmount += order.CargoAmount;
                        order.AdditionalInfo.CargoReturn = true;
                    }
                    else
                        returnCargoAmount = order.CargoAmount;

                    if (!request.ReflectFixedShippingAmount)
                    {
                        if (WebSiteInfo.User.Value.Settings.IadeAyar.FixedCargoRefundAmount > 0)
                        {
                            totalReturnAmount -= WebSiteInfo.User.Value.Settings.IadeAyar.FixedCargoRefundAmount;
                            returnFixedAmount = WebSiteInfo.User.Value.Settings.IadeAyar.FixedCargoRefundAmount;
                        }
                    }

                    if (WebSiteInfo.User.Value.Settings.IadeAyar.ReduceShippingCost && request.ReduceShippingCostAmount > 0)
                    {
                        totalReturnAmount -= request.ReduceShippingCostAmount;
                        returnReduceShippingCostAmountForReport = request.ReduceShippingCostAmount;
                    }



                    if (request.PayDoorReturn && !order.AdditionalInfo.PayDoorReturn)
                    {
                        totalReturnAmount += approvedPayments.Sum(x => x.PayDoorAmount);
                        order.AdditionalInfo.PayDoorReturn = true;
                    }
                    else
                        returnPayDoorAmount = approvedPayments.Sum(x => x.PayDoorAmount);

                    if (WebSiteInfo.User.Value.Settings.IadeAyar.HediyeCekiniIadeEt && order.GiftVoucherAmount > 0)
                    {
                        if ((await _orderCampaignService.IsCheckReturnVouchers(order.ID, cancellationToken)).Model)
                        {
                            totalReturnAmount += order.GiftVoucherAmount;
                        }
                    }

                    var bankTransferPaymentType = new List<int>
                    {
                        (int)PaymentType.Nakit,
                        (int)PaymentType.Havale,
                        (int)PaymentType.KapidaOdemeKrediKarti,
                        (int)PaymentType.KapidaOdemeNakit
                    };

                    var kkPaymentType = new List<int>
                    {
                        (int)PaymentType.KrediKarti,
                        (int)PaymentType.PayCell,
                        (int)PaymentType.IyziPay,
                        (int)PaymentType.GarantiPay,
                        (int)PaymentType.Bakiye,
                        (int)PaymentType.HediyeCeki,
                        (int)PaymentType.HepsiPayV2
                    };
                    totalReturnAmountForReport = totalReturnAmount;
                    if (request.ReturnPaymentType > -1)
                    {
                        if (request.ReturnPaymentType != (int)OrderPaymentReturnType.Bakiye)
                        {
                            if (request.ReturnPaymentType == (int)OrderPaymentReturnType.KrediKartinaIade
                                || request.ReturnPaymentType == (int)OrderPaymentReturnType.PayCell
                                || request.ReturnPaymentType == (int)OrderPaymentReturnType.IyziPayIade
                                || request.ReturnPaymentType == (int)OrderPaymentReturnType.GarantiPay
                                || request.ReturnPaymentType == (int)OrderPaymentReturnType.HepsiPayV2)
                            {
                                kkPaymentType = kkPaymentType.OrderBy(x => request.ReturnPaymentType).ToList();
                                var selectedPayments = approvedPayments.Where(x => kkPaymentType.Contains(x.PaymentType)).OrderBy(x => kkPaymentType.IndexOf(x.PaymentType)).ToList();
                                foreach (var payment in selectedPayments)
                                {
                                    ReturnPaymentAddDto returnPayment = new ReturnPaymentAddDto
                                    {
                                        MemberID = order.MemberID,
                                        MemberName = order.Customer,
                                        OrderID = order.ID,
                                        PersonID = WebSiteInfo.User.Value.ID,
                                        PersonName = WebSiteInfo.User.Value.Name,
                                        PaymentID = payment.ID,
                                        PaymentType = payment.PaymentType,
                                        Date = DateTime.Now
                                    };

                                    if (totalReturnAmount > payment.Amount)
                                    {
                                        returnPayment.Amount = payment.Amount;
                                        await _returnPaymentService.Add(returnPayment, cancellationToken);
                                        totalReturnAmount -= payment.Amount;
                                    }
                                    else
                                    {
                                        returnPayment.Amount = totalReturnAmount;
                                        await _returnPaymentService.Add(returnPayment, cancellationToken);
                                        totalReturnAmount = 0;
                                    }
                                    totalReturnAmountForReport = returnPayment.Amount;
                                }

                                if (totalReturnAmount > 0.01)
                                {
                                    ReturnPaymentAddDto returnPayment = new ReturnPaymentAddDto
                                    {
                                        MemberID = order.MemberID,
                                        MemberName = order.Customer,
                                        OrderID = order.ID,
                                        PersonID = WebSiteInfo.User.Value.ID,
                                        PersonName = WebSiteInfo.User.Value.Name,
                                        PaymentID = 0,
                                        PaymentType = (int)PaymentType.Havale,
                                        Date = DateTime.Now,
                                        Amount = totalReturnAmount,
                                        IBAN = request.IBAN,
                                        IBANNameAndLastName = request.IBANName
                                    };

                                    //IBAN olmayan müşteriler aranacak.
                                    if (!string.IsNullOrEmpty(returnPayment.IBAN) && !string.IsNullOrEmpty(returnPayment.IBANNameAndLastName))
                                    {
                                        await _returnPaymentService.Add(returnPayment, cancellationToken);
                                    }
                                    else
                                    {
                                        await _customerServicesManager.Add(
                                            new CustomerServiceAddDto
                                            {
                                                CallingMemberName = order.Customer,
                                                CallByMemberID = order.MemberID,
                                                CallByPhoneNumber = order.CustomerTelephone,
                                                StatusID = (int)CustomerServiceStatus.Bekliyor,
                                                ReturnAmount = returnPayment.Amount,
                                                PaymentType = returnPayment.PaymentType,
                                                OrderID = order.ID,
                                                OrderNo = order.OrderNo,
                                                OrderDeliveryPhone = order.CustomerTelephone,
                                                Type = (int)CustomerServiceType.IbanBekleyenSiparisler,
                                                PaymentID = 0,
                                                CallByMemberName = "",
                                            }, cancellationToken);
                                        await _orderMovementService.AddAsync(new OrderMovementAddDto
                                        {
                                            OrderID = order.ID,
                                            AgentID = WebSiteInfo.User.Value.ID,
                                            isSystem = true,
                                            Name = WebSiteInfo.User.Value.Name,
                                            Message = "Müşteri hizmetlerine otomatik yönlendirildi.",
                                        }, cancellationToken);
                                    }
                                }
                            }
                            else
                            {
                                var bankTransferPaymentTypeAmount = approvedPayments.Where(x => bankTransferPaymentType.Contains(x.PaymentType)).Sum(x => x.Amount);
                                ReturnPaymentAddDto returnPayment = new ReturnPaymentAddDto
                                {
                                    MemberID = order.MemberID,
                                    MemberName = order.Customer,
                                    OrderID = order.ID,
                                    PersonID = WebSiteInfo.User.Value.ID,
                                    PersonName = WebSiteInfo.User.Value.Name,
                                    PaymentID = 0,
                                    PaymentType = (int)PaymentType.Havale,
                                    Date = DateTime.Now,
                                    IBAN = request.IBAN,
                                    Amount = totalReturnAmount,
                                    IBANNameAndLastName = request.IBANName
                                };

                                if (totalReturnAmount > bankTransferPaymentTypeAmount)
                                {
                                    returnPayment.Amount = bankTransferPaymentTypeAmount;
                                    totalReturnAmount -= bankTransferPaymentTypeAmount;
                                }
                                else
                                {
                                    returnPayment.Amount = totalReturnAmount;
                                    totalReturnAmount = 0;
                                }

                                if (!string.IsNullOrEmpty(returnPayment.IBAN) && !string.IsNullOrEmpty(returnPayment.IBANNameAndLastName))
                                {
                                    if (returnPayment.Amount > 0.01)
                                    {
                                        await _returnPaymentService.Add(returnPayment, cancellationToken);
                                    }
                                }
                                else
                                {
                                    await _customerServicesManager.Add(
                                        new CustomerServiceAddDto
                                        {
                                            CallingMemberName = order.Customer,
                                            CallByMemberID = order.MemberID,
                                            CallByPhoneNumber = order.CustomerTelephone,
                                            StatusID = (int)CustomerServiceStatus.Bekliyor,
                                            ReturnAmount = returnPayment.Amount,
                                            PaymentType = returnPayment.PaymentType,
                                            OrderID = order.ID,
                                            OrderNo = order.OrderNo,
                                            OrderDeliveryPhone = order.CustomerTelephone,
                                            Type = (int)CustomerServiceType.IbanBekleyenSiparisler,
                                            PaymentID = 0,
                                            CallByMemberName = "",
                                        }, cancellationToken);

                                    await _orderMovementService.AddAsync(new OrderMovementAddDto
                                    {
                                        OrderID = order.ID,
                                        AgentID = WebSiteInfo.User.Value.ID,
                                        isSystem = true,
                                        Name = WebSiteInfo.User.Value.Name,
                                        Message = "Müşteri hizmetlerine otomatik yönlendirildi.",
                                    }, cancellationToken);
                                }

                                if (totalReturnAmount > 0.01)
                                {
                                    var selectedPayments = approvedPayments.Where(x => !bankTransferPaymentType.Contains(x.PaymentType)).ToList();
                                    foreach (var payment in selectedPayments)
                                    {
                                        returnPayment.PaymentID = payment.ID;
                                        returnPayment.PaymentID = payment.PaymentType;
                                        returnPayment.Date = DateTime.Now;
                                        if (totalReturnAmount > payment.Amount)
                                        {
                                            returnPayment.Amount = payment.Amount;
                                            await _returnPaymentService.Add(returnPayment, cancellationToken);
                                            totalReturnAmount -= payment.Amount;
                                        }
                                        else
                                        {
                                            returnPayment.Amount = totalReturnAmount;
                                            await _returnPaymentService.Add(returnPayment, cancellationToken);
                                            totalReturnAmount = 0;
                                        }
                                    }
                                }
                                totalReturnAmountForReport = returnPayment.Amount;
                            }
                        }
                        else
                        {
                            if (order.MemberID == 0)
                            {
                                ReturnPaymentAddDto returnPayment = new ReturnPaymentAddDto
                                {
                                    MemberID = order.MemberID,
                                    MemberName = order.Customer,
                                    OrderID = order.ID,
                                    PersonID = WebSiteInfo.User.Value.ID,
                                    PersonName = WebSiteInfo.User.Value.Name,
                                    PaymentID = 0,
                                    PaymentType = (int)PaymentType.HediyeCeki,
                                    Date = DateTime.Now,
                                    Amount = totalReturnAmount
                                };

                                int returnAddPaymentID = (await _returnPaymentService.AddReturnID(returnPayment, cancellationToken)).Model;
                                var names = order.Customer.Split(' ');
                                await _ticimaxWarehouseService.CreateGiftVoucher(new CreateGiftVoucherRequest
                                {
                                    Amounth = totalReturnAmount,
                                    CreatedID = WebSiteInfo.User.Value.ID,
                                    CreatedName = WebSiteInfo.User.Value.Name,
                                    OrderNo = order.OrderNo,
                                    MemberName = names.FirstOrDefault(),
                                    MemberLastName = names.LastOrDefault(),
                                    MemberMailAddress = order.CustomerMail,
                                    GSM = order.CustomerTelephone,
                                    Phone = order.CustomerTelephone
                                }, cancellationToken);

                                await _orderPaymentService.AddPayment(
                                    new OrderPaymentAddDto
                                    {
                                        OrderID = order.ID,
                                        PaymentQueryApproved = true,
                                        PaymentType = (int)PaymentType.Bakiye,
                                        Amount = -1 * totalReturnAmount,
                                        Approved = 1,
                                        PaymentNote = "",
                                        Currency = order.CurrencyCode,
                                        MemberID = order.MemberID,
                                    }, cancellationToken);

                                await _orderService.UpdateAmount(new OrderServiceUpdateAmountDto { OrderID = order.ID }, cancellationToken);
                                await _returnPaymentService.UpdatePaid(new ReturnPaymentUpdatePaidDto { ID = returnAddPaymentID, isPaid = true }, cancellationToken);
                            }
                            else
                            {
                                ReturnPaymentAddDto returnPayment = new ReturnPaymentAddDto
                                {
                                    MemberID = order.MemberID,
                                    MemberName = order.Customer,
                                    OrderID = order.ID,
                                    PersonID = WebSiteInfo.User.Value.ID,
                                    PersonName = WebSiteInfo.User.Value.Name,
                                    PaymentID = 0,
                                    PaymentType = (int)PaymentType.Bakiye,
                                    Date = DateTime.Now,
                                    Amount = totalReturnAmount
                                };

                                int returnAddPaymentID = (await _returnPaymentService.AddReturnID(returnPayment, cancellationToken)).Model;
                                //TODO UYE BAKİYE EKLEME
                                var payments = approvedPayments
                                    .GroupBy(x => x.PaymentType)
                                    .Select(x =>
                                        new OrderPayment
                                        {
                                            ID = x.FirstOrDefault().ID,
                                            Amount = x.Sum(y => y.Amount),
                                            PaymentType = x.Key
                                        }).Where(x => x.Amount > 0).ToList();

                                var totalReturnAmounthPay = totalReturnAmount;
                                foreach (var payment in payments)
                                {
                                    if (totalReturnAmount > 0)
                                    {
                                        Balance balance = new Balance();
                                        balance.PersonID = order.MemberID;
                                        balance.Description = $"{order.OrderNo} no'lu siparişten iade yapıldı. (WMS)";
                                        balance.CurrencyCode = order.CurrencyCode;
                                        balance.AddingDate = DateTime.Now;
                                        balance.AddingPersonID = WebSiteInfo.User.Value.ID;
                                        balance.AddingPerson = WebSiteInfo.User.Value.Name;
                                        balance.OrderID = order.ID;

                                        MemberBalanceAddDto memberBalance = new MemberBalanceAddDto
                                        {
                                            MemberID = order.MemberID,
                                            CurrencyCode = order.CurrencyCode,
                                            Date = DateTime.Now
                                        };

                                        if (payment.PaymentType == (int)PaymentType.Bakiye)
                                        {
                                            foreach (var usedBalance in order.AdditionalInfo.MemberBalance.Where(x => x.RemainingRefundAmount > 0))
                                            {
                                                memberBalance.isRemittance = usedBalance.canBeTransferred;
                                                if (totalReturnAmount > usedBalance.RemainingRefundAmount)
                                                {
                                                    memberBalance.Amount = usedBalance.RemainingRefundAmount;
                                                    await _memberBalanceService.Add(memberBalance, cancellationToken);
                                                    totalReturnAmount -= usedBalance.RemainingRefundAmount;
                                                    usedBalance.RemainingRefundAmount = 0;
                                                }
                                                else
                                                {
                                                    memberBalance.Amount = totalReturnAmount;
                                                    await _memberBalanceService.Add(memberBalance, cancellationToken);
                                                    usedBalance.RemainingRefundAmount -= totalReturnAmount;
                                                    totalReturnAmount = 0;
                                                }
                                                balance.Amount = memberBalance.Amount;
                                                await _balanceService.AddAsync(balance, cancellationToken);
                                            }
                                            await _orderService.SetAdditionalInformation(new OrderSetAdditionalInformationDto { ID = order.ID, AdditionalInfo = order.AdditionalInfo }, cancellationToken);
                                        }
                                        else
                                        {
                                            memberBalance.isRemittance = bankTransferPaymentType.Contains(payment.PaymentType);
                                            if (totalReturnAmount > payment.Amount)
                                            {
                                                memberBalance.Amount = payment.Amount;
                                                await _memberBalanceService.Add(memberBalance, cancellationToken);
                                                totalReturnAmount -= payment.Amount;
                                            }
                                            else
                                            {
                                                memberBalance.Amount = totalReturnAmount;
                                                await _memberBalanceService.Add(memberBalance, cancellationToken);
                                                totalReturnAmount = 0;
                                            }

                                            balance.Amount = memberBalance.Amount;
                                            if (!memberBalance.isRemittance)
                                            {
                                                balance.PaymentID = payment.ID;
                                                balance.PaymentType = payment.PaymentType;
                                                balance.RemainingRefundAmount = memberBalance.Amount;
                                            }
                                            await _balanceService.AddAsync(balance, cancellationToken);
                                        }
                                    }
                                }
                                await _orderPaymentService.AddPayment(
                                    new OrderPaymentAddDto
                                    {
                                        OrderID = order.ID,
                                        PaymentQueryApproved = true,
                                        PaymentType = (int)PaymentType.Bakiye,
                                        Amount = -1 * totalReturnAmounthPay,
                                        Approved = 1,
                                        PaymentNote = "",
                                        Currency = order.CurrencyCode,
                                        MemberID = order.MemberID,
                                    }, cancellationToken);

                                totalReturnAmountForReport = totalReturnAmounthPay;

                                await _returnPaymentService.UpdatePaid(new ReturnPaymentUpdatePaidDto { ID = returnAddPaymentID, isPaid = true }, cancellationToken);
                            }
                        }
                    }
                    else
                    {
                        //Return Payment Type == -1  -> Müşteri Hizmetlerine aktarılsın.
                        //Return Payment Type == -2  -> Ayara bağlı olarak Kapıda ödeme&Teslim edilmemiş siparişlerin hızlı iadesi için sadece log atılması işlemi
                        string orderMovementMessage = "";
                        if (request.ReturnPaymentType == -1)
                        {
                            await _customerServicesManager.Add(
                            new CustomerServiceAddDto
                            {
                                CallByMemberName = order.Customer,
                                CallByMemberID = order.MemberID,
                                CallByPhoneNumber = order.CustomerTelephone,
                                StatusID = (int)CustomerServiceStatus.Bekliyor,
                                ReturnAmount = totalReturnAmount,
                                PaymentType = -1,
                                OrderID = order.ID,
                                OrderNo = order.OrderNo,
                                OrderDeliveryPhone = order.CustomerTelephone,
                                Type = (int)CustomerServiceType.IadeAramasiBekleyen,
                            }, cancellationToken);

                            orderMovementMessage = "Müşteri hizmetlerine otomatik yönlendirildi.";
                        }
                        else if (request.ReturnPaymentType == -2 && WebSiteInfo.User.Value.Settings.IadeAyar.CashOnDeliveryAndUndeliveredOrdersFastReturn)
                            orderMovementMessage = "Kapıda ödeme ve teslim edilmemiş sipariş olarak hızlı iade yapıldı.";

                        totalReturnAmountForReport = totalReturnAmount;
                        await _orderMovementService.AddAsync(new OrderMovementAddDto
                        {
                            OrderID = order.ID,
                            AgentID = WebSiteInfo.User.Value.ID,
                            isSystem = true,
                            Name = WebSiteInfo.User.Value.Name,
                            Message = orderMovementMessage,
                        }, cancellationToken);
                        await _orderService.SetAdditionalInformation(new OrderSetAdditionalInformationDto { ID = order.ID, AdditionalInfo = order.AdditionalInfo }, cancellationToken);
                    }
                }
            }
            else
            {
                if (WebSiteInfo.User.Value.Settings.IadeAyar.KapidanDonenSiparisMusteriHizmetlerineAktar)
                {
                    await _customerServicesManager.Add(
                        new CustomerServiceAddDto
                        {
                            CallByMemberName = order.Customer,
                            CallByMemberID = order.MemberID,
                            CallByPhoneNumber = order.CustomerTelephone,
                            StatusID = (int)CustomerServiceStatus.Bekliyor,
                            ReturnAmount = totalReturnAmount,
                            PaymentType = -1,
                            OrderID = order.ID,
                            OrderNo = order.OrderNo,
                            OrderDeliveryPhone = order.CustomerTelephone,
                            Type = (int)CustomerServiceType.IadeAramasiBekleyen,
                        }, cancellationToken);

                    await _orderMovementService.AddAsync(new OrderMovementAddDto
                    {
                        OrderID = order.ID,
                        AgentID = WebSiteInfo.User.Value.ID,
                        isSystem = true,
                        Name = WebSiteInfo.User.Value.Name,
                        Message = "Müşteri hizmetlerine otomatik yönlendirildi.",
                    }, cancellationToken);
                }

                var doorPayments = order.Payments.Where(x => x.Approved == 1 && x.Amount > 0 && (x.PaymentType == 2 || x.PaymentType == 3));
                foreach (var payment in doorPayments)
                {
                    await _orderPaymentService.SetApproved(new OrderPaymentSetApprovedDto { ID = payment.ID, Approved = 4 }, cancellationToken);
                }

                if (WebSiteInfo.User.Value.Settings.IadeAyar.KOYasaklamaSiparisAdedi > 0 && order.MemberID > 0)
                {
                    int totalNotDelivery = 1;
                    var pastOrders = (await _orderService.GetList(new OrderGetListDto { MemberID = order.MemberID, PaymentTypeList = new List<int> { 2, 3 }, OrderStatus = (OrderStatus)WebSiteInfo.User.Value.Settings.IadeAyar.TeslimatOncesiIadeDurumu }, null, cancellationToken)).Model.Count;
                    if (pastOrders > 0)
                    {
                        totalNotDelivery += pastOrders;
                    }

                    if (totalNotDelivery >= WebSiteInfo.User.Value.Settings.IadeAyar.KOYasaklamaSiparisAdedi)
                    {
                        await _returnPaymentService.CustomerRedPayDoor(new ReturnPaymentCustomerRedPayDoorDto { ID = order.MemberID }, cancellationToken);
                    }
                }

            }


            _logger.LogTrace(JsonSerializerWrapper.Serialize(new TiciWmsLogDto
            {
                Date = DateTime.Now,
                Details = request.ToJsonSerialize(),
                LogType = "IADE",
                PersonID = WebSiteInfo.User.Value.ID,
                Process = "DEPOURUNTOPLAMA",
            }));

            List<Task> waitingTask = new List<Task>();


            string returnPaymentTypeStr = "";
            if (request.ReturnPaymentType == -1)
                returnPaymentTypeStr = "Müşteri Hizmetlerine Aktarılsın.";
            else
                returnPaymentTypeStr = EnumExtensions.GetStringValue<OrderPaymentReturnType>(request.ReturnPaymentType);

            waitingTask.Add(_orderMovementService.AddAsync(
                new OrderMovementAddDto
                {
                    AgentID = WebSiteInfo.User.Value.ID,
                    isSystem = true,
                    Message = $"Siparişte {totalReturnAmountForReport.ToString("F2")} {order.CurrencyCode} ({returnPaymentTypeStr}) iade ödemesi için işlem yapılmıştır.",
                    Name = WebSiteInfo.User.Value.Name,
                    OrderID = id,
                }, cancellationToken));

            SetOrderStatusRequest statusRequest = new SetOrderStatusRequest
            {
                OrderId = id,
                SendMail = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.MailBilgilendir,
            };

            if (request.DeliveryBeforeReturn)
                statusRequest.StatusId = WebSiteInfo.User.Value.Settings.IadeAyar.TeslimatOncesiIadeDurumu;
            else if (products.Sum(x => x.Piece) == request.Products.Sum(x => x.ReturnPiece))
                statusRequest.StatusId = WebSiteInfo.User.Value.Settings.IadeAyar.TamIadeDurumu;
            else
                statusRequest.StatusId = WebSiteInfo.User.Value.Settings.IadeAyar.ParcaliIadeDurumu;

            if (statusRequest.StatusId > -1)
                await _orderService.SetOrderStatus(new OrderSetStatusDto(statusRequest.OrderId, statusRequest.StatusId, false), cancellationToken);

            DateTime defaultDate = new DateTime(2000, 1, 1, 0, 0, 0);
            DateTime now = DateTime.Now;


            var returnOrderRequestId = await _returnOrderRequestService.Add(new ReturnOrderRequest()
            {
                MemberId = order.MemberID,
                MemberName = order.Customer,
                OrderId = order.ID,
                PaymentTypeId = order.PaymentTypeID,
                CreatedDate = DateTime.Now,
                CargoCompanyId = order.CargoCompanyID,
                CargoIntegrationId = order.CargoIntegrationId,
                CargoCode = order.CargoCode,
                ReasonForCancellationId = defaultReturnCause.ID,//IPTALIADENEDEN_ID
                GiftCertificateCode = order.GiftVoucher,
                IbanName = request.IBANName,
                Iban = request.IBAN,
                Amount = totalReturnAmountForReport,
                Currency = order.CurrencyCode,
                StoreId = WebSiteInfo.User.Value.StoreID,
                OrderNo = order.OrderNo,
                StatusId = 2

            }, cancellationToken);

            foreach (var item in order.Products)
            {
                //Osman: 18.10.2024 WMS-2992 -> Veritabanından kontrol edildi. İşlem yapıldı olarak panelde görünen kayıtların
                //iadetalep_urun  tablosunda ürün kaydı bulunmuyor. Bu sebeple yorum satırına alındı. 
                //_returnOrderRequestService.Add StatusId güncellendi.


                //await _returnOrderRequestService.AddProduct(new ReturnOrderProductRequest()
                //{
                //    ReturnRequestId = returnOrderRequestId,
                //    OrderId = order.ID,
                //    OrderProductId = item.OrderProductID,
                //    ProductCardId = item.ProductCardID,
                //    ProductId = item.ProductID,
                //    Quantity = item.Piece,
                //    CancelReturnRequestId = defaultReturnCause.ID,
                //    StatusId = 1
                //}, cancellationToken);

                _logger.LogTrace(JsonSerializerWrapper.Serialize(new ReturnOrderLogDto
                {
                    OrderID = item.OrderID,
                    OrderNumber = order.OrderNo,
                    ProductID = item.ProductID,
                    ProductType = 1,
                    Piece = item.Piece,
                    PersonID = WebSiteInfo.User.Value.ID,
                    CargoReturn = request.CargoReturn,
                    DeliveryBeforeReturn = request.DeliveryBeforeReturn,
                    OrderStatus = statusRequest.StatusId,
                    ReturnCauseID = defaultReturnCause.ID,
                    ReturnPaymentType = request.ReturnPaymentType,
                    IBAN = request.IBAN,
                    IBANName = request.IBANName,
                }));

                waitingTask.Add(_productMovementService.CreateMovement(item.ProductID, new CreateProductMovementRequest(ProductMovementProcessType.ReturnProductFromReturnOrder, item.Piece, ProductMovementMessage.ReturnProductFromReturnOrder(item.OrderID), null), cancellationToken));

                WebSiteInfo.User.Value.Events.Add(new DomainEvent(ReturnOrderEvent.Completed, new ReturnOrderCompletedEvent(item.OrderID, item.OrderProductID, order.Date.ToTimestamp(), new ReturnOrderCompletedProduct(item.ProductID, item.ProductName, item.SalesUnit, item.ReturnPiece, item.Amount, item.ReturnReasonID, item.CategoryId, request.DeliveryBeforeReturn, item.SupplierId, item.Supplier, item.AdditionalOptions), new ReturnOrderInfo(returnPayDoorAmount, returnFixedAmount, returnCargoAmount, request.ReturnPaymentType, totalReturnAmountForReport, returnReduceShippingCostAmountForReport, DateTime.Now.ToTimestamp(), returnOrderPaymentsInfo, item.ReturnPiece, order.MemberID, order.Customer, returnBankCommissionAmount))));
            }

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(ReturnOrderEvent.PaymentReportCompleted, new ReturnOrderPaymentCompletedEvent(order.ID, order.Date.ToTimestamp(), new ReturnOrderInfo(returnPayDoorAmount, returnFixedAmount, returnCargoAmount, request.ReturnPaymentType, totalReturnAmountForReport, returnReduceShippingCostAmountForReport, DateTime.Now.ToTimestamp(), returnOrderPaymentsInfo, order.Products.Select(x => x.ReturnPiece).Sum(), order.MemberID, order.Customer, returnBankCommissionAmount))));

            if (WebSiteInfo.User.Value.Settings.IadeAyar.EntegrasyonAktif)
                await _orderService.SetPackagingStatus(new OrderSetPackagingStatusDto { IDs = new List<int> { id }, StatusID = WebSiteInfo.User.Value.Settings.IadeAyar.IadeFaturaKesilecekPaketlemeDurumID }, cancellationToken);

            Task.WaitAll(waitingTask.ToArray());

        }

        private async Task ReturnWarehouseCheck(List<OrderProduct> products, CancellationToken cancellationToken)
        {
            if (WebSiteInfo.User.Value.Settings.IadeAyar.IadeDeposu > 0)
            {
                var returnWarehouseShelf = (await _shelfService.GetList(new ShelfGetListDto { WarehouseId = WebSiteInfo.User.Value.Settings.IadeAyar.IadeDeposu }, new PagingDto(0, 1), cancellationToken)).Model.FirstOrDefault();
                if (returnWarehouseShelf != null)
                {
                    await _shelfProductService.Add(
                        new ShelfProductAddDto
                        {
                            Type = ProductMovementStockControlTypeEnum.ReturnOrderProductStockAdd.ToString(),
                            ObjectId = products.Select(x => x.OrderID).Distinct().FirstOrDefault().ToString(),
                            ShelfItems = products.Select(x =>
                                new ShelfProductAddItemDto
                                {
                                    ProductID = x.ProductID,
                                    ShelfID = returnWarehouseShelf.ID,
                                    WarehouseID = returnWarehouseShelf.WarehouseID,
                                    ShelfStock = x.ReturnPiece
                                }).ToList(),
                        }, cancellationToken);
                }
            }
        }

        public async Task<DataResult<ReturnOrderGetOrderResponseDto>> GetOrder(string barcode, CancellationToken cancellationToken)
        {
            DataResult<ReturnOrderGetOrderResponseDto> response = new DataResult<ReturnOrderGetOrderResponseDto>();
            response.Model = new ReturnOrderGetOrderResponseDto();

            int orderId = 0;
            var order = (await _orderService.GetList(new OrderGetListDto { OrderNo = barcode }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
            {
                order = (await _orderService.GetList(new OrderGetListDto { CargoCode = barcode }, null, cancellationToken)).Model.FirstOrDefault();
                if (order == null && int.TryParse(barcode, out orderId))
                    order = (await _orderService.GetList(new OrderGetListDto { OrderID = orderId }, null, cancellationToken)).Model.FirstOrDefault();
            }

            if (order == null)
                throw new NotFoundException("ORDER_IS_NOT_FOUND", new KeyValuePair<string, string>("barcode", barcode));

            await _ticimaxWarehouseService.OrderCargoControl(
                new OrderCargoControlRequest
                {
                    OrderIDs = new List<int> { order.ID },
                    AccessToken = (WebSiteInfo.User.Value.DomainName + DateTime.Now.Day + DateTime.Now.Month + DateTime.Now.Year).ConvertStringToMD5().Sha256Hash()
                }, cancellationToken);


            order.Products = (await _orderProductService.GetProductList(new Core.Order.Entities.Filters.OrderProductFilter { OrderID = order.ID, Status = -1, ProductNameFields = WebSiteInfo.User.Value.Settings.UrunAdiAlani, ImageAddress = WebSiteInfo.User.Value.ImagePath }, cancellationToken)).Model;
            if (order.Products.Count > 0)
            {
                response.Model.isCampaignOrder = order.Products.Any(x => x.CampaignDiscountAmount > 0);

                var campaigns = (await _orderCampaignService.GetList(
                    new Core.Order.Entities.Filters.OrderCampaignFilter
                    {
                        OrderID = order.ID
                    }, null, cancellationToken)).Model.Where(x => x.ProductID > 0).ToList();

                foreach (var campaign in campaigns)
                {
                    var product = order.Products.OrderByDescending(x => x.CampaignDiscountAmount).FirstOrDefault(x => x.ProductID == campaign.ProductID && !x.CampaignDefinition.Any(y => y.CampaignID == campaign.CampaignID));
                    if (product != null)
                        product.CampaignDefinition.Add(new OrderProductCampaign { CampaignID = campaign.CampaignID, CampaignDefinition = campaign.CampaignDescription });
                }

                var returnOrderProductRequests = await _returnOrderRequestService.GetReturnRequestProducts(new ReturnOrderProductRequestFilter { OrderId = order.ID }, cancellationToken);
                if (returnOrderProductRequests.Count > 0)
                {
                    foreach (var product in order.Products)
                    {
                        var returnOrderProductRequest = returnOrderProductRequests.FirstOrDefault(x => x.OrderProductId == product.OrderProductID);
                        product.CancelReturnRequestId = returnOrderProductRequest?.CancelReturnRequestId ?? 0;
                        product.CancelReturnRequestName = returnOrderProductRequest?.CancelReturnRequestName;
                        product.CancelReturnReasonTypeName = returnOrderProductRequest?.CancelReturnReasonTypeName;
                    }
                }

                response.Model.ReturnOrderRequest = await _returnOrderRequestService.GetReturnRequest(order.ID, null, cancellationToken);

                order.Payments = (await _orderPaymentService.GetList(new OrderPaymentGetListDto { OrderID = order.ID, isApproved = true }, cancellationToken)).Model;
                response.Model.Order = order;
                response.Model.ReturnCausies = (await _orderProductReturnCauseService.GetList(new OrderProductReturnCauseGetListDto { Active = 1 }, null, cancellationToken)).Model;
                var returnType = Enumeration.GetAll<OrderPaymentReturnType>() as Dictionary<int, string>;

                if (!order.Payments.Any(x => x.PaymentType == (int)PaymentType.IyziPay))
                    returnType.Remove((int)OrderPaymentReturnType.IyziPayIade);

                if (!order.Payments.Any(x => x.PaymentType == (int)PaymentType.GarantiPay))
                    returnType.Remove((int)OrderPaymentReturnType.GarantiPay);

                if (!order.Payments.Any(x => x.PaymentType == (int)PaymentType.PayCell))
                    returnType.Remove((int)OrderPaymentReturnType.PayCell);

                if (!order.Payments.Any(x => x.PaymentType == (int)PaymentType.HepsiPayV2))
                    returnType.Remove((int)OrderPaymentReturnType.HepsiPayV2);

                if (!WebSiteInfo.User.Value.PaymentSettings.BakiyeAyar.Aktif)
                    returnType.Remove((int)OrderPaymentReturnType.Bakiye);

                if (WebSiteInfo.User.Value.OrderSettings.IadeAyar.OdemeTipineGoreIade)
                {
                    var groupByPaymentType = order.Payments.Where(x => x.Amount > 0 && x.Approved == 1).GroupBy(x => x.PaymentType).Select(x => x.Key).ToList();

                    List<int> showReturnType = new List<int>();

                    if (WebSiteInfo.User.Value.PaymentSettings.BakiyeAyar.Aktif && order.MemberID != 0)
                    {
                        showReturnType.Add((int)OrderPaymentReturnType.Bakiye);
                    }

                    foreach (var paymentType in groupByPaymentType)
                    {
                        if (paymentType == (int)PaymentType.KrediKarti)
                        {
                            showReturnType.Add((int)OrderPaymentReturnType.KrediKartinaIade);
                        }
                        else if (paymentType == (int)PaymentType.IyziPay)
                        {
                            showReturnType.Add((int)OrderPaymentReturnType.IyziPayIade);
                        }
                        else if (paymentType == (int)PaymentType.GarantiPay)
                        {
                            showReturnType.Add((int)OrderPaymentReturnType.GarantiPay);
                        }
                        else if (paymentType == (int)PaymentType.PayCell)
                        {
                            showReturnType.Add((int)OrderPaymentReturnType.PayCell);
                        }
                        else if (paymentType == (int)PaymentType.KapidaOdemeKrediKarti || paymentType == (int)PaymentType.KapidaOdemeNakit || paymentType == (int)PaymentType.Nakit || paymentType == (int)PaymentType.Havale)
                        {
                            showReturnType.Add((int)OrderPaymentReturnType.BankaHesabinaIade);
                        }
                        else if (paymentType == (int)PaymentType.HepsiPayV2)
                        {
                            showReturnType.Add((int)OrderPaymentReturnType.KrediKartinaIade);
                            showReturnType.Add((int)OrderPaymentReturnType.BankaHesabinaIade);
                        }
                    }

                    returnType = returnType.Where(x => showReturnType.Distinct().Contains(x.Key.ToInt32())).ToDictionary(t => t.Key, t => t.Value);
                }

                response.Model.ReturnType = returnType;
            }
            else
                throw new NotFoundException("ORDER_RETURN_PRODUCTS_IS_NOT_FOUND");

            return response;
        }

        public async Task<List<ReturnOrderProductRequest>> GetReturnRequest(int orderId, CancellationToken cancellationToken)
        {
            if (orderId <= 0)
                throw new NotFoundException("ORDER_NOT_FOUND");

            var returnOrderProductRequests = await _returnOrderRequestService.GetReturnRequestProducts(new ReturnOrderProductRequestFilter { OrderId = orderId }, cancellationToken);
            if (returnOrderProductRequests == null)
                return new List<ReturnOrderProductRequest>();

            return returnOrderProductRequests;
        }
    }
}