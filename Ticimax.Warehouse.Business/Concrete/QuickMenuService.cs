using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class QuickMenuService : BaseService, IQuickMenuService
    {
        private readonly IStoreAgentDal _storeAgentDal;

        public QuickMenuService(IStoreAgentDal storeAgentDal)
        {
            _storeAgentDal = storeAgentDal;
        }

        public async Task<ErrorResponse> UpdateTableQuickMenu(QuickMenuUpdateDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            await _storeAgentDal.UpdateTableQuickMenuAsync(
                new Entities.Concrete.StoreAgent
                {
                    ID = WebSiteInfo.User.Value.ID,
                    QuickMenus = request.QuickMenus.Take(5).ToList()
                }, cancellationToken);

            return response;
        }

        public async Task<DataResult<List<QuickMenu>>> GetQuickMenus(CancellationToken cancellationToken)
        {
            DataResult<List<QuickMenu>> response = new DataResult<List<QuickMenu>>();

            response.Model = await _storeAgentDal.GetQuickMenusAsync(
                new Entities.Concrete.StoreAgent
                {
                    ID = WebSiteInfo.User.Value.ID
                }, cancellationToken);

            return response;
        }
    }
}