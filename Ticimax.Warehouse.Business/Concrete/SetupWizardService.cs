using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Business.Abstract;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class SetupWizardService : ISetupWizardService
    {
        private readonly IWarehouseService _warehouseService;
        private readonly IStoreAgentService _storeAgentService;
        private readonly IWarehouseAuthGroupDefinitionService _warehouseAuthGroupDefinitionService;
        private readonly IWarehouseAuthGroupService _warehouseAuthGroupService;
        private readonly IInvoiceThemeDal _invoiceThemeDal;
        private readonly IInvoiceSettingsDal _invoiceSettingsDal;
        private readonly IWarehouseAuthGroupDal _warehouseAuthGroupDal;
        private readonly IShelfService _shelfService;
        private readonly ITransactionalCommandService _transactionalCommandService;

        public SetupWizardService
        (
            IWarehouseService warehouseService,
            IStoreAgentService storeAgentService,
            IWarehouseAuthGroupDefinitionService warehouseAuthGroupDefinitionService,
            IWarehouseAuthGroupService warehouseAuthGroupService,
            IInvoiceThemeDal invoiceThemeDal,
            IInvoiceSettingsDal invoiceSettingsDal,
            IWarehouseAuthGroupDal warehouseAuthGroupDal,
            IShelfService shelfService,
            ITransactionalCommandService transactionalCommandService)
        {
            _warehouseService = warehouseService;
            _storeAgentService = storeAgentService;
            _warehouseAuthGroupDefinitionService = warehouseAuthGroupDefinitionService;
            _warehouseAuthGroupService = warehouseAuthGroupService;
            _invoiceThemeDal = invoiceThemeDal;
            _invoiceSettingsDal = invoiceSettingsDal;
            _warehouseAuthGroupDal = warehouseAuthGroupDal;
            _shelfService = shelfService;
            _transactionalCommandService = transactionalCommandService;
        }

        public async Task Completed(CancellationToken cancellationToken)
        {
            //ticimax_fatura_ayar tablosu dolduruluyor...
            await _invoiceSettingsDal.AddAsync(new InvoiceSettings
            {
                Series = "A",
                RankStart = 0,
                InvoiceNoLength = 10,
                Template = "{ }",
                TemplateSettings = new InvoiceTemplateSettings()
            }, cancellationToken);

            //Varsayılan şema ekleniyor.
            await _invoiceThemeDal.AddAsync(new InvoiceTheme
            {
                Name = "Varsayılan Şema",
                Html = await File.ReadAllTextAsync("wwwroot/InvoicePage/Invoice.html", cancellationToken),
                IsDefault = true,
                Settings = new InvoiceThemeSettings()
            }, cancellationToken);

            //Sistem de tanımlı olan bütün depoların mal kabul lokasyonları ve ana base lokasyonları oluşturuluyor...
            var waitForAddShelf = new List<ShelfAddDto>();
            var warehouses = (await _warehouseService.GetList(new WarehouseGetListDto(), null, cancellationToken)).Model.ToList();
            foreach (var item in warehouses)
            {
                waitForAddShelf.Add(new ShelfAddDto { Barcode = "MALKABULALANI", Code = "MALKABULALANI", Definition = "Mal Kabul Alanı", Rank = 999, IsMissingProductShelf = false, IsEmptyShelf = true, StoreID = item.StoreID, WarehouseID = item.ID });
                waitForAddShelf.Add(new ShelfAddDto { Barcode = "ANALOKASYON", Code = "ANALOKASYON", Definition = "Ana Lokasyon", Rank = 999, IsMissingProductShelf = false, IsEmptyShelf = true, StoreID = item.StoreID, WarehouseID = item.ID });
            }

            await _shelfService.MultipleAdd(new ShelfAddListDto { ShelfList = waitForAddShelf }, cancellationToken);

            //Setup Kullanıcısını Siliyoruz.
            await _storeAgentService.Delete(new StoreAgentDeleteDto { ID = WebSiteInfo.User.Value.ID }, cancellationToken);
        }

        public async Task Started(CancellationToken cancellationToken)
        {
            await CreateDefaultAuthGroups(cancellationToken);
        }

        public async Task CreateDefaultAuthGroups(CancellationToken cancellationToken)
        {
            var alreadyHaveGroups = new List<string>
            {
                "Yonetici",
                "Toplayici",
                "Birlestirme",
                "KaliteKontrol",
                "MalKabulveUrunYerlestirme",
                "Sayim",
                "Iade"
            };

            var authGroupsCount = await _warehouseAuthGroupDal.GetCountAsync(cancellationToken: cancellationToken);
            if (authGroupsCount == 0)
            {
                foreach (var group in alreadyHaveGroups)
                {
                    await _warehouseAuthGroupService.Add(new WarehouseAuthGroupAddDto { Defination = group, IsDefault = true }, cancellationToken);
                }

                await _transactionalCommandService.TransactionalExecuteAsync();
            }

            var authGroups = await _warehouseAuthGroupDal.GetListAsync(cancellationToken: cancellationToken);

            foreach (var group in alreadyHaveGroups)
            {
                var authGroup = authGroups.FirstOrDefault(x => x.Defination == group);
                if (authGroup != null)
                    await _warehouseAuthGroupDefinitionService.GetList(new WarehouseAuthGroupDefinitionGetListDto { AuthGroupID = authGroup.ID }, WebSiteInfo.User.Value.Version, cancellationToken);
            }
        }
    }
}