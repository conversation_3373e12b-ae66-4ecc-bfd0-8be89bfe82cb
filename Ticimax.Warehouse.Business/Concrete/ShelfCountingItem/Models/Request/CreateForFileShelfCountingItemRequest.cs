using System;
using System.Collections.Generic;

namespace Ticimax.Warehouse.Business.Concrete.ShelfCountingItem.Models.Request
{
    public class CreateForFileShelfCountingItemRequest
    {
        public CreateForFileShelfCountingItemRequest()
        {
        }

        public CreateForFileShelfCountingItemRequest(Guid fileId, List<int> shelfIds, List<int> productIds, bool selectAll)
        {
            FileId = fileId;
            ShelfIds = shelfIds;
            ProductIds = productIds;
            SelectAll = selectAll;
        }

        public Guid FileId { get; set; }

        public List<int> ShelfIds { get; set; }

        public List<int> ProductIds { get; set; }

        public bool SelectAll { get; set; }
    }
}