using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Business.Concrete;
using Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request;
using Ticimax.Core.Product.Business.Concrete.ProductMovement;
using Ticimax.Core.Product.Entities.Concrete;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums;
using Ticimax.Core.Product.Entities.Dtos;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.ShelfCountingFileItem.Events;
using Ticimax.Warehouse.Business.Concrete.ShelfCountingItem.Enums;
using Ticimax.Warehouse.Business.Concrete.ShelfCountingItem.Models.Request;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.Item;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.Item.Dtos;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.Item.Enums;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.ShelfCountingItem
{
#nullable enable
    public class ShelfCountingItemService : IShelfCountingItemService
    {
        private readonly IShelfCountingItemDal _shelfCountingItemDal;
        private readonly IShelfProductService _shelfProductService;
        private readonly IShelfService _shelfService;
        private readonly IProductService _productService;
        private readonly IOrderCollectionService _orderCollectionService;
        private readonly IServiceProvider _services;
        private readonly IPickingProductDal _pickingProductDal;
        private readonly IProductMovementService _productMovementService;


        public ShelfCountingItemService(
            IShelfCountingItemDal shelfCountingItemDal,
            IShelfProductService shelfProductService,
            IShelfService shelfService,
            IProductService productService,
            IOrderCollectionService orderCollectionService, IServiceProvider services, IPickingProductDal pickingProductDal, IProductMovementService productMovementService)
        {
            _shelfCountingItemDal = shelfCountingItemDal;
            _shelfProductService = shelfProductService;
            _shelfService = shelfService;
            _productService = productService;
            _orderCollectionService = orderCollectionService;
            _services = services;
            _pickingProductDal = pickingProductDal;
            _productMovementService = productMovementService;
        }

        public async Task<Pageable<ShelfCountingItemDetailedDto>> GetDetailed(Guid? fileId, int? shelfId, int? productId, string? status, bool? pickedProduct, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _shelfCountingItemDal.Get(fileId, shelfId, productId, status, pickedProduct, pageSize, pageIndex, cancellationToken);
            var counts = await _shelfCountingItemDal.Count(fileId, shelfId, productId, status, pickedProduct, cancellationToken);

            var productIds = contents.Select(x => x.ProductId).ToList();
            List<Product> products = new List<Product>();
            if (productIds.Any())
                products = await _productService.GetListAsync(new ProductGetListDto() { IDList = productIds }, cancellationToken: cancellationToken);

            var list = new List<ShelfCountingItemDetailedDto>();
            foreach (var content in contents)
            {
                var product = products.FirstOrDefault(x => x.ID == content.ProductId);
                if (product == null)
                    continue;

                list.Add(new ShelfCountingItemDetailedDto(product.Image, product.ProductName + " " + product.AdditionalOptions, product.Barcode, product.PurchaseAmount, content));
            }

            return new Pageable<ShelfCountingItemDetailedDto>(pageIndex, pageSize, counts, list);
        }

        public async Task<ShelfCountingItemAggregate> GetById(Guid id, CancellationToken cancellationToken)
        {
            var item = await _shelfCountingItemDal.GetById(id, cancellationToken);
            if (item == null)
                throw new NotFoundException("SHELF_COUNTING_ITEM_NOT_FOUND", new KeyValuePair<string, string>("Id", id.ToString()));

            return item;
        }

        public async Task<Pageable<ShelfCountingItemAggregate>> Get(Guid? fileId, int? shelfId, int? productId, string? status, bool? pickedProduct, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _shelfCountingItemDal.Get(fileId, shelfId, productId, status, pickedProduct, pageSize, pageIndex, cancellationToken);
            var counts = await _shelfCountingItemDal.Count(fileId, shelfId, productId, status, pickedProduct, cancellationToken);

            return new Pageable<ShelfCountingItemAggregate>(pageIndex, pageSize, counts, contents);
        }

        public async Task<ShelfCountingItemDetailedDto> Create(CreateShelfCountingItemRequest request, CancellationToken cancellationToken)
        {
            var shelf = (await _shelfService.GetList(new ShelfGetListDto
            { ID = request.ShelfId },
                null,
                cancellationToken)).Model.FirstOrDefault();

            if (shelf == null)
                throw new NotFoundException("SHELF_NOT_FOUND", new KeyValuePair<string, string>("id", request.ShelfId.ToString()));

            var alreadyHaveProducts = await Get(request.FileId, request.ShelfId, null, null, null, int.MaxValue, 0, cancellationToken);
            if (alreadyHaveProducts.Contents.Count > 0 && !alreadyHaveProducts.Contents.Any(x => x.Status != ShelfCountingItemStatus.Created || x.Status != ShelfCountingItemStatus.Processing))
                throw new BusinessException("SHELF_STATUS_IS_NOT_VALID");

            var product = (await _productService.GetListAsync(new ProductGetListDto() { ID = request.ProductId }, cancellationToken: cancellationToken)).FirstOrDefault();

            var item = new ShelfCountingItemAggregate(request.FileId, request.ShelfId, shelf.Definition, request.ProductId, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, request.ProductOldCount, request.ProductCount, product.PurchasePrice);

            await _shelfCountingItemDal.Create(item, cancellationToken);


            return new ShelfCountingItemDetailedDto(product.Image, product.ProductName + " " + product.AdditionalOptions, product.Barcode, product.PurchaseAmount, item);
        }

        public async Task ResetByShelfIdAndProductId(int shelfId, int productId, CancellationToken cancellationToken)
        {
            var contents = await _shelfCountingItemDal.Get(null, shelfId, productId > 0 ? productId : null, null, null, int.MaxValue, 0, cancellationToken);
            contents.ForEach(x =>
            {
                x.Reset();
                _shelfCountingItemDal.Reset(x, cancellationToken);
            });
        }

        public async Task<List<ShelfCountingItemAggregate>> CreateByFile(CreateForFileShelfCountingItemRequest request, CancellationToken cancellationToken)
        {
            List<int> shelfIds = null;
            if (request.SelectAll && !request.ShelfIds.Any())
                request.ShelfIds = (await _shelfService.GetList(new ShelfGetListDto(), null, cancellationToken)).Model.Select(x => x.ID).ToList();

            if (request.ShelfIds.Any())
            {
                var shelfIncludesChild = new List<ShelfIncludesChildDto>();
                foreach (var shelfId in request.ShelfIds)
                {
                    var shelf = await _shelfService.GetShelfIncludesChildById(shelfId, cancellationToken);
                    shelfIncludesChild.Add(shelf);
                }

                shelfIds = shelfIncludesChild.SelectMany(x => x.ChildShelfs).Select(x => x.ID).ToList();
                shelfIds.AddRange(request.ShelfIds);
            }

            var shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto
            { ShelfIds = shelfIds, ProductIDList = request.ProductIds, IsStockAvailable = true },
                null,
                cancellationToken)).Model;

            if(shelfIds == null && request.ProductIds != null && request.ProductIds.Count > 0 && shelfProducts.Count > 0)
                shelfIds = shelfProducts.Select(x => x.ShelfID).ToList();

            var shelfs = (await _shelfService.GetList(new ShelfGetListDto() { IDList = shelfIds })).Model;
            //shelfIds = shelfProducts.Select(x => x.ShelfID).ToList();

            if (shelfProducts.Count > 0)
            {
                //var shelfCountingItems = shelfProducts.
                //                Select(x => new ShelfCountingItemAggregate(request.FileId, x.ShelfID, x.ShelfName,
                //                x.ProductID, 0, "", x.ShelfStock, 0, x.PurchasePrice)).ToList();

                List<ShelfCountingItemAggregate> shelfCountingItems = new List<ShelfCountingItemAggregate>();

                foreach (var shelf in shelfs)
                {
                    var shelfProduct = shelfProducts.Where(x => x.ShelfID == shelf.ID).ToList();
                    if(shelfProduct != null && shelfProduct.Count > 0)
                    {
                        shelfCountingItems.AddRange(shelfProduct.
                               Select(x => new ShelfCountingItemAggregate(request.FileId, x.ShelfID, x.ShelfName,
                                x.ProductID, 0, "", x.ShelfStock, 0, x.PurchasePrice)).ToList());
                    }
                    else
                    {
                        shelfCountingItems.Add(new ShelfCountingItemAggregate(request.FileId, shelf.ID, shelf.Definition,
                        0, 0, "", 0, 0, 0));
                    }
                }

                foreach (var shelfId in shelfIds)
                {
                    if (!shelfCountingItems.Any(x => x.ShelfId == shelfId))
                    {
                        var shelf = await _shelfService.GetList(new ShelfGetListDto() { ID = shelfId });
                        shelfCountingItems.Add(new ShelfCountingItemAggregate(request.FileId) { ShelfId = shelfId, ShelfName = shelf.Model.FirstOrDefault().Definition });
                    }
                }

                await _shelfCountingItemDal.CreateBulk(shelfCountingItems, cancellationToken);

                return shelfCountingItems;
            }

            var shelfModel = await _shelfService.GetList(new ShelfGetListDto() { IDList = shelfIds });
            var bulkList = shelfIds.Select(x => new ShelfCountingItemAggregate(request.FileId) { ShelfId = x, ShelfName = shelfModel.Model.FirstOrDefault().Definition }).ToList();

            if (bulkList == null || bulkList.Count == 0)
                throw new NotFoundException("SHELF_COUNTING_FILE_ITEMS_NOT_FOUND");

            await _shelfCountingItemDal.CreateBulk(bulkList, cancellationToken);

            return bulkList;
        }

        public async Task<(double, double)> CompletedByFile(Guid fileId, bool changeEcommerce, CancellationToken cancellationToken)
        {
            var shelfItems = new List<ShelfProductAddItemDto>();
            var allItems = new List<ShelfCountingItemAggregate>();
            var items = await Get(fileId, null, null, ShelfCountingItemStatus.ShelfCompleted, null, int.MaxValue, 0, cancellationToken);
            allItems.AddRange(items.Contents);
            var productIds = items.Contents.Select(x => x.ProductId).Where(x => x > 0).Distinct().ToList();

            foreach (var productId in productIds)
            {
                var products = await Get(null, null, productId, ShelfCountingItemStatus.ShelfCompleted, null, int.MaxValue, 0, cancellationToken);
                allItems.AddRange(products.Contents.Where(x => x.FileId != fileId));
            }

            double productCount = (await Get(fileId, null, null, null, null, int.MaxValue, 0, cancellationToken)).Contents.Where(x => x.Status != ShelfCountingItemStatus.Created && x.Status != ShelfCountingItemStatus.Processing).Sum(x => x.ProductCount);

            var allocatedProducts = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        FindStatus = false,
                        PreparedStatusList = new List<int> { 0, 1, 6 }
                    }
                },
                cancellationToken)).Model;

            var fileProducts = await _productService.GetList(new ProductGetListDto() { IDList = productIds, AddSubQueries = false });
            foreach (var x in allItems)
            {
                x.Completed();
                var fileProduct = fileProducts.Model.FirstOrDefault(o => o.ID == x.ProductId);
                if (fileProduct != null)
                    x.UpdateTotalCost(x.ProductCount * fileProduct.PurchasePrice);

                await _shelfCountingItemDal.Completed(x, cancellationToken);
                if (x.ProductId <= 0 || x.ShelfId <= 0)
                    continue;

                await _shelfProductService.DeleteByProductAndShelfID(new ShelfProductDeleteByProductIDAndShelfIDDto() { ProductID = x.ProductId, ShelfID = x.ShelfId }, cancellationToken);
                var thisShelfProductWaiting = allocatedProducts.Products.Where(y => y.ProductID == x.ProductId && y.ShelfID == x.ShelfId).Sum(y => y.Piece);

                if(x.ProductCount - thisShelfProductWaiting == 0)
                {
                    await _productMovementService.CreateMovement(
                               x.ProductId,
                               new CreateProductMovementRequest(ProductMovementProcessType.ShelfCountingRegulation, 0, ProductMovementMessage.ShelfCountingRegulation(x.ShelfName), null),
                               cancellationToken);
                }

                if (thisShelfProductWaiting >= x.ProductCount)
                {
                    continue;
                }
                else if (thisShelfProductWaiting < x.ProductCount)
                {
                    shelfItems.Add(new ShelfProductAddItemDto
                    {
                        ProductID = x.ProductId,
                        ShelfID = x.ShelfId,
                        ShelfStock = x.ProductCount - thisShelfProductWaiting,
                    });
                }
            }

            if (shelfItems.Any())
            {
                await _shelfProductService.Add(new ShelfProductAddDto
                {
                    ShelfItems = shelfItems,
                    ShelfCounting = true
                },
                    cancellationToken);
            }

            if (changeEcommerce)
            {
                var transferWaitProduct = await _services.GetRequiredService<IWarehouseProductTransferService>().GetNonPickProducts(cancellationToken);

                var filter = new PickingProductFilter
                {
                    OrderStatus = OrderStatus.On,
                    PackageStatus = PackageStatus.Beklemede,
                    PriorityPackageStatus = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu,
                };

                var alreadyHaveOrders = await _pickingProductDal.GetListAsync(filter, null, cancellationToken);

                foreach (var productGroup in items.Contents.GroupBy(x => x.ProductId))
                {
                    if (productGroup.Key <= 0)
                        continue;

                    var alreadySoldProductCount = alreadyHaveOrders.Where(x => x.ProductID == productGroup.Key).Sum(x => x.Piece);
                    //var shelfStoreId = WebSiteInfo.User.Value.IsOneStore ? 0 : (await _shelfService.GetList(new ShelfGetListDto() { ID = content.ShelfId }, null, cancellationToken)).Model.First().StoreID;
                    double stock = productGroup.Sum(x => x.ProductCount);
                    foreach (var content in productGroup)
                    {
                        var transferProductCount = transferWaitProduct.Where(x => x.ProductId == content.ProductId && x.ShelfId == content.ShelfId).Sum(x => x.Quantity);
                        stock -= transferProductCount;

                        var assignedProductCount = allocatedProducts.Products.Where(x => x.ProductID == content.ProductId && x.ShelfID == content.ShelfId).Sum(x => x.Piece);
                        stock -= assignedProductCount;
                    }

                    stock -= alreadySoldProductCount;

                    if (stock < 0)
                        stock = 0;

                    await _productService.OptimizeStock(new ProductAddStockDto() { ConsignmentModuleActive = WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive, ProductID = productGroup.Key, Piece = stock, StoreID = 0 }, cancellationToken);
                }
            }

            return (productCount, allItems.Sum(x => x.TotalCost));
        }

        public async Task Pick(Guid id, double quantity, CancellationToken cancellationToken)
        {
            var item = await _shelfCountingItemDal.GetById(id, cancellationToken);
            if (item == null)
                throw new NotFoundException("SHELF_COUNTING_ITEM_IS_NOT_FOUND");
            if (item.Status == ShelfCountingItemStatus.ShelfCompleted)
                throw new BusinessException("SHELF_ALREADY_HAVE_COMPLETED");

            item.Pick(quantity, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name);
            await _shelfCountingItemDal.Pick(item, cancellationToken);

            //var file = await _shelfCountingFilesService.GetById(item.FileId, cancellationToken);
            //file.Processing();
            //await _shelfCountingFilesService.Update(file.Id, new UpdateShelfCountingFileRequest(file.Name, file.Status), cancellationToken);

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfCountingFileItemEvents.Picked, new ShelfCountingFileItemEventPayload(item)));
        }

        public async Task ShelfComplete(Guid fileId, int shelfId, CancellationToken cancellationToken)
        {
            var items = await Get(fileId, shelfId, null, null, null, int.MaxValue, 0, cancellationToken);
            foreach (var item in items.Contents)
            {
                item.ShelfCompleted(WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name);
                await _shelfCountingItemDal.ShelfComplete(item, cancellationToken);
            }
        }

        public async Task Revert(Guid id, double quantity, CancellationToken cancellationToken)
        {
            var item = await _shelfCountingItemDal.GetById(id, cancellationToken);
            item.Revert(quantity);
            await _shelfCountingItemDal.Revert(item, cancellationToken);
        }

        public async Task Delete(Guid id, CancellationToken cancellationToken)
        {
            var item = await GetById(id, cancellationToken);
            item.Delete();
            await _shelfCountingItemDal.Delete(id, cancellationToken);
        }

        public async Task DeleteByFile(Guid id, CancellationToken cancellationToken)
        {
            var items = await Get(id, null, null, null, null, int.MaxValue, 0, cancellationToken);
            items.Contents.ForEach(x => x.Delete());
            await _shelfCountingItemDal.DeleteByFileId(id, cancellationToken);
        }

        public async Task<Pageable<ShelfCountingItemGroupedByShelfDto>> GetGroupedByShelf(Guid? fileId, int differentType, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _shelfCountingItemDal.GetGroupedByShelf(fileId, differentType, pageSize, pageIndex, cancellationToken);
            var counts = await _shelfCountingItemDal.CountGroupedByShelf(fileId, differentType, cancellationToken);

            return new Pageable<ShelfCountingItemGroupedByShelfDto>(pageIndex, pageSize, counts, contents);
        }

        public async Task<Pageable<ShelfCountingItemGroupedByProductDto>> GetGroupedByProduct(ShelfCountingGrouppedByProductRequest request, CancellationToken cancellationToken)
        {
            var contents = await _shelfCountingItemDal.GetGroupedByProduct(request.FileId,
                request.ProductId, request.Status, request.ShelfID, request.CreatedDateStart, request.CreatedDateEnd, request.DifferentType, request.ShelfAndProductGroup,
                request.PageSize, request.PageIndex, cancellationToken);
            var counts = await _shelfCountingItemDal.GetGroupedByProductCount(request.FileId, 
                request.ProductId, request.Status, request.ShelfID, request.CreatedDateStart, request.CreatedDateEnd, request.DifferentType, request.ShelfAndProductGroup, cancellationToken);

            return new Pageable<ShelfCountingItemGroupedByProductDto>(request.PageIndex, request.PageSize, counts, contents);
        }

       
    }
}