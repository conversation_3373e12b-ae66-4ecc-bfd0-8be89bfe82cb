using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using RedLockNet.SERedis;
using RedLockNet.SERedis.Configuration;
using StackExchange.Redis;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Business.Concrete;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.KargoHelper.DTO.Enums;
using Ticimax.KargoHelper.DTO.Models;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Cargo.Events;
using Ticimax.Warehouse.Business.Concrete.CargoChange.Enums;
using Ticimax.Warehouse.Business.Concrete.CargoChange.Events;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.PrintService;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Static;
using static Google.Apis.Requests.BatchRequest;

namespace Ticimax.Warehouse.Business.Concrete.Cargo
{
    public class CargoService : ICargoService
    {
        private readonly ICargoIntegrationService _cargoIntegrationService;
        private readonly ITicimaxWarehouseService _ticimaxWarehouseService;
        private readonly IOrderCargoPacketService _orderCargoPacketService;
        private readonly IConnectionMultiplexer _redisConnect;
        private readonly ICargoCompanyService _cargoCompanyService;
        private readonly IPrintService _printService;
        private readonly IOrderService _orderService;
        private readonly IOrderMovementService _orderMovementService;
        private readonly IOrderPaymentService _orderPaymentService;


        public CargoService(
            ICargoIntegrationService cargoIntegrationService
            , ITicimaxWarehouseService ticimaxWarehouseService
            , IOrderCargoPacketService orderCargoPacketService
            , IConnectionMultiplexer redisConnect, ICargoCompanyService cargoCompanyService, IPrintService printService
            , IOrderService orderService, IOrderMovementService orderMovementService, IOrderPaymentService orderPaymentService)
        {
            _cargoIntegrationService = cargoIntegrationService;
            _ticimaxWarehouseService = ticimaxWarehouseService;
            _orderCargoPacketService = orderCargoPacketService;
            _redisConnect = redisConnect;
            _cargoCompanyService = cargoCompanyService;
            _printService = printService;
            _orderService = orderService;
            _orderMovementService = orderMovementService;
            _orderPaymentService = orderPaymentService;
        }

        public async Task<BindPacketResponse> Send(int orderId, int cargoCompanyId, int cargoIntegrationId, int orderPaymentType, List<int> orderProductIds = null, bool multiPacketCargoSend = false, string shippingType = "", int packageCount = 0, CancellationToken cancellationToken = default)
        {
            BindPacketResponse packetResponse = null;
            if (!WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.KargoGonderimAktif)
                return null;

            var cargoCompany = (await _cargoCompanyService.GetList(new CargoCompanyGetListDto() { ID = cargoCompanyId }, null, cancellationToken)).Model.FirstOrDefault();
            if (cargoCompany == null)
                throw new NotFoundException("CARGO_COMPANY_IS_NOT_FOUND", new KeyValuePair<string, string>("cargoId", cargoCompanyId.ToString()));

            if (!cargoCompany.WarehouseSendActive)
                return null;

            CargoIntegration integration = null;
            var orderPayments = (await _orderPaymentService.GetList(new OrderPaymentGetListDto { OrderID = orderId, isApproved = true }, cancellationToken)).Model;
            bool isPaymentAtDooorContains = false;
            if(orderPayments.Count > 1)
                isPaymentAtDooorContains = orderPayments.Any(x => x.PaymentType == (int)PaymentType.KapidaOdemeKrediKarti || x.PaymentType == (int)PaymentType.KapidaOdemeNakit);
            
            if (WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.CargoSendUseIntegrationId)
            {
                if (cargoIntegrationId > 0)
                    integration = (await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto { ID = cargoIntegrationId, Active = 1 }, null, cancellationToken)).Model.FirstOrDefault();
                else
                {
                    if (orderPaymentType == (int)PaymentType.KapidaOdemeNakit || orderPaymentType == (int)PaymentType.KapidaOdemeKrediKarti || isPaymentAtDooorContains)
                        integration = (await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto { ID = WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.DefaultDoorPaymentCargoIntegrationId, Active = 1 }, null, cancellationToken)).Model.FirstOrDefault();
                    else
                        integration = (await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto { ID = WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.DefaultCargoIntegrationId, Active = 1 }, null, cancellationToken)).Model.FirstOrDefault();
                }
            }
            else
            {
                var cargoIntegrations = (await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto { CargoCompanyID = cargoCompanyId, Active = 1 }, null, cancellationToken)).Model;
                if (WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.SetCargoIntegrationnCashOnDeliveryType)
                {
                    if (orderPaymentType == (int)PaymentType.KapidaOdemeNakit || orderPaymentType == (int)PaymentType.KapidaOdemeKrediKarti || isPaymentAtDooorContains)
                        integration = cargoIntegrations.FirstOrDefault(x => x.WarehouseSettings.IsPaymentAtTheDoor);
                    else
                        integration = cargoIntegrations.FirstOrDefault(x => !x.WarehouseSettings.IsPaymentAtTheDoor);
                }

                if (integration == null)
                    integration = cargoIntegrations.FirstOrDefault();
            }


            if (integration == null)
                throw new NotFoundException("CARGO_INTEGRATION_IS_NOT_FOUND");

            var resultCargoApi = await _ticimaxWarehouseService.CargoApiLogin(integration.ApiUsername, integration.ApiPassword, integration.ID);
            string cargoApiAccessToken = resultCargoApi?.access_token;

            var settings = new BLAyar
            {
                AlanAdi = WebSiteInfo.User.Value.DomainName,
                BolgeAyar = WebSiteInfo.User.Value.SiteSettings.BolgeAyar.ToJsonSerialize().ToJsonDeserialize<BLBolgeAyar>(),
                FirmaBilgileri = WebSiteInfo.User.Value.CompanyInformation.ToJsonSerialize().ToJsonDeserialize<BLFirmaBilgileri>(),
                IadeKargoAyar = WebSiteInfo.User.Value.ReturnCargoSettings.ToJsonSerialize().ToJsonDeserialize<BLIadeKargoAyar>(),
                KapidaOdemeSmsOnayAktif = WebSiteInfo.User.Value.PayDoorCheckoutSmsApprovalActive,
                MailSystemVersion = WebSiteInfo.User.Value.SiteSettings.MailSystemVersion,
                UserID = WebSiteInfo.User.Value.ID,
                VarsayilanParaBirimi = WebSiteInfo.User.Value.SiteSettings.VarsayilanParaBirimi,
                VarsayilanSiteDili = WebSiteInfo.User.Value.SiteSettings.VarsayilanSiteDili,
                LisansId = WebSiteInfo.User.Value.MinioSettings.FolderName,
                SatisOndalikBasamak = WebSiteInfo.User.Value.SiteSettings.FiyatFormat.SatisOndalikBasamak,
                KargoApiAccessToken = cargoApiAccessToken
            };

            MySqlConnection cargoCnn = new MySqlConnection();
            if (cargoCnn.State != ConnectionState.Open)
            {
                cargoCnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value) + "DefaultCommandTimeout=1800;";
                await cargoCnn.OpenAsync(cancellationToken);
            }

            var packet = KargoHelper.BLL.Cargo.CreatePacket(ref cargoCnn, ref cargoCnn, orderId, integration.ID, WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.KargoSiparisUrunDurumId, settings, orderProductIds, packageCount);
            if (packet == null || packet.ID == 0)
            {
                WebSiteInfo.User.Value.Commands = new List<IDbCommand>();
                throw new NotFoundException("PACKET_IS_NOT_CREATED");
            }

            var cargoSettings = integration.WarehouseSettings;
            var cargoApiParameter = new KargoApiParameter();
            if (cargoSettings != null)
            {
                cargoApiParameter.BranchCode = cargoSettings.BranchCode;
                cargoApiParameter.GonderiTipi = cargoSettings.ShippingType;
                cargoApiParameter.KoliAdet = cargoSettings.BoxPiece;
                cargoApiParameter.MasrafKodu = cargoSettings.CostCode;
                cargoApiParameter.SiparisDurumu = (SiparisDurumlari)cargoSettings.OrderStatus;
                cargoApiParameter.SmsGonder = cargoSettings.SmsSend;
                cargoApiParameter.SmsGonderAyariMng = cargoSettings.SmsSelection;
                cargoApiParameter.TelefonIhbar = cargoSettings.TelephoneNotice;
                cargoApiParameter.HepsijetV2 = new KargoApiParametreHepsijetV2(WebSiteInfo.User.Value.Warehouse);
                if (multiPacketCargoSend)
                {
                    cargoApiParameter.KoliAdet = packageCount;
                    cargoApiParameter.GonderiTipi = shippingType;
                }
            }

            using (var redlockFactory = RedLockFactory.Create(new List<RedLockMultiplexer> { new RedLockMultiplexer(_redisConnect) }))
            {
                var resource = $"{Info.DomainName.Value}:SendCargo";
                var expiryTime = TimeSpan.FromSeconds(120);
                var waitTime = TimeSpan.FromSeconds(240);
                var retryTime = TimeSpan.FromMilliseconds(300);

                await using (var redLock = await redlockFactory.CreateLockAsync(resource, expiryTime, waitTime, retryTime, cancellationToken))
                {
                    if (redLock.IsAcquired)
                    {
                        if (cargoCnn.State != ConnectionState.Open)
                            await cargoCnn.OpenAsync(cancellationToken);

                        packetResponse = KargoHelper.BLL.Cargo.BindPacket(ref cargoCnn, ref cargoCnn, settings, cargoApiParameter, packet, 0);
                        if (packetResponse.IsError)
                        {
                            if (cargoCnn.State != ConnectionState.Closed)
                                await cargoCnn.CloseAsync();

                            WebSiteInfo.User.Value.Commands = new List<IDbCommand>();
                            throw new BusinessException(packetResponse.ErrorMessage);
                        }
                    }
                    else
                    {
                        if (cargoCnn.State != ConnectionState.Closed)
                            await cargoCnn.CloseAsync();
                        WebSiteInfo.User.Value.Commands = new List<IDbCommand>();
                        throw new BusinessException("PROCESS_USE_A_ANOTHER_USER");
                    }
                }
            }
            if (WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.CargoSendUseBackground)
                WebSiteInfo.User.Value.Events.Add(new DomainEvent("wms.cargo.send", new SendCargoEvent(Info.DomainName.Value, settings, packetResponse, cargoApiParameter)));
            else
                KargoHelper.BLL.Cargo.SendPacket(ref cargoCnn, ref cargoCnn, settings, packetResponse.KargoPaketi, packetResponse.bLKargoEntegrasyon, packetResponse.SiparisKargoPaketi, packetResponse.siparis, cargoApiParameter, packetResponse.pttBarkod, false);

            if (cargoCnn.State != ConnectionState.Closed)
                await cargoCnn.CloseAsync();
            return packetResponse;
        }

        public async Task<BindPacketResponse> ReSendByPacketId(int cargoPacketId, CancellationToken cancellationToken)
        {
            var packet = await _orderCargoPacketService.GetById(cargoPacketId, cancellationToken);

            await _ticimaxWarehouseService.SetOrderProductStatus(new SetOrderProductStatusRequest
            {
                OrderId = packet.OrderId,
                Products = packet.OrderProductIds.Select(x => new OrderProductStatus
                { OrderLineId = x, Process = (int)UrunDurumlari.Beklemede, StatusId = 0, Quantity = 0 }).ToList()
            }, cancellationToken);

            await _orderCargoPacketService.Delete(cargoPacketId, cancellationToken);

            return await Send(packet.OrderId, packet.CargoCompanyId, packet.CargoIntegrationId, packet.OrderPaymentType, packet.OrderProductIds, cancellationToken: cancellationToken);
        }

        public async Task<List<BindPacketResponse>> ReSendByOrderId(int orderId, CancellationToken cancellationToken)
        {
            var sendResponses = new List<BindPacketResponse>();

            var packets = await _orderCargoPacketService.GetByOrderId(orderId, cancellationToken);
            foreach (var packet in packets)
            {
                await _ticimaxWarehouseService.SetOrderProductStatus(new SetOrderProductStatusRequest
                {
                    OrderId = packet.OrderId,
                    Products = packet.OrderProductIds.Select(x => new OrderProductStatus
                    { OrderLineId = x, Process = (int)UrunDurumlari.Beklemede, StatusId = 0, Quantity = 0 }).ToList()
                }, cancellationToken);

                await _orderCargoPacketService.Delete(packet.Id, cancellationToken);

                sendResponses.Add(await Send(packet.OrderId, packet.CargoCompanyId, packet.CargoIntegrationId, packet.OrderPaymentType, packet.OrderProductIds));
            }

            return sendResponses;
        }

        public async Task<CargoPrintDto> ReSendButChangeIntegrationByCargoBarcode(int id, int cargoIntegrationId, CancellationToken cancellationToken)
        {
            var packet = await _orderCargoPacketService.GetById(id, cancellationToken);

            await _ticimaxWarehouseService.SetOrderProductStatus(new SetOrderProductStatusRequest
            {
                OrderId = packet.OrderId,
                Products = packet.OrderProductIds.Select(x => new OrderProductStatus
                { OrderLineId = x, Process = (int)UrunDurumlari.Beklemede, StatusId = 0, Quantity = 0 }).ToList()
            }, cancellationToken);

            await _orderService.UpdateOrderCargoIntegration(packet.OrderId, cargoIntegrationId, cancellationToken);

            await _orderCargoPacketService.Delete(packet.Id, cancellationToken);
            var newPacket = await Send(packet.OrderId, packet.CargoCompanyId, cargoIntegrationId, packet.OrderPaymentType, packet.OrderProductIds, cancellationToken: cancellationToken);
            var printData = await _printService.PrintCargo(packet.OrderId, newPacket.SiparisKargoPaketi.ID, cargoIntegrationId, null, false, cancellationToken);

            await _orderMovementService.AddAsync(new OrderMovementAddDto
            {
                OrderID = packet.OrderId,
                AgentID = WebSiteInfo.User.Value.ID,
                isSystem = true,
                Name = WebSiteInfo.User.Value.Name,
                Message = @$"{DateTime.Now.ToString("dd.MM.yyyy HH:mm")} tarihinde, 
                        {WebSiteInfo.User.Value.Name} kullanıcısı 
                        {packet.CargoIntegrationDefinition} kargodan
                        {newPacket.SiparisKargoPaketi.Kargo_Entegrasyon_Tanim} 
                        kargoya değiştirilmiştir.",
            }, cancellationToken);

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(CargoChangeEvent.Completed,
                        new CargoChangeCompletedEvent(WebSiteInfo.User.Value.DomainName, WebSiteInfo.User.Value.WarehouseID,
                        WebSiteInfo.User.Value.StoreID, packet.Barcode, newPacket?.KargoPaketi?.OrderCode, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, packet.OrderId,
                        packet.CargoIntegrationId, packet.CargoCompanyId, cargoIntegrationId, newPacket.bLKargoEntegrasyon.KargoFirmaID, DateTime.Now.ToTimestamp())));

            return new CargoPrintDto(printData);
        }
    }
}