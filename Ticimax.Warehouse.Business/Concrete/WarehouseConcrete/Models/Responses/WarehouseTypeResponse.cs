using System.Collections.Generic;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseConcrete.Models.Responses
{
    public class WarehouseTypeResponse
    {
        public List<WarehouseTypeResponseItem> Types { get; set; } = new List<WarehouseTypeResponseItem>();
    }

    public class WarehouseTypeResponseItem
    {
        public WarehouseTypeResponseItem(int id, string name)
        {
            Id = id;
            Name = name;
        }

        public int Id { get; set; }

        public string Name { get; set; }
    }
}