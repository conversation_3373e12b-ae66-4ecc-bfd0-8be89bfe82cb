using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Entities.Concrete;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.StoreDtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class StoreService : BaseService, IStoreService
    {
        private readonly IStoreDal _storeDal;
        private readonly IStoreAgentService _storeAgentService;
        private readonly IWarehouseDal _warehouseDal;
        private readonly ICountryCityDistrictDal _countryCityDistrictDal;

        public StoreService(IStoreDal storeDal, ICountryCityDistrictDal countryCityDistrictDal, IWarehouseDal warehouseDal, IStoreAgentService storeAgentService)
        {
            _storeDal = storeDal;
            _countryCityDistrictDal = countryCityDistrictDal;
            _warehouseDal = warehouseDal;
            _storeAgentService = storeAgentService;
        }

        public async Task<ErrorResponse> Add(StoreAddDto request, CancellationToken cancellationToken)
        {
            List<Country> countries = await _countryCityDistrictDal.GetCountryListAsync(cancellationToken);
            if (request.CountryID > 0 && !countries.Any(x => x.ID == request.CountryID))
                throw new BusinessException("UlkeBilgisiHatali");

            List<City> cities = await _countryCityDistrictDal.GetCityListAsync(new CityFilter { ID = request.ProvinceID }, cancellationToken);
            if (request.ProvinceID > 0 && !cities.Any())
                throw new BusinessException("IlBilgisiHatali");

            List<District> districts = await _countryCityDistrictDal.GetDistrictListAsync(new DistrictFilter { DistrictID = request.DistrictID }, cancellationToken);
            if (request.DistrictID > 0 && !districts.Any())
                throw new BusinessException("SemtBilgisiHatali");

            ErrorResponse response = new ErrorResponse();

            await _storeDal.AddAsync(request.ToEntity(), cancellationToken);

            return response;
        }

        public async Task<ErrorResponse> Update(int id, StoreUpdateDto request, CancellationToken cancellationToken)
        {
            var store = (await _storeDal.GetListAsync(new StoreFilter { StoreID = id }, null, cancellationToken)).FirstOrDefault();
            if (store == null)
                throw new BusinessException("MagazaBilgisiHatali");

            List<Country> countries = await _countryCityDistrictDal.GetCountryListAsync(cancellationToken);
            if (request.CountryID > 0 && !countries.Any(x => x.ID == request.CountryID))
                throw new BusinessException("UlkeBilgisiHatali");

            store.CountryID = request.CountryID;

            List<City> cities = await _countryCityDistrictDal.GetCityListAsync(new CityFilter { ID = request.ProvinceID }, cancellationToken);
            if (request.ProvinceID > 0 && !cities.Any())
                throw new BusinessException("IlBilgisiHatali");

            store.ProvinceID = request.ProvinceID;

            List<District> districts = await _countryCityDistrictDal.GetDistrictListAsync(new DistrictFilter { DistrictID = request.DistrictID }, cancellationToken);
            if (request.DistrictID > 0 && !districts.Any())
                throw new BusinessException("SemtBilgisiHatali");

            store.DistrictID = request.DistrictID;

            ErrorResponse response = new ErrorResponse();

            if (!string.IsNullOrEmpty(request.Description))
                store.Description = request.Description;

            if (!string.IsNullOrEmpty(request.Address))
                store.Address = request.Address;

            if (!string.IsNullOrEmpty(request.Telephone))
                store.Telephone = request.Telephone;

            if (!string.IsNullOrEmpty(request.Faks))
                store.Faks = request.Faks;

            if (!string.IsNullOrEmpty(request.Gsm))
                store.Gsm = request.Gsm;

            if (!string.IsNullOrEmpty(request.StoreCode))
                store.StoreCode = request.StoreCode;

            await _storeDal.UpdateAsync(store, cancellationToken);
            return response;
        }

        public async Task Delete(int id, CancellationToken cancellationToken)
        {
            var store = (await _storeDal.GetListAsync(new StoreFilter { StoreID = id }, null, cancellationToken)).FirstOrDefault();
            if (store == null)
                throw new BusinessException("MagazaBilgisiHatali");

            var warehouses = await _warehouseDal.GetListAsync(new WarehouseFilter { StoreID = id });
            if (warehouses != null && warehouses.Count > 0)
                throw new BusinessException("MagazayaBagliDepolarBulundugundanMagazaSilinimez");

            var agents = (await _storeAgentService.GetList(new StoreAgentGetListDto { StoreID = id }, null, cancellationToken)).Model;
            if (agents != null && agents.Count > 0)
                throw new BusinessException("MagazayaBagliKullanicilarBulundugundanMagazaSilinimez");

            //var orders = _orderService.GetList(new OrderGetListDto { StoreID = id });
            //if (orders != null && orders.Model.Count > 0)
            //    throw new BusinessException("MagazayaBagliSiparislerBulundugundanMagazaSilinimez");

            await _storeDal.DeleteAsync(store, cancellationToken);
        }

        public async Task<DataResult<List<Store>>> GetList(StoreGetListDto request, PagingDto paging, CancellationToken cancellationToken)
        {
            DataResult<List<Store>> response = new DataResult<List<Store>>();

            response.Model = await _storeDal.GetListAsync(request.ToFilter(), paging != null ? new StorePaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken);
            if (request.isGetCount)
                response.Count = await _storeDal.GetCountAsync(request.ToFilter(), cancellationToken);

            return response;
        }

        public async Task<DataResult<int>> GetCount(StoreGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _storeDal.GetCountAsync(request.ToFilter(), cancellationToken);

            return response;
        }
    }
}