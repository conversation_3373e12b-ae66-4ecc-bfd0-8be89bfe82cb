using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Enums;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Request;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Response;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseJobZone
{
    public class WarehouseJobZoneService : IWarehouseJobZoneService
    {
        private readonly string _jobZoneApiUrl;

        public WarehouseJobZoneService(IConfiguration configuration)
        {
            _jobZoneApiUrl = configuration.GetValue<string>(ConfigKeys.JobZoneApiUrl);
        }

        #region Job
        public async Task CreateWarehouseJob(CreateWarehouseJobRequest request, CancellationToken cancellationToken)
        {
            request.CreatorId = WebSiteInfo.User.Value.ID;
            var payload = JsonSerializerWrapper.Serialize(request);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_jobZoneApiUrl}jobs"),
                Method = HttpMethod.Post,
                Content = new StringContent(payload, Encoding.UTF8, "application/json")
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new HttpClient())
            {
                var response = await client.SendAsync(message, cancellationToken);
                await response.HandleKnownExceptions();
                if (response.IsSuccessStatusCode)
                    return;

                await response.HandleKnownExceptions();
                response.EnsureSuccessStatusCode();
                return;
            }
        }

        public async Task<WarehouseJobZoneAggregate> GetJobById(Guid jobId, CancellationToken cancellationToken)
        {
            var endpoint = $"{_jobZoneApiUrl}jobs/{jobId}";

            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Get
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new HttpClient())
            {
                var response = await client.SendAsync(message, cancellationToken);

                if (response.IsSuccessStatusCode)
                    return await JsonSerializerWrapper.Deserialize<WarehouseJobZoneAggregate>(response.Content);

                await response.HandleKnownExceptions();
                response.EnsureSuccessStatusCode();
                return null;
            }
        }
        public async Task<Pageable<WarehouseJobZoneAggregate>> FilterJobs(FilterWarehouseJobFilter filter, CancellationToken cancellationToken)
        {
            string endPoint = $"{_jobZoneApiUrl}jobs?pageSize={filter.PageSize}&pageIndex={filter.PageIndex}";
            if (!string.IsNullOrEmpty(filter.Head))
                endPoint += $"&head={filter.Head}";
            if (filter.Status.HasValue)
                endPoint += $"&status={filter.Status.Value.ToInt32()}";
            if (filter.CreatorId.HasValue)
                endPoint += $"&creatorId={filter.CreatorId.Value}";
            if (filter.IsRecurring.HasValue)
                endPoint += $"&isRecurring={filter.IsRecurring.Value}";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endPoint),
                Method = HttpMethod.Get
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new HttpClient())
            {
                var response = await client.SendAsync(message, cancellationToken);

                if (response.IsSuccessStatusCode)
                    return await JsonSerializerWrapper.Deserialize<Pageable<WarehouseJobZoneAggregate>>(response.Content);

                await response.HandleKnownExceptions();
                response.EnsureSuccessStatusCode();
                return null;
            }
        }

        public async Task<bool> DeleteWarehouseJob(Guid id, CancellationToken cancellationToken)
        {
            var endpoint = $"{_jobZoneApiUrl}jobs/{id}";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Delete
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }
        public async Task UpdateWarehouseJob(Guid id, UpdateWarehouseJobRequest request, CancellationToken cancellationToken)
        {
            var endpoint = $"{_jobZoneApiUrl}jobs/{id}";
            request.CreatorId = WebSiteInfo.User.Value.ID;
            var payload = JsonSerializerWrapper.Serialize(request);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Put,
                Content = new StringContent(payload, Encoding.UTF8, "application/json")
            };

            using (HttpClient client = new())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return;
            }
        }
        #endregion

        #region Flows
        public async Task<List<FlowTypesResponse>> GetFlowType(CancellationToken cancellationToken)
        {
            var flowTypes = Enum.GetNames(typeof(FlowType))
                   .ToDictionary(t => (int)Enum.Parse(typeof(FlowType), t), t => t)
                   .Select(x => new FlowTypesResponse
                   {
                       Id = x.Key,
                       Name = x.Value,
                   }).ToList();

            return flowTypes;
        }
        #endregion

        #region Condition

        public async Task<List<ConditionTypesResponse>> GetConditionTypes(CancellationToken cancellationToken)
        {
            var data = Enum.GetNames(typeof(ConditionType))
                    .ToDictionary(t => (int)Enum.Parse(typeof(ConditionType), t), t => t)
                    .Select(x => new ConditionTypesResponse
                    {
                        Id = x.Key,
                        Name = x.Value
                    }).ToList();

            return data;
        }

        public async Task<List<ConditionOperatorsResponse>> GetConditionOperators(CancellationToken cancellationToken)
        {
            var data = Enum.GetNames(typeof(ConditionOperator))
                    .ToDictionary(t => (int)Enum.Parse(typeof(ConditionOperator), t), t => t)
                    .Select(x => new ConditionOperatorsResponse
                    {
                        Id = x.Key,
                        Name = x.Value
                    }).ToList();

            return data;
        }

        public async Task<List<ConditionConjunctionsResponse>> GetConditionConjunctions(CancellationToken cancellationToken)
        {
            var data = Enum.GetNames(typeof(ConditionConjunction))
                    .ToDictionary(t => (int)Enum.Parse(typeof(ConditionConjunction), t), t => t)
                    .Select(x => new ConditionConjunctionsResponse
                    {
                        Id = x.Key,
                        Name = x.Value
                    }).ToList();

            return data;
        }

        #endregion
    }
}