using System;
using System.Collections.Generic;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Enums;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Response
{
    public class WarehouseJobZoneAggregate
    {
        public Guid Id { get; set; }
        public string Head { get; set; }
        public string Description { get; set; }
        public JobStatus Status { get; set; }
        public int CreatorId { get; set; }
        public List<WarehouseJobZoneFlow> Flows { get; set; }
        public ConditionType? ConditionType { get; set; }
        public List<WarehouseJobZoneCondition>? Conditions { get; set; }
        public long? EstimatedStartDate { get; set; }
        public bool? IsRecurring { get; set; }
        public string? Cron { get; set; }
        public string? HangfireJobId { get; set; }
    }
}