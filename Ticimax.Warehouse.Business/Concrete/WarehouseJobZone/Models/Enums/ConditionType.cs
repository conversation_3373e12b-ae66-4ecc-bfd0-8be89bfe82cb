
namespace Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Enums
{
    public enum ConditionType
    {
        CarAssigned,
        PickingProductSelected,
        OrderStatusChanged,
        OrderReset,
        ProductAssigned,
        TableSelected,
        QualityControlCompleted,
        MissingProductFound,
        WaitingOrderCreated,
        StoreAgentLeftTable,
        WarehouseCarCreated,
        WarehouseCarUpdated,
        WarehouseCarDeleted,
        CarLeft,
        ProductPicked,
        StoreAgentCreated,
        SupplierCreated,
        ReturnOrderCompleted,
        PurchaseOrderCreated,
        PurchaseOrderProductPicked,
        ShelfCountingFileCreated,
        ShelfCountingFileCompleted,
        ShelfCountingFileDeleted,
        Shelf<PERSON>reated,
        ShelfClosedForSale,
        ShelfOpenedForSale,
        ShelfDeleted,
        ShelfUpdated,
        WarehouseTableCreated,
        WarehouseTableDeleted,
        WarehouseTableUpdated,
        WarehouseProductTransferFileCreated,
        WarehouseProductTransferFilePickCompleted,
        PickingProductPickedDifferent,
        PurchaseOrderProductTransferedShelf,
        ShelfProductAdded,
        ShelfProductChanged,
        Shelf<PERSON>roductReduced,
    }
}
