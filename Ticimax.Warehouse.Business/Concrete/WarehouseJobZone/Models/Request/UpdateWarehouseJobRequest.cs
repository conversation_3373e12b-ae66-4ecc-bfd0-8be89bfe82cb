using StackExchange.Redis;
using System.Collections.Generic;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Enums;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Response;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Request
{
    public class UpdateWarehouseJobRequest
    {
        public string Head { get; set; }
        public string Description { get; set; }
        public JobStatus Status { get; set; }
        public int CreatorId { get; set; }
        public List<WarehouseJobZoneFlow> Flows { get; set; }
        public ConditionType? ConditionType { get; set; }
        public List<Condition>? Conditions { get; set; }
        public long? EstimatedStartDate { get; set; }
        public bool? IsRecurring { get; set; }
        public string? Cron { get; set; }
    }
}
