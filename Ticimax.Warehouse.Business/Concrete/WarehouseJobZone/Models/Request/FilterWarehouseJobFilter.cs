
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Enums;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Request
{
    public class FilterWarehouseJobFilter
    {
        public string? Head { get; set; }
        public JobStatus? Status { get; set; }
        public int? CreatorId { get; set; }
        public bool? IsRecurring { get; set; }
        public int PageSize { get; set; } = 20;
        public int PageIndex { get; set; } = 1;
    }
}
