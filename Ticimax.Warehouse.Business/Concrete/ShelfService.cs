using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Entities.Concrete.ShelfMovement.Enums;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Request;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Response;
using Ticimax.Warehouse.Business.Concrete.Shelfs.Enums;
using Ticimax.Warehouse.Business.Concrete.Shelfs.Events;
using Ticimax.Warehouse.Business.Concrete.WarehouseEmail;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.ShelfDtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class ShelfService : BaseService, IShelfService
    {
        private readonly IShelfDal _shelfDal;
        private readonly IShelfProductDal _shelfProductDal;
        private readonly IWarehouseShelfMovementService _warehouseShelfMovementService;
        private readonly IProductService _productService;
        private readonly IWarehouseEmailService _warehouseEmailService;

        public ShelfService(IShelfDal shelfDal, IShelfProductDal shelfProductDal, IProductService productService, IWarehouseEmailService warehouseEmailService, IWarehouseShelfMovementService warehouseShelfMovementService)
        {
            _shelfDal = shelfDal;
            _shelfProductDal = shelfProductDal;
            _productService = productService;
            _warehouseEmailService = warehouseEmailService;
            _warehouseShelfMovementService = warehouseShelfMovementService;
        }

        public async Task<ErrorResponse> Add(ShelfAddDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var count = await _shelfDal.GetCountAsync(new ShelfFilter { Barcode = request.Barcode, WarehouseId = request.WarehouseID, StoreId = request.StoreID }, cancellationToken);
            if (count != 0)
                throw new BusinessException("BarkodKullanilmaktadir");
            else
            {
                var entity = request.ToEntity();
                await _shelfDal.AddAsync(entity, cancellationToken);
                WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfEvents.Created, new ShelfEventPayloadPayload(entity)));
            }

            return response;
        }

        public async Task<ErrorResponse> MultipleAdd(ShelfAddListDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            List<ShelfAddDto> removeShelf = new List<ShelfAddDto>();

            var shelfsGet = await _shelfDal.GetListAsync(null, null, cancellationToken);
            foreach (var requestShelf in request.ShelfList)
            {
                var shelf = shelfsGet.FirstOrDefault(x => x.Barcode == requestShelf.Barcode);
                if (shelf != null)
                    removeShelf.Add(requestShelf);
            }

            if (removeShelf.Count > 0)
            {
                var removeShelfItemBarcodes = removeShelf.Select(x => x.Barcode).ToList();
                request.ShelfList.RemoveAll(x => removeShelfItemBarcodes.Contains(x.Barcode));

                response.IsError = true;
                response.ErrorMessage = $"Bazı barkodlar sistem de mevcut olduğundan eklenememiştir. Eklenemeyen barkodlar şu şekilde; {string.Join(",", removeShelfItemBarcodes)}";
            }

            if (request.ShelfList.Count > 0)
                await _shelfDal.MultipleAddAsync(request.ShelfList.Select(x => x.ToEntity()).ToList(), cancellationToken);

            return response;
        }

        public async Task CloseForSale(int id, CancellationToken cancellationToken)
        {
            var shelf = await GetShelfIncludesChildById(id, cancellationToken);
            if (shelf == null)
                throw new NotFoundException("SHELF_IS_NOT_FOUND");

            if (!shelf.IsOpenForSale)
                throw new BusinessException("SHELF_IS_ALREADY_CLOSED_FOR_SALE");

            var shelfIds = shelf.ChildShelfs.Select(x => x.ID).ToList();
            shelfIds.Add(shelf.Id);

            var shelfProducts = await _shelfProductDal.GetListAsync(new ShelfProductFilter() { ShelfIds = shelfIds }, null, cancellationToken);
            foreach (var shelfProduct in shelfProducts)
            {
                var reduceStockResult = await _productService.ReduceStock(new()
                {
                    ProductID = shelfProduct.ProductID,
                    Piece = shelfProduct.ShelfStock,
                    StoreID = WebSiteInfo.User.Value.IsOneStore ? 0 : shelf.StoreId,
                    ConsignmentModuleActive = WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive,
                }, cancellationToken);

                if (reduceStockResult.SendStockSafetyEmail)
                {
                    List<string> tos = new List<string>();
                    if (WebSiteInfo.User.Value.Settings.GuvenliStokMail.Contains(','))
                        tos.AddRange(WebSiteInfo.User.Value.Settings.GuvenliStokMail.Split(',').ToList());
                    else
                        tos.Add(WebSiteInfo.User.Value.Settings.GuvenliStokMail);

                    await _warehouseEmailService.SendSafetyStockMail(tos, reduceStockResult.Product.ID, reduceStockResult.Product.Barcode, reduceStockResult.Product.ProductName, reduceStockResult.Product.StockPiece, cancellationToken);
                }
            }

            var allShelfs = shelf.ChildShelfs;
            allShelfs.Add(new Shelf(shelf.Id, shelf.ParentId, shelf.Definition, shelf.Code, shelf.Barcode, shelf.WarehouseId, shelf.StoreId, shelf.Rank, shelf.IsMissingProductShelf, shelf.IsEmptyShelf, shelf.IsOpenForSale, shelf.IsOpenPicking));
            foreach (var childShelf in allShelfs)
            {
                childShelf.IsOpenForSale = false;
                await _shelfDal.UpdateAsync(childShelf, cancellationToken);
                await _warehouseShelfMovementService.AddAsync(new WarehouseShelfMovementAddDto()
                {
                    ShelfId = childShelf.ID,
                    MemberId = WebSiteInfo.User.Value.ID,
                    MemberName = WebSiteInfo.User.Value.Name,
                    ProcessType = ShelfMovementProcessType.CloseForSale,
                    CreatedDate = DateTime.Now
                }, cancellationToken);
            }

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfEvents.ClosedForSale,
                new ShelfEventPayloadPayload(new Shelf(shelf.Id, shelf.ParentId, shelf.Definition, shelf.Code, shelf.Barcode, shelf.WarehouseId, shelf.StoreId, shelf.Rank, shelf.IsMissingProductShelf, shelf.IsEmptyShelf, shelf.IsOpenForSale, shelf.IsOpenPicking))));
        }

        public async Task CloseForPicking(int id, CancellationToken cancellationToken)
        {
            var shelf = await GetShelfIncludesChildById(id, cancellationToken);
            if (shelf == null)
                throw new NotFoundException("SHELF_IS_NOT_FOUND");

            if (!shelf.IsOpenPicking)
                throw new BusinessException("SHELF_IS_ALREADY_CLOSED_FOR_PICKING");

            var shelfIds = shelf.ChildShelfs.Select(x => x.ID).ToList();
            shelfIds.Add(shelf.Id);

            var shelfProducts = await _shelfProductDal.GetListAsync(new ShelfProductFilter() { ShelfIds = shelfIds }, null, cancellationToken);

            var allShelfs = shelf.ChildShelfs;
            allShelfs.Add(new Shelf(shelf.Id, shelf.ParentId, shelf.Definition, shelf.Code, shelf.Barcode, shelf.WarehouseId, shelf.StoreId, shelf.Rank, shelf.IsMissingProductShelf, shelf.IsEmptyShelf, shelf.IsOpenForSale, shelf.IsOpenPicking));
            foreach (var childShelf in allShelfs)
            {
                childShelf.IsOpenPicking = false;
                await _shelfDal.UpdateAsync(childShelf, cancellationToken);
                await _warehouseShelfMovementService.AddAsync(new WarehouseShelfMovementAddDto()
                {
                    ShelfId = childShelf.ID,
                    MemberId = WebSiteInfo.User.Value.ID,
                    MemberName = WebSiteInfo.User.Value.Name,
                    ProcessType = ShelfMovementProcessType.CloseForPicking,
                    CreatedDate = DateTime.Now
                }, cancellationToken);
            }

        }

        public async Task OpenForPicking(int id, CancellationToken cancellationToken)
        {
            var shelf = await GetShelfIncludesChildById(id, cancellationToken);
            if (shelf == null)
                throw new NotFoundException("SHELF_IS_NOT_FOUND");

            if (shelf.IsOpenPicking)
                throw new BusinessException("SHELF_IS_ALREADY_OPENED_FOR_PICKING");

            var shelfIds = shelf.ChildShelfs.Select(x => x.ID).ToList();
            shelfIds.Add(shelf.Id);

            var shelfProducts = await _shelfProductDal.GetListAsync(new ShelfProductFilter() { ShelfIds = shelfIds }, null, cancellationToken);

            var allShelfs = shelf.ChildShelfs;
            allShelfs.Add(new Shelf(shelf.Id, shelf.ParentId, shelf.Definition, shelf.Code, shelf.Barcode, shelf.WarehouseId, shelf.StoreId, shelf.Rank, shelf.IsMissingProductShelf, shelf.IsEmptyShelf, shelf.IsOpenForSale, shelf.IsOpenPicking));
            foreach (var childShelf in allShelfs)
            {
                childShelf.IsOpenPicking = true;
                await _shelfDal.UpdateAsync(childShelf, cancellationToken);
                await _warehouseShelfMovementService.AddAsync(new WarehouseShelfMovementAddDto()
                {
                    ShelfId = childShelf.ID,
                    MemberId = WebSiteInfo.User.Value.ID,
                    MemberName = WebSiteInfo.User.Value.Name,
                    ProcessType = ShelfMovementProcessType.OpenForPicking,
                    CreatedDate = DateTime.Now
                }, cancellationToken);
            }
        }

        public async Task OpenForSale(int id, CancellationToken cancellationToken)
        {
            var shelf = await GetShelfIncludesChildById(id, cancellationToken);
            if (shelf == null)
                throw new NotFoundException("SHELF_IS_NOT_FOUND");

            if (shelf.IsOpenForSale)
                throw new BusinessException("SHELF_IS_ALREADY_OPENED_FOR_SALE");

            var shelfIds = shelf.ChildShelfs.Select(x => x.ID).ToList();
            shelfIds.Add(shelf.Id);

            var shelfProducts = await _shelfProductDal.GetListAsync(new ShelfProductFilter() { ShelfIds = shelfIds }, null, cancellationToken);
            foreach (var shelfProduct in shelfProducts)
            {
                await _productService.AddStock(new()
                {
                    ProductID = shelfProduct.ProductID,
                    Piece = shelfProduct.ShelfStock,
                    StoreID = WebSiteInfo.User.Value.IsOneStore ? 0 : shelf.StoreId,
                    ConsignmentModuleActive = WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive,
                }, cancellationToken);
            }

            var allShelfs = shelf.ChildShelfs;
            allShelfs.Add(new Shelf(shelf.Id, shelf.ParentId, shelf.Definition, shelf.Code, shelf.Barcode, shelf.WarehouseId, shelf.StoreId, shelf.Rank, shelf.IsMissingProductShelf, shelf.IsEmptyShelf, shelf.IsOpenForSale, shelf.IsOpenPicking));
            foreach (var childShelf in allShelfs)
            {
                childShelf.IsOpenForSale = true;
                await _shelfDal.UpdateAsync(childShelf, cancellationToken);
                await _warehouseShelfMovementService.AddAsync(new WarehouseShelfMovementAddDto()
                {
                    ShelfId = childShelf.ID,
                    MemberId = WebSiteInfo.User.Value.ID,
                    MemberName = WebSiteInfo.User.Value.Name,
                    ProcessType = ShelfMovementProcessType.OpenForSale,
                    CreatedDate = DateTime.Now
                }, cancellationToken);
            }

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfEvents.OpenedForSale,
                new ShelfEventPayloadPayload(new Shelf(shelf.Id, shelf.ParentId, shelf.Definition, shelf.Code, shelf.Barcode, shelf.WarehouseId, shelf.StoreId, shelf.Rank, shelf.IsMissingProductShelf, shelf.IsEmptyShelf, shelf.IsOpenForSale, shelf.IsOpenPicking))));
        }

        public async Task<ErrorResponse> Delete(int id, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var shelf = (await _shelfDal.GetListAsync(new ShelfFilter { ID = id }, null, cancellationToken)).FirstOrDefault();
            if (shelf == null)
                throw new NotFoundException("SilinmekIstenenRafBulunamadi");

            if (shelf.IsEmptyShelf)
                throw new BusinessException("EMPTY_SHELF_IS_NOT_DELETEABLE");

            if (await _shelfDal.ShelfHaveProduct(shelf, cancellationToken))
                throw new BusinessException("RafKullanimdaSilinemez");

            await _shelfDal.DeleteAsync(shelf, cancellationToken);

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfEvents.Deleted, new ShelfEventPayloadPayload(shelf)));

            return response;
        }

        public async Task<DataResult<int>> GetCount(ShelfGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _shelfDal.GetCountAsync(request.ToFilter(), cancellationToken);

            return response;
        }

        public async Task<DataResult<List<Shelf>>> GetList(ShelfGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<Shelf>> response = new DataResult<List<Shelf>>();

            response.Model = (await _shelfDal.GetListAsync(request.ToFilter(), paging != null ? new ShelfPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();

            if (request.isGetCount)
                response.Count = (await GetCount(request, cancellationToken)).Model;

            return response;
        }

        public async Task<ErrorResponse> Update(int id, ShelfUpdateDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();
            var shelf = (await _shelfDal.GetListAsync(new ShelfFilter { ID = id }, null, cancellationToken)).FirstOrDefault();
            if (shelf != null)
            {
                if (shelf.Barcode != request.Barcode)
                {
                    var barcodeControl = await _shelfDal.GetCountAsync(new ShelfFilter { Barcode = request.Barcode }, cancellationToken);
                    if (barcodeControl == 0)
                        await _shelfDal.UpdateAsync(request.ToEntity(id), cancellationToken);
                    else
                        throw new BusinessException("BuBarkodaAitRafBulunmaktadir");
                }
                else
                    await _shelfDal.UpdateAsync(request.ToEntity(id), cancellationToken);
            }
            else
                throw new NotFoundException("RafBulunamadi");

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfEvents.Updated, new ShelfEventPayloadPayload(shelf)));

            return response;
        }

        public async Task<ShelfIncludesChildAndParentsDto> GetShelfById(int id, CancellationToken cancellationToken)
        {
            var shelf = (await _shelfDal.GetListAsync(new ShelfFilter { ID = id }, null, cancellationToken)).FirstOrDefault();
            if (shelf == null)
                throw new NotFoundException("SHELF_NOT_FOUND", new KeyValuePair<string, string>("id", id.ToString()));

            var childShelfs = (await _shelfDal.GetListAsync(new ShelfFilter { ParentId = id }, null, cancellationToken)).ToList();
            var parentShelfs = await GetMyParentsById(shelf.ParentId, cancellationToken);

            return new ShelfIncludesChildAndParentsDto(shelf, childShelfs, parentShelfs);
        }

        public async Task<ShelfIncludesChildDto> GetShelfIncludesChildById(int id, CancellationToken cancellationToken)
        {
            var shelf = (await _shelfDal.GetListAsync(new ShelfFilter
            { ID = id }, null, cancellationToken)).FirstOrDefault();

            var childShelfs = await GetAllTree(new List<int> { id }, cancellationToken);

            return new ShelfIncludesChildDto(shelf, childShelfs);
        }

        public async Task<Pageable<ShelfIncludesChildCountDto>> GetShelfIncludesChildCount(ShelfGetListDto request, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _shelfDal.GetShelfIncludesChildCountAsync(request.ToFilter(), pageSize, pageIndex, cancellationToken);

            foreach (var shelf in contents)
            {
                var allStock = await _shelfProductDal.GetShelfChildrenStock(shelf.Id, cancellationToken);
                shelf.AllStock = allStock;
            }

            var counts = await _shelfDal.CountAsync(request.ToFilter(), cancellationToken);

            return new Pageable<ShelfIncludesChildCountDto>(pageIndex, pageSize, counts, contents);
        }

        private async Task<List<Shelf>> GetMyParentsById(int parentId, CancellationToken cancellationToken)
        {
            var parentShelf = new List<Shelf>();

            if (parentId > 0)
            {
                var parent = (await _shelfDal.GetListAsync(new ShelfFilter { ID = parentId }, null, cancellationToken)).FirstOrDefault();

                if (parent != null)
                {
                    if (parent.ParentId > 0)
                    {
                        parentShelf.Add(parent);
                        parentShelf.AddRange(await GetMyParentsById(parent.ParentId, cancellationToken));
                    }
                    else
                        parentShelf.Add(parent);
                }
            }

            return parentShelf;
        }
        public async Task<List<int>> ChildrenShelfIdsGetListAsync(int shelfId, CancellationToken cancellationToken)
        {
            return await _shelfDal.ChildrenShelfIdsGetListAsync(shelfId, cancellationToken);
        }



        private async Task<List<Shelf>> GetAllTree(List<int> shelfIds, CancellationToken cancellationToken)
        {
            var shelfs = new List<Shelf>();
            var childShelfs = (await GetList(new ShelfGetListDto { ParentIds = shelfIds }, null, cancellationToken)).Model;

            if (childShelfs.Count > 0)
            {
                shelfs.AddRange(childShelfs);
                shelfs.AddRange(await GetAllTree(childShelfs.Select(x => x.ID).ToList(), cancellationToken));
            }

            return shelfs;
        }
    }
}