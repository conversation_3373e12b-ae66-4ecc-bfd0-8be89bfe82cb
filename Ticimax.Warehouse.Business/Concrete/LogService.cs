using System.Threading.Tasks;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class LogService : BaseService, ILogService
    {
        private readonly ILogDal _logDal;

        public LogService(ILogDal logDal)
        {
            _logDal = logDal;
        }

        public async Task AddStoreAgentCancelOrderLogAsync(StoreAgentCancelOrderLog request)
        {
            await _logDal.AddStoreAgentCancelOrderLogAsync(request);
        }
    }
}