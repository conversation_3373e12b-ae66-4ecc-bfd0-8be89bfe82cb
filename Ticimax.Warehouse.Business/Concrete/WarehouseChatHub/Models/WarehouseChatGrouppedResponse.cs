
using System;
using System.Collections.Generic;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseChatHub.Models
{
    public class WarehouseChatGrouppedResponse
    {
        public Guid ConnectionId { get; set; }
        public int UnReadCount { get; set; }
        public long LastMessageDate { get; set; }
        public string LastMessage { get; set; }
        public List<int> UserIds { get; set; }
        public List<WarehouseChatGrouppedUserInfo> UserInfo { get; set; } = new List<WarehouseChatGrouppedUserInfo>();
    }
    public class WarehouseChatGrouppedUserInfo
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
    }
}
