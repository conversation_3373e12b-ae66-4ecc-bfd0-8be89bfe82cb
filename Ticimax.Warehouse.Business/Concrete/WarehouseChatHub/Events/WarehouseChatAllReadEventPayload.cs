
using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseChatHub.Events
{
    public class WarehouseChatAllReadEventPayload : BaseDomainEvent
    {
        public WarehouseChatAllReadEventPayload(string domainName, Guid connectionId, int receiverId)
        {
            DomainName = domainName;
            ConnectionId = connectionId;
            ReceiverId = receiverId;
        }
        public string DomainName { get; set; }
        public Guid ConnectionId { get; set; }
        public int ReceiverId { get; set; }
    }
}
