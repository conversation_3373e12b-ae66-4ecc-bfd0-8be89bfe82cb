using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseChatHub.Events
{
    public class WarehouseChatEventPayload : BaseDomainEvent
    {
        public WarehouseChatEventPayload(string domainName, string message, int senderId, Guid connectionId)
        {
            DomainName = domainName;
            Message = message;
            SenderId = senderId;
            ConnectionId = connectionId;
        }
        public string DomainName { get; set; }
        public string Message { get; set; }
        public int SenderId { get; set; }
        public Guid ConnectionId { get; set; }
    }
}
