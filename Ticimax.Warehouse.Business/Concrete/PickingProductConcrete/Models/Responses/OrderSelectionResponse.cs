using System;
using System.Collections.Generic;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Models.Responses
{
    public class OrderSelectionTotalResponse
    {
        public int OrderCount { get; set; }

        public int ProductCount { get; set; }
        
        public List<OrderSelectionResponse> Rows { get; set; }
    }
    public class OrderSelectionResponse
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public List<OrderSelectionItem> Items { get; set; }
    }

    public class OrderSelectionItem
    {
        public string Type { get; set; }

        public string Definition { get; set; }

        public string Icon { get; set; }

        public int OrderCount { get; set; }

        public int ProductCount { get; set; }

        public string Class { get; set; }

        public string AuthCode { get; set; }

        public string Url { get; set; }

        public int? Piece { get; set; }
        public List<OrderSelectionOrderInfoItem> OrderInfo { get; set; }
    }
}