namespace Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Models.Responses
{
    public class OrderSelectionProductResponse
    {
        public OrderSelectionProductResponse()
        {
        }

        public OrderSelectionProductResponse(int ıd, string name, string barcode, string ımage, double totalPiece, string stockCode, string shelfNames)
        {
            Id = ıd;
            Name = name;
            Barcode = barcode;
            Image = ımage;
            TotalPiece = totalPiece;
            StockCode = stockCode;
            ShelfNames = shelfNames;
        }

        public int Id { get; set; }

        public string Name { get; set; }

        public string Barcode { get; set; }

        public string Image { get; set; }

        public double TotalPiece { get; set; }
        public string StockCode { get; set; }
        public string ShelfNames { get; set; }
    }
}