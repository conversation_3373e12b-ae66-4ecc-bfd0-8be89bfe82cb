using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Events
{
    public class PickedEventPayload : BaseDomainEvent
    {
        public PickedEventPayload(int orderId, int orderProductId, int shelfId, string shelfName, double quantity,
            int productId, string productName, string stockCode, string barcode, bool isMissingProduct, long missingDate, long orderDate)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            WarehouseId = WebSiteInfo.User.Value.WarehouseID;
            StoreId = WebSiteInfo.User.Value.StoreID;
            UserId = WebSiteInfo.User.Value.ID;
            Username = WebSiteInfo.User.Value.Name;
            OrderId = orderId;
            OrderProductId = orderProductId;
            Quantity = quantity;
            ShelfId = shelfId;
            ShelfName = shelfName;
            Token = WebSiteInfo.User.Value.WarehouseToken;
            SendSms = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.SmsBilgilendir;
            SendMail = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.MailBilgilendir;
            IsOmnichannelActive = WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive;
            OmnichannelWaitingChangeStatus = WebSiteInfo.User.Value.Settings.OmnichannelSettings.PickProductStatus;
            OmnichannelWaitingChangeProcess = (int)ProductStatus.Islemde;
            ProductId = productId;
            ProductName = productName;
            StockCode = stockCode;
            IsMissingProduct = isMissingProduct;
            Barcode = barcode;
            MissingDate = missingDate;
            OrderDate = orderDate;
        }

        public string DomainName { get; set; }

        public int WarehouseId { get; set; }

        public int StoreId { get; set; }

        public int UserId { get; set; }

        public string Username { get; set; }

        public int OrderId { get; set; }

        public int OrderProductId { get; set; }

        public int ShelfId { get; set; }
        public string ShelfName { get; set; }

        public double Quantity { get; set; }

        public string Token { get; set; }

        public bool SendSms { get; set; }

        public bool SendMail { get; set; }

        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public string StockCode { get; set; }
        public string Barcode { get; set; }

        public bool IsOmnichannelActive { get; set; }

        public int OmnichannelWaitingChangeStatus { get; set; }
        
        public int OmnichannelWaitingChangeProcess { get; set; }
        public bool IsMissingProduct { get; set; }
        public long MissingDate { get; set; }
        public long OrderDate { get; set; }
    }
}