using System;
using System.Collections.Generic;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.ValueObjects;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Request
{
    public class ShelfImportRequest
    {
        public List<ShelfImportItemRequest> Items { get; set; }
        
        public List<ExcelJobParameter> Parameters { get; set; }

    }

    public class ShelfImportItemRequest
    {
        public Guid RowId { get; set; }

        public int ParentId { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public string Barcode { get; set; }

        public int Rank { get; set; }

        public bool IsMissingProductShelf { get; set; }

        public bool IsEmptyShelf { get; set; }

        public bool IsOpenForSale { get; set; }
        public bool IsOpenForPicking { get; set; }

        public Shelf ToEntity(int warehouseId, int storeId)
        {
            return new Shelf(0, ParentId, Definition, Code, Barcode, warehouseId, storeId, Rank, IsMissingProductShelf, IsEmptyShelf, IsOpenForSale, IsOpenForPicking);
        }
    }
    
}