using System.Collections.Generic;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Enums;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.ValueObjects;

namespace Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Request
{
    public class CreateExcelJobRequest
    {
        public int UserId { get; set; }

        public string Username { get; set; }

        public string Name { get; set; }

        public string Type { get; set; }

        public string FileUrl { get; set; } = "";

        public string FolderName { get; set; } = "";

        public string RequestUrl { get; set; }

        public ExcelProcessType ProcessType { get; set; }

        public List<ExcelJobParameter> Parameters { get; set; } = new List<ExcelJobParameter>();

        public List<ExcelJobColumnMatch> Columns { get; set; }
        public ExcelJobCallBackSettings CallBackSettings { get; set; }
    }
}