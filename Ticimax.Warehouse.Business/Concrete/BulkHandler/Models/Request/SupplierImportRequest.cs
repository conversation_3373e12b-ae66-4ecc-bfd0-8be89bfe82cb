using System;
using System.Collections.Generic;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.ValueObjects;

namespace Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Request
{
    public class SupplierImportRequest
    {
        public List<SupplierImportRequestItem> Items { get; set; }

        public List<ExcelJobParameter> Parameters { get; set; }
    }

    public class SupplierImportRequestItem
    {
        public Guid RowId { get; set; }
        public string Definition { get; set; }
        public string MailAddress { get; set; }
        public string TelephoneNumber { get; set; }
        public string SupplierCode { get; set; }
        public string ContactUserName { get; set; }
        public string ContactUserSurname { get; set; }
        public string ContactUserRole { get; set; }
        public string ContactUserTelephone { get; set; }
        public string ContactUserMail { get; set; }
    }
}
