using System;
using System.Collections.Generic;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.ValueObjects;

namespace Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Request
{
    public class WarehouseCarImportRequest
    {
        public List<WarehouseCarImportRequestItem> Items { get; set; }

        public List<ExcelJobParameter> Parameters { get; set; }
    }
    public class WarehouseCarImportRequestItem
    {
        public Guid RowId { get; set; }

        public string Definition { get; set; }

        public string Barcode { get; set; }

        public bool IsActive { get; set; }
    }
}
