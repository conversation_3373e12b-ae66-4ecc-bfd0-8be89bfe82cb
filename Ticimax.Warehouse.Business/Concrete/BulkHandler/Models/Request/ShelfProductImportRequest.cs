using System;
using System.Collections.Generic;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.ValueObjects;

namespace Ticimax.Warehouse.Business.Concrete.BulkHandler.Models.Request
{
    public class ShelfProductImportRequest
    {
        public List<ShelfProductImportItemRequest> Items { get; set; }

        public List<ExcelJobParameter> Parameters { get; set; }
    }

    public class ShelfProductImportItemRequest
    {
        public Guid RowId { get; set; }
        public string ShelfBarcode { get; set; }

        public string ProductBarcode { get; set; }

        public double Piece { get; set; }
    }
}