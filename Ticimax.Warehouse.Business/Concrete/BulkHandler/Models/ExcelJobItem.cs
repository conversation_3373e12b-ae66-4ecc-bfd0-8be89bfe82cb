using System;
using System.Collections.Generic;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Enums;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.ValueObjects;

namespace Ticimax.Warehouse.Business.Concrete.BulkHandler.Models
{
    public class ExcelJobItem
    {
        public Guid Id { get; set; }

        public int UserId { get; set; }

        public string Username { get; set; }

        public string Name { get; set; }

        public string Type { get; set; }

        public string RequestUrl { get; set; }

        public string FolderName { get; set; }

        public string FileUrl { get; set; }

        public string Status { get; set; }

        public ExcelProcessType ProcessType { get; set; }

        public List<ExcelJobParameter> Parameters { get; set; }

        public List<ExcelJobColumnMatch> Columns { get; set; }

        public long CreatedDate { get; set; }

        public long LastModifiedDate { get; set; }
    }
}