using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ticimax.Warehouse.Business.Concrete.BulkHandler.Enums;

namespace Ticimax.Warehouse.Business.Concrete.BulkHandler.ValueObjects
{
    public class ExcelJobCallBackSettings
    {
        public bool CallBackActive { get; set; }
        public string SuccessCallBackUrl { get; set; }
        public string FailedCallBackUrl { get; set; }
        public ExcelJobCallBackSettings()
        {
            // Burası type'a göre ayarlanmalı. Get yani export işlemlerinde call back yapmaya gerek yok
            CallBackActive = ExcelJobSettings.Active; 
            SuccessCallBackUrl = ExcelJobSettings.SuccessUrl;
            FailedCallBackUrl = ExcelJobSettings.FailedUrl;
        }
    }
}
