using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.Shelfs.Events
{
    public class ShelfEventPayloadPayload : BaseDomainEvent
    {
        public ShelfEventPayloadPayload(Entities.Concrete.Shelf shelf)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            UserId = WebSiteInfo.User.Value.ID;
            ID = shelf.ID;
            ParentId = shelf.ParentId;
            Definition = shelf.Definition;
            Code = shelf.Code;
            Barcode = shelf.Barcode;
            WarehouseID = shelf.WarehouseID;
            StoreID = shelf.StoreID;
            Rank = shelf.Rank;
            IsMissingProductShelf = shelf.IsMissingProductShelf;
            AddingDate = shelf.AddingDate;
            IsEmptyShelf = shelf.IsEmptyShelf;
            IsOpenForSale = shelf.IsOpenForSale;
        }

        public string DomainName { get; set; }
        public int UserId { get; set; }
        public int ID { get; set; }
        public int ParentId { get; set; }
        public string Definition { get; set; }
        public string Code { get; set; }
        public string Barcode { get; set; }
        public int WarehouseID { get; set; }
        public int StoreID { get; set; }
        public int Rank { get; set; }
        public bool IsMissingProductShelf { get; set; }
        public DateTime AddingDate { get; set; }
        public bool IsEmptyShelf { get; set; }
        public bool IsOpenForSale { get; set; }
    }
}
