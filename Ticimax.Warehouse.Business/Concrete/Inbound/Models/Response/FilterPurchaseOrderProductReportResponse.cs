using System;
using Ticimax.Warehouse.Business.Concrete.Inbound.ValueObjects;

namespace Ticimax.Warehouse.Business.Concrete.Inbound.Models.Response
{
    public class FilterPurchaseOrderProductReportResponse
    {
        public Guid Id { get; set; }

        public string DomainName { get; set; }

        public int WarehouseId { get; set; }

        public int StoreId { get; set; }

        public Guid PurchaseOrderId { get; set; }

        public Guid PurhcaseOrderProductId { get; set; }

        public PurchaseOrderSupplier Supplier { get; set; }

        public PurchaseOrderProduct Product { get; set; }

        public double Quantity { get; set; }

        public double Price { get; set; }

        public double TotalPrice { get; set; }

        public double PickedProductCount { get; set; }

        public double ShelvedProductCount { get; set; }

        public int UserId { get; set; }

        public string Username { get; set; }

        public long CreatedDate { get; set; }
        public DateTime CreatedDateConvert { get; set; }
    }
}
