using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Utilities.Service;
using Ticimax.Extension.Helpers;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Inbound.Enums;
using Ticimax.Warehouse.Business.Concrete.WarehouseGeneralSettings;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.BusinessEntities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Static;
using static Common.Logging.Configuration.ArgUtils;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class SiteSettingsService : BaseService, ISiteSettingsService
    {
        private readonly ISiteSettingsDal _siteSettingsDal;
        private readonly IOrderProductStatusService _orderProductStatusService;
        private readonly IWarehouseService _warehouseService;
        private readonly IPackagingStatusService _packagingStatusService;
        private readonly IUserInformationService _userInformationService;
        private readonly IOrderService _orderService;
        private readonly ICargoIntegrationService _cargoIntegrationService;
        private readonly ICountryCityDistrictDal _countryCityDistrictDal;

        public SiteSettingsService(
            ISiteSettingsDal siteSettingsDal
            , IOrderProductStatusService orderProductStatusService
            , IWarehouseService warehouseService
            , IPackagingStatusService packagingStatusService
            , IUserInformationService userInformationService
            , IOrderService orderService, ICargoIntegrationService cargoIntegrationService,
            ICountryCityDistrictDal countryCityDistrictDal)
        {
            _siteSettingsDal = siteSettingsDal;
            _orderProductStatusService = orderProductStatusService;
            _warehouseService = warehouseService;
            _packagingStatusService = packagingStatusService;
            _userInformationService = userInformationService;
            _orderService = orderService;
            _cargoIntegrationService = cargoIntegrationService;
            _countryCityDistrictDal = countryCityDistrictDal;
        }

        public async Task<WarehouseSettings> GetSettings(int warehouseId, int storeId, CancellationToken cancellationToken)
        {
            return await _siteSettingsDal.SelectWarehouseSettingsAsync(warehouseId, storeId, cancellationToken);
        }

        public async Task<ErrorResponse> UpdateWarehouseSettings(int warehouseId, int storeId, SetWarehouseSettingsDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            if (request.IsDefault)
            {
                var warehouses = (await _warehouseService.GetList(new WarehouseGetListDto(), null, cancellationToken)).Model;
                foreach (var warehouse in warehouses.Where(x => x.ID != warehouseId))
                {
                    await UpdateSettings(warehouse.ID, warehouse.StoreID, request, cancellationToken);
                }
            }

            response.Model = await UpdateSettings(warehouseId, storeId, request, cancellationToken);

            return response;
        }

        private async Task<BLDepoAyar> UpdateSettings(int warehouseId, int storeId, SetWarehouseSettingsDto request, CancellationToken cancellationToken)
        {
            var warehouseSetting = await GetSettings(warehouseId, storeId, cancellationToken);
            BLDepoAyar warehouseSettings = warehouseSetting.Settings ?? new BLDepoAyar();

            FillSettings(warehouseSettings.GetType().AssemblyQualifiedName, warehouseSettings, request);

            await _siteSettingsDal.UpdateWarehouseSettingsAsync(WebSiteInfo.User.Value.ID, warehouseId, storeId, warehouseSettings, cancellationToken);

            var siteSettings = (await _siteSettingsDal.SelectAsync(cancellationToken: cancellationToken)).FirstOrDefault();
            var menus = GetPaketMenu(siteSettings);
            if (menus.Any())
            {
                warehouseSettings.PacketSettings.ConsignmentProductModulActive = menus.Any(x => x.MenuID == 297);
                warehouseSettings.PacketSettings.OmniChannelDistributionActive = menus.Any(x => x.MenuID == 1143);
            }

            WebSiteInfo.User.Value.Settings = warehouseSettings;

            await _userInformationService.UpdateAlreadyUsersSettings(warehouseId, storeId, warehouseSettings, cancellationToken);

            return warehouseSettings;
        }

        private List<PaketMenuModel> GetPaketMenu(BLSiteAyarlari siteAyarlari)
        {
            var menuler = new List<PaketMenuModel>();
            try
            {
                menuler = TicimaxMenu.GetPaketMenu(siteAyarlari.TicimaxPaketID);
                var ozelMenu = siteAyarlari.TicimaxEkMenu.Split(',');
                foreach (var oMenu in ozelMenu)
                {
                    if (!string.IsNullOrEmpty(oMenu))
                    {
                        menuler.Add(new PaketMenuModel { MenuID = Convert.ToInt32(oMenu), PaketID = siteAyarlari.TicimaxPaketID });
                    }
                }
            }
            catch (Exception)
            {
            }

            return menuler;
        }

        public async Task<WarehouseSettingsDto> ReturnDefaultSettings(int warehouseId, int storeId, CancellationToken cancellationToken)
        {
            WarehouseSettingsDto response = new WarehouseSettingsDto();
            SetWarehouseSettingsDto setWarehouseSettingsDto = new SetWarehouseSettingsDto();
            List<SettingsPropertyDto> settingsPropertyDtos = new List<SettingsPropertyDto>();

            await _siteSettingsDal.UpdateWarehouseSettingsAsync(WebSiteInfo.User.Value.ID, warehouseId, storeId, new BLDepoAyar(), cancellationToken);

            response = await GetWarehouseSettings(warehouseId, storeId, cancellationToken);

            setWarehouseSettingsDto.Settings = settingsPropertyDtos;

            await UpdateWarehouseSettings(warehouseId, storeId, setWarehouseSettingsDto, cancellationToken);

            return response;
        }

        public async Task<BLDepoAyar> GetBLWarehouseSettings(int warehouseId, int storeId, CancellationToken cancellationToken)
        {
            var settings = await _siteSettingsDal.SelectWarehouseSettingsAsync(warehouseId, storeId, cancellationToken);
            return settings.Settings;
        }

        public async Task<WarehouseSettingsDto> GetWarehouseSettings(int warehouseId, int storeId, CancellationToken cancellationToken)
        {
            WarehouseSettingsDto warehouseSettings = new WarehouseSettingsDto();

            var warehouseSetting = await GetSettings(warehouseId, storeId, cancellationToken);
            var dbSettings = warehouseSetting.Settings ?? new BLDepoAyar();
            var orderStatus = Enumeration.GetAll<OrderStatus>();
            var productStatus = (await _orderProductStatusService.GetList(new Core.Order.Entities.Dtos.OrderProductStatusServiceGetListDto(), cancellationToken)).Model;

            var countries = await _countryCityDistrictDal.GetCountryList(new CountryFilter() { Active = 1 }, cancellationToken);

            var timeSlots = GetTimeSlots();
            var warehouses = (await _warehouseService.GetList(new WarehouseGetListDto(), null, cancellationToken)).Model;
            var packagingStatus = await _packagingStatusService.GetList(new Core.Order.Entities.Filters.PackagingStatusFilter(), cancellationToken);
            var orderSources = await _orderService.GetOrderSources(cancellationToken);
            var integrations = (await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto() { Active = 1 }, cancellationToken: cancellationToken)).Model;

            #region GeneralSettings

            warehouseSettings.GeneralSettings.Label = "GenelAyarlar";
            warehouseSettings.GeneralSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "ArabaModulu",
                    PropertyName = "ArabaModulAktif",
                    Value = dbSettings.ArabaModulAktif,
                    Information = "ArabaModuluBilgilendirme",
                    Label = warehouseSettings.GeneralSettings.Label
                });

            warehouseSettings.GeneralSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "UrunAdiAlani",
                    PropertyName = "UrunAdiAlani",
                    Information = "UrunAdiAlaniBilgilendirme",
                    Type = 2,
                    Value = dbSettings.UrunAdiAlani,
                    Label = warehouseSettings.GeneralSettings.Label,
                    SelectorItems = new List<WarehouseSettingsSelectorItem>
                    {
                        new WarehouseSettingsSelectorItem
                        {
                            Name = "UrunAdi",
                            Value = "URUNADI"
                        },
                        new WarehouseSettingsSelectorItem
                        {
                            Name = "OzelAlan1",
                            Value = "OZELALAN1"
                        },
                        new WarehouseSettingsSelectorItem
                        {
                            Name = "OzelAlan2",
                            Value = "OZELALAN2"
                        },
                        new WarehouseSettingsSelectorItem
                        {
                            Name = "OzelAlan3",
                            Value = "OZELALAN3"
                        },
                        new WarehouseSettingsSelectorItem
                        {
                            Name = "OzelAlan4",
                            Value = "OZELALAN4"
                        },
                        new WarehouseSettingsSelectorItem
                        {
                            Name = "OzelAlan5",
                            Value = "OZELALAN5"
                        },
                    }
                });
            warehouseSettings.GeneralSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "GuvenliStokAdedi",
                    PropertyName = "GuvenliStokAdedi",
                    Value = dbSettings.GuvenliStokAdedi,
                    Information = "GuvenliStokAdediBilgilendirme",
                    Type = 1,
                    Label = warehouseSettings.GeneralSettings.Label
                });

            warehouseSettings.GeneralSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "GuvenliStokMail",
                    PropertyName = "GuvenliStokMail",
                    Value = dbSettings.GuvenliStokMail,
                    Information = "GuvenliStokMailBilgilendirme",
                    Type = 1,
                    Label = warehouseSettings.GeneralSettings.Label
                });

            warehouseSettings.GeneralSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.GeneralSettings.Label,
                    PropertyName = "WorkingDays",
                    Name = "WorkingDays",
                    Information = "WorkingDays",
                    Value = dbSettings.WorkingDays,
                    Type = 3,
                    SelectorItems = new List<WarehouseSettingsSelectorItem>() 
                    {
                        new WarehouseSettingsSelectorItem()
                        {
                            Name = WarehouseWorkingDays.MondayName,
                            Value = WarehouseWorkingDays.MondayValue
                        },
                        new WarehouseSettingsSelectorItem()
                        {
                            Name = WarehouseWorkingDays.TuesdayName,
                            Value = WarehouseWorkingDays.TuesdayValue
                        },
                        new WarehouseSettingsSelectorItem()
                        {
                            Name = WarehouseWorkingDays.WednesdayName,
                            Value = WarehouseWorkingDays.WednesdayValue
                        },
                        new WarehouseSettingsSelectorItem()
                        {
                            Name = WarehouseWorkingDays.ThursdayName,
                            Value = WarehouseWorkingDays.ThursdayValue
                        },
                        new WarehouseSettingsSelectorItem()
                        {
                            Name = WarehouseWorkingDays.FridayName,
                            Value = WarehouseWorkingDays.FridayValue
                        },
                        new WarehouseSettingsSelectorItem()
                        {
                            Name = WarehouseWorkingDays.SaturdayName,
                            Value = WarehouseWorkingDays.SaturdayValue
                        },
                        new WarehouseSettingsSelectorItem()
                        {
                            Name = WarehouseWorkingDays.SundayName,
                            Value = WarehouseWorkingDays.SundayValue
                        }
                    }
                });
            
                warehouseSettings.GeneralSettings.Settings.Add(
                    new WarehouseSettingsItemDto
                    {
                        Name = "WorkingHourStart",
                        PropertyName = "WorkingHourStart",
                        Value = dbSettings.WorkingHourStart,
                        Information = "WorkingHourStartInformation",
                        Type = 2,
                        Label = warehouseSettings.GeneralSettings.Label,
                        SelectorItems = timeSlots.Select(x => new WarehouseSettingsSelectorItem()
                        {
                            Name = x,
                            Value = x
                        }).ToList()
                    });

                 warehouseSettings.GeneralSettings.Settings.Add(
                    new WarehouseSettingsItemDto
                    {
                        Name = "WorkingHourEnd",
                        PropertyName = "WorkingHourEnd",
                        Value = dbSettings.WorkingHourEnd,
                        Information = "WorkingHourEndInformation",
                        Type = 2,
                        Label = warehouseSettings.GeneralSettings.Label,
                        SelectorItems = timeSlots.Select(x => new WarehouseSettingsSelectorItem() {
                        Name = x,
                        Value = x
                        }).ToList()
                    });

            #endregion GeneralSettings

            #region PickingProducts

            warehouseSettings.PickingProductSettings.Label = "UrunToplamaAyarlari";
            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.UrunToplamaTipi",
                    Name = "UrunOkutmaTipi",
                    Information = "UrunOkutmaTipiBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.UrunToplamaTipi,
                    Type = 2,
                    SelectorItems =
                        new List<WarehouseSettingsSelectorItem>
                        {
                            new WarehouseSettingsSelectorItem
                            {
                                Name = "Arttirmali",
                                Value = "0"
                            },
                            new WarehouseSettingsSelectorItem
                            {
                                Name = "ElleGiris",
                                Value = "1"
                            },
                            new WarehouseSettingsSelectorItem
                            {
                                Name = "ArttirmaliVeElleGiris",
                                Value = "2"
                            }
                        }
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.TeslimatTarihiGunLimiti",
                    Name = "TeslimatTarihiGunLimiti",
                    Information = "TeslimatTarihiGunLimitiBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.TeslimatTarihiGunLimiti,
                    Type = 1
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.FazlaOkutma",
                    Name = "FazlaOkutma",
                    Information = "FazlaOkutmaBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.FazlaOkutma,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.SiparisUrunDegistirmeTutarOrani",
                    Name = "SiparisUrunDegistirmeTutarOrani",
                    Information = "SiparisUrunDegistirmeTutarOraniBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.SiparisUrunDegistirmeTutarOrani,
                    Type = 1
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.MailBilgilendir",
                    Name = "MailBilgilendir",
                    Information = "MailBilgilendirBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.MailBilgilendir,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.SmsBilgilendir",
                    Name = "SmsBilgilendir",
                    Information = "SmsBilgilendirBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.SmsBilgilendir,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.CokluBarkod",
                    Name = "CokluBarkod",
                    Information = "CokluBarkodBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.CokluBarkod,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.RaftaStokYoksaSiparisDagitma",
                    Name = "RaftaStokYoksaSiparisDagitma",
                    Information = "RaftaStokYoksaSiparisDagitmaBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.RaftaStokYoksaSiparisDagitma,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.MalKabuldeAdreslenmemisUrunlerSiparisDagitma",
                    Name = "MalKabuldeAdreslenmemisUrunlerSiparisDagitma",
                    Information = "MalKabuldeAdreslenmemisUrunlerSiparisDagitmaBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.MalKabuldeAdreslenmemisUrunlerSiparisDagitma,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.SiparisToplamaKaliteKontrol",
                    Name = "SiparisToplamaKaliteKontrol",
                    Information = "SiparisToplamaKaliteKontrolBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.SiparisToplamaKaliteKontrol,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.ButunEksikUrunleriListele",
                    Name = "ButunEksikUrunleriListele",
                    Information = "ButunEksikUrunleriListeleBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.ButunEksikUrunleriListele,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.UrunToplamaYontemi",
                    Name = "UrunToplamaYontemi",
                    Information = "UrunToplamaYontemiBilgilendirme",
                    Type = 2,
                    Value = dbSettings.UrunToplamaAyar.UrunToplamaYontemi,
                    SelectorItems = new List<WarehouseSettingsSelectorItem>
                    {
                        new WarehouseSettingsSelectorItem { Name = "OnceRafSonraUrun", Value = "1" },
                        //new WarehouseSettingsSelectorItem { Name = "OnceUrunSonraRaf", Value = "2" },
                        new WarehouseSettingsSelectorItem { Name = "SadeceUrun", Value = "3" },
                        //new WarehouseSettingsSelectorItem { Name = "OnceRafSonraKutuSonraUrun", Value = "4" }
                        new WarehouseSettingsSelectorItem { Name = "OnceSiparisSonraUrun", Value = "5" }
                    }
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.EksikUrunIadeNedenId",
                    Name = "EksikUrunIadeNedeni",
                    Information = "EksikUrunIadeNedeniBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.EksikUrunIadeNedenId,
                    Type = 2,
                    SelectorItems = productStatus
                        .Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.Operation.ToString() })
                        .ToList()
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.MasaTekUrunKaliteKontrol",
                    Name = "MasaTekUrunKaliteKontrol",
                    Information = "MasaTekUrunKaliteKontrolBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.MasaTekUrunKaliteKontrol,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.RaftaBulunamayanUrununKontrolu",
                    Name = "RaftaBulunamayanUrununKontrolu",
                    Information = "RaftaBulunamayanUrununKontroluBilgilendirme",
                    Value = dbSettings.UrunToplamaAyar.RaftaBulunamayanUrununKontrolu,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.MissingProductAdminApproval",
                    Name = "MissingProductAdminApproval",
                    Information = "MissingProductAdminApprovalInformation",
                    Value = dbSettings.UrunToplamaAyar.MissingProductAdminApproval,
                });

            warehouseSettings.PickingProductSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = warehouseSettings.PickingProductSettings.Label,
                   PropertyName = "UrunToplamaAyar.StockControlInTransfer",
                   Name = "StockControlInTransfer",
                   Information = "StockControlInTransferInformation",
                   Value = dbSettings.UrunToplamaAyar.StockControlInTransfer,
               });

            warehouseSettings.PickingProductSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = warehouseSettings.PickingProductSettings.Label,
                   PropertyName = "UrunToplamaAyar.NotIncludingTheGoodsReceivingShelf",
                   Name = "NotIncludingTheGoodsReceivingShelf",
                   Information = "NotIncludingTheGoodsReceivingShelfInformation",
                   Value = dbSettings.UrunToplamaAyar.NotIncludingTheGoodsReceivingShelf,
               });

            warehouseSettings.PickingProductSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = warehouseSettings.PickingProductSettings.Label,
                   PropertyName = "UrunToplamaAyar.TotalizerControl",
                   Name = "TotalizerControl",
                   Information = "TotalizerControlInformation",
                   Value = dbSettings.UrunToplamaAyar.TotalizerControl,
               });
            warehouseSettings.PickingProductSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = warehouseSettings.PickingProductSettings.Label,
                   PropertyName = "UrunToplamaAyar.PickingProductResetProcess",
                   Name = "PickingProductResetProcess",
                   Information = "PickingProductResetProcessInformation",
                   Value = dbSettings.UrunToplamaAyar.PickingProductResetProcess,
               });

            warehouseSettings.PickingProductSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = warehouseSettings.PickingProductSettings.Label,
                   PropertyName = "UrunToplamaAyar.MultipleProductPicking",
                   Name = "MultipleProductPicking",
                   Information = "MultipleProductPickingInformation",
                   Value = dbSettings.UrunToplamaAyar.MultipleProductPicking,
               });

            warehouseSettings.PickingProductSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = warehouseSettings.PickingProductSettings.Label,
                   PropertyName = "UrunToplamaAyar.WarehouseTransferBulkProductControl",
                   Name = "WarehouseTransferBulkProductControl",
                   Information = "WarehouseTransferBulkProductControlInformation",
                   Value = dbSettings.UrunToplamaAyar.WarehouseTransferBulkProductControl,
               });

            warehouseSettings.PickingProductSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = warehouseSettings.PickingProductSettings.Label,
                   PropertyName = "UrunToplamaAyar.WarehouseTransferWebStockReduce",
                   Name = "WarehouseTransferWebStockReduce",
                   Information = "WarehouseTransferWebStockReduceInformation",
                   Value = dbSettings.UrunToplamaAyar.WarehouseTransferWebStockReduce,
               });

            warehouseSettings.PickingProductSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = warehouseSettings.PickingProductSettings.Label,
                    PropertyName = "UrunToplamaAyar.CountryOfWarehouse",
                    Name = "CountryOfWarehouse",
                    Information = "CountryOfWarehouseInformation",
                    Value = dbSettings.UrunToplamaAyar.CountryOfWarehouse,
                    Type = 2,
                    SelectorItems = countries
                        .Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() })
                        .ToList()
                });


            #endregion PickingProducts

            #region OrderCombine

            warehouseSettings.OrderCombineSettings.Label = "OrderCombineSettings";
            warehouseSettings.OrderCombineSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "OrderCombineSettings",
                PropertyName = "OrderCombineSettings.RequestedParcelActive",
                Name = "RequestedParcelActive",
                Information = "RequestedParcelActiveInformation",
                Value = dbSettings.OrderCombineSettings.RequestedParcelActive,
            });


            warehouseSettings.OrderCombineSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "OrderCombineSettings",
                PropertyName = "OrderCombineSettings.MultipleProductCombine",
                Name = "MultipleProductCombine",
                Information = "MultipleProductCombineInformation",
                Value = dbSettings.OrderCombineSettings.MultipleProductCombine,
            });


            warehouseSettings.OrderCombineSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "OrderCombineSettings",
                PropertyName = "OrderCombineSettings.MultipleProductQualityControl",
                Name = "MultipleProductQualityControl",
                Information = "MultipleProductQualityControlInformation",
                Value = dbSettings.OrderCombineSettings.MultipleProductQualityControl,
            });

            #endregion

            #region ProductPlacementSettings

            warehouseSettings.ProductPlacementSettings.Label = "UrunYerlestirmeAyarlari";
            warehouseSettings.ProductPlacementSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "UrunYerlestirmeAyarlari",
                    PropertyName = "UrunYerlestirmeAyar.YerlestirmedeStokEkle",
                    Name = "YerlestirmedeStokEkle",
                    Information = "YerlestirmedeStokEkleBilgilendirme",
                    Value = dbSettings.UrunYerlestirmeAyar.YerlestirmedeStokEkle
                });

            warehouseSettings.ProductPlacementSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "UrunYerlestirmeAyarlari",
                    PropertyName = "UrunYerlestirmeAyar.UrunYerlestirirkenEksikVarsaSiteyeStokEkleme",
                    Name = "UrunYerlestirirkenEksikVarsaSiteyeStokEkleme",
                    Information = "UrunYerlestirirkenEksikVarsaSiteyeStokEklemeBilgilendirme",
                    Value = dbSettings.UrunYerlestirmeAyar.UrunYerlestirirkenEksikVarsaSiteyeStokEkleme
                });

            warehouseSettings.ProductPlacementSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "UrunYerlestirmeAyarlari",
                    PropertyName = "UrunYerlestirmeAyar.SadeceAktifUrun",
                    Name = "SadeceAktifUrun",
                    Information = "SadeceAktifUrunBilgilendirme",
                    Value = dbSettings.UrunYerlestirmeAyar.SadeceAktifUrun
                });

            if (dbSettings.MalKabulIslemleri)
            {
                warehouseSettings.ProductPlacementSettings.Settings.Add(
                    new WarehouseSettingsItemDto
                    {
                        Label = "UrunYerlestirmeAyarlari",
                        PropertyName = "UrunYerlestirmeAyar.MalKabulAdreslemedeStokEkle",
                        Name = "MalKabulAdreslemedeStokEkle",
                        Information = "MalKabulAdreslemedeStokEkleBilgilendirme",
                        Value = dbSettings.UrunYerlestirmeAyar.MalKabulAdreslemedeStokEkle
                    });
            }

            warehouseSettings.ProductPlacementSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "UrunYerlestirmeAyarlari",
                    PropertyName = "UrunYerlestirmeAyar.RaftaUrunBulunamadigindaSiteStokGuncelle",
                    Name = "RaftaUrunBulunamadigindaSiteStokGuncelle",
                    Information = "RaftaUrunBulunamadigindaSiteStokGuncelleBilgilendirme",
                    Value = dbSettings.UrunYerlestirmeAyar.RaftaUrunBulunamadigindaSiteStokGuncelle
                });

            warehouseSettings.ProductPlacementSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "UrunYerlestirmeAyarlari",
                    PropertyName = "UrunYerlestirmeAyar.DoNotScanDifferentItemsAtAcceptance",
                    Name = "DoNotScanDifferentItemsAtAcceptance",
                    Information = "DoNotScanDifferentItemsAtAcceptanceInformation",
                    Value = dbSettings.UrunYerlestirmeAyar.DoNotScanDifferentItemsAtAcceptance
                });

            warehouseSettings.ProductPlacementSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "UrunYerlestirmeAyarlari",
                    PropertyName = "UrunYerlestirmeAyar.TransferProductPlacementApprovalNotRequired",
                    Name = "TransferProductPlacementApprovalNotRequired",
                    Information = "TransferProductPlacementApprovalNotRequiredInformation",
                    Value = dbSettings.UrunYerlestirmeAyar.TransferProductPlacementApprovalNotRequired,
                });

            warehouseSettings.ProductPlacementSettings.Settings.Add(
            new WarehouseSettingsItemDto
            {
                Label = "UrunYerlestirmeAyarlari",
                PropertyName = "UrunYerlestirmeAyar.MultipleProductPickingForCountingFile",
                Name = "MultipleProductPickingForCountingFile",
                Information = "MultipleProductPickingForCountingFileInformation",
                Value = dbSettings.UrunYerlestirmeAyar.MultipleProductPickingForCountingFile,
            });


            warehouseSettings.ProductPlacementSettings.Settings.Add(
            new WarehouseSettingsItemDto
            {
                Label = "UrunYerlestirmeAyarlari",
                PropertyName = "UrunYerlestirmeAyar.RecommendedShelf",
                Name = "RecommendedShelf",
                Information = "RecommendedShelfInformation",
                Value = dbSettings.UrunYerlestirmeAyar.RecommendedShelf,
            });

            warehouseSettings.ProductPlacementSettings.Settings.Add(
            new WarehouseSettingsItemDto
            {
                Label = "UrunYerlestirmeAyarlari",
                PropertyName = "UrunYerlestirmeAyar.CriticStock",
                Name = "CriticStock",
                Information = "CriticStockInformation",
                Value = dbSettings.UrunYerlestirmeAyar.CriticStock,
                Type = 1
            });

            #endregion ProductPlacementSettings

            #region QualityControlSettings

            warehouseSettings.QualityControlSettings.Label = "KaliteKontrolAyarlari";
            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.DesiGirisAktif",
                    Name = "DesiGirisAktif",
                    Information = "DesiGirisAktifBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.DesiGirisAktif
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.HediyeIrsaliyeAktif",
                    Name = "HediyeIrsaliyeAktif",
                    Information = "HediyeIrsaliyeAktifBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.HediyeIrsaliyeAktif
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.KargoGonderimAktif",
                    Name = "KargoGonderimAktif",
                    Information = "KargoGonderimAktifBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.KargoGonderimAktif
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "ERPUyariMailAdresi",
                    Information = "ERPUyariMailAdresiBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.ERPUyariMailAdresi,
                    PropertyName = "KaliteKontrolAyar.ERPUyariMailAdresi",
                    Type = 1
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "FaturaOlustur",
                    Information = "FaturaOlusturBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.FaturaOlustur,
                    PropertyName = "KaliteKontrolAyar.FaturaOlustur",
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "KoliGoster",
                    PropertyName = "KaliteKontrolAyar.KoliGoster",
                    Information = "KoliGosterBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.KoliGoster
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "KargoUrunDurum",
                    PropertyName = "KaliteKontrolAyar.KargoSiparisUrunDurumId",
                    Information = "KargoUrunDurumBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.KargoSiparisUrunDurumId,
                    SelectorItems = productStatus.Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() }).ToList(),
                    Type = 2
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "KargoCiktisiAl",
                    PropertyName = "KaliteKontrolAyar.KargoCiktisiAl",
                    Information = "KargoCiktisiAlInformation",
                    Value = dbSettings.KaliteKontrolAyar.KargoCiktisiAl
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "PrintOrderPdf",
                    PropertyName = "KaliteKontrolAyar.PrintOrderPdf",
                    Information = "PrintOrderPdfInformation",
                    Value = dbSettings.KaliteKontrolAyar.PrintOrderPdf
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "InvoiceCameOut",
                    PropertyName = "KaliteKontrolAyar.InvoiceCameOut",
                    Information = "InvoiceCameOutInformation",
                    Value = dbSettings.KaliteKontrolAyar.InvoiceCameOut
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "ShowCustomCargoPdf",
                    PropertyName = "KaliteKontrolAyar.ShowCustomCargoPdf",
                    Information = "ShowCustomCargoPdfInformation",
                    Value = dbSettings.KaliteKontrolAyar.ShowCustomCargoPdf
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "CustomCargoPdfIntegrations",
                    Information = "CustomCargoPdfIntegrationsInformation",
                    PropertyName = "KaliteKontrolAyar.CustomCargoPdfIntegrations",
                    Value = dbSettings.KaliteKontrolAyar.CustomCargoPdfIntegrations,
                    SelectorItems = integrations.Select(x => new WarehouseSettingsSelectorItem { Name = x.Description, Value = x.ID.ToString() }).ToList(),
                    Type = 3
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "EntegrasyonAyarlari",
                    Name = "EntegrasyonAktif",
                    Information = "EntegrasyonAktifBilgilendirme",
                    PropertyName = "KaliteKontrolAyar.EntegrasyonAktif",
                    Value = dbSettings.KaliteKontrolAyar.EntegrasyonAktif
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "EntegrasyonAyarlari",
                    Name = "FaturaKesilecekPaketlemeDurum",
                    Information = "FaturaKesilecekPaketlemeDurumBilgilendirme",
                    PropertyName = "KaliteKontrolAyar.FaturaKesilecekPaketlemeDurumID",
                    Value = dbSettings.KaliteKontrolAyar.FaturaKesilecekPaketlemeDurumID,
                    SelectorItems = packagingStatus.Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() }).ToList(),
                    Type = 2
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "EntegrasyonAyarlari",
                    Name = "FaturaKesilenPaketlemeDurumu",
                    Information = "FaturaKesilenPaketlemeDurumu",
                    PropertyName = "KaliteKontrolAyar.FaturaKesilenPaketlemeDurumID",
                    Value = dbSettings.KaliteKontrolAyar.FaturaKesilenPaketlemeDurumID,
                    SelectorItems = packagingStatus.Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() }).ToList(),
                    Type = 2
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.SipariseStoksuzUrunEkle",
                    Name = "SipariseStoksuzUrunEkle",
                    Information = "SipariseStoksuzUrunEkleBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.SipariseStoksuzUrunEkle
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.SiparisiEksikGonder",
                    Name = "SiparisiEksikGonder",
                    Information = "SiparisiEksikGonderBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.SiparisiEksikGonder
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.AvailableSendCargoOrderSources",
                    Name = "AvailableSendCargoOrderSources",
                    Information = "AvailableSendCargoOrderSourcesBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.AvailableSendCargoOrderSources.ToList(),
                    Type = 3,
                    SelectorItems = orderSources.Select(x => new WarehouseSettingsSelectorItem() { Name = x, Value = x }).ToList()
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.CargoSendUseIntegrationId",
                    Name = "CargoSendUseIntegrationId",
                    Information = "CargoSendUseIntegrationIdBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.CargoSendUseIntegrationId
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.MultiPacket",
                    Name = "MultiPacket",
                    Information = "MultiPacketInformation",
                    Value = dbSettings.KaliteKontrolAyar.MultiPacket
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "DefaultCargoIntegrationId",
                    Information = "DefaultCargoIntegrationIdBilgilendirme",
                    PropertyName = "KaliteKontrolAyar.DefaultCargoIntegrationId",
                    Value = dbSettings.KaliteKontrolAyar.DefaultCargoIntegrationId,
                    SelectorItems = integrations.Select(x => new WarehouseSettingsSelectorItem { Name = x.Description, Value = x.ID.ToString() }).ToList(),
                    Type = 2
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    Name = "DefaultDoorPaymentCargoIntegrationId",
                    Information = "DefaultDoorPaymentCargoIntegrationIdBilgilendirme",
                    PropertyName = "KaliteKontrolAyar.DefaultDoorPaymentCargoIntegrationId",
                    Value = dbSettings.KaliteKontrolAyar.DefaultDoorPaymentCargoIntegrationId,
                    SelectorItems = integrations.Select(x => new WarehouseSettingsSelectorItem { Name = x.Description, Value = x.ID.ToString() }).ToList(),
                    Type = 2
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.InvoiceCreateUseBackground",
                    Name = "InvoiceCreateUseBackground",
                    Information = "InvoiceCreateUseBackgroundBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.InvoiceCreateUseBackground
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = "KaliteKontrolAyarlari",
                   PropertyName = "KaliteKontrolAyar.CargoSendUseBackground",
                   Name = "CargoSendUseBackground",
                   Information = "CargoSendUseBackgroundInformation",
                   Value = dbSettings.KaliteKontrolAyar.CargoSendUseBackground
               });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.MultipleProductPicking",
                    Name = "MultipleProductPicking",
                    Information = "MultipleProductPickingBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.MultipleProductPicking
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = "KaliteKontrolAyarlari",
                   PropertyName = "KaliteKontrolAyar.ShowGiftPackage",
                   Name = "ShowGiftPackage",
                   Information = "ShowGiftPackageBilgilendirme",
                   Value = dbSettings.KaliteKontrolAyar.ShowGiftPackage
               });

            warehouseSettings.QualityControlSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "KaliteKontrolAyarlari",
                    PropertyName = "KaliteKontrolAyar.OrderNoteShow",
                    Name = "OrderNoteShow",
                    Information = "OrderNoteShowBilgilendirme",
                    Value = dbSettings.KaliteKontrolAyar.OrderNoteShow
                });

            warehouseSettings.QualityControlSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = "KaliteKontrolAyarlari",
                   PropertyName = "KaliteKontrolAyar.SetCargoIntegrationnCashOnDeliveryType",
                   Name = "SetCargoIntegrationnCashOnDeliveryType",
                   Information = "SetCargoIntegrationnCashOnDeliveryTypeInformation",
                   Value = dbSettings.KaliteKontrolAyar.SetCargoIntegrationnCashOnDeliveryType
               });

            warehouseSettings.QualityControlSettings.Settings.Add(
               new WarehouseSettingsItemDto
               {
                   Label = "KaliteKontrolAyarlari",
                   PropertyName = "KaliteKontrolAyar.DirectQualityControlCompletion",
                   Name = "DirectQualityControlCompletion",
                   Information = "DirectQualityControlCompletionInformation",
                   Value = dbSettings.KaliteKontrolAyar.DirectQualityControlCompletion
               });

            #endregion QualityControlSettings

            #region ReturnSettings

            warehouseSettings.ReturnSettings.Label = "IadeAyarlari";
            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "IadeDeposu",
                    Information = "IadeDeposuBilgilendirme",
                    PropertyName = "IadeAyar.IadeDeposu",
                    Value = dbSettings.IadeAyar.IadeDeposu,
                    SelectorItems = warehouses.Select(x =>
                        new WarehouseSettingsSelectorItem
                        {
                            Name = x.Definition,
                            Value = x.ID.ToString()
                        }).ToList(),
                    Type = 2
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "IadeNedeniAktif",
                    Information = "IadeNedeniAktifBilgilendirme",
                    PropertyName = "IadeAyar.IadeNedeniAktif",
                    Value = dbSettings.IadeAyar.IadeNedeniAktif,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "IadeStokEkle",
                    Information = "IadeStokEkleBilgilendirme",
                    PropertyName = "IadeAyar.StokEkle",
                    Value = dbSettings.IadeAyar.StokEkle,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "IadeListesineEkle",
                    Information = "IadeListesineEkleBilgilendirme",
                    PropertyName = "IadeAyar.IadeListesineEkle",
                    Value = dbSettings.IadeAyar.IadeListesineEkle,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "HediyeCekiniIadeEt",
                    Information = "HediyeCekiniIadeEtBilgilendirme",
                    PropertyName = "IadeAyar.HediyeCekiniIadeEt",
                    Value = dbSettings.IadeAyar.HediyeCekiniIadeEt,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "ReduceShippingCost",
                    Information = "ReduceShippingCostInformation",
                    PropertyName = "IadeAyar.ReduceShippingCost",
                    Value = dbSettings.IadeAyar.ReduceShippingCost,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "MultiplePaymentTransferToCustomerService",
                    Information = "MultiplePaymentTransferToCustomerServiceInformation",
                    PropertyName = "IadeAyar.MultiplePaymentTransferToCustomerService",
                    Value = dbSettings.IadeAyar.MultiplePaymentTransferToCustomerService,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "KapidanDonenSiparisMusteriHizmetlerineAktar",
                    Information = "KapidanDonenSiparisMusteriHizmetlerineAktarBilgilendirme",
                    PropertyName = "IadeAyar.KapidanDonenSiparisMusteriHizmetlerineAktar",
                    Value = dbSettings.IadeAyar.KapidanDonenSiparisMusteriHizmetlerineAktar,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "PartialReturnOnUndeliveredOrder",
                    Information = "PartialReturnOnUndeliveredOrderInformation",
                    PropertyName = "IadeAyar.PartialReturnOnUndeliveredOrder",
                    Value = dbSettings.IadeAyar.PartialReturnOnUndeliveredOrder,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "KapidaOdemeSiparisYasaklamaAdedi",
                    Information = "KapidaOdemeSiparisYasaklamaAdediBilgilendirme",
                    PropertyName = "IadeAyar.KOYasaklamaSiparisAdedi",
                    Value = dbSettings.IadeAyar.KOYasaklamaSiparisAdedi,
                    Type = 1
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "FixedCargoRefundAmount",
                    Information = "FixedCargoRefundAmountInformation",
                    PropertyName = "IadeAyar.FixedCargoRefundAmount",
                    Value = dbSettings.IadeAyar.FixedCargoRefundAmount,
                    Type = 1
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "NumberOfDaysForReturningOrder",
                    Information = "NumberOfDaysForReturningOrderInformation",
                    PropertyName = "IadeAyar.NumberOfDaysForReturningOrder",
                    Value = dbSettings.IadeAyar.NumberOfDaysForReturningOrder,
                    Type = 1
                });

            var returnStatusSettings = orderStatus.Select(x =>
                new WarehouseSettingsSelectorItem
                {
                    Name = x.Value,
                    Value = x.Key.ToString()
                }).ToList();

            returnStatusSettings.Add(new WarehouseSettingsSelectorItem { Name = "DegisiklikOlmasin", Value = "-1" });
            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "TeslimatOncesiIadeDurumu",
                    Information = "TeslimatOncesiIadeDurumuBilgilendirme",
                    PropertyName = "IadeAyar.TeslimatOncesiIadeDurumu",
                    Value = dbSettings.IadeAyar.TeslimatOncesiIadeDurumu,
                    Type = 2,
                    SelectorItems = returnStatusSettings
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "ParcaliIadeDurumu",
                    Information = "ParcaliIadeDurumuBilgilendirme",
                    PropertyName = "IadeAyar.ParcaliIadeDurumu",
                    Value = dbSettings.IadeAyar.ParcaliIadeDurumu,
                    Type = 2,
                    SelectorItems = returnStatusSettings
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "TamIadeDurumu",
                    PropertyName = "IadeAyar.TamIadeDurumu",
                    Information = "TamIadeDurumuBilgilendirme",
                    Value = dbSettings.IadeAyar.TamIadeDurumu,
                    Type = 2,
                    SelectorItems = returnStatusSettings
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "OrderProductCanceledStatus",
                    PropertyName = "IadeAyar.OrderProductCanceledStatus",
                    Information = "OrderProductCanceledStatusBilgilendirme",
                    Value = dbSettings.IadeAyar.OrderProductCanceledStatus,
                    Type = 2,
                    SelectorItems = productStatus
                        .Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() })
                        .ToList()
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "CashOnDeliveryAndUndeliveredOrdersFastReturn",
                    Information = "CashOnDeliveryAndUndeliveredOrdersFastReturnBilgilendirme",
                    PropertyName = "IadeAyar.CashOnDeliveryAndUndeliveredOrdersFastReturn",
                    Value = dbSettings.IadeAyar.CashOnDeliveryAndUndeliveredOrdersFastReturn,
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "IadeAyarlari",
                    Name = "RefundBankCommission",
                    Information = "RefundBankCommissionInformation",
                    PropertyName = "IadeAyar.RefundBankCommission",
                    Value = dbSettings.IadeAyar.RefundBankCommission
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "EntegrasyonAyarlari",
                    Name = "EntegrasyonAktif",
                    Information = "EntegrasyonAktifBilgilendirme",
                    PropertyName = "IadeAyar.EntegrasyonAktif",
                    Value = dbSettings.IadeAyar.EntegrasyonAktif
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "EntegrasyonAyarlari",
                    Name = "IadeFaturaKesilecekPaketlemeDurum",
                    Information = "IadeFaturaKesilecekPaketlemeDurumBilgilendirme",
                    PropertyName = "IadeAyar.IadeFaturaKesilecekPaketlemeDurumID",
                    Value = dbSettings.IadeAyar.IadeFaturaKesilecekPaketlemeDurumID,
                    SelectorItems = packagingStatus.Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() }).ToList(),
                    Type = 2
                });

            warehouseSettings.ReturnSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "EntegrasyonAyarlari",
                    Name = "IadeFaturaKesilenPaketlemeDurumu",
                    Information = "IadeFaturaKesilenPaketlemeDurumuBilgilendirme",
                    PropertyName = "IadeAyar.IadeFaturaKesilenPaketlemeDurumID",
                    Value = dbSettings.IadeAyar.IadeFaturaKesilenPaketlemeDurumID,
                    SelectorItems = packagingStatus.Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() }).ToList(),
                    Type = 2
                });

            #endregion ReturnSettings

            #region OrderSelectSettings

            warehouseSettings.OrderSelectSettings.Label = "SiparisSecimAyarlari";
            warehouseSettings.OrderSelectSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "TekUrunluSiparisSayisi",
                    Information = "TekUrunluSiparisSayisiBilgilendirme",
                    PropertyName = "SiparisSecimAyar.TekUrunluSiparisSayisi",
                    Value = dbSettings.SiparisSecimAyar.TekUrunluSiparisSayisi,
                    Type = 1,
                    Label = warehouseSettings.OrderSelectSettings.Label
                });

            warehouseSettings.OrderSelectSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "CokUrunluSiparisSayisi",
                    Information = "CokUrunluSiparisSayisiBilgilendirme",
                    PropertyName = "SiparisSecimAyar.CokUrunluSiparisSayisi",
                    Value = dbSettings.SiparisSecimAyar.CokUrunluSiparisSayisi,
                    Type = 1,
                    Label = warehouseSettings.OrderSelectSettings.Label
                });

            warehouseSettings.OrderSelectSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "KarisikSiparisSayisi",
                    Information = "KarisikSiparisSayisiBilgilendirme",
                    PropertyName = "SiparisSecimAyar.KarisikSiparisSayisi",
                    Value = dbSettings.SiparisSecimAyar.KarisikSiparisSayisi,
                    Type = 1,
                    Label = warehouseSettings.OrderSelectSettings.Label
                });

            warehouseSettings.OrderSelectSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "MarketPlaceSiparisSayisi",
                    Information = "MarketPlaceSiparisSayisiBilgilendirme",
                    PropertyName = "SiparisSecimAyar.MarketPlaceSiparisSayisi",
                    Value = dbSettings.SiparisSecimAyar.MarketPlaceSiparisSayisi,
                    Type = 1,
                    Label = warehouseSettings.OrderSelectSettings.Label
                });

            warehouseSettings.OrderSelectSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "KargoSiparisSayisi",
                    Information = "KargoSiparisSayisiBilgilendirme",
                    PropertyName = "SiparisSecimAyar.KargoSiparisSayisi",
                    Value = dbSettings.SiparisSecimAyar.KargoSiparisSayisi,
                    Type = 1,
                    Label = warehouseSettings.OrderSelectSettings.Label
                });

            warehouseSettings.OrderSelectSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "PaymentTypeOrderCount",
                    Information = "PaymentTypeOrderCountInformation",
                    PropertyName = "SiparisSecimAyar.PaymentTypeOrderCount",
                    Value = dbSettings.SiparisSecimAyar.PaymentTypeOrderCount,
                    Type = 1,
                    Label = warehouseSettings.OrderSelectSettings.Label
                });

            warehouseSettings.OrderSelectSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "Öncelikli Sipariş Paketleme Durumu",
                    PropertyName = "SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu",
                    Value = dbSettings.SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu,
                    Type = 2,
                    SelectorItems = packagingStatus.Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() }).ToList(),
                    Label = warehouseSettings.OrderSelectSettings.Label
                });

            warehouseSettings.OrderSelectSettings.Settings.Add(
                  new WarehouseSettingsItemDto
                  {
                      Name = "OrderSortingType",
                      PropertyName = "SiparisSecimAyar.OrderSortingType",
                      Information = "OrderSortingType",
                      Type = 2,
                      Value = dbSettings.SiparisSecimAyar.OrderSortingType,
                      Label = warehouseSettings.OrderSelectSettings.Label,
                      SelectorItems = new List<WarehouseSettingsSelectorItem>
                      {
                        new WarehouseSettingsSelectorItem
                        {
                            Name = "SiparisTarihi",
                            Value = "SIPARISTARIHI"
                        },
                        new WarehouseSettingsSelectorItem
                        {
                            Name = "TeslimatTarihi",
                            Value = "TESLIMATTARIHI"
                        }
                      }
                  });

            warehouseSettings.OrderSelectSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Name = "OrderSelectionDayCount",
                    Information = "OrderSelectionDayCountInformation",
                    PropertyName = "SiparisSecimAyar.OrderSelectionDayCount",
                    Value = dbSettings.SiparisSecimAyar.OrderSelectionDayCount,
                    Type = 1,
                    Label = warehouseSettings.OrderSelectSettings.Label
                });


            #endregion OrderSelectSettings

            #region DistributorSettings

            warehouseSettings.DistributorSettings.Label = "DagiticiAyarlari";
            warehouseSettings.DistributorSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "DagiticiAyarlari",
                    Name = "PaketlemeDurumu",
                    Information = "PaketlemeDurumu",
                    PropertyName = "DagiticiAyar.PaketlemeDurumID",
                    Value = dbSettings.DagiticiAyar.PaketlemeDurumID,
                    SelectorItems = packagingStatus.Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() }).ToList(),
                    Type = 2
                });

            #endregion DistributorSettings

            #region MarketplaceSettings

            warehouseSettings.MarketplaceSettings.Label = "PazaryeriAyarlari";
            warehouseSettings.MarketplaceSettings.Settings.Add(
                new WarehouseSettingsItemDto
                {
                    Label = "PazarYeriAyarlari",
                    Name = "SipariseDosyaYukle",
                    Information = "SipariseDosyaYukleBilgilendirme",
                    PropertyName = "PazaryeriAyar.SipariseDosyaYukle",
                    Value = dbSettings.PazaryeriAyar.SipariseDosyaYukle
                });

            #endregion

            #region TelephoneCentralSettings

            warehouseSettings.TelephoneCentralSettings.Label = "SantralAyarlari";
            warehouseSettings.TelephoneCentralSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "SantralAyarlari",
                Name = "Aktif",
                Information = "SantralAyarlariBilgilendirme",
                PropertyName = "TelephoneCentralSettings.Active",
                Value = dbSettings.TelephoneCentralSettings.Active,
            });

            warehouseSettings.TelephoneCentralSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "SantralAyarlari",
                Name = "Key",
                Information = "SantralAyarlariKeyBilgilendirme",
                PropertyName = "TelephoneCentralSettings.Key",
                Value = dbSettings.TelephoneCentralSettings.Key,
                Type = 1
            });

            warehouseSettings.TelephoneCentralSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "SantralAyarlari",
                Name = "KayitAktif",
                Information = "SantralAyarlariKayitBilgilendirme",
                PropertyName = "TelephoneCentralSettings.IsRecording",
                Value = dbSettings.TelephoneCentralSettings.IsRecording,
            });

            #endregion

            #region TicimaxAdminSettings

            if (WebSiteInfo.User.Value.isTicimaxUser)
            {
                warehouseSettings.TicimaxAdminSettings.Label = "TicimaxAdminAyarlari";

                warehouseSettings.TicimaxAdminSettings.Settings.Add(
                    new WarehouseSettingsItemDto
                    {
                        PropertyName = "MalKabulIslemleri",
                        Name = "MalKabulIslemleri",
                        Information = "MalKabulIslemleri",
                        Value = dbSettings.MalKabulIslemleri,
                        Label = warehouseSettings.TicimaxAdminSettings.Label
                    });
                warehouseSettings.TicimaxAdminSettings.Settings.Add(
                    new WarehouseSettingsItemDto
                    {
                        PropertyName = "AddDirectlyToShelf",
                        Name = "AddDirectlyToShelf",
                        Information = "AddDirectlyToShelf",
                        Value = dbSettings.AddDirectlyToShelf,
                        Label = warehouseSettings.TicimaxAdminSettings.Label
                    });


                warehouseSettings.TicimaxAdminSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            }

            #endregion TicimaxAdminSettings

            #region BarcodeSettings

            warehouseSettings.BarcodeSettings.Label = "BarcodeSettings";
            warehouseSettings.BarcodeSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "BarcodeSettings",
                Name = "SegmentedBarcodeActive",
                Information = "SegmentedBarcodeActiveInfo",
                PropertyName = "BarcodeSettings.SegmentedBarcodeSettings.Active",
                Value = dbSettings.BarcodeSettings.SegmentedBarcodeSettings.Active,
                Lock = false
            });

            warehouseSettings.BarcodeSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "BarcodeSettings",
                Name = "SegmentedBarcodeProductBarcodeSegmentLength",
                Information = "SegmentedBarcodeProductBarcodeSegmentLengthInfo",
                PropertyName = "BarcodeSettings.SegmentedBarcodeSettings.ProductBarcodeSegmentLength",
                Value = dbSettings.BarcodeSettings.SegmentedBarcodeSettings.ProductBarcodeSegmentLength,
                Type = 1,
                Lock = false
            });

            warehouseSettings.BarcodeSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "BarcodeSettings",
                Name = "SegmentedBarcodeProductQuantitySegmentLength",
                Information = "SegmentedBarcodeProductQuantitySegmentLengthInfo",
                PropertyName = "BarcodeSettings.SegmentedBarcodeSettings.ProductQuantitySegmentLength",
                Value = dbSettings.BarcodeSettings.SegmentedBarcodeSettings.ProductQuantitySegmentLength,
                Type = 1,
                Lock = false
            });

            warehouseSettings.BarcodeSettings.Settings.Add(new WarehouseSettingsItemDto
            {
                Label = "BarcodeSettings",
                Name = "SegmentedBarcodeSmartScalePrefixNumbers",
                Information = "SegmentedBarcodeSmartScalePrefixNumbersInfo",
                PropertyName = "BarcodeSettings.SegmentedBarcodeSettings.SmartScalePrefixNumbers",
                Value = dbSettings.BarcodeSettings.SegmentedBarcodeSettings.SmartScalePrefixNumbers,
                Type = 1,
                Lock = false
            });

            #endregion

            #region OmnichannelSettings

            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                warehouseSettings.OmnichannelSettings.Label = "OmnichannelSettings";

                warehouseSettings.OmnichannelSettings.Settings.Add(
                    new WarehouseSettingsItemDto
                    {
                        Label = warehouseSettings.PickingProductSettings.Label,
                        PropertyName = "OmnichannelSettings.PickProductStatus",
                        Name = "PickProductStatus",
                        Information = "PickProductStatusInformation",
                        Value = dbSettings.OmnichannelSettings.PickProductStatus <= 0 ? productStatus.FirstOrDefault(x => x.Operation == (int)ProductStatus.Islemde)!.ID.ToString() : dbSettings.OmnichannelSettings.PickProductStatus.ToString(),
                        Type = 2,
                        SelectorItems = productStatus
                            .Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() })
                            .ToList()
                    });

                warehouseSettings.OmnichannelSettings.Settings.Add(
                    new WarehouseSettingsItemDto
                    {
                        Label = warehouseSettings.PickingProductSettings.Label,
                        PropertyName = "OmnichannelSettings.CompletedProductStatus",
                        Name = "CompletedProductStatus",
                        Information = "CompletedProductStatusInformation",
                        Value = dbSettings.OmnichannelSettings.CompletedProductStatus <= 0 ? productStatus.FirstOrDefault(x => x.Operation == (int)ProductStatus.KargoyaVerildi)!.ID.ToString() : dbSettings.OmnichannelSettings.CompletedProductStatus.ToString(),
                        Type = 2,
                        SelectorItems = productStatus
                            .Select(x => new WarehouseSettingsSelectorItem { Name = x.Definition, Value = x.ID.ToString() })
                            .ToList()
                    });
            }

            #endregion


            warehouseSettings.GeneralSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            warehouseSettings.OrderSelectSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            warehouseSettings.OrderCombineSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            warehouseSettings.PickingProductSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            warehouseSettings.ProductPlacementSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            warehouseSettings.QualityControlSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            warehouseSettings.ReturnSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            warehouseSettings.DistributorSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());
            warehouseSettings.MarketplaceSettings.Settings.Where(x => x.Type == 2).ToList().ForEach(x => x.Value = x.Value.ToString());

            return warehouseSettings;
        }

        private void FillSettings(string behindSettings, object settings, SetWarehouseSettingsDto request, string propName = "")
        {
            var instanse = Activator.CreateInstance(Type.GetType(behindSettings));
            var prefix = !string.IsNullOrEmpty(propName) ? propName + "." : "";
            var properties = instanse.GetType().GetProperties().Where(x => request.Settings.Any(y => y.Key.StartsWith(prefix + x.Name)));

            foreach (var property in properties)
            {
                var lastPropName = prefix + property.Name;
                if (property.PropertyType.AssemblyQualifiedName.Contains("Ticimax"))
                    FillSettings(property.PropertyType.AssemblyQualifiedName, property.GetValue(settings), request, lastPropName);
                else
                    SetPropertyValue(settings, property, request.Settings.FirstOrDefault(x => x.Key == lastPropName).Value);
            }
        }

        private static void SetPropertyValue(object obj, PropertyInfo propertyInfo, object formValue)
        {
            if (propertyInfo != null)
            {
                if (propertyInfo.PropertyType == typeof(bool))
                {
                    if (formValue.ToString() == "0" || formValue.ToString() == "1") //"0" ya da "1" gelen string değerin boolen yapılabilmesi için integer yapıyoruz.
                    {
                        var valueInt = Convert.ToInt32(formValue);
                        propertyInfo.SetValue(obj, Convert.ChangeType(valueInt, propertyInfo.PropertyType), null);
                    }
                    else if (formValue.ToString() == "on")
                        propertyInfo.SetValue(obj, true, null);
                    else
                        propertyInfo.SetValue(obj, Convert.ChangeType(formValue, propertyInfo.PropertyType));
                }
                else if (propertyInfo.PropertyType == typeof(List<string>))
                {
                    var value = formValue.ToString().Split(',').Where(x => x != "").ToList();
                    propertyInfo.SetValue(obj, value, null);
                }
                else
                {
                    //nullable değerler için underlyingtype'ı alıp işlem yapıyoruz.
                    Type t = Nullable.GetUnderlyingType(propertyInfo.PropertyType) ?? propertyInfo.PropertyType;
                    //var value = formValue == null ? null : Convert.ChangeType(formValue, t);
                    var value = string.IsNullOrEmpty(formValue.ToString()) ? null : Convert.ChangeType(formValue, t);
                    propertyInfo.SetValue(obj, value, null);
                }
            }
        }
        private List<string> GetTimeSlots()
        {
            List<string> timeSlots = new List<string>();
            TimeSpan startTime = TimeSpan.FromHours(0);     
            TimeSpan endTime = TimeSpan.FromHours(23.5);    
            TimeSpan interval = TimeSpan.FromMinutes(30);   

            for (TimeSpan time = startTime; time <= endTime; time += interval)
            {
                timeSlots.Add(time.ToString(@"hh\:mm"));
            }

            return timeSlots;
        }


    }
}