#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Extensions.Configuration;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.EInvoiceError.Request;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.EInvoiceError.Request.Client;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.EInvoiceError.Response;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.OrderStatusJob.Request;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.OrderStatusJob.Response;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator
{
    public class TicimaxInvalidatorService : BaseService, ITicimaxInvalidatorService
    {
        private readonly string _ticimaxInvalidatorServiceUrl;
        private readonly ITicimaxWarehouseService _ticimaxWarehouseService;
        private readonly IOrderService _orderService;

        public TicimaxInvalidatorService(IConfiguration configuration, ITicimaxWarehouseService ticimaxWarehouseService, IOrderService orderService)
        {
            _ticimaxInvalidatorServiceUrl = configuration.GetValue<string>(ConfigKeys.TicimaxInvalidatorApiUrl);
            _ticimaxWarehouseService = ticimaxWarehouseService;
            _orderService = orderService;
        }

        public async Task<Pageable<EInvoiceErrorResponse>> GetEInvoiceError(EInvoiceErrorRequest request, CancellationToken cancellationToken)
        {
            await IssueSuccessfulInvoices(cancellationToken);
            var query = HttpUtility.ParseQueryString(string.Empty);

            if (request.OrderId.HasValue)
                query["orderId"] = request.OrderId.Value.ToString();
            if (request.OrderDateStart.HasValue)
                query["orderDateStart"] = request.OrderDateStart.Value.ToString();
            if (request.OrderDateEnd.HasValue)
                query["orderDateEnd"] = request.OrderDateEnd.Value.ToString();

            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_ticimaxInvalidatorServiceUrl);
                var response = await client.GetAsync("invoice-errors?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();
                var result = await JsonSerializerWrapper.Deserialize<Pageable<EInvoiceErrorResponse>>(response.Content, cancellationToken);
                result.Contents.ForEach(x => x.OrderDateConvert =
                     DateTimeOffset.FromUnixTimeMilliseconds(x.OrderDate).LocalDateTime);
                return result;
            }
        }

        public async Task IssueSuccessfulInvoices(CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["pageSize"] = int.MaxValue.ToString();
            query["pageIndex"] = "1";

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_ticimaxInvalidatorServiceUrl);
                var response = await client.GetAsync("invoice-errors?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                var data = await JsonSerializerWrapper.Deserialize<Pageable<EInvoiceErrorResponse>>(response.Content, cancellationToken);
                List<int> orderIds = data.Contents.Select(x => x.OrderId).ToList();
                if (orderIds != null && orderIds.Count > 0)
                {
                    InvoicedOrderListDto request = new InvoicedOrderListDto(orderIds);
                    var invoicedOrders = await _orderService.GetInvoicedOrderList(request.ToFilter(), null, cancellationToken);
                    List<int> deletedOrderIds = new List<int>();
                    deletedOrderIds = invoicedOrders.Model.Where(x => orderIds.Contains(x.OrderId)).Select(x => x.OrderId).ToList();
                    if (deletedOrderIds.Count > 0)
                    {
                        var payload = JsonSerializerWrapper.Serialize(new DeleteBulkEInvoiceErrorClientRequest(deletedOrderIds));

                        var deletedMessage = new HttpRequestMessage
                        {
                            RequestUri = new Uri($"{_ticimaxInvalidatorServiceUrl}invoice-errors/bulk"),
                            Method = HttpMethod.Delete,
                            Content = new StringContent(payload, Encoding.UTF8, "application/json")
                        };

                        deletedMessage.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);
                        using (HttpClient deletedClient = new())
                        {
                            var deletedResponse = await deletedClient.SendAsync(deletedMessage, cancellationToken);
                            if (!deletedResponse.IsSuccessStatusCode)
                            {
                                await deletedResponse.HandleKnownExceptions(cancellationToken);
                                deletedResponse.EnsureSuccessStatusCode();
                            }
                        }
                    }

                }
            }
        }

        public async Task ReCreateEInvoice(int orderId, CancellationToken cancellationToken)
        {
            await _ticimaxWarehouseService.TokenInitialize();
            var order = (await _orderService.GetList(new OrderGetListDto() { OrderID = orderId })).Model.FirstOrDefault();
            if (order == null)
                throw new BusinessException("ORDER_NOT_FOUND");

            ReCreateEInvoiceRequest request = new ReCreateEInvoiceRequest(order.Date.ToTimestamp());

            var payload = JsonSerializerWrapper.Serialize(request);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_ticimaxInvalidatorServiceUrl}invoice-errors/orders/{orderId}/re-create"),
                Method = HttpMethod.Post,
                Content = new StringContent(payload, Encoding.UTF8, "application/json")
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (!response.IsSuccessStatusCode)
                {
                    await response.HandleKnownExceptions(cancellationToken);
                    response.EnsureSuccessStatusCode();
                }
            }
        }
        public async Task OrderStatusUpdated(int orderId, OrderStatusUpdateRequest request, CancellationToken cancellationToken)
        {
            var payload = JsonSerializerWrapper.Serialize(request);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_ticimaxInvalidatorServiceUrl}order-status/{orderId}/update"),
                Method = HttpMethod.Post,
                Content = new StringContent(payload, Encoding.UTF8, "application/json")
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (!response.IsSuccessStatusCode)
                {
                    await response.HandleKnownExceptions(cancellationToken);
                    response.EnsureSuccessStatusCode();
                }
            }
        }

        public async Task ReCreateEInvoiceBulk(ReCreateEInvoiceBulkRequest request, CancellationToken cancellationToken)
        {
            if (request.OrderIds == null || request.OrderIds.Count == 0)
                throw new BusinessException("MustSelectOrder");

            foreach (var orderId in request.OrderIds)
            {
                await ReCreateEInvoice(orderId, cancellationToken);
            }
        }

        public async Task<Pageable<OrderStatusJobResponse>> GetOrderStatus(OrderStatusJobRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);

            query["orderId"] = request.OrderId.ToString();

            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_ticimaxInvalidatorServiceUrl);
                var response = await client.GetAsync("order-status?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                return await JsonSerializerWrapper.Deserialize<Pageable<OrderStatusJobResponse>>(response.Content, cancellationToken);
            }
        }
    }
}