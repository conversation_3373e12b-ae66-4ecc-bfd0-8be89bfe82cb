using System;

namespace Ticimax.Warehouse.Business.Concrete.Report.WarehouseTransfer.Models.Response
{
    public class WarehouseTransferResponse
    {
        public Guid Id { get; set; }
        public string DomainName { get; set; }

        public int ProductId { get; set; }

        public Guid FileId { get; set; }
        public double Quantity { get; set; }

        public double PickQuantity { get; set; }

        public double ControlQuantity { get; set; }

        public bool IsTransferred { get; set; }

        public bool CheckCompleted { get; set; }
        public long CreatedDate { get; set; }
    }
}
