using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class OrderCancelFromStoreAgentLogService : BaseService, IOrderCancelFromStoreAgentLogService
    {
        private readonly IOrderCancelFromStoreAgentLogDal _orderCancelFromStoreAgentLogDal;
        private readonly ILogDal _logDal;

        public OrderCancelFromStoreAgentLogService(IOrderCancelFromStoreAgentLogDal orderCancelFromStoreAgentLogDal, ILogDal logDal)
        {
            _orderCancelFromStoreAgentLogDal = orderCancelFromStoreAgentLogDal;
            _logDal = logDal;
        }

        public async Task<ErrorResponse> GetCount(OrderCancelFromStoreAgentFilterDto filter = null, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            await _logDal.CreateTable("kullanici_siparis_iptal");
            ErrorResponse errorResponse = new ErrorResponse();
            filter.TableDate = DateTime.Now.ToString("yyyyMM");
            errorResponse.Model = await _orderCancelFromStoreAgentLogDal.GetCountAsync(filter.ToFilter(), cancellationToken);
            return errorResponse;
        }
    }
}