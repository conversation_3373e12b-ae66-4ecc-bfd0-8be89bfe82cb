using Microsoft.Extensions.Logging;
using RedLockNet.SERedis;
using RedLockNet.SERedis.Configuration;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.Business.Concrete;
using Ticimax.Core.CrossCuttingConcerns.Caching;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Business.Concrete.OrderProducts.Enums;
using Ticimax.Core.Order.Business.Concrete.OrderProducts.Events;
using Ticimax.Core.Order.Business.Concrete.PickingProducts.Events;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums;
using Ticimax.Core.Product.Entities.Dtos;
using Ticimax.Core.Product.Entities.Enums;
using Ticimax.Core.Utilities.Service;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Enums;
using Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Events;
using Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Models.Requests;
using Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Models.Responses;
using Ticimax.Warehouse.Business.Concrete.PickingProducts.Enums;
using Ticimax.Warehouse.Business.Concrete.PickingProducts.Events;
using Ticimax.Warehouse.Business.Concrete.Report.Enum;
using Ticimax.Warehouse.Business.Concrete.Report.ProductExtraction.ValueObjects;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Enums;
using Ticimax.Warehouse.Business.Concrete.Report.MissingProducts.Events;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.Item.Enums;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File.Enums;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;
using static Common.Logging.Configuration.ArgUtils;
using PackageStatus = Ticimax.Core.Order.Entities.Enums.PackageStatus;
using Ticimax.Warehouse.Business.Concrete.Review.Enums;
using Ticimax.Warehouse.Business.Concrete.Review.Events;
using Ticimax.Warehouse.Business.Concrete.Report.TableMovements.Enums;
using Ticimax.Warehouse.Business.Concrete.Report.TableMovements.Events;
using Ticimax.Warehouse.Business.Concrete.TableMovements.Enums;

#nullable enable
namespace Ticimax.Warehouse.Business.Concrete
{
    public class PickingProductService : BaseService, IPickingProductService
    {
        private readonly IPickingProductDal _pickingProductDal;
        private readonly IWarehouseTableService _warehouseTableService;
        private readonly IWarehouseParcelService _warehouseParcelService;
        private readonly IShelfProductService _shelfProductService;
        private readonly IOrderCollectionService _orderCollectionService;
        private readonly IStoreAgentService _storeAgentService;
        private readonly IWarehouseCarService _warehouseCarService;
        private readonly IOrderProductService _orderProductService;
        private readonly IOrderService _orderService;
        private readonly IOrderPaymentService _orderPaymentService;
        private readonly ITicimaxWarehouseService _ticimaxWarehouseService;
        private readonly IOrderProductStatusService _orderProductStatusService;
        private readonly IOrderMovementService _orderMovementService;
        private readonly ILogService _logService;
        private readonly ILogger<PickingProductService> _logger;
        private readonly ICustomerServicesManager _customerServicesManager;
        private readonly IInvoiceService _invoiceService;
        private readonly IProductService _productService;
        private readonly IWarehouseCarProductService _warehouseCarProductService;
        private readonly IShelfService _shelfService;
        private readonly ICacheManager _cacheManager;
        private readonly IWarehouseBoxService _warehouseBoxService;
        private readonly IOrderCancelFromStoreAgentLogService _orderCancelFromStoreAgentLogService;
        private readonly IPrintService _printService;
        private readonly IImageService _imageService;
        private readonly ITransactionalCommandService _transactionalCommandService;
        private readonly IConnectionMultiplexer _redisConnect;
        private readonly IWarehouseProductTransferFileDal _warehouseProductTransferFileDal;
        private readonly IProductMovementService _productMovementService;
        private readonly IStoreService _storeService;
        private readonly IShelfCountingFilesService _shelfCountingFilesService;
        private readonly IWarehouseEmailService _warehouseEmailService;
        private readonly ICargoIntegrationService _cargoIntegrationService;
        private readonly ICargoCompanyService _cargoCompanyService;
        private readonly ICountryCityDistrictDal _countryCityDistrictDal;

        public PickingProductService
        (
            IPickingProductDal pickingProductDal
            , IWarehouseTableService warehouseTableService
            , IWarehouseParcelService warehouseParcelService
            , IShelfProductService shelfProductService
            , IOrderCollectionService orderCollectionService
            , IStoreAgentService storeAgentService
            , IWarehouseCarService warehouseCarService
            , IOrderProductService orderProductService
            , IOrderService orderService
            , IOrderPaymentService orderPaymentService
            , ITicimaxWarehouseService ticimaxWarehouseService
            , IOrderProductStatusService orderProductStatusService
            , IOrderMovementService orderMovementService
            , ILogService logService
            , ILogger<PickingProductService> logger
            , ICustomerServicesManager customerServicesManager
            , IInvoiceService invoiceService
            , IProductService productService
            , IWarehouseCarProductService warehouseCarProductService
            , IShelfService shelfService
            , ICacheManager cacheManager
            , IWarehouseBoxService warehouseBoxService
            , IOrderCancelFromStoreAgentLogService orderCancelFromStoreAgentLogService
            , IPrintService printService
            , IImageService imageService
            , ITransactionalCommandService transactionalCommandService
            , IConnectionMultiplexer redisConnect
            , IWarehouseProductTransferFileDal warehouseProductTransferFileDal
            , IProductMovementService productMovementService
            , IStoreService storeService
            , IShelfCountingFilesService shelfCountingFilesService
            , IWarehouseEmailService warehouseEmailService
            , ICargoIntegrationService cargoIntegrationService
            , ICargoCompanyService cargoCompanyService
            , ICountryCityDistrictDal countryCityDistrictDal)
        {
            _pickingProductDal = pickingProductDal;
            _warehouseTableService = warehouseTableService;
            _warehouseParcelService = warehouseParcelService;
            _shelfProductService = shelfProductService;
            _orderCollectionService = orderCollectionService;
            _storeAgentService = storeAgentService;
            _warehouseCarService = warehouseCarService;
            _orderProductService = orderProductService;
            _orderService = orderService;
            _orderPaymentService = orderPaymentService;
            _ticimaxWarehouseService = ticimaxWarehouseService;
            _orderProductStatusService = orderProductStatusService;
            _orderMovementService = orderMovementService;
            _logService = logService;
            _logger = logger;
            _customerServicesManager = customerServicesManager;
            _invoiceService = invoiceService;
            _productService = productService;
            _warehouseCarProductService = warehouseCarProductService;
            _shelfService = shelfService;
            _cacheManager = cacheManager;
            _warehouseBoxService = warehouseBoxService;
            _orderCancelFromStoreAgentLogService = orderCancelFromStoreAgentLogService;
            _printService = printService;
            _imageService = imageService;
            _transactionalCommandService = transactionalCommandService;
            _redisConnect = redisConnect;
            _warehouseProductTransferFileDal = warehouseProductTransferFileDal;
            _productMovementService = productMovementService;
            _storeService = storeService;
            _shelfCountingFilesService = shelfCountingFilesService;
            _warehouseEmailService = warehouseEmailService;
            _cargoIntegrationService = cargoIntegrationService;
            _cargoCompanyService = cargoCompanyService;
            _countryCityDistrictDal = countryCityDistrictDal;
        }

        public async Task<ErrorResponse> DistributionProductAsync(PickingProductServiceDistributionProductDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            int type = request.Type.Split('-').FirstOrDefault().ToInt32();
            if (WebSiteInfo.User.Value.Settings.ArabaModulAktif)
            {
                if (WebSiteInfo.User.Value.WarehouseCar == null)
                    throw new NotFoundException("KullanimdaArabaBulunamadi");

                if (!string.IsNullOrEmpty(WebSiteInfo.User.Value.WarehouseCar.SetNo))
                    throw new BusinessException("ArabanizaAitBirSetBulunmaktadir");

                var carProducts = (await _warehouseCarProductService.GetList(new WarehouseCarProductGetListDto { WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID }, null, cancellationToken)).Model;
                if (carProducts.Count > 0)
                    throw new BusinessException("ArabanizdaUrunBulunmaktadirTemizlemenizGerekmektedir");
            }

            var count = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                        PreparedID = WebSiteInfo.User.Value.ID,
                        FindStatus = false,
                        PreparedStatusList = new List<int> { 0, 1, 6 },
                        OrderID = request.OrderId > 0 ? request.OrderId : null
                    }
                }, cancellationToken)).Model.Products.Count;

            if (count > 0)
            {
                var set = (await _orderCollectionService.GetCollectionSet(
                    new GetOrderCollectionSetDto
                    {
                        Filter = new OrderCollectionSetFilter
                        {
                            PreparedID = WebSiteInfo.User.Value.ID,
                            FindStatus = false,
                            PreparedStatusList = new List<int> { 0, 1, 6 }
                        }
                    }, cancellationToken)).Model;

                if (set != null)
                {
                    var setProduct = set.Products.FirstOrDefault();
                    if (string.IsNullOrEmpty(WebSiteInfo.User.Value.SetNo))
                    {
                        await _storeAgentService.UpdateSetNo(new StoreAgentUpdateSetNoDto
                        {
                            ID = WebSiteInfo.User.Value.ID,
                            SetNo = set.SetNo,
                        }, cancellationToken);

                        WebSiteInfo.User.Value.SetNo = set.SetNo;

                        await _storeAgentService.UpdateTableID(new StoreAgentUpdateTableIDDto
                        {
                            PersonID = WebSiteInfo.User.Value.ID,
                            TableID = setProduct.TableID,
                        }, cancellationToken);

                        WebSiteInfo.User.Value.TableID = setProduct.TableID;
                    }

                    if (WebSiteInfo.User.Value.Settings.ArabaModulAktif)
                    {
                        var car = (await _warehouseCarService.GetList(new WarehouseCarGetListDto { CarID = setProduct.CarID, isActive = true }, null, cancellationToken)).Model.FirstOrDefault();
                        if (setProduct.CarID > 0)
                        {
                            // Kullanıcının üzerinde Set de tanımlı olmayan bir araba var ise mevcuttaki arabasını bıraktırıp tamamlanmayan set'in arabasına atıyoruz
                            var userCar = (await _warehouseCarService.GetList(new WarehouseCarGetListDto { PersonID = WebSiteInfo.User.Value.ID }, null, cancellationToken)).Model.FirstOrDefault();
                            if (userCar != null && userCar.ID != car.ID)
                                await _warehouseCarService.LeaveCar(new WarehouseCarLeaveCarDto { Barcode = userCar.Barcode }, cancellationToken);
                        }

                        if (car.PersonID == 0)
                        {
                            await _warehouseCarService.UpdateSetNo(new WarehouseCarUpdateSetNoDto { PersonID = WebSiteInfo.User.Value.ID, SetNo = set.SetNo }, cancellationToken);
                            WebSiteInfo.User.Value.WarehouseCar.SetNo = set.SetNo;
                        }
                    }

                    throw new BusinessException("TamamlanmamisSiparisinizBulunmaktadirVeUzerinizdekiSetNoGuncellenmistir");
                }

                throw new BusinessException("UstunuzdeSiparisBulunmaktadırYeniSetAlamazsiniz");
            }

            var filter = new PickingProductGetDistiributionProductDto(new PickingProductFilter
            {
                OrderStatus = OrderStatus.On,
                PackageStatus = PackageStatus.Beklemede,
                PriorityPackageStatus = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu,
                OrderID = request.OrderId
            });

            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                filter.Filter.OrderStatus = null;
                filter.Filter.PackageStatus = null;
                filter.Filter.OrderStatusList.AddRange(new List<OrderStatus>()
                {
                    OrderStatus.On,
                    OrderStatus.Onaylandi,
                    OrderStatus.Paketleniyor,
                    OrderStatus.KargoyaVerildi
                });
            }

            if (type == 2)
                filter.Filter.ProductCount = 1;
            else if (type == 3)
                filter.Filter.ProductCountLarge = 1;
            else if (type == 5)
            {
                filter.Filter.ProductCount = 0;
                filter.Filter.ProductCountLarge = 0;
            }
            else if (type >= 1000)
            {
                filter.Filter.ProductCount = 0;
                filter.Filter.ProductCountLarge = 0;
                var typeInfo = request.Type.Split('-').LastOrDefault();
                if (type == 1000)
                {
                    filter.Filter.CargoCompanyOrderType = request.Type.Split("-")[1].ToString();
                    filter.Filter.CargoCompanyId = typeInfo.ToInt32();
                }
                else if (type == 2000)
                {
                    filter.Filter.MarketPlaceOrderType = request.Type.Split("-")[1].ToString();
                    filter.Filter.OrderSource = typeInfo?.ToLower();
                }
                else if (type == 3000)
                    filter.Filter.PaymentType = typeInfo.ToInt32();
                else if (type == 4100)
                    filter.Filter.CargoIntegrationId = typeInfo.ToInt32();
                else if (type == 4200)
                    filter.Filter.CountryId = typeInfo.ToInt32();
                else if (type == 4300)
                {
                    filter.Filter.SetManagementOrders = true;
                    string setNo = request.Type.Substring(request.Type.IndexOf('-') + 1);

                    var set = await _orderCollectionService.OrdersInTheSet(new OrderInTheSetFilter()
                    {
                        SetNo = setNo
                    }, cancellationToken);

                    var orderIds = set.Model.Select(x => x.OrderID).Distinct().ToList();
                    if (orderIds.Count > 0)
                        filter.Filter.OrderIDList = orderIds;
                }
            }

            var distuributionProducts = (await GetDistributionProduct(filter, null, cancellationToken)).Model;

            List<PickingProduct> oncelikliUrunler = distuributionProducts.Where(x => x.PackagingStatusID == filter.Filter.PriorityPackageStatus).ToList();

            foreach (var oncelikliUrun in oncelikliUrunler)
                distuributionProducts.Remove(oncelikliUrun);

            List<PickingProduct> productList = new List<PickingProduct>();
            switch (type)
            {
                case 1:
                    //Varsa Öncelikli ürün siparişi yoksa normal bir sipariş veriliyor.
                    if (oncelikliUrunler.Count > 0)
                    {
                        var firstOrder = oncelikliUrunler.OrderBy(x => x.OrderID).First().OrderID;
                        productList = oncelikliUrunler.Where(x => x.OrderID == firstOrder).ToList();
                    }

                    if (productList.Count == 0)
                        productList = distuributionProducts.GroupBy(x => x.OrderID).OrderBy(x => x.Key).FirstOrDefault()?.ToList() ?? new List<PickingProduct>();

                    break;
                case 2:
                    //Tekli ürünler geldiği için önce öncelikli ürünleri sonrada önceliği olmayan ürünleri veriyoruz. Eksik kalırsa tamamlama yapıyoruz.
                    productList = oncelikliUrunler.OrderBy(x => x.OrderID).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.TekUrunluSiparisSayisi).ToList();
                    int eksikKalanTekSiparisSayisi = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.TekUrunluSiparisSayisi - productList.GroupBy(x => x.OrderID).Select(x => x.Key).Count();
                    if (eksikKalanTekSiparisSayisi > 0)
                        productList.AddRange(distuributionProducts.OrderBy(x => x.OrderID).Take(eksikKalanTekSiparisSayisi).ToList());

                    break;
                case 3:
                    //Burada ürünler başka siparişlere ait olabileceği için önce sipariş id listesi oluşturup oradan alınacakları belirliyoruz. Burası yeterli gelmezse diğer ürünler ile eksiği tamamlıyoruz.
                    var cokUrunluOncelikliSiparisler = oncelikliUrunler.GroupBy(x => x.OrderID).Select(x => x.Key).OrderBy(x => x).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.CokUrunluSiparisSayisi).ToList();
                    productList = oncelikliUrunler.Where(x => cokUrunluOncelikliSiparisler.Contains(x.OrderID)).ToList();
                    int eksikKalanCokSiparisSayisi = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.CokUrunluSiparisSayisi - productList.Select(x => x.OrderID).Distinct().Count();
                    if (eksikKalanCokSiparisSayisi > 0)
                    {
                        var keyList = distuributionProducts.GroupBy(x => x.OrderID).OrderBy(x => x.Key).Select(x => x.Key).Take(eksikKalanCokSiparisSayisi).ToList();
                        productList.AddRange(distuributionProducts.Where(x => keyList.Contains(x.OrderID)).ToList());
                    }

                    break;
                case 4:
                    productList = distuributionProducts;
                    break;
                case 5:
                    //Burada tekli veyahutta çoklu gibi bir durum olmadığı için çokludaki ile aynı işlemi uyguluyoruz.
                    var karisikOncelikliSiparisler = oncelikliUrunler.GroupBy(x => x.OrderID).Select(x => x.Key).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KarisikSiparisSayisi).ToList();
                    productList = oncelikliUrunler.Where(x => karisikOncelikliSiparisler.Contains(x.OrderID)).ToList();
                    var eksikKalanKarisikSiparisSayisi = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KarisikSiparisSayisi - productList.Select(x => x.OrderID).Distinct().Count();
                    if (eksikKalanKarisikSiparisSayisi > 0)
                    {
                        var keyList = distuributionProducts.OrderBy(x => x.OrderDate).ThenBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(x => x.Key).Take(eksikKalanKarisikSiparisSayisi).ToList();
                        productList.AddRange(distuributionProducts.Where(x => keyList.Contains(x.OrderID)).ToList());
                    }

                    break;
            }

            var allShelfs = await _shelfService.GetList(new ShelfGetListDto() { });

            if (type == 1000 || type == 4100)
            {
                var carrierPriorityOrder = oncelikliUrunler.GroupBy(x => x.OrderID).Select(x => x.Key).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KargoSiparisSayisi).ToList();
                productList = oncelikliUrunler.Where(x => carrierPriorityOrder.Contains(x.OrderID)).ToList();
                var missingCarrierOrderCount = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KargoSiparisSayisi - productList.Select(x => x.OrderID).Distinct().Count();
                if (missingCarrierOrderCount > 0)
                {
                    var keyList = distuributionProducts.OrderBy(x => x.OrderDate).ThenBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(x => x.Key).Take(missingCarrierOrderCount).ToList();
                    productList.AddRange(distuributionProducts.OrderBy(x => x.OrderDate).Where(x => keyList.Contains(x.OrderID)).ToList());
                }
            }
            else if (type == 2000)
            {
                var marketPlacePriorityOrder = oncelikliUrunler.GroupBy(x => x.OrderID).Select(x => x.Key).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.MarketPlaceSiparisSayisi).ToList();
                productList = oncelikliUrunler.Where(x => marketPlacePriorityOrder.Contains(x.OrderID)).ToList();
                var missingMarketPlaceOrderCount = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.MarketPlaceSiparisSayisi - productList.Select(x => x.OrderID).Distinct().Count();
                if (missingMarketPlaceOrderCount > 0)
                {
                    var keyList = distuributionProducts.OrderBy(x => x.OrderDate).ThenBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(x => x.Key).Take(missingMarketPlaceOrderCount).ToList();
                    productList.AddRange(distuributionProducts.OrderBy(x => x.OrderDate).Where(x => keyList.Contains(x.OrderID)).ToList());
                }
            }
            else if (type == 3000)
            {
                var carrierPriorityOrder = oncelikliUrunler.GroupBy(x => x.OrderID).Select(x => x.Key).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.PaymentTypeOrderCount).ToList();
                productList = oncelikliUrunler.Where(x => carrierPriorityOrder.Contains(x.OrderID)).ToList();
                var missingCarrierOrderCount = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.PaymentTypeOrderCount - productList.Select(x => x.OrderID).Distinct().Count();
                if (missingCarrierOrderCount > 0)
                {
                    var keyList = distuributionProducts.OrderBy(x => x.OrderDate).ThenBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(x => x.Key).Take(missingCarrierOrderCount).ToList();
                    productList.AddRange(distuributionProducts.OrderBy(x => x.OrderDate).Where(x => keyList.Contains(x.OrderID)).ToList());
                }
            }
            else if (type is 4000 or 4001 or 4002 or 4003 or 4004)
            {
                int typeInfo = 0;
                if (type != 4000)
                    typeInfo = request.Type.Split('-').LastOrDefault().ToInt32();


                int index = request.Type.IndexOf('-');
                var typeInfoStockCode = request.Type.Substring(index + 1);
                distuributionProducts.AddRange(oncelikliUrunler);

                var orderIds = new List<int>();
                foreach (var orderGroupped in distuributionProducts.GroupBy(x => x.OrderID))
                {
                    var differentProduct = orderGroupped.Select(x => x.ProductID).Distinct();
                    var differentProductStockCode = orderGroupped.Select(x => x.StockCode).Distinct();
                    var diffrentProductList = differentProduct.ToList();
                    var diffrentProductStockCodeList = differentProductStockCode.ToList();
                    if (type == 4000 && diffrentProductStockCodeList.Count() == 1 && diffrentProductStockCodeList.Any(x => x == typeInfoStockCode))
                        orderIds.Add(orderGroupped.Key);

                    var differentLocation = orderGroupped.Select(x => x.ShelfID).Distinct();
                    var diffrentLocationList = differentLocation.ToList();
                    if (type == 4001 && diffrentLocationList.Count() == 1 && diffrentLocationList.Any(x => x == typeInfo))
                        orderIds.Add(orderGroupped.Key);

                    if (type == 4002 || type == 4003 || type == 4004)
                    {
                        List<int> orderGroupMainShelfIds = new List<int>();
                        bool allProductEqualShelfParentId = false;
                        foreach (var orderGrouppedItem in orderGroupped.ToList())
                        {
                            var parentShelfId = GetTopParentShelfId(allShelfs.Model, orderGrouppedItem.ShelfID);
                            orderGroupMainShelfIds.Add(parentShelfId);
                        }
                        if (orderGroupMainShelfIds.Count > 0)
                            allProductEqualShelfParentId = !orderGroupMainShelfIds.Any(x => x != typeInfo);
                        if (type == 4002 && orderGroupMainShelfIds.Distinct().Count() == 1 && orderGroupMainShelfIds.Any(x => x != 0) && allProductEqualShelfParentId)
                            orderIds.Add(orderGroupped.Key);

                        //4003 ve 4004 'de aynı ana lokasyon bazında bulunan siparişleri tek ürünlü veya çok ürünlü şeklinde ayırıp toplamaya başlıyoruz.
                        if (type == 4003 && orderGroupMainShelfIds.Distinct().Count() == 1 &&
                                    orderGroupMainShelfIds.Any(x => x != 0) && allProductEqualShelfParentId && orderGroupped.ToList().Sum(x => x.OrderProductPiece) == 1)
                            orderIds.Add(orderGroupped.Key);

                        if (type == 4004 && orderGroupMainShelfIds.Distinct().Count() == 1 &&
                                    orderGroupMainShelfIds.Any(x => x != 0) && allProductEqualShelfParentId && orderGroupped.ToList().Sum(x => x.OrderProductPiece) > 1)
                            orderIds.Add(orderGroupped.Key);

                    }
                }
                if (type == 4002 || type == 4004)
                    orderIds = orderIds.OrderBy(x => x).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.CokUrunluSiparisSayisi).ToList();
                else if (type == 4003)
                    orderIds = orderIds.OrderBy(x => x).Take(100).ToList();

                productList = distuributionProducts.Where(x => orderIds.Contains(x.OrderID)).ToList();
            }
            else if (type == 4200)
            {
                //TODO: WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KargoSiparisSayisi -> Buradaki Take için yeni bir ayar tanımlanacak mı?
                var countriesOrder = oncelikliUrunler.GroupBy(x => x.OrderID).Select(x => x.Key).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KargoSiparisSayisi).ToList();
                productList = oncelikliUrunler.Where(x => countriesOrder.Contains(x.OrderID)).ToList();
                var countriesMissingOrderCount = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KargoSiparisSayisi - productList.Select(x => x.OrderID).Distinct().Count();
                if (countriesMissingOrderCount > 0)
                {
                    var keyList = distuributionProducts.OrderBy(x => x.OrderDate).ThenBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(x => x.Key).Take(countriesMissingOrderCount).ToList();
                    productList.AddRange(distuributionProducts.OrderBy(x => x.OrderDate).Where(x => keyList.Contains(x.OrderID)).ToList());
                }
            }

            else if (type == 4300)
            {
                var setManagementOrders = oncelikliUrunler.GroupBy(x => x.OrderID).Select(x => x.Key).Take(WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KargoSiparisSayisi).ToList();
                productList = oncelikliUrunler.Where(x => setManagementOrders.Contains(x.OrderID)).ToList();
                var countriesMissingOrderCount = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.KargoSiparisSayisi - productList.Select(x => x.OrderID).Distinct().Count();
                if (countriesMissingOrderCount > 0)
                {
                    var keyList = distuributionProducts.OrderBy(x => x.OrderDate).ThenBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(x => x.Key).Take(countriesMissingOrderCount).ToList();
                    productList.AddRange(distuributionProducts.OrderBy(x => x.OrderDate).Where(x => keyList.Contains(x.OrderID)).ToList());
                }
            }

            if (productList.Count > 0)
            {
                productList.Where(x => x.PickerID == 0).ToList().ForEach(x => x.PickerID = WebSiteInfo.User.Value.ID);

                productList.ForEach(x =>
                {
                    x.ProductType = 1;
                    x.WarehouseID = WebSiteInfo.User.Value.WarehouseID;
                    x.WarehouseName = WebSiteInfo.User.Value.Warehouse;
                });

                if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.UrunToplamaYontemi == 4)
                {
                    var shelfs = (await _shelfService.GetList(new ShelfGetListDto { IsEmptyShelf = false }, null, cancellationToken)).Model;
                    var warehouseBoxProducts = await _warehouseBoxService.GetList(new WarehouseBoxGetListDto());
                    if (warehouseBoxProducts.Model.Count == 0)
                        return new ErrorResponse { IsError = true, ErrorMessage = "KutuBilgisiBulunamadi" };

                    distuributionProducts.ForEach(async x =>
                    {
                        if (!warehouseBoxProducts.Model.Any(y => y.Products.Any(z => z.Product.ID == x.ProductID && z.Piece >= x.Piece)))
                        {
                            x.WarehouseID = 0;
                            x.WarehouseName = null;
                            x.WarehouseCode = null;
                            x.ShelfID = 0;
                            x.ShelfRank = 999999;
                            x.ShelfBarcode = "BULUNAMAYAN";
                            x.ShelfName = "BULUNAMAYAN";
                            x.WarehouseBoxID = 0;
                            x.WarehouseBoxBarcode = "BULUNAMAYAN";
                        }

                        if (warehouseBoxProducts.Model.Any(y => y.Products.Any(z => z.Product.ID == x.ProductID && z.Piece >= x.Piece)))
                        {
                            var boxProducts = warehouseBoxProducts.Model.FirstOrDefault(y => y.Products.Any(z => z.Product.ID == x.ProductID && z.Piece >= x.Piece));
                            var product = boxProducts.Products.FirstOrDefault(y => y.Product.ID == x.ProductID && y.Piece >= x.Piece);
                            var shelf = shelfs.FirstOrDefault(y => y.ID == boxProducts.ShelfID);
                            if (shelf != null)
                            {
                                x.WarehouseID = shelf.WarehouseID;
                                x.WarehouseName = "";
                                x.WarehouseCode = "";
                                x.ShelfID = shelf.ID;
                                x.ShelfBarcode = shelf.Barcode;
                                x.ShelfName = shelf.Definition;
                                x.ShelfRank = shelf.Rank;
                                x.WarehouseBoxID = boxProducts.ID;
                                x.WarehouseBoxBarcode = boxProducts.Barcode;

                                product.Piece -= x.Piece;
                                boxProducts.Products[boxProducts.Products.FindIndex(y => y.Product.ID == product.Product.ID)] = product;
                                warehouseBoxProducts.Model[warehouseBoxProducts.Model.FindIndex(y => y.ID == boxProducts.ID)] = boxProducts;
                                await _warehouseBoxService.UpdateBoxProducts(new WarehouseBoxProductsUpdateDto { ID = boxProducts.ID, Products = boxProducts.Products }, cancellationToken);
                            }
                            else
                            {
                                x.WarehouseID = 0;
                                x.WarehouseName = null;
                                x.WarehouseCode = null;
                                x.ShelfID = 0;
                                x.ShelfRank = 999999;
                                x.ShelfBarcode = "BULUNAMAYAN";
                                x.ShelfName = "BULUNAMAYAN";
                                x.WarehouseBoxID = 0;
                                x.WarehouseBoxBarcode = "BULUNAMAYAN";
                            }
                        }
                    });
                }

                // set oluşturulacak bir kayıt da depo ID tanımlaması olmamış ise set oluşturmayı iptal ediyoruz...
                if (productList.Any(x => x.WarehouseID == 0))
                    throw new BusinessException("WAREHOUSE_ID_IS_NOT_TO_BE_EMPTY");

                string setNo = "";
                if (type != 4300)
                    setNo = await _pickingProductDal.DistributionProductAsync(productList, cancellationToken); // Burada ürün dağıtım tipi ürün bazlı ise raf stoğundan düşme işlemi yapılır ve dağıtılan ürün tablosuna eklenir
                else
                {
                    setNo = request.Type.Substring(request.Type.IndexOf('-') + 1);
                    await _orderCollectionService.SetUpdatePreparedID(setNo, WebSiteInfo.User.Value.ID, cancellationToken);
                }


                if (string.IsNullOrWhiteSpace(setNo))
                    throw new BusinessException("SET_NO_IS_NOT_EMPTY");

                await _storeAgentService.UpdateSetNo(new StoreAgentUpdateSetNoDto { ID = WebSiteInfo.User.Value.ID, SetNo = setNo }, cancellationToken);


                //TODO : Queue ile çözülecek.
                if (!string.IsNullOrEmpty(setNo) && WebSiteInfo.User.Value.Settings.GuvenliStokAdedi > 0 && !string.IsNullOrEmpty(WebSiteInfo.User.Value.Settings.GuvenliStokMail))
                {
                    var shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto { ProductIDList = productList.Select(x => x.ProductID).ToList() }, null, cancellationToken)).Model;

                    var tos = WebSiteInfo.User.Value.Settings.GuvenliStokMail.Split(',').ToList();

                    shelfProducts.GroupBy(x => new { TargetID = x.ProductID, TargetBarcode = x.Barcode })
                        .Select(x => new
                        {
                            Target = x.FirstOrDefault(y => y.ProductID == x.Key.TargetID)?.ProductName,
                            x.Key.TargetID,
                            x.Key.TargetBarcode,
                            TotalStock = x.Sum(y => y.ShelfStock)
                        })
                        .Where(x => WebSiteInfo.User.Value.Settings.GuvenliStokAdedi >= x.TotalStock)
                        .ToList()
                        .ForEach(async x => { await _warehouseEmailService.SendSafetyStockMail(tos, x.TargetID, x.TargetBarcode, x.Target, x.TotalStock, cancellationToken); });
                }

                if (!string.IsNullOrEmpty(setNo))
                {
                    WebSiteInfo.User.Value.SetNo = setNo;
                    if (WebSiteInfo.User.Value.Settings.ArabaModulAktif)
                    {
                        await _warehouseCarService.UpdateSetNo(new WarehouseCarUpdateSetNoDto
                        {
                            PersonID = WebSiteInfo.User.Value.ID,
                            SetNo = setNo
                        }, cancellationToken);

                        WebSiteInfo.User.Value.WarehouseCar.SetNo = setNo;
                    }
                }

                List<Task> waitingTask = new List<Task>();
                string carMessage = WebSiteInfo.User.Value.WarehouseCar != null ? $"Araba ismi: {WebSiteInfo.User.Value.WarehouseCar.Definition}" : "";
                foreach (var orderId in productList.Select(x => x.OrderID).Distinct())
                {
                    waitingTask.Add(_orderMovementService.AddAsync(
                        new OrderMovementAddDto
                        {
                            AgentID = WebSiteInfo.User.Value.ID,
                            isSystem = true,
                            Message = $"Sipariş toplama {WebSiteInfo.User.Value.Name} toplayıcısının setine({setNo}) atandı. {carMessage}",
                            Name = WebSiteInfo.User.Value.Name,
                            OrderID = orderId,
                        }, cancellationToken));
                }

                foreach (var product in productList)
                {
                    waitingTask.Add(_productMovementService.CreateMovement(
                        product.ProductID,
                        new CreateProductMovementRequest(ProductMovementProcessType.Allocated, product.Piece, ProductMovementMessage.Allocated(product.ShelfName, product.OrderID), null),
                        cancellationToken));
                }

                Task.WaitAll(waitingTask.ToArray());

                await _orderService.SetPackagingStatus(
                    new OrderSetPackagingStatusDto
                    {
                        IDs = productList.Select(x => x.OrderID).Distinct().ToList(),
                        Status = Core.Order.Entities.Enums.PackageStatus.Paketleniyor,
                    }, cancellationToken);

                await _orderService.UpdateOrderPreparedID(
                    new UpdateOrderPreparedIDDto
                    {
                        OrderIDs = productList.Select(x => x.OrderID).Distinct().ToList(),
                        PreparedID = WebSiteInfo.User.Value.ID,
                    }, cancellationToken);

                //TODO: eğer setNo oluştuysa stok ile ilgili eventı koyalım buraya.
                if (!string.IsNullOrEmpty(setNo))
                {
                    List<ProductMovementStockControlInfo> productMovementStockList = new List<ProductMovementStockControlInfo>();

                    var dbProductList = (await _productService.GetList(new ProductGetListDto() { IDList = productList.Select(x => x.ProductID).ToList(), AddSubQueries = false })).Model;
                    var grouppedPickingProducts = productList.GroupBy(x => x.ProductID).Select(group => new { ProductId = group.Key, TotalPickingQuantity = group.Sum(x => x.Piece) });

                    foreach (var groupPickingProduct in grouppedPickingProducts)
                    {
                        var dbProduct = dbProductList.FirstOrDefault(x => x.ID == groupPickingProduct.ProductId);
                        if (dbProduct != null)
                        {
                            var shelfStock = (await _shelfProductService.GetList(new ShelfProductGetListDto() { ProductID = dbProduct.ID, WarehouseID = WebSiteInfo.User.Value.WarehouseID }, null, cancellationToken)).Model.Sum(x => x.ShelfStock);

                            ProductMovementStockControlInfo stockInfo = new ProductMovementStockControlInfo();
                            stockInfo.Piece = groupPickingProduct.TotalPickingQuantity;
                            stockInfo.WebStock = dbProduct.StockPiece;
                            stockInfo.ConsigmentStock = dbProduct.ConsignmentStockPiece;
                            stockInfo.ShelfStock = shelfStock - groupPickingProduct.TotalPickingQuantity;
                            stockInfo.OldWebStock = dbProduct.StockPiece;
                            stockInfo.OldConsigmentStock = dbProduct.ConsignmentStockPiece;
                            stockInfo.OldShelfStock = shelfStock;
                            stockInfo.ProductId = dbProduct.ID;
                            stockInfo.ProductName = dbProduct.ProductName;
                            stockInfo.DomainName = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.DomainName : "";
                            stockInfo.StoreId = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.StoreID : 0;
                            stockInfo.WarehouseId = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.WarehouseID : 0;
                            stockInfo.CreatedDate = DateTime.Now.ToTimestamp();
                            stockInfo.Type = ProductMovementStockControlTypeEnum.OrderSelectStockReduce.ToString();
                            stockInfo.ObjectId = string.Join(",", productList.Select(x => x.OrderID).Distinct().ToList());
                            productMovementStockList.Add(stockInfo);
                        }
                    }

                    WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvent.Select, new SelectProductEventPayload(productMovementStockList)));
                }
            }
            else
                throw new NotFoundException("DagitilacakUrunBulunamadi");


            return response;
        }

        public async Task<List<PickingProduct>> GetNotDistributionProduct(PickingProductGetDistiributionProductDto request, CancellationToken cancellationToken)
        {
            return await _pickingProductDal.GetListAsync(request.Filter, null, cancellationToken);
        }

        public async Task<DataResult<List<PickingProduct>>> GetDistributionProduct(PickingProductGetDistiributionProductDto request, PickingProductPaging paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<PickingProduct>> response = new DataResult<List<PickingProduct>>();

            if (paging == null) paging = new PickingProductPaging();
            if (WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OrderSortingType == "SIPARISTARIHI")
            {
                paging.SortingValue = "s.TARIH";
                paging.SortingDirection = "ASC";
            }
            else if (WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OrderSortingType == "TESLIMATTARIHI")
            {
                paging.SortingValue = "s.TESLIMATGUNU ASC, s.TESLIMATSAATI";
                paging.SortingDirection = "ASC";
            }

            List<PickingProduct> pickingProducts = (await _pickingProductDal.GetListAsync(request.Filter, paging, cancellationToken)).ToList();
            if (pickingProducts.Any())
            {
                List<ShelfProductInPiece> shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto
                {
                    WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                    ProductIDList = pickingProducts.Where(x => x.ShelfID == 0).Select(x => x.ProductID).Distinct().ToList(),
                    IsStockAvailable = true,
                    IsEmptyShelf = false,
                    IsPickingOpened = true
                }, null, cancellationToken)).Model.ToList();


                if (request.Filter.IsPickingProductScreen)
                {
                    // Ürünleri "ShelfID == 0" olanları tek seferde alıyoruz.
                    var pickingProductsWithNoShelf = pickingProducts.Where(x => x.ShelfID == 0).ToList();

                    // ShelfProduct verilerini ProductID’ye göre gruplayarak dictionary yapısına taşıyoruz.
                    var shelfProductGroups = shelfProducts
                        .GroupBy(sp => sp.ProductID)
                        .ToDictionary(g => g.Key, g => g.ToList());

                    foreach (var product in pickingProductsWithNoShelf)
                    {
                        if (!shelfProductGroups.TryGetValue(product.ProductID, out var productShelves))
                        {
                            continue; // Bu ürün için raf yoksa atla.
                        }

                        // Her ürün için uygun bir raf bulana kadar rafları sırayla dolaşıyoruz.
                        foreach (var shelfProduct in productShelves)
                        {
                            var assignedTotalStock = pickingProducts
                                .Where(x => x.ShelfID == shelfProduct.ShelfID && x.ProductID == product.ProductID)
                                .Sum(x => x.Piece);

                            if (shelfProduct.ShelfStock > assignedTotalStock)
                            {
                                // Uygun raf bulundu, ürün bilgilerini güncelle.
                                product.ShelfID = shelfProduct.ShelfID;
                                product.ShelfName = shelfProduct.ShelfName;
                                product.ShelfBarcode = shelfProduct.ShelfBarcode;
                                product.ShelfRank = shelfProduct.ShelfRank;
                                product.WarehouseID = shelfProduct.WarehouseID;
                                product.WarehouseName = shelfProduct.WarehouseName;
                                product.WarehouseCode = shelfProduct.WarehouseCode;
                                product.ShelfType = (int)ShelfType.WarehouseShelf;
                                break; // Uygun raf bulunduğunda diğer raflara bakmaya gerek yok, çıkış yap.
                            }
                        }
                    }

                }
                else
                {
                    foreach (var product in pickingProducts.Where(x => x.ShelfID == 0))
                    {
                        List<int> denenenRaflar = new List<int>();
                        var shelfProduct = shelfProducts.FirstOrDefault(x => x.ProductID == product.ProductID);
rafStokDene:
                        if (shelfProduct != null)
                        {
                            var assignedTotalStock = pickingProducts.Where(x => x.ShelfID == shelfProduct.ShelfID && x.ProductID == product.ProductID).Sum(x => x.Piece);
                            if (shelfProduct.ShelfStock > assignedTotalStock)
                            {
                                product.ShelfID = shelfProduct.ShelfID;
                                product.ShelfName = shelfProduct.ShelfName;
                                product.ShelfBarcode = shelfProduct.ShelfBarcode;
                                product.ShelfRank = shelfProduct.ShelfRank;
                                product.WarehouseID = shelfProduct.WarehouseID;
                                product.WarehouseName = shelfProduct.WarehouseName;
                                product.WarehouseCode = shelfProduct.WarehouseCode;
                                product.ShelfType = (int)ShelfType.WarehouseShelf;
                            }
                            else
                            {
                                denenenRaflar.Add(shelfProduct.ShelfID);
                                shelfProduct = shelfProducts.FirstOrDefault(x => !denenenRaflar.Contains(x.ShelfID) && x.ProductID == product.ProductID);
                                goto rafStokDene;
                            }
                        }
                    }
                }


                if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.MalKabuldeAdreslenmemisUrunlerSiparisDagitma && pickingProducts.Any(x => x.ShelfID == 0))
                {
                    var goodsAcceptProductShelf = (await _shelfProductService.GetList(new ShelfProductGetListDto { ProductIDList = pickingProducts.Where(x => x.ShelfID == 0).Select(x => x.ProductID).Distinct().ToList(), IsEmptyShelf = true }, null, cancellationToken)).Model;
                    if (goodsAcceptProductShelf.Count > 0)
                    {
                        foreach (var product in pickingProducts.Where(x => x.ShelfID == 0))
                        {
                            List<int> denenenRaflar = new List<int>();
                            var shelfProduct = goodsAcceptProductShelf.FirstOrDefault(x => x.ProductID == product.ProductID);
rafStokDene:
                            if (shelfProduct != null)
                            {
                                var assignedTotalStock = pickingProducts.Where(x =>
                                        x.ShelfID == shelfProduct.ShelfID && x.ProductID == product.ProductID)
                                    .Sum(x => x.Piece);

                                if (shelfProduct.ShelfStock > assignedTotalStock)
                                {
                                    product.ShelfID = shelfProduct.ShelfID;
                                    product.ShelfName = shelfProduct.ShelfName;
                                    product.ShelfBarcode = shelfProduct.ShelfBarcode;
                                    product.ShelfRank = shelfProduct.ShelfRank;
                                    product.WarehouseID = shelfProduct.WarehouseID;
                                    product.WarehouseName = shelfProduct.WarehouseName;
                                    product.WarehouseCode = shelfProduct.WarehouseCode;
                                    product.ShelfType = (int)ShelfType.WarehouseShelf;
                                }
                                else
                                {
                                    denenenRaflar.Add(shelfProduct.ShelfID);
                                    shelfProduct = goodsAcceptProductShelf.FirstOrDefault(x => !denenenRaflar.Contains(x.ShelfID) && x.ProductID == product.ProductID);
                                    goto rafStokDene;
                                }
                            }
                        }
                    }
                }

                if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.RaftaStokYoksaSiparisDagitma && request.Filter.ShelfStockDistibutionControl)
                {
                    var orderGroup = pickingProducts.GroupBy(x => new { x.OrderID }).ToList();
                    foreach (var order in orderGroup)
                    {
                        // eğer siparişin bir ürünü bile raf da bulunamazsa o siparişi dağıtımdan çıkarıyoruz...
                        var orderShelfDistributorControl = pickingProducts.FirstOrDefault(x => x.OrderID == order.Key.OrderID && x.ShelfID == 0);
                        if (orderShelfDistributorControl != null)
                            pickingProducts.RemoveAll(x => x.OrderID == order.Key.OrderID);
                    }
                }
                else
                {
                    // --> İknci olarak bütün ürün adeti raf stoğunda bulunamaz ise Bulunamadı olarak atanıyor....
                    foreach (var nonShelfProduct in pickingProducts.Where(x => x.ShelfID == 0))
                    {
                        nonShelfProduct.ShelfRank = 999999;
                        nonShelfProduct.ShelfBarcode = "BULUNAMAYAN";
                        nonShelfProduct.ShelfName = "BULUNAMAYAN";
                        nonShelfProduct.ShelfType = (int)ShelfType.NotFoundShelf;
                    }
                }

                pickingProducts = pickingProducts.OrderBy(x => x.ShelfRank).ToList();
            }

            response.Model = pickingProducts;
            return response;
        }

        public async Task<OrderSelectionTotalResponse> GetOrderSelection(PickingProductGetDistiributionProductDto request, CancellationToken cancellationToken)
        {

            string cacheKey = $"{WebSiteInfo.User.Value.DomainName}-OrderSelection-{WebSiteInfo.User.Value.ID}";

            var cacheResponse = await _cacheManager.Get<OrderSelectionTotalResponse>(cacheKey);
            if (cacheResponse != null)
                return cacheResponse;

            OrderSelectionTotalResponse response = new OrderSelectionTotalResponse();

            var result = await GetDistributionProduct(request, null, cancellationToken);
            if (!result.IsError && result.Model != null)
            {
                response.Rows = await GetFiltredAndPaginatedOrders(result, cancellationToken);
                response.OrderCount = response.Rows.First(x => x.Id == 1).Items.First().OrderCount;
                response.ProductCount = response.Rows.First(x => x.Id == 1).Items.First().ProductCount;
            }

            await _cacheManager.Add(cacheKey, response, 60 * 5);

            return response;
        }

        public async Task ResetOrderSelectionCache(CancellationToken cancellationToken)
        {
            await _cacheManager.Remove($"{WebSiteInfo.User.Value.DomainName}-OrderSelection-{WebSiteInfo.User.Value.ID}");
        }


        public async Task<DataResult<GetPickingProductsDto>> GetPickingProducts(int? orderId = null, int? shelfId = null, int? dataLimit = null, CancellationToken cancellationToken = default)
        {
            DataResult<GetPickingProductsDto> response = new DataResult<GetPickingProductsDto>();

            response.Model.Products = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        SetNo = WebSiteInfo.User.Value.SetNo,
                        PreparedID = WebSiteInfo.User.Value.ID,
                        OrderID = orderId,
                        ShelfID = shelfId,
                        PreparationStatus = 0,
                        FindStatus = orderId.HasValue ? null : false,
                        ProductNameFields = WebSiteInfo.User.Value.Settings?.UrunAdiAlani,
                        DataLimit = dataLimit ?? 20,
                        isGrouping = !WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive
                    }
                }, cancellationToken)).Model.Products;

            var productIds = response.Model.Products.Select(x => x.ProductID).ToList();
            if (productIds.Count > 0)
            {
                var products = await _productService.GetListAsync(new ProductGetListDto { IDList = productIds, AddSubQueries = false });
                foreach (var item in response.Model.Products)
                {
                    if (item.ProductType == ProductType.Product)
                    {
                        var product = products.FirstOrDefault(x => x.ID == item.ProductID);
                        if (product != null)
                        {
                            item.isProductPieceDecimal = product.isDecimalPiece;
                            item.Amount = product.Amount;
                            item.KDVAmount = product.KDVAmount;
                            item.Brand = product.Brand;
                            item.Category = product.Category;
                        }
                    }
                }
            }

            return response;
        }

        public async Task<DataResult<List<OrderProduct>>> Export(int? orderId = null, int? shelfId = null, string? setNo = null, int? preparedId = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<OrderProduct>> response = new DataResult<List<OrderProduct>>();

            response.Model = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        SetNo = string.IsNullOrEmpty(setNo) ? WebSiteInfo.User.Value.SetNo : setNo,
                        PreparedID = preparedId.HasValue ? preparedId.Value : WebSiteInfo.User.Value.ID,
                        ShelfID = shelfId,
                        OrderID = orderId,
                        ProductNameFields = WebSiteInfo.User.Value.Settings?.UrunAdiAlani
                    }
                }, cancellationToken)).Model.Products;

            var cargoCompanyList = await _cargoCompanyService.GetList(new CargoCompanyGetListDto() { Active = true });

            DataResult<List<StoreAgent>> agentList = null;
            if (response.Model.Any(x => x.PreparedID != 0))
            {
                agentList = await _storeAgentService.GetList(new StoreAgentGetListDto()
                {
                    IDs = response.Model.Select(x => x.PreparedID).Distinct().ToList()
                });
            }
            var productIds = response.Model.Select(x => x.ProductID).ToList();
            if (productIds.Count > 0)
            {
                var products = await _productService.GetListAsync(new ProductGetListDto { IDList = productIds, AddSubQueries = false }, null, cancellationToken);
                foreach (var item in response.Model)
                {
                    if (item.ProductType == ProductType.Product)
                    {
                        var product = products.FirstOrDefault(x => x.ID == item.ProductID);
                        if (product != null)
                        {
                            item.isProductPieceDecimal = product.isDecimalPiece;
                            item.Amount = product.Amount;
                            item.KDVAmount = product.KDVAmount;
                            item.Brand = product.Brand;
                            item.Category = product.Category;
                        }
                    }
                    var cargoCompany = cargoCompanyList.Model.FirstOrDefault(x => x.ID == item.CargoCompanyId);
                    if (cargoCompany != null)
                        item.CargoCompanyName = cargoCompany.Description;

                    if (agentList != null)
                    {
                        var prepared = agentList.Model.FirstOrDefault(x => x.ID == item.PreparedID);
                        if (prepared != null)
                            item.PreparedName = prepared.Name + " " + prepared.LastName;
                    }
                }
            }

            return response;
        }

        public async Task<PickingProductInformation> GetPickingProductInformation(CancellationToken cancellationToken)
        {
            var response = new PickingProductInformation();

            var set = (await _orderCollectionService.GetPendingCollectSet(new OrderCollectionGetPendingCollectSetDto { PreparedID = WebSiteInfo.User.Value.ID }, cancellationToken)).Model.FirstOrDefault();
            if (set == null)
            {
                if (string.IsNullOrWhiteSpace(WebSiteInfo.User.Value.SetNo))
                    throw new NotFoundException("SiparisinizYok");

                set = new OrderCollectionSet();
                set.SetNo = WebSiteInfo.User.Value.SetNo;
            }

            if (WebSiteInfo.User.Value.Settings.ArabaModulAktif)
            {
                if (WebSiteInfo.User.Value.WarehouseCar == null)
                    throw new NotFoundException("ArabaBulunamadi");

                if (string.IsNullOrEmpty(WebSiteInfo.User.Value.WarehouseCar.SetNo))
                {
                    await _warehouseCarService.UpdateSetNo(new WarehouseCarUpdateSetNoDto
                    {
                        PersonID = WebSiteInfo.User.Value.ID,
                        SetNo = WebSiteInfo.User.Value.SetNo,
                    }, cancellationToken);

                    WebSiteInfo.User.Value.WarehouseCar.SetNo = set.SetNo;
                }

                if (WebSiteInfo.User.Value.WarehouseCar.SetNo != set.SetNo)
                    throw new BusinessException("SecilenSetNoArabadanFarkli");
            }

            if (WebSiteInfo.User.Value.SetNo != set.SetNo)
            {
                WebSiteInfo.User.Value.SetNo = set.SetNo;
                throw new BusinessException("SecilenSetNoKullanicidanFarkli");
            }

            response.SetNo = WebSiteInfo.User.Value.SetNo;

            response.TotalProductCount = (await _orderCollectionService.ProductCountInSet(new OrderCollectionProductCountInSetDto { SetNo = WebSiteInfo.User.Value.SetNo }, cancellationToken)).Model;

            response.CollectedProductCount = (await _orderCollectionService.CollectedProductCountInSet(new OrderCollectionCollectedProductCountInSetDto { SetNo = WebSiteInfo.User.Value.SetNo }, cancellationToken)).Model;

            response.MissingProductCountInSet = (await _orderCollectionService.MissingProductCountInSet(new OrderCollectionMissingProductCountInSetDto { SetNo = WebSiteInfo.User.Value.SetNo }, cancellationToken)).Model;

            response.RemainingProductCount = response.TotalProductCount - response.CollectedProductCount - response.MissingProductCountInSet;

            return response;
        }

        public async Task<ErrorResponse> ProductFind(PickingProductFindDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            if (request.Piece == 0)
                throw new BusinessException("PICKING_PRODUCT_PIECE_NOT_EQUALS_ZERO", new KeyValuePair<string, string>("piece", request.Piece.ToString()));

            var shelf = request.ShelfID > 0 ? (await _shelfService.GetList(new ShelfGetListDto { ID = request.ShelfID }, null, cancellationToken)).Model.FirstOrDefault() : null;
            //if (shelf == null)
            //    throw new NotFoundException("SHELF_NOT_FOUND", new KeyValuePair<string, string>("shelfId", request.ShelfID.ToString()));

            if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.UrunToplamaYontemi == 4)
            {
                if (!request.WarehouseBoxID.HasValue || request.WarehouseBoxID == 0)
                    throw new BusinessException("KutuBilgisiZorunludur");

                var collection = (await _orderCollectionService.GetList(new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        ShelfID = request.ShelfID,
                        SetNo = request.SetNo,
                        OrderID = request.OrderID,
                        OrderProductID = request.OrderProductID,
                        WarehouseBoxID = request.WarehouseBoxID,
                        ProductID = request.ProductID,
                        isGrouping = false
                    }
                }, cancellationToken)).Model.FirstOrDefault()?.Products.FirstOrDefault();

                if (collection == null)
                    throw new NotFoundException("UrunBulunamadi");

                var warehouseBox = (await _warehouseBoxService.GetList(new WarehouseBoxGetListDto
                {
                    WarehouseBoxID = request.WarehouseBoxID,
                    ShelfID = request.ShelfID,
                    ProductIDs = collection.ProductType == ProductType.Product ? new List<int> { request.ProductID } : new List<int>(),
                }, null, cancellationToken)).Model.FirstOrDefault();

                if (warehouseBox == null)
                    throw new NotFoundException("KutuBulunamadi");
            }

            bool collectionFoundProduct = request.isMissingProduct && request.IsOrderCombinesRequest;

            var filter = new GetOrderCollectionSetDto
            {
                Filter = new OrderCollectionSetFilter
                {
                    SetNo = request.SetNo,
                    OrderID = request.OrderID,
                    PreparedID = WebSiteInfo.User.Value.ID,
                    PreparationStatus = 0,
                    FindStatus = false,
                    isGrouping = true,
                    MultipleBarcode = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.CokluBarkod,
                    OrderCombinesProductFound = collectionFoundProduct
                }
            };

            if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.UrunToplamaTipi == 4)
                filter.Filter.WarehouseBoxID = request.WarehouseBoxID;

            var orderProducts = (await _orderCollectionService.GetCollectionSet(filter, cancellationToken)).Model.Products;

            OrderProduct? orderProduct = null;
            if (request.OrderProductID > 0 && request.ShelfID > 0)
                orderProduct = orderProducts.FirstOrDefault(x => x.OrderProductID == request.OrderProductID && x.ShelfID == request.ShelfID);

            if (orderProduct == null && request.OrderProductID > 0)
                orderProduct = orderProducts.FirstOrDefault(x => x.OrderProductID == request.OrderProductID);

            if (orderProduct == null && request.ProductID > 0)
                orderProduct = orderProducts.FirstOrDefault(x => x.ProductID == request.ProductID);

            if (orderProduct == null && !string.IsNullOrEmpty(request.ProductBarcode))
            {
                if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.CokluBarkod)
                    orderProduct = orderProducts.FirstOrDefault(x => x.Barcode == request.ProductBarcode || x.Barcodes.Contains(request.ProductBarcode));
                else
                    orderProduct = orderProducts.FirstOrDefault(x => x.Barcode == request.ProductBarcode && x.OccurrencesPiece < x.Piece);
            }

            if (orderProduct == null)
                throw new NotFoundException("UrunBulunamadi");

            if (orderProduct.PreparationStatus == (int)SetPreparedStatus.SiparisIptalEdilmis || orderProduct.OrderStatus == (int)OrderStatus.Iptal || orderProduct.OrderStatus == (int)OrderStatus.IptalTalebi)
            {
                string orderStatusExceptionMessage = orderProduct.PreparationStatus == (int)SetPreparedStatus.SiparisIptalEdilmis || orderProduct.OrderStatus == (int)OrderStatus.Iptal ? "ORDER_STATUS_IS_CANCELLED" : "ORDER_STATUS_IS_CANCELLATION_REQUEST";
                await _orderCollectionService.DeleteByOrderID(orderProduct.OrderID, cancellationToken);
                throw new BusinessException(orderStatusExceptionMessage, new KeyValuePair<string, string>("orderId", request.OrderID.ToString()));
            }

            if (orderProduct.Status == 2)
            {
                await _orderCollectionService.DeleteByOrderProductIdForOneRow(orderProduct.OrderProductID, cancellationToken);
                throw new BusinessException("ORDER_PRODUCT_IS_CANCELLED");
            }

            if (orderProduct.OccurrencesPiece + request.Piece > orderProduct.Piece && !(orderProduct.isProductPieceDecimal && WebSiteInfo.User.Value.Settings.UrunToplamaAyar.FazlaOkutma) && !collectionFoundProduct)
                throw new BusinessException("BulunanAdetToplamAdettenBuyukOlamaz");

            if (orderProduct.OccurrencesPiece + request.Piece > orderProduct.Piece && ((orderProduct.OccurrencesPiece + request.Piece % 1 == 0)) && !collectionFoundProduct)
                throw new BusinessException("BulunanFazlaOndaliktanFazlaOlamaz");

            await OrderOverPaymentCheck(request.OrderID, new List<OrderOverPaymentCheckModel>
            {
                new OrderOverPaymentCheckModel
                    { ProductId = orderProduct.ProductID, ProductCount = request.Piece }
            }, cancellationToken);

            await _orderService.SetPackagingStatus(
                new OrderSetPackagingStatusDto
                {
                    IDs = new List<int> { request.OrderID },
                    Status = Core.Order.Entities.Enums.PackageStatus.Paketleniyor,
                }, cancellationToken);

            if (request.isMissingProduct && WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                await DoRejectStore(request.OrderID, request.OrderProductID, request.Piece, true, cancellationToken);
                WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvent.Picked,
                        new PickedEventPayload(request.OrderID, request.OrderProductID, request.ShelfID, shelf?.Definition, request.Piece,
                        orderProduct.ProductID, orderProduct.ProductName, orderProduct.StockCode, orderProduct.Barcode, request.isMissingProduct, DateTime.Now.ToTimestamp(), orderProduct.OrderDate.ToTimestamp())));

                throw new BusinessException("ORDER_PRODUCT_REJECT_AND_CHANGED_STORE_INFORMATION");
            }

            if (request.Piece > 1 || request.Piece % 1 > 0)
            {
                filter.Filter.isGrouping = false;
                filter.Filter.FindStatus = null;
                filter.Filter.OrderProductID = orderProduct.OrderProductID;
                var pickQuantity = 0;
                var setProducts = (await _orderCollectionService.GetCollectionSet(filter, cancellationToken)).Model.Products;
                var products = setProducts.Where(x => (request.ShelfID <= 0 || x.ShelfID == request.ShelfID) && (Math.Round(x.Piece, 2) != Math.Round(x.OccurrencesPiece, 2) && (Math.Round(x.MissingPiece, 2) + Math.Round(x.OccurrencesPiece, 2) != Math.Round(x.Piece, 2))) && !(x.Piece % 1 > 0)).Take(Math.Floor(request.Piece).ToInt32());
                foreach (var product in products)
                {
                    pickQuantity++;
                    await _orderProductService.UpdateProductFind(
                        new UpdateProductFindDto
                        {
                            ID = product.ID,
                            isMissingProduct = request.isMissingProduct,
                            Piece = 1,
                            OrderProductID = orderProduct.OrderProductID,
                            OrderCombinesProductFound = collectionFoundProduct,
                            SetNo = orderProduct.SetNo,
                            CarID = WebSiteInfo.User.Value.Settings.ArabaModulAktif ? WebSiteInfo.User.Value.WarehouseCar.ID : 0,
                        }, cancellationToken);
                }

                if (request.Piece % 1 > 0)
                {
                    var product = setProducts.FirstOrDefault(x => (request.ShelfID <= 0 || x.ShelfID == request.ShelfID) && x.Piece % 1 > 0);
                    if (product == null)
                    {
                        product = orderProduct;

                        await _orderProductService.UpdateProductFind(
                            new UpdateProductFindDto
                            {
                                ID = product.ID,
                                isMissingProduct = request.isMissingProduct,
                                OrderCombinesProductFound = collectionFoundProduct,
                                Piece = request.Piece % 1,
                                OrderProductID = orderProduct.OrderProductID,
                                SetNo = orderProduct.SetNo,
                                CarID = WebSiteInfo.User.Value.Settings.ArabaModulAktif ? WebSiteInfo.User.Value.WarehouseCar.ID : 0,
                            }, cancellationToken);
                    }
                    else if (product.OccurrencesPiece + request.Piece - pickQuantity > 1)
                    {
                        // decimal olan değeri sıfırlıyoruz
                        await _orderProductService.ResetOccurenciesPieceForCollection(product.ID, cancellationToken);

                        var newProduct = setProducts.FirstOrDefault(x => !products.Select(x => x.ID).ToList().Contains(x.ID) && (request.ShelfID <= 0 || x.ShelfID == request.ShelfID) && x.Piece != x.OccurrencesPiece && !(x.Piece % 1 > 0));
                        await _orderProductService.UpdateProductFind(
                            new UpdateProductFindDto
                            {
                                ID = newProduct.ID,
                                isMissingProduct = request.isMissingProduct,
                                OrderCombinesProductFound = collectionFoundProduct,
                                Piece = 1,
                                OrderProductID = orderProduct.OrderProductID,
                                SetNo = orderProduct.SetNo,
                                CarID = WebSiteInfo.User.Value.Settings.ArabaModulAktif ? WebSiteInfo.User.Value.WarehouseCar.ID : 0,
                                NotUpdateForOrderProductTable = true
                            }, cancellationToken);

                        await _orderProductService.UpdateProductFind(
                            new UpdateProductFindDto
                            {
                                ID = product.ID,
                                isMissingProduct = request.isMissingProduct,
                                OrderCombinesProductFound = collectionFoundProduct,
                                Piece = (product.OccurrencesPiece + request.Piece) - 1 - pickQuantity,
                                OrderProductID = orderProduct.OrderProductID,
                                SetNo = orderProduct.SetNo,
                                CarID = WebSiteInfo.User.Value.Settings.ArabaModulAktif ? WebSiteInfo.User.Value.WarehouseCar.ID : 0,
                            }, cancellationToken);
                    }
                    else
                    {
                        await _orderProductService.UpdateProductFind(
                            new UpdateProductFindDto
                            {
                                ID = product.ID,
                                isMissingProduct = request.isMissingProduct,
                                OrderCombinesProductFound = collectionFoundProduct,
                                Piece = request.Piece % 1,
                                OrderProductID = orderProduct.OrderProductID,
                                SetNo = orderProduct.SetNo,
                                CarID = WebSiteInfo.User.Value.Settings.ArabaModulAktif ? WebSiteInfo.User.Value.WarehouseCar.ID : 0,
                            }, cancellationToken);
                    }
                }
            }
            else
            {
                await _orderProductService.UpdateProductFind(
                    new UpdateProductFindDto
                    {
                        ID = orderProduct.ID,
                        isMissingProduct = request.isMissingProduct,
                        OrderCombinesProductFound = collectionFoundProduct,
                        Piece = request.Piece,
                        OrderProductID = orderProduct.OrderProductID,
                        SetNo = orderProduct.SetNo,
                        CarID = WebSiteInfo.User.Value.Settings.ArabaModulAktif ? WebSiteInfo.User.Value.WarehouseCar.ID : 0,
                    }, cancellationToken);
            }

            await _productMovementService.CreateMovement(
                orderProduct.ProductID,
                new CreateProductMovementRequest(ProductMovementProcessType.Picked, request.Piece, ProductMovementMessage.Picked(orderProduct.ShelfName), null),
                cancellationToken);

            if (WebSiteInfo.User.Value.Settings.ArabaModulAktif)
            {
                await _warehouseCarProductService.Add(
                    new WarehouseCarProductAddDto
                    {
                        WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID,
                        OrderID = request.OrderID,
                        Piece = request.Piece,
                        ProductID = orderProduct.ProductType == ProductType.Product ? request.ProductID : 0,
                        OrderCombinesProductFound = collectionFoundProduct,
                        Settings = new WarehouseCarProductSettings
                        {
                            isDecimalPiece = orderProduct.isProductPieceDecimal,
                            isMissingProduct = request.isMissingProduct
                        }
                    }, cancellationToken);

                _logger.LogTrace(JsonSerializerWrapper.Serialize(new WarehouseCarTableProductLog
                {
                    UserId = WebSiteInfo.User.Value.ID,
                    Username = WebSiteInfo.User.Value.Name,
                    OrderId = orderProduct.OrderID,
                    ProductId = orderProduct.ProductType == ProductType.Product ? 0 : orderProduct.ProductID,
                    Qty = request.Piece,
                    CarId = WebSiteInfo.User.Value.WarehouseCar.ID,
                    SetNo = orderProduct.SetNo,
                }));
            }

            _logger.LogTrace(JsonSerializerWrapper.Serialize(new ProductPickingLogDto
            {
                PersonID = WebSiteInfo.User.Value.ID,
                PersonName = WebSiteInfo.User.Value.Name,
                OrderID = request.OrderID,
                WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                ProductID = orderProduct.ProductType == ProductType.Product ? request.ProductID : 0,
                ShelfID = request.ShelfID,
                WarehouseName = WebSiteInfo.User.Value.Warehouse,
                Piece = request.Piece,
                isMissingProduct = request.isMissingProduct,
                BEVersion = request.ApiVersion,
                UIVersion = request.UIVersion
            }));

            string logMessage = "";
            if (request.isMissingProduct)
            {
                if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.RaftaBulunamayanUrununKontrolu)
                {
                    var existing = await _shelfCountingFilesService.Get("urun_otomatik_sayim", null, request.ShelfID.ToString(), ShelfCountingItemStatus.Created, 0, 1, 1, cancellationToken);
                    if (existing == null || (existing.Contents == null || !existing.Contents.Any()))
                        await _shelfCountingFilesService.Create(new ShelfCountingFile.Models.Request.CreateShelfCountingFileRequest
                        {
                            ShelfIds = new List<int> { request.ShelfID },
                            Name = "urun_otomatik_sayim"
                        }, cancellationToken);
                }

                if (!WebSiteInfo.User.Value.Settings.ArabaModulAktif)
                    await OrderAddMissingProduct(new OrderAddMissingProductDto(request.OrderID, request.OrderProductID, request.Piece, request.MissingProductReturnCauseID), cancellationToken);

                _logger.LogTrace(JsonSerializerWrapper.Serialize(
                    new TiciWmsLogDto
                    {
                        Date = DateTime.Now,
                        Details = $"{request.OrderID} ID'li siparişte {request.ProductID} ID'li üründen {request.Piece} adet Eksik Ürün olarak işaretlendi.",
                        LogType = "URUNBULUNDUOTOMATİKSİPARİS",
                        PersonID = WebSiteInfo.User.Value.ID,
                        Process = "DEPOURUNTOPLAMA",
                    }));
                string orderMovementMessage = $"{orderProduct.Barcode ?? ""} barkodlu ürünü bulamadım.";
                await _orderMovementService.AddAsync(
                new OrderMovementAddDto
                {
                    AgentID = WebSiteInfo.User.Value.ID,
                    isSystem = true,
                    Message = orderMovementMessage,
                    Name = WebSiteInfo.User.Value.Name,
                    OrderID = request.OrderID,
                }, cancellationToken);
            }
            else
            {
                logMessage = $"{request.OrderID} ID'li siparişte {request.ProductID} ID'li üründen {request.Piece} adet {request.ShelfID} ID'li rafta bulundu.";

                if (shelf != null)
                {
                    logMessage += $"Rafın sıra bilgisi {shelf.Rank}'dir. ";
                    logMessage += shelf.IsMissingProductShelf ? "Eksik Ürün rafıdır. " : "";
                }

                _logger.LogTrace(JsonSerializerWrapper.Serialize(
                    new TiciWmsLogDto
                    {
                        Date = DateTime.Now,
                        Details = logMessage,
                        LogType = "URUNBULUNDUOTOMATİKSİPARİS",
                        PersonID = WebSiteInfo.User.Value.ID,
                        Process = "DEPOURUNTOPLAMA",
                    }));

                string orderMovementMessage = shelf != null
                            ? $"{orderProduct.Barcode ?? ""} barkodlu üründen, {shelf.Definition} isimli raftan {request.Piece} adet buldum."
                            : $"{orderProduct.Barcode} barkodlu üründen {request.Piece} adet buldum.";

                await _orderMovementService.AddAsync(
                    new OrderMovementAddDto
                    {
                        AgentID = WebSiteInfo.User.Value.ID,
                        isSystem = true,
                        Message = orderMovementMessage,
                        Name = WebSiteInfo.User.Value.Name,
                        OrderID = request.OrderID,
                    }, cancellationToken);
            }

            await _ticimaxWarehouseService.TokenInitialize();
            WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvent.Picked,
                new PickedEventPayload(request.OrderID, request.OrderProductID, request.ShelfID, shelf?.Definition, request.Piece,
                orderProduct.ProductID, orderProduct.ProductName, orderProduct.StockCode, orderProduct.Barcode, request.isMissingProduct, DateTime.Now.ToTimestamp(), orderProduct.OrderDate.ToTimestamp())));

            return response;
        }

        public async Task<DataResult<List<OrderProduct>>> GetProductListOnType(int combineProductType, CancellationToken cancellationToken)
        {
            DataResult<List<OrderProduct>> response = new DataResult<List<OrderProduct>>();

            if (!string.IsNullOrEmpty(WebSiteInfo.User.Value.SetNo))
            {
                var set = await GetProducts(new OrderCollectionSetFilter { SetNo = WebSiteInfo.User?.Value?.SetNo, isGrouping = true }, combineProductType);
                if (set.Products == null)
                    throw new NotFoundException("SeteAitVerilerBulunamadi");

                switch (combineProductType)
                {
                    case 1:
                        response.Model = set?.Products.Where(x => x.MissingPiece > 0).ToList();
                        response.Model.ForEach(x => x.Piece = x.MissingPiece);
                        break;

                    case 2:
                        response.Model = set?.Products?.Where(x => x.OccurrencesPiece > 0).ToList();
                        response.Model?.ForEach(x => x.Piece = x.OccurrencesPiece);

                        var orderProductGrupped = response.Model.GroupBy(x => x.OrderID)
                            .Select(g => new { OrderId = g.Key, TotalOccurrencesPiece = g.Sum(y => y.OccurrencesPiece) });

                        foreach (var orderProduct in response.Model)
                        {
                            var totalOccurrencesPiece = orderProductGrupped
                                .Where(x => x.OrderId == orderProduct.OrderID).Select(x => x.TotalOccurrencesPiece).FirstOrDefault();
                            if (totalOccurrencesPiece == 1)
                                orderProduct.CanBeMarkedMissingOrderCombines = true;
                        }
                        break;

                    case 3:
                        response.Model = set?.Products.Where(x => x.Piece > x.MissingPiece + x.OccurrencesPiece)
                            .ToList();

                        response.Model.ForEach(x => x.Piece = (x.Piece - (x.MissingPiece + x.OccurrencesPiece)));
                        break;

                    case 4:
                        var setProductList4 = set?.Products.ToList();
                        var distinctList4 = set?.Products.DistinctBy(x => x.ProductID).ToList();
                        foreach (var item in distinctList4)
                        {
                            item.Piece = setProductList4.Where(x => x.ProductID == item.ProductID)
                                .Sum(x => x.Piece);
                        }

                        response.Model = distinctList4;
                        break;

                    case 5:
                        var setProductList = set?.Products.Where(x => x.BoxID > 0 && x.OccurrencesPiece > 0 && x.PreparationStatus == 1 && x.BoxID > 0).ToList();
                        var distinctList = set?.Products.Where(x => x.BoxID > 0 && x.OccurrencesPiece > 0 && x.PreparationStatus == 1 && x.BoxID > 0).DistinctBy(x => x.ProductID).ToList();
                        foreach (var item in distinctList)
                        {
                            item.Piece = setProductList.Where(x => x.ProductID == item.ProductID).Sum(x => x.Piece);
                        }

                        response.Model = distinctList;
                        break;
                }
            }

            return response;
        }

        public async Task<DataResult<OrderCompleatedResponseDto>> OrderCompleted(OrderCompletedDto request, CancellationToken cancellationToken)
        {
            DataResult<OrderCompleatedResponseDto> response = new DataResult<OrderCompleatedResponseDto>();

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = request.OrderID, CargoType = WebSiteInfo.User.Value.CargoType }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
                throw new NotFoundException("ORDER_NOT_FOUND", new KeyValuePair<string, string>("orderId", request.OrderID.ToString()));

            var collection = await _orderCollectionService.GetCollectionSet(new GetOrderCollectionSetDto
            {
                Filter = new OrderCollectionSetFilter
                { OrderID = request.OrderID }
            }, cancellationToken);

            if (collection.Model.Products.Any(x => x.OccurrencesPiece == 0 && x.MissingPiece == 0))
                throw new BusinessException("");

            await _orderService.OrderCompleted(request.OrderID, WebSiteInfo.User.Value.CargoType, WebSiteInfo.User.Value.Settings.UrunToplamaAyar.FazlaOkutma, cancellationToken);

            if (WebSiteInfo.User.Value.Settings.ArabaModulAktif && WebSiteInfo.User.Value.WarehouseCar != null)
            {
                await _warehouseCarProductService.DeleteCarAllProduct(new WarehouseCarProductDeleteCarAllProductDto
                {
                    OrderID = request.OrderID,
                    WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID,
                }, cancellationToken);
            }

            _logger.LogTrace(JsonSerializerWrapper.Serialize(
                new TiciWmsLogDto
                {
                    Date = DateTime.Now,
                    Details = request.OrderID + "numaralı sipariş tamamladı.",
                    LogType = "SIPARISTAMAMLA",
                    PersonID = WebSiteInfo.User.Value.ID,
                    Process = "DEPOURUNTOPLAMA",
                }));

            await _orderMovementService.AddAsync(
                new OrderMovementAddDto
                {
                    AgentID = WebSiteInfo.User.Value.ID,
                    isSystem = true,
                    Message = "Sipariş Tamamlandı.",
                    Name = WebSiteInfo.User.Value.Name,
                    OrderID = order.ID,
                }, cancellationToken);

            var resp = await OrderPrint(new OrderPrintRequestDto { OrderID = order.ID }, cancellationToken);
            response.IsError = resp.IsError;
            response.ErrorMessage = resp.ErrorMessage;
            response.Model.OrderHtml = resp.Model.OrderHtml;
            response.Model.OrderPDF = resp.Model.OrderPDF;

            var base64 = await _imageService.CreateQr(order.ID.ToString());
            response.Model.Base64QrCode = base64.Image;

            return response;
        }

        public async Task<ErrorResponse> OrderAddProduct(OrderAddProductDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            if (!new List<int> { 1, 2 }.Contains(request.ReasonID.ToInt32()))
                throw new BusinessException("SipariseUrunEklemeNedeniBilgisiHatali");

            if (request.ReasonID == OrderAddProductReason.ProductAlternative && request.Products.Any(x => !x.OrderProductID.HasValue || x.OrderProductID == 0 || !x.OrderProductPiece.HasValue || x.OrderProductPiece == 0d))
                throw new BusinessException("AlternatifUrunBilgileriHatali");

            await OrderOverPaymentCheck(request.OrderID, request.Products.Select(x => new OrderOverPaymentCheckModel
            { ProductId = x.ProductID, ProductCount = x.ProductPiece }).ToList(), cancellationToken);

            var originOrderProducts = (await _orderProductService.GetProductList(new OrderProductFilter { OrderID = request.OrderID }, cancellationToken)).Model;
            foreach (var item in request.Products)
            {
                if (request.ReasonID == OrderAddProductReason.ProductAlternative)
                {
                    if (originOrderProducts.Any(x => x.OrderProductID == item.OrderProductID && x.Piece == x.OccurrencesPiece + x.MissingPiece))
                        throw new BusinessException("SiparisUrunToplanmisIslemiGerceklestiremezsiniz");

                    if (originOrderProducts.Any(x => x.OrderProductID == item.OrderProductID && item.ProductPiece > x.Piece - (x.OccurrencesPiece + x.MissingPiece)))
                        throw new BusinessException("SiparisUrunToplanmisAdetIcinIslemiGerceklestiremezsiniz");

                    if (originOrderProducts.Any(x => x.OrderProductID == item.OrderProductID && x.ProductID == item.ProductID))
                        throw new BusinessException("AyniUrunuAlternatifOlarakEkleyemezsiniz");

                    var orderProduct = originOrderProducts.FirstOrDefault(x => x.OrderProductID == item.OrderProductID);
                    if (orderProduct == null)
                        throw new NotFoundException("SiparisUrunBulunamadi");

                    if (item.OrderProductPiece > orderProduct.Piece)
                        throw new BusinessException("SiparisUrunAdediHatali");

                    var orderProductStatus = (await _orderProductStatusService.GetList(new OrderProductStatusServiceGetListDto { Operation = 2, Active = 1 }, cancellationToken)).Model.FirstOrDefault();
                    if (orderProductStatus == null)
                        throw new NotFoundException("SiparisUrunDurumlariBulunamadi");

                    var changeStatus = await _ticimaxWarehouseService.SetOrderProductStatus(
                        new SetOrderProductStatusRequest
                        {
                            OrderId = request.OrderID,
                            Products = new List<OrderProductStatus>
                            {
                                new OrderProductStatus
                                {
                                    OrderLineId = orderProduct.OrderProductID,
                                    Quantity = item.OrderProductPiece.Value,
                                    StatusId = orderProductStatus.ID,
                                    Process = orderProductStatus.Operation
                                }
                            }
                        }, cancellationToken);

                    if (changeStatus.IsError)
                        throw new BusinessException(changeStatus.ErrorMessage);

                    var orderCollectionProducts = (await _orderCollectionService.GetCollectionSet(new GetOrderCollectionSetDto
                    {
                        Filter = new OrderCollectionSetFilter
                        { OrderProductID = orderProduct.OrderProductID, FindStatus = false }
                    }, cancellationToken)).Model;

                    double piece = item.OrderProductPiece.Value;
                    foreach (var orderCollectionProduct in orderCollectionProducts.Products.Where(x => x.Piece >= x.MissingPiece + x.OccurrencesPiece).Take(Math.Floor(piece).ToInt32()).ToList())
                    {
                        await _orderCollectionService.DeleteByID(orderCollectionProduct.ID, cancellationToken);
                    }

                    if (piece % 1 > 0)
                    {
                        var decimalProduct = orderCollectionProducts.Products.FirstOrDefault(x => x.Piece % 1 > 0);
                        if (decimalProduct != null)
                            await _orderCollectionService.DeleteByID(decimalProduct.ID, cancellationToken);
                    }

                    //Setten silinen ürünü raflarına geri ekliyoruz.
                    if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.UrunToplamaYontemi != 5)
                    {
                        await _shelfProductService.Add(new ShelfProductAddDto
                        {
                            AddStockWebSite = WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.YerlestirmedeStokEkle,
                            ShelfItems = orderCollectionProducts.Products.Select(x => new ShelfProductAddItemDto
                            {
                                ProductID = x.ProductID,
                                ShelfID = x.ShelfID,
                                ShelfStock = x.Piece,
                                WarehouseID = x.WarehouseID
                            }).ToList()
                        }, cancellationToken);
                    }
                    await _transactionalCommandService.TransactionalExecuteAsync();
                }

                var addProductToOrderResult = await _ticimaxWarehouseService.AddProductToOrder(
                    new AddProductToOrderRequest
                    {
                        OrderId = request.OrderID,
                        ProductId = item.ProductID,
                        Quantity = item.ProductPiece
                    }, cancellationToken);

                if (addProductToOrderResult.IsError)
                    throw new BusinessException("ServiseErisilemiyor");

                if (addProductToOrderResult.OrderLineId > 0)
                {
                    var pickingProducts = new List<PickingProduct>();
                    //Eğer bu ürün daha önce sette varsa sipariş ürün id aynı olduğu için GetDistributionProductta gelmeyecek. Bu yüzden olan bir rafı seçip o rafa direk manuel toplama giriyoruz.
                    if (originOrderProducts.Any(x => x.ProductID == item.ProductID))
                    {
                        var product = originOrderProducts.First(x => x.ProductID == item.ProductID);
                        var shelfProduct = (await _shelfProductService.GetList(new ShelfProductGetListDto
                        { ProductID = item.ProductID }, null, cancellationToken)).Model.FirstOrDefault(x => x.ShelfStock >= item.ProductPiece);

                        var shelfId = 0;
                        var shelfName = "BULUNAMAYAN";
                        var shelfBarcode = "BULUNAMAYAN";
                        var shelfType = (int)ShelfType.NotFoundShelf;
                        if (shelfProduct != null)
                        {
                            shelfId = shelfProduct.ShelfID;
                            shelfName = shelfProduct.ShelfName;
                            shelfBarcode = shelfProduct.ShelfBarcode;
                            shelfType = (int)ShelfType.WarehouseShelf;
                        }

                        double piece = item.ProductPiece;
                        for (int i = 0; i < Math.Floor(piece); i++)
                        {
                            pickingProducts.Add(new PickingProduct
                            {
                                OrderID = product.OrderID,
                                Barcode = product.Barcode,
                                MissingPiece = 0,
                                OccurrencesPiece = 0,
                                OrderProductID = product.OrderProductID,
                                Piece = 1,
                                ProductCardID = product.ProductCardID,
                                ProductID = item.ProductID,
                                ProductType = (int)product.ProductType == 0 ? 1 : (int)product.ProductType,
                                ShelfID = shelfId,
                                ShelfName = shelfName,
                                ShelfBarcode = shelfBarcode,
                                ShelfType = shelfType,
                                SetNo = WebSiteInfo.User.Value.SetNo,
                                StoreID = WebSiteInfo.User.Value.StoreID,
                                WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                                PickerID = WebSiteInfo.User.Value.ID,
                            });
                        }

                        if (piece % 1 > 0)
                        {
                            pickingProducts.Add(new PickingProduct
                            {
                                OrderID = product.OrderID,
                                Barcode = product.Barcode,
                                MissingPiece = 0,
                                OccurrencesPiece = 0,
                                OrderProductID = product.OrderProductID,
                                Piece = Math.Round(piece % 1, 2),
                                ProductCardID = product.ProductCardID,
                                ProductID = item.ProductID,
                                ProductType = (int)product.ProductType == 0 ? 1 : (int)product.ProductType,
                                ShelfID = shelfId,
                                ShelfName = shelfName,
                                ShelfBarcode = shelfBarcode,
                                ShelfType = shelfType,
                                SetNo = WebSiteInfo.User.Value.SetNo,
                                StoreID = WebSiteInfo.User.Value.StoreID,
                                WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                                PickerID = WebSiteInfo.User.Value.ID,
                            });
                        }
                    }
                    else
                    {
                        //Eğer daha önce eklenmiş bir ürün değilse yani adet arttırılmamışsa tekrardan dağıtımdan bu ürünün arıyoruz ve bizim setimize ekliyoruz.
                        var products = (await GetDistributionProduct(new PickingProductGetDistiributionProductDto(
                            new PickingProductFilter
                            {
                                OrderID = request.OrderID,
                                ShelfStockDistibutionControl = false,
                                IgnoreTheRemainingAmount = (!WebSiteInfo.User.Value.Settings.ArabaModulAktif) &&
                                            (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.UrunToplamaYontemi == 5)
                            }), null, cancellationToken)).Model;

                        if (products.Any())
                        {
                            products.ForEach(x =>
                            {
                                x.SetNo = WebSiteInfo.User.Value.SetNo;
                                x.StoreID = WebSiteInfo.User.Value.StoreID;
                                x.WarehouseID = WebSiteInfo.User.Value.WarehouseID;
                                x.PickerID = WebSiteInfo.User.Value.ID;
                                x.ProductType = 1;
                            });
                        }

                        pickingProducts.AddRange(products);
                    }

                    await _pickingProductDal.DistributionProductAsync(pickingProducts, cancellationToken);
                }

                response.IsError = addProductToOrderResult.IsError;
                response.ErrorMessage = addProductToOrderResult.ErrorMessage;

                if (!response.IsError)
                {
                    _logger.LogTrace(JsonSerializerWrapper.Serialize(
                        new TiciWmsLogDto
                        {
                            Date = DateTime.Now,
                            Details = $"{request.OrderID} ID'li siparişe {item.ProductID} ID'li üründen {item.ProductPiece} adet eklendi",
                            LogType = "URUNEKLE",
                            PersonID = WebSiteInfo.User.Value.ID,
                            Process = "DEPOURUNTOPLAMA",
                        }));

                    await _orderMovementService.AddAsync(
                        new OrderMovementAddDto
                        {
                            AgentID = WebSiteInfo.User.Value.ID,
                            isSystem = true,
                            Message = $"Siparişe {item.ProductID} ID'li üründen {item.ProductPiece} adet eklendim.",
                            Name = WebSiteInfo.User.Value.Name,
                            OrderID = request.OrderID,
                        }, cancellationToken);

                    string logMessage = string.Empty;
                    switch (request.ReasonID)
                    {
                        case OrderAddProductReason.CustomerSue:
                            logMessage = $"Müşteri Talebi ile {item.ProductID} ID'li ürün siparişe eklenmiştir";
                            break;
                        case OrderAddProductReason.ProductAlternative:
                            logMessage = $"Ürün Alternatifi ile {originOrderProducts.FirstOrDefault(x => x.OrderProductID == item.OrderProductID)?.ProductID} ID'li sipariş ürününün alternatifi olarak {item.ProductID} ID'li ürün siparişe eklenmiştir";
                            break;
                    }

                    _logger.LogTrace(JsonSerializerWrapper.Serialize(new OrderAddProductLogDto
                    {
                        UserID = WebSiteInfo.User.Value.ID,
                        OrderID = request.OrderID,
                        ReasonID = request.ReasonID,
                        Message = logMessage,
                    }));
                }
            }

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvents.PickedDifferent,
                new PickedDifferentProductEventPayload(request.OrderID, (int)request.ReasonID, request.Products)));

            return response;
        }

        private async Task OrderAddMissingProduct(OrderAddMissingProductDto request, CancellationToken cancellationToken)
        {
            var productStatus = (await _orderProductStatusService.GetList(new OrderProductStatusServiceGetListDto { Active = 1, Operation = 2 }, cancellationToken)).Model.FirstOrDefault();
            var missingProductResponse = await _ticimaxWarehouseService.SetOrderProductStatus(
                new SetOrderProductStatusRequest
                {
                    OrderId = request.OrderID,
                    Products = new List<OrderProductStatus>
                    {
                        new OrderProductStatus
                        {
                            OrderLineId = request.OrderProductID,
                            Process = productStatus.Operation,
                            ReturnCauseId = request.ReturnCauseID,
                            Quantity = request.MissingPiece,
                            StatusId = productStatus.ID
                        }
                    }
                }, cancellationToken);

            if (!missingProductResponse.IsError)
            {
                await _orderMovementService.AddAsync(
                    new OrderMovementAddDto
                    {
                        AgentID = WebSiteInfo.User.Value.ID,
                        isSystem = true,
                        Message = $"Siparişe {request.OrderProductID} ID'li sipariş ürününü {request.MissingPiece} eksik adet olarak işaretledim.",
                        Name = WebSiteInfo.User.Value.Name,
                        OrderID = request.OrderID,
                    }, cancellationToken);

                await _orderService.SetPackagingStatus(
                    new OrderSetPackagingStatusDto
                    {
                        IDs = new List<int> { request.OrderID },
                        Status = Core.Order.Entities.Enums.PackageStatus.EksikUrun,
                    }, cancellationToken);
            }
        }

        public async Task<DataResult<MissingProductCombineResponseDto>> MissingProductCombine(MissingProductCombineRequestDto request, CancellationToken cancellationToken)
        {
            DataResult<MissingProductCombineResponseDto> response = new DataResult<MissingProductCombineResponseDto>();

            var table = (await _warehouseTableService.GetListAsync(new WarehouseTableGetListDto { Barcode = request.TableBarcode }, cancellationToken)).Model.FirstOrDefault();
            if (table == null)
                throw new NotFoundException("MasaBulunamadi");

            List<StoreAgent> userAgents = (await _storeAgentService.GetList(new StoreAgentGetListDto
            {
                StoreID = WebSiteInfo.User.Value.StoreID,
                Active = 1,
                WarehouseID = WebSiteInfo.User.Value.WarehouseID
            }, null, cancellationToken)).Model;

            if (userAgents.Count > 0)
            {
                var userTableControl = userAgents.FirstOrDefault(x => x.ID == WebSiteInfo.User.Value.ID && x.TableID > 0 && x.TableID != table.ID);
                if (userTableControl != null)
                    throw new BusinessException("KullanicininZatenMasasiVar");

                var currentTableUserControl = userAgents.FirstOrDefault(x => x.ID == WebSiteInfo.User.Value.ID && x.TableID == table.ID);
                if (currentTableUserControl == null)
                {
                    if (table.UserID != 0)
                        throw new BusinessException("MasaKullanimdaOldugandanAlinamaz");
                }

                await _storeAgentService.UpdateTableID(new StoreAgentUpdateTableIDDto { PersonID = WebSiteInfo.User.Value.ID, TableID = table.ID }, cancellationToken);
                WebSiteInfo.User.Value.Table = table.Definition;
                WebSiteInfo.User.Value.TableID = table.ID;
            }

            table.Parcels = (await _warehouseParcelService.GetList(new WarehouseParcelGetListDto { TableID = table.ID, FillParcel = false }, null, cancellationToken)).Model;

            var set = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        TableID = table.ID,
                        isGrouping = false,
                        BoxAssign = true
                    }
                }, cancellationToken)).Model;

            table.SetNo = set.SetNo;

            foreach (var parcel in table.Parcels)
            {
                var parcelAssignProduct = set.Products.Where(x => x.BoxID == parcel.ID).ToList();
                if (parcelAssignProduct.Count > 0)
                {
                    var orderGroups = parcelAssignProduct.GroupBy(x => new { x.OrderID });
                    foreach (var orderGroup in orderGroups)
                    {
                        if (request.isMissingProductParcel)
                        {
                            var orderProducts = orderGroup.ToList();
                            parcel.TotalNumberOfProducts += orderProducts.Sum(x => x.Piece);
                            parcel.TotalNumberOfPreparedProducts += orderProducts.Where(x => x.PreparationStatus == 1).Sum(x => x.Piece);
                            parcel.TotalNumberOfNotOnShelf += orderProducts.Where(x => x.PreparationStatus == 5).Sum(x => x.Piece);
                            parcel.TotalNumberOfMissingProducts += orderProducts.Where(x => x.MissingPiece > 0).Sum(x => x.MissingPiece);
                            parcel.isCancelOrder = orderProducts.Any(x => x.PreparationStatus == 4);

                            var missingProductControl = orderProducts.FirstOrDefault(x => x.MissingPiece > 0 && (x.PreparationStatus == 0 || x.PreparationStatus == 4)); // sadece eksik ürün bulunan kolileri göstermek için...
                            if (missingProductControl != null)
                            {
                                parcel.OrderIDList.Add(orderGroup.Key.OrderID);
                                parcel.OrdersType.Add(new OrdersType { OrderID = orderGroup.Key.OrderID });
                            }
                            else
                                parcel.OrderIDList.Remove(orderGroup.Key.OrderID);
                        }
                        else
                        {
                            var orderProducts = orderGroup.ToList();
                            parcel.TotalNumberOfProducts += orderProducts.Sum(x => x.Piece);
                            parcel.TotalNumberOfPreparedProducts += orderProducts.Where(x => x.PreparationStatus == 1).Sum(x => x.Piece);
                            parcel.TotalNumberOfNotOnShelf += orderProducts.Where(x => x.PreparationStatus == 5).Sum(x => x.Piece);
                            parcel.isCancelOrder = orderProducts.Any(x => x.PreparationStatus == 4);

                            parcel.OrderIDList.Add(orderGroup.Key.OrderID);
                            parcel.OrdersType.Add(new OrdersType { OrderID = orderGroup.Key.OrderID });
                        }
                    }
                }
            }

            table.Parcels = table.Parcels.Where(x => x.OrderIDList.Count > 0).ToList();

            foreach (var parcel in table.Parcels)
            {
                parcel.OrderIDList = parcel.OrderIDList.Distinct().ToList();
            }

            response.Model.Table = table;


            return response;
        }

        public async Task<DataResult<GetMissingOrderProductResponseDto>> GetMissingOrderProduct(GetMissingOrderProductRequestDto request, CancellationToken cancellationToken)
        {
            DataResult<GetMissingOrderProductResponseDto> response = new DataResult<GetMissingOrderProductResponseDto>();

            OrderCollectionSet set = new OrderCollectionSet();
            set.Products = new List<OrderProduct>();

            var setControl = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        TableID = request.TableID,
                        BoxID = request.ParcelID,
                        OrderIDList = request.OrderIDList,
                        isGrouping = false
                    }
                }, cancellationToken)).Model;

            if (setControl != null && setControl.Products.Count > 0)
            {
                var ordersGroup = setControl.Products.GroupBy(x => x.OrderID);

                foreach (var item in ordersGroup)
                {
                    set.Products.AddRange((await _orderCollectionService.GetCollectionSet(
                        new GetOrderCollectionSetDto
                        {
                            Filter = new OrderCollectionSetFilter
                            {
                                OrderID = item.Key,
                                isGrouping = false,
                                PreparedStatusList = new List<int> { 0, 1, 4, 5, 6 },
                                TableID = request.TableID,
                                BoxID = request.ParcelID,
                            }
                        }, cancellationToken)).Model.Products);
                }
            }

            if (set.Products.Count > 0)
            {
                response.Model.Products = set.Products;
                var orders = set.Products.GroupBy(x => new { x.OrderID });
                foreach (var order in orders)
                {
                    response.Model.CustomerServices.Add(
                        new OrderIsCustomerServicesDto
                        {
                            OrderID = order.Key.OrderID,
                            IsCustomerServices = (await _customerServicesManager.GetCount(
                                new CustomerServiceGetListDto
                                {
                                    OrderID = order.Key.OrderID,
                                    StatusID = CustomerServiceStatus.Bekliyor,
                                    Type = CustomerServiceType.EksikUrunBekleyenSiparisler
                                }, cancellationToken)).Model > 0,
                            IsCancelOrder = order.Any(x => x.PreparationStatus == 4)
                        });
                }
            }
            else
                throw new NotFoundException("SiparisBulunamadi");

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvents.MissingFound,
                new PickingProductMissingFoundEventPayload(request.TableID, request.ParcelID, request.OrderIDList, response.Model.Products, response.Model.CustomerServices)));

            return response;
        }

        public async Task<ErrorResponse> MissingProductDelete(MissingProductDeleteRequestDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();
            List<OrderProduct> setProducts = null;

            var orderType = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        ID = request.ID,
                        isGrouping = false
                    }
                }, cancellationToken)).Model.Products.FirstOrDefault();

            if (orderType != null && orderType.PreparationStatus != 3)
            {
                setProducts = (await _orderCollectionService.GetCollectionSet(
                    new GetOrderCollectionSetDto
                    {
                        Filter = new OrderCollectionSetFilter
                        {
                            OrderID = request.OrderID,
                            isGrouping = false
                        }
                    }, cancellationToken)).Model.Products;

                var returnProductCause = (await _orderProductStatusService.GetList(new OrderProductStatusServiceGetListDto { Operation = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.EksikUrunIadeNedenId, Active = 1 }, cancellationToken)).Model.FirstOrDefault();

                if (returnProductCause != null)
                {
                    await _ticimaxWarehouseService.SetOrderProductStatus(
                        new SetOrderProductStatusRequest
                        {
                            OrderId = request.OrderID,
                            Products = new List<OrderProductStatus>
                            {
                                new OrderProductStatus
                                {
                                    OrderLineId = request.OrderProductID,
                                    Process = returnProductCause.Operation,
                                    Quantity = 1,
                                    StatusId = returnProductCause.ID,
                                    ReturnCauseId = returnProductCause.ID
                                }
                            }
                        }, cancellationToken);

                    await _orderCollectionService.SetPreparingStatusCollectionProduct(
                        new SetPreparingStatusCollectionProductDto
                        {
                            ID = request.ID,
                            PreparingStatus = 7,
                        }, cancellationToken);

                    if (WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.RaftaUrunBulunamadigindaSiteStokGuncelle)
                    {
                        var storeStockCount = (await _productService.StoreStockGetCount(new StoreStockFilter { StoreID = WebSiteInfo.User.Value.WarehouseID, ProductID = orderType.ProductID }, cancellationToken)).Model;
                        var reduceStockResult = await _productService.ReduceStock(new ProductUpdateStockDto
                        {
                            ProductID = orderType.ProductID,
                            Piece = 1,
                            StoreID = storeStockCount > 0 ? WebSiteInfo.User.Value.WarehouseID : default(int),
                        }, cancellationToken);

                        if (reduceStockResult.SendStockSafetyEmail)
                        {
                            List<string> tos = new List<string>();
                            if (WebSiteInfo.User.Value.Settings.GuvenliStokMail.Contains(','))
                                tos.AddRange(WebSiteInfo.User.Value.Settings.GuvenliStokMail.Split(',').ToList());
                            else
                                tos.Add(WebSiteInfo.User.Value.Settings.GuvenliStokMail);

                            await _warehouseEmailService.SendSafetyStockMail(tos, reduceStockResult.Product.ID, reduceStockResult.Product.Barcode, reduceStockResult.Product.ProductName, reduceStockResult.Product.StockPiece, cancellationToken);
                        }
                    }

                    _logger.LogTrace(JsonSerializerWrapper.Serialize(new TiciWmsLogDto
                    {
                        Date = DateTime.Now,
                        Details = $"{request.OrderID} ID'li siparişten {request.ProductID} ID'li üründen 1 adet çıkardı",
                        LogType = "DEPOURUNTOPLAMA",
                        PersonID = WebSiteInfo.User.Value.ID,
                        Process = "EKSIKURUN",
                    }));

                    await _orderMovementService.AddAsync(new OrderMovementAddDto
                    {
                        AgentID = WebSiteInfo.User.Value.ID,
                        isSystem = true,
                        Name = WebSiteInfo.User.Value.Name,
                        OrderID = request.OrderID,
                        Message = $"Siparişten {request.ProductID} ID'li üründen 1 adet çıkardım",
                    }, cancellationToken);

                    setProducts = setProducts.Where(x => x.ID != request.ID).ToList();
                    double totalProductCount = setProducts.Sum(x => x.Piece);
                    double preparedProductCount = setProducts.Where(x => x.PreparationStatus == 1).Sum(x => x.Piece);
                    if (totalProductCount == preparedProductCount)
                    {
                        await _orderService.SetPackagingStatus(new OrderSetPackagingStatusDto
                        {
                            IDs = new List<int> { request.OrderID },
                            Status = PackageStatus.FaturaBekliyor,
                        }, cancellationToken);
                    }
                }
                else
                    throw new NotFoundException("EksikUrunIadeNedeniBilgisineUlasilamadi");
            }
            else
                throw new BusinessException("UrununDurumuSilmekIcinUygunDegil");

            return response;
        }

        [Obsolete]
        public async Task<DataResult<OrderPrintResponseDto>> OrderPrint(OrderPrintRequestDto request, CancellationToken cancellationToken)
        {
            DataResult<OrderPrintResponseDto> response = new DataResult<OrderPrintResponseDto>();

            var printResponse = await _printService.PrintOrder(request.OrderID, cancellationToken);
            if (!string.IsNullOrWhiteSpace(printResponse.OrderHtml) || printResponse.OrderPdf != null)
            {
                response.Model.OrderPDF = printResponse.OrderPdf;
                response.Model.OrderHtml = printResponse.OrderHtml;
                return response;
            }

            return response;
        }

        public async Task<DataResult<OrderInvoicePrintResponseDto>> OrderInvoicePrint(OrderInvoicePrintRequestDto request, CancellationToken cancellationToken)
        {
            DataResult<OrderInvoicePrintResponseDto> response = new DataResult<OrderInvoicePrintResponseDto>();

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = request.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
            if (order != null)
            {
                order.Payments = (await _orderPaymentService.GetList(new OrderPaymentGetListDto { OrderID = order.ID }, cancellationToken)).Model;
                order.Products = (await _orderProductService.GetProductList(new OrderProductFilter { OrderID = order.ID }, cancellationToken)).Model;
                var invoiceResponse = await _invoiceService.CreateInvoice(new CreateInvoiceRequestDto { Order = order }, cancellationToken);
                if (!invoiceResponse.IsError && invoiceResponse.Model != null)
                    response.Model.OrderInvoiceHtml = invoiceResponse.Model.InvoiceHtml;
                else
                    throw new NotFoundException("FaturaBulunamadi");
            }
            else
                throw new NotFoundException("SiparisBulunamadi");

            return response;
        }

        public async Task<DataResult<OrderProductPrintResponseDto>> OrderProductPrint(OrderProductPrintRequestDto request, CancellationToken cancellationToken)
        {
            DataResult<OrderProductPrintResponseDto> response = new DataResult<OrderProductPrintResponseDto>();

            var products = (await _orderProductService.GetProductList(new OrderProductFilter { OrderID = request.OrderID }, cancellationToken)).Model;
            string html = "<html><head><meta charset='UTF-8'></head><body>";
            if (products.Count > 0)
            {
                html += "<table>";
                foreach (var product in products)
                {
                    if (product.Piece != product.OccurrencesPiece)
                    {
                        html += "<tr style='font-size:17px;'><td><b>Stok Kodu :</b> " + product.StockCode +
                                "</td><td><b>Ürün Adı :</b> " + product.ProductName + " " + product.AdditionalOptions +
                                "</td><td><b>Raf Adı :</b> " + product.ShelfName + "</td></tr>";
                    }
                }

                html += "</table>";
                html += "</body></html>";

                response.Model.OrderProductHtml = html;
            }

            return response;
        }

        public async Task<ErrorResponse> OrderReset(OrderResetRequestDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = request.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
                throw new BusinessException("SiparisDurumuUygunDegil");

            await _orderService.UpdateOrderPreparedID(
                new UpdateOrderPreparedIDDto
                {
                    OrderIDs = new List<int> { request.OrderID },
                    PreparedID = 0,
                }, cancellationToken);


            return response;
        }

        public async Task<ErrorResponse> OrderCancel(OrderCancelRequestDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = request.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
            if (order != null)
            {
                if (order.Status >= (int)OrderStatus.KargoyaVerildi)
                    throw new BusinessException("SiparisDurumuUygunDegil");

                order.Products = (await _orderProductService.GetProductList(new OrderProductFilter { OrderID = order.ID }, cancellationToken)).Model;
                var user = (await _storeAgentService.GetList(new StoreAgentGetListDto { ID = WebSiteInfo.User.Value.ID }, null, cancellationToken)).Model.FirstOrDefault();
                if (user != null && user.OrderCancelLimit > 0)
                {
                    var userLimit = await _orderCancelFromStoreAgentLogService.GetCount(new OrderCancelFromStoreAgentFilterDto { UserID = user.ID, FilterDate = DateTime.Now }, null, cancellationToken);
                    if (!userLimit.IsError && userLimit.Model.ToInt32() >= user.OrderCancelLimit)
                        throw new BusinessException("GunlukKullanimLimitinizDolmustur");
                }

                await _orderService.SetOrderStatus(new OrderSetStatusDto(request.OrderID, (int)OrderStatus.Iptal, false), cancellationToken);

                await _orderCollectionService.DeleteByOrderID(request.OrderID, cancellationToken);

                if (request.ReasonID.HasValue)
                {
                    var orderProductCancelStatus = (await _orderProductStatusService.GetList(new OrderProductStatusServiceGetListDto { Active = 1, Operation = 2 }, cancellationToken)).Model.FirstOrDefault();
                    if (order.Products != null && order.Products.Count > 0)
                    {
                        List<OrderProductStatus> serviceRequest = order.Products.Select(x => new OrderProductStatus
                        {
                            OrderLineId = x.OrderProductID,
                            Process = orderProductCancelStatus.Operation,
                            Quantity = x.Piece,
                            ReturnCauseId = request.ReasonID.Value,
                            StatusId = orderProductCancelStatus.ID
                        }).ToList();

                        var serviceResponse = await _ticimaxWarehouseService.SetOrderProductStatus(
                            new SetOrderProductStatusRequest
                            {
                                OrderId = request.OrderID,
                                Products = serviceRequest
                            }, cancellationToken);

                        if (serviceResponse.IsError)
                            return new ErrorResponse { IsError = true, ErrorMessage = serviceResponse.ErrorMessage };
                    }
                }


                await _orderService.UpdateOrderPreparedID(
                    new UpdateOrderPreparedIDDto
                    {
                        OrderIDs = new List<int> { request.OrderID },
                        PreparedID = 0,
                    }, cancellationToken);

                _logger.LogTrace(JsonSerializerWrapper.Serialize(
                    new TiciWmsLogDto
                    {
                        Date = DateTime.Now,
                        Details = $"{request.OrderID} ID'li siparişi iptal etti",
                        LogType = "DEPOURUNTOPLAMA",
                        Process = "SIPARISIPTAL",
                    }));

                await _orderMovementService.AddAsync(
                    new OrderMovementAddDto
                    {
                        AgentID = WebSiteInfo.User.Value.ID,
                        isSystem = true,
                        Message = "Siparişi iptal ettim.",
                        Name = WebSiteInfo.User.Value.Name,
                        OrderID = request.OrderID,
                    }, cancellationToken);

                // TODO : Kullanıcı sipariş iptal (redise vs yazılıp log tablosu kaldırılacak)
                await _logService.AddStoreAgentCancelOrderLogAsync(new StoreAgentCancelOrderLog
                {
                    Date = DateTime.Now,
                    UserID = WebSiteInfo.User.Value.ID,
                    UserName = WebSiteInfo.User.Value.Username,
                    OrderID = request.OrderID,
                    ReasonID = request.ReasonID ?? 0
                });
            }
            else
                throw new NotFoundException("SiparisBulunamadi");

            return response;
        }

        public async Task<ErrorResponse> ParcelOrderReduce(ParcelOrderReduceRequestDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();
            OrderCollectionSet collection = null;
            List<ParcelOrderReduceOrders> orders = new List<ParcelOrderReduceOrders>();
            WarehouseParcel? parcel = null;
            WarehouseTable? table = null;

            if (request.ParcelID > 0)
            {
                //if (!WebSiteInfo.User.Value.isTicimaxUser)
                //    throw new BusinessException("KullaniciTipiBuIslemiYapmayaUygunDegildir");

                collection = (await _orderCollectionService.GetCollectionSet(
                    new GetOrderCollectionSetDto
                    {
                        Filter = new OrderCollectionSetFilter
                        {
                            BoxID = request.ParcelID,
                            BoxAssign = true
                        }
                    }, cancellationToken)).Model;

                if (collection != null && collection.Products != null && collection.Products.Count > 0)
                {
                    var collectionOrderIDAndOrderType = collection.Products.GroupBy(x => new { x.OrderID });
                    foreach (var item in collectionOrderIDAndOrderType)
                    {
                        orders.Add(new ParcelOrderReduceOrders { OrderID = item.Key.OrderID });
                    }
                }

                parcel = (await _warehouseParcelService.GetList(new WarehouseParcelGetListDto { ID = request.ParcelID }, null, cancellationToken)).Model.FirstOrDefault();
                if (parcel != null)
                {
                    table = (await _warehouseTableService.GetListAsync(new WarehouseTableGetListDto { ID = parcel.TableID }, cancellationToken)).Model.FirstOrDefault();
                    if (table == null)
                        throw new NotFoundException("KoliyeAitMasaBulunamadi");
                }
                else
                    throw new NotFoundException("KoliBulunamadi");

                WebSiteInfo.User.Value.Events.Add(new DomainEvent(TableMovementsEvent.Completed,
                                            new TableMovementsCompletedEvent(table.ID, table.Definition,
                                    TableMovementsProcessEnum.ParcelClear.ToString(), $"{parcel.Definition} isimli koli temizlendi.")));
            }
            else
            {
                collection = (await _orderCollectionService.GetCollectionSet(new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        OrderIDList = request.OrderID
                    }
                }, cancellationToken)).Model;

                if (collection != null && collection.Products != null && collection.Products.Count > 0)
                {
                    var collectionOrderIDAndOrderType = collection.Products.GroupBy(x => new { x.OrderID });
                    foreach (var item in collectionOrderIDAndOrderType)
                    {
                        orders.Add(new ParcelOrderReduceOrders { OrderID = item.Key.OrderID });
                    }
                }
            }

            if (collection != null && collection.Products != null && collection.Products.Count > 0)
            {
                foreach (var item in orders)
                {
                    OrderProduct? setOrder = collection.Products.FirstOrDefault(x => x.OrderID == item.OrderID);

                    var order = (await _orderService.GetList(new OrderGetListDto { OrderID = item.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
                    if (order == null)
                        throw new NotFoundException("SiparisBulunamadi");

                    await _customerServicesManager.Add(new CustomerServiceAddDto
                    {
                        CallByMemberName = order.Customer,
                        CallByMemberID = order.MemberID,
                        CallByPhoneNumber = order.CustomerTelephone,
                        StatusID = (int)CustomerServiceStatus.Bekliyor,
                        PaymentType = order.PaymentTypeID,
                        OrderID = order.ID,
                        OrderNo = order.OrderNo,
                        OrderDeliveryPhone = order.CustomerTelephone,
                        Type = (int)CustomerServiceType.EksikUrunBekleyenSiparisler,
                    }, cancellationToken);

                    await _orderMovementService.AddAsync(new OrderMovementAddDto
                    {
                        OrderID = item.OrderID,
                        AgentID = WebSiteInfo.User.Value.ID,
                        isSystem = true,
                        Name = WebSiteInfo.User.Value.Name,
                        Message = "Sipariş eksik olduğu için koliden çıkarıldı. Müşteri hizmetlerine otomatik yönlendirildi.",
                    }, cancellationToken);

                    var car = (await _warehouseCarService.GetList(new WarehouseCarGetListDto { SetNo = setOrder.SetNo }, null, cancellationToken)).Model.FirstOrDefault();
                    if (car != null)
                        await _warehouseCarProductService.DeleteCarAllProduct(new WarehouseCarProductDeleteCarAllProductDto { OrderID = item.OrderID, WarehouseCarID = car.ID }, cancellationToken);

                    _logger.LogTrace(JsonSerializerWrapper.Serialize(new TiciWmsLogDto
                    {
                        PersonID = WebSiteInfo.User.Value.ID,
                        Date = DateTime.Now,
                        Details =
                            $"{item} ID'li siparişte {setOrder.ProductID} ID'li ürün eksik olduğundan ({setOrder.ShelfID} ID'li rafta bulunamadı), koliden çıkarma işlemi yapıldığından. İşlem müşteri hizmetlerine aktarılmıştır.",
                        LogType = "KOLIDENURUNCIKAR",
                        Process = "EKSIKURUNKOLIDENCIKAR",
                    }));

                    await _pickingProductDal.DeleteAsync(new PickingProduct { OrderID = item.OrderID }, cancellationToken);

                    await _orderService.SetPackagingStatus(new OrderSetPackagingStatusDto
                    {
                        IDs = new List<int> { item.OrderID },
                        Status = PackageStatus.EksikUrun,
                    }, cancellationToken);
                }

                if (request.ParcelID > 0)
                {
                    var collectionSetNoGroupBy = collection.Products.GroupBy(x => x.SetNo);
                    if (collectionSetNoGroupBy.Count() > 0)
                    {
                        foreach (var setNo in collectionSetNoGroupBy)
                        {
                            _logger.LogTrace(JsonSerializerWrapper.Serialize(new ParcelClearLogDto
                            {
                                TableName = table.Definition,
                                TableBarcode = table.Barcode,
                                ParcelBarcode = parcel.Barcode,
                                ParcelName = parcel.Definition,
                                ClearSetNo = setNo.Key,
                                PersonID = WebSiteInfo.User.Value.ID,
                            }));
                        }
                    }
                }
            }
            else
                throw new NotFoundException("KolidenCikarilacakSetVeyaSiparisBulunamadi");

            return response;
        }

        public async Task<ErrorResponse> ParcelOrderReduceBulk(ParcelOrderReduceRequestBulkDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();
            OrderCollectionSet collection = null;
            List<ParcelOrderReduceOrders> orders = new List<ParcelOrderReduceOrders>();
            WarehouseTable? table = null;

            if (request.ParcelIDs == null)
                throw new NotFoundException("PARCEL_IDS_IS_REQUIRED");
            if (request.ParcelIDs.Count == 0)
                throw new NotFoundException("PARCEL_IDS_IS_REQUIRED");


            collection = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        BoxIDs = request.ParcelIDs,
                        BoxAssign = true
                    }
                }, cancellationToken)).Model;

            if (collection != null && collection.Products != null && collection.Products.Count > 0)
            {
                var collectionOrderIDAndOrderType = collection.Products.GroupBy(x => new { x.OrderID });
                foreach (var item in collectionOrderIDAndOrderType)
                {
                    orders.Add(new ParcelOrderReduceOrders { OrderID = item.Key.OrderID });
                }
            }

            var parcels = (await _warehouseParcelService.GetList(new WarehouseParcelGetListDto { IDList = request.ParcelIDs }, null, cancellationToken)).Model.ToList();
            if (parcels != null && parcels.Count > 0)
            {
                foreach (var parcel in parcels)
                {
                    table = (await _warehouseTableService.GetListAsync(new WarehouseTableGetListDto { ID = parcel.TableID }, cancellationToken)).Model.FirstOrDefault();
                    if (table == null)
                        throw new NotFoundException("KoliyeAitMasaBulunamadi");
                }
            }
            else
                throw new NotFoundException("KoliBulunamadi");


            if (collection != null && collection.Products != null && collection.Products.Count > 0)
            {
                foreach (var item in orders)
                {
                    OrderProduct? setOrder = collection.Products.FirstOrDefault(x => x.OrderID == item.OrderID);

                    var order = (await _orderService.GetList(new OrderGetListDto { OrderID = item.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
                    if (order == null)
                        throw new NotFoundException("SiparisBulunamadi");

                    await _customerServicesManager.Add(new CustomerServiceAddDto
                    {
                        CallByMemberName = order.Customer,
                        CallByMemberID = order.MemberID,
                        CallByPhoneNumber = order.CustomerTelephone,
                        StatusID = (int)CustomerServiceStatus.Bekliyor,
                        PaymentType = order.PaymentTypeID,
                        OrderID = order.ID,
                        OrderNo = order.OrderNo,
                        OrderDeliveryPhone = order.CustomerTelephone,
                        Type = (int)CustomerServiceType.EksikUrunBekleyenSiparisler,
                    }, cancellationToken);

                    await _orderMovementService.AddAsync(new OrderMovementAddDto
                    {
                        OrderID = item.OrderID,
                        AgentID = WebSiteInfo.User.Value.ID,
                        isSystem = true,
                        Name = WebSiteInfo.User.Value.Name,
                        Message = "Sipariş eksik olduğu için koliden çıkarıldı. Müşteri hizmetlerine otomatik yönlendirildi.",
                    }, cancellationToken);

                    var car = (await _warehouseCarService.GetList(new WarehouseCarGetListDto { SetNo = setOrder.SetNo }, null, cancellationToken)).Model.FirstOrDefault();
                    if (car != null)
                        await _warehouseCarProductService.DeleteCarAllProduct(new WarehouseCarProductDeleteCarAllProductDto { OrderID = item.OrderID, WarehouseCarID = car.ID }, cancellationToken);

                    _logger.LogTrace(JsonSerializerWrapper.Serialize(new TiciWmsLogDto
                    {
                        PersonID = WebSiteInfo.User.Value.ID,
                        Date = DateTime.Now,
                        Details =
                            $"{item} ID'li siparişte {setOrder.ProductID} ID'li ürün eksik olduğundan ({setOrder.ShelfID} ID'li rafta bulunamadı), koliden çıkarma işlemi yapıldığından. İşlem müşteri hizmetlerine aktarılmıştır.",
                        LogType = "KOLIDENURUNCIKAR",
                        Process = "EKSIKURUNKOLIDENCIKAR",
                    }));

                    await _pickingProductDal.DeleteAsync(new PickingProduct { OrderID = item.OrderID }, cancellationToken);

                    await _orderService.SetPackagingStatus(new OrderSetPackagingStatusDto
                    {
                        IDs = new List<int> { item.OrderID },
                        Status = PackageStatus.EksikUrun,
                    }, cancellationToken);
                }


                foreach (var parcel in parcels)
                {
                    var collectionSetNoGroupBy = collection.Products.GroupBy(x => x.SetNo);
                    if (collectionSetNoGroupBy.Count() > 0)
                    {
                        foreach (var setNo in collectionSetNoGroupBy)
                        {
                            _logger.LogTrace(JsonSerializerWrapper.Serialize(new ParcelClearLogDto
                            {
                                TableName = table.Definition,
                                TableBarcode = table.Barcode,
                                ParcelBarcode = parcel.Barcode,
                                ParcelName = parcel.Definition,
                                ClearSetNo = setNo.Key,
                                PersonID = WebSiteInfo.User.Value.ID,
                            }));
                        }
                    }
                }
            }
            else
                throw new NotFoundException("KolidenCikarilacakSetVeyaSiparisBulunamadi");

            return response;
        }

        public async Task<ErrorResponse> MissingProductCallCustomerServices(MissingProductCallCustomerServicesRequestDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = request.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
                await _pickingProductDal.DeleteAsync(new PickingProduct { OrderID = request.OrderID }, cancellationToken);
            else
            {
                var customerServicesList = (await _customerServicesManager.GetList(
                    new CustomerServiceGetListDto
                    {
                        OrderID = request.OrderID,
                        StatusID = CustomerServiceStatus.Bekliyor,
                        Type = CustomerServiceType.EksikUrunBekleyenSiparisler
                    }, null, cancellationToken)).Model;

                if (customerServicesList.Count == 0)
                {
                    await _customerServicesManager.Add(
                        new CustomerServiceAddDto
                        {
                            CallByMemberName = order.Customer,
                            CallByMemberID = order.MemberID,
                            CallByPhoneNumber = order.CustomerTelephone,
                            StatusID = (int)CustomerServiceStatus.Bekliyor,
                            PaymentType = -1,
                            OrderID = order.ID,
                            OrderNo = order.OrderNo,
                            OrderDeliveryPhone = order.CustomerTelephone,
                            Type = (int)CustomerServiceType.EksikUrunBekleyenSiparisler,
                        }, cancellationToken);

                    await _orderMovementService.AddAsync(new OrderMovementAddDto
                    {
                        OrderID = order.ID,
                        AgentID = WebSiteInfo.User.Value.ID,
                        isSystem = true,
                        Name = WebSiteInfo.User.Value.Name,
                        Message = "Müşteri hizmetlerine otomatik yönlendirildi.",
                    }, cancellationToken);
                }
            }

            return response;
        }

        public async Task<DataResult<MissingProductListResponseDto>> MissingProductList(MissingProductListRequestDto request, CancellationToken cancellationToken)
        {
            DataResult<MissingProductListResponseDto> response = new DataResult<MissingProductListResponseDto>();

            OrderCollectionSet collection = new OrderCollectionSet();
            List<OrderProduct> collectionProductsList = new List<OrderProduct>();
            List<OrderProduct> products = new List<OrderProduct>();
            List<WarehouseCarProduct> notProcessCarProduct = new List<WarehouseCarProduct>();

            var readCar = (await _warehouseCarService.GetList(new WarehouseCarGetListDto { CarBarcode = request.Barcode, isActive = true }, null, cancellationToken)).Model.FirstOrDefault();
            if (readCar != null)
            {
                if (!string.IsNullOrEmpty(readCar.SetNo))
                {
                    var collectionProducts = (await _orderCollectionService.GetCollectionSet(
                        new GetOrderCollectionSetDto
                        {
                            Filter = new OrderCollectionSetFilter
                            {
                                SetNo = readCar.SetNo,
                                isGrouping = false,
                                PreparedStatusList = new List<int> { 0, 1 },
                                ProductNameFields = WebSiteInfo.User.Value.Settings.UrunAdiAlani
                            }
                        }, cancellationToken)).Model;

                    foreach (var item in collectionProducts.Products)
                    {
                        var product = (await _orderCollectionService.GetCollectionSet(
                            new GetOrderCollectionSetDto
                            {
                                Filter = new OrderCollectionSetFilter
                                {
                                    ID = item.ID,
                                    SetNo = readCar.SetNo,
                                    isGrouping = false,
                                    PreparedStatusList = new List<int> { 0, 1 },
                                    ProductNameFields = WebSiteInfo.User.Value.Settings.UrunAdiAlani
                                }
                            }, cancellationToken)).Model.Products.FirstOrDefault();

                        if (product != null)
                            collectionProductsList.Add(product);
                    }

                    collection.Products = collectionProductsList;

                    if (collection.Products == null || collection.Products.Count == 0)
                    {
                        if (await _cacheManager.Get($"{WebSiteInfo.User.Value.DomainName}:{WebSiteInfo.User.Value.SetNo}:{WebSiteInfo.User.Value.ID}") != null)
                        {
                            var deletedOrders = _cacheManager.Get<List<int>>($"{WebSiteInfo.User.Value.DomainName}:{WebSiteInfo.User.Value.SetNo}:{WebSiteInfo.User.Value.ID}").GetAwaiter().GetResult();
                            if (deletedOrders != null && deletedOrders.Count > 0)
                                throw new BusinessException($"Tümü Eksik Olarak İşaretlenen Siparişler Var Devam Edebilmek İçin Eksik Sipariş Listesinden İşlem Yapmalısınız. Siparişler: {string.Join(",", deletedOrders)}", new KeyValuePair<string, string>("deletedOrders", string.Join(",", deletedOrders)));
                        }

                        var warehouseOrderCollectionProducts = (await _orderCollectionService.GetCollectionSet(
                            new GetOrderCollectionSetDto
                            {
                                Filter = new OrderCollectionSetFilter
                                {
                                    SetNo = WebSiteInfo.User.Value.SetNo,
                                    CarID = WebSiteInfo.User.Value.WarehouseCar.ID,
                                    isGrouping = true,
                                    MissingProduct = true,
                                    PreparationStatus = 5,
                                    PreparedID = WebSiteInfo.User.Value.ID
                                }
                            }, cancellationToken)).Model;

                        if (warehouseOrderCollectionProducts != null && warehouseOrderCollectionProducts.Products?.Count > 0)
                        {
                            var deletedOrders = new List<int>();
                            foreach (var x in warehouseOrderCollectionProducts.Products)
                            {
                                if (!deletedOrders.Any(y => y == x.OrderID) && warehouseOrderCollectionProducts.Products.Where(y => y.OrderID == x.OrderID).Sum(y => y.MissingPiece) == x.OrderProductCount)
                                {
                                    deletedOrders.Add(x.OrderID);
                                    await _orderCollectionService.DeleteByOrderID(x.OrderID, cancellationToken);
                                    await _warehouseCarProductService.Reduce(new WarehouseCarProductReduceDto { Piece = x.MissingPiece, OrderID = x.OrderID, ProductID = x.ProductID, WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID }, cancellationToken);
                                }
                            }

                            if (deletedOrders.Count > 0)
                            {
                                await _cacheManager.Add($"{WebSiteInfo.User.Value.DomainName}:{WebSiteInfo.User.Value.SetNo}:{WebSiteInfo.User.Value.ID}", deletedOrders, 3600);
                                throw new BusinessException($"Tümü Eksik Olarak İşaretlenen Siparişler Var Devam Edebilmek İçin Eksik Sipariş Listesinden İşlem Yapmalısınız. Siparişler: {string.Join(",", deletedOrders)}", new KeyValuePair<string, string>("deletedOrders", string.Join(",", deletedOrders)));
                            }
                            var warehouseCarProducts = (await _warehouseCarProductService.GetList(
                                new WarehouseCarProductGetListDto
                                {
                                    WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID,
                                    TargetIDs = warehouseOrderCollectionProducts.Products.Select(x => x.ProductID).ToList(),
                                    isGroupBy = true,
                                }, null, cancellationToken)).Model;

                            if (warehouseOrderCollectionProducts.Products.Count > 0 && warehouseCarProducts != null && warehouseCarProducts.Count == 0)
                                throw new NotFoundException($"MISSING_PRODUCTS_IS_NOT_FOUND", new KeyValuePair<string, string>("barcode", string.Join(",", warehouseOrderCollectionProducts.Products.Select(x => x.Barcode))));
                        }

                        throw new BusinessException("ArabadaGosterilecekEksikUrunYok");
                    }

                    if (WebSiteInfo.User.Value.WarehouseCar != null)
                    {
                        var carProducts = (await _warehouseCarProductService.GetList(new WarehouseCarProductGetListDto { WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID }, null, cancellationToken)).Model;
                        if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.MissingProductAdminApproval)
                            notProcessCarProduct.AddRange(carProducts.Where(x => x.Settings.approvalStatus > 0).ToList());

                        if (carProducts != null && !carProducts.Any(x => x.Settings.isMissingProduct))
                        {
                            var warehouseOrderCollectionProducts = (await _orderCollectionService.GetCollectionSet(
                                new GetOrderCollectionSetDto
                                {
                                    Filter = new OrderCollectionSetFilter
                                    {
                                        SetNo = WebSiteInfo.User.Value.SetNo,
                                        CarID = WebSiteInfo.User.Value.WarehouseCar.ID,
                                        isGrouping = true,
                                        MissingProduct = true,
                                        PreparationStatus = 5,
                                        PreparedID = WebSiteInfo.User.Value.ID
                                    }
                                }, cancellationToken)).Model;

                            if (warehouseOrderCollectionProducts != null && warehouseOrderCollectionProducts.Products?.Count > 0)
                            {
                                var deletedOrders = new List<int>();
                                warehouseOrderCollectionProducts.Products.ForEach(async x =>
                                {
                                    if (!deletedOrders.Any(y => y == x.OrderID) && warehouseOrderCollectionProducts.Products.Where(y => y.OrderID == x.OrderID).Sum(y => y.MissingPiece) == x.OrderProductCount)
                                    {
                                        deletedOrders.Add(x.OrderID);
                                        await _orderCollectionService.DeleteByOrderID(x.OrderID, cancellationToken);
                                        await _warehouseCarProductService.Reduce(new WarehouseCarProductReduceDto { Piece = x.MissingPiece, OrderID = x.OrderID, ProductID = x.ProductID, WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID }, cancellationToken);
                                    }
                                });

                                if (deletedOrders.Count > 0)
                                {
                                    await _cacheManager.Add($"{WebSiteInfo.User.Value.DomainName}:{WebSiteInfo.User.Value.SetNo}:{WebSiteInfo.User.Value.ID}", deletedOrders, 3600);
                                    throw new BusinessException($"Tümü Eksik Olarak İşaretlenen Siparişler Var Devam Edebilmek İçin Eksik Sipariş Listesinden İşlem Yapmalısınız. Siparişler: {string.Join(",", deletedOrders)}", new KeyValuePair<string, string>("deletedOrders", string.Join(",", deletedOrders)));
                                }

                                var warehouseCarProducts = (await _warehouseCarProductService.GetList(
                                    new WarehouseCarProductGetListDto
                                    {
                                        WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID,
                                        TargetIDs = warehouseOrderCollectionProducts.Products.Select(x => x.ProductID).ToList(),
                                        isGroupBy = true,
                                    }, null, cancellationToken)).Model;

                                if (warehouseOrderCollectionProducts.Products.Count > 0 && warehouseCarProducts != null && warehouseCarProducts.Count == 0)
                                    throw new NotFoundException($"MISSING_PRODUCTS_IS_NOT_FOUND", new KeyValuePair<string, string>("barcode", string.Join(",", warehouseOrderCollectionProducts.Products.Select(x => x.Barcode))));
                            }
                        }
                    }
                }
                else
                    throw new NotFoundException("ArabadaSetBulunamadi");
            }
            else
                throw new NotFoundException("ArabaBulunamadi");


            var missingParcels = (await _warehouseParcelService.GetList(new WarehouseParcelGetListDto { isMissingProductParcel = true }, null, cancellationToken)).Model.Select(x => x.ID).ToList();
            var missingProducts = collection.Products.Where(x => x.MissingPiece > 0).ToList();
            var setProducts = missingProducts.Where(x => missingParcels.Contains(x.BoxID)).ToList();

            if (setProducts.Count == 0)
                setProducts = missingProducts;

            var notAssignProduct = missingProducts.Except(setProducts).Where(x => x.PreparationStatus == 0).ToList();
            if (notAssignProduct.Count > 0)
            {
                var orderIDList = notAssignProduct.Select(x => x.OrderID).Distinct();
                foreach (var orderID in orderIDList)
                {
                    int totalProductCount = collection.Products.Count(x => x.OrderID == orderID);
                    int missingProductCount = notAssignProduct.Count(x => x.OrderID == orderID);
                    if (totalProductCount == missingProductCount)
                        setProducts.AddRange(notAssignProduct.Where(x => x.OrderID == orderID));
                }
            }

            if (setProducts.Count > 0)
            {
                var productIDList = setProducts.Select(x => x.ProductID).Distinct().ToList();
                var shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto { ProductIDList = productIDList, IsStockAvailable = true }, null, cancellationToken)).Model.OrderBy(x => x.ShelfRank);
                foreach (var setProduct in setProducts)
                {
                    var product = products.FirstOrDefault(x => x.ProductID == setProduct.ProductID);
                    if (product != null)
                    {
                        product.Piece += setProduct.Piece;
                        product.OccurrencesPiece += setProduct.OccurrencesPiece;
                        product.MissingPiece += setProduct.MissingPiece;
                    }
                    else
                    {
                        var shelfProduct = shelfProducts.FirstOrDefault(x => x.ProductID == (setProduct.ProductType == ProductType.Product ? setProduct.ProductID : 0));
                        if (shelfProduct != null)
                        {
                            setProduct.ShelfID = shelfProduct.ShelfID;
                            setProduct.ShelfName = shelfProduct.ShelfName;
                            setProduct.ShelfBarcode = shelfProduct.ShelfBarcode;
                            setProduct.ShelfRank = shelfProduct.ShelfRank;
                        }
                        else
                        {
                            setProduct.ShelfID = 0;
                            setProduct.ShelfName = "";
                            setProduct.ShelfBarcode = "";
                        }

                        products.Add(setProduct);
                    }
                }

                if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.MissingProductAdminApproval)
                {
                    List<int> notProcessProductIds = notProcessCarProduct.Select(x => x.ProductID).ToList();
                    response.Model.Products = products.Where(x => notProcessProductIds.Contains(x.ProductID)).ToList();
                    foreach (var product in response.Model.Products)
                    {
                        var carProduct = notProcessCarProduct.FirstOrDefault(x => x.ProductID == product.ProductID);
                        if (carProduct != null)
                        {
                            product.ApprovalStatus = carProduct.Settings.approvalStatus;
                            product.RecommendedShelf = carProduct.Settings.shelfName;
                        }

                    }
                }
                else
                    response.Model.Products = products;
            }

            return response;
        }

        public async Task MissingProductListProductFind(MissingProductListProductFindRequestDto request, CancellationToken cancellationToken)
        {
            OrderProduct readProduct = null;
            string logDetails = "";
            string logType = "";
            double piece = request.Piece <= 0 ? 1 : request.Piece;

            var collection = (await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        SetNo = request.SetNo,
                        ProductID = request.ProductID,
                        MissingProduct = true,
                        OrderCombinesProductFound = true,
                        isGrouping = false,
                        ProductNameFields = WebSiteInfo.User.Value.Settings.UrunAdiAlani
                    }
                }, cancellationToken)).Model;

            var readingProducts = collection.Products.Where(x => x.ProductID == request.ProductID && x.PreparationStatus != 5).ToList();
            if (readingProducts.Count == 0)
                throw new NotFoundException("UrunSetteBulunamadi");

            if (request.isMissingProduct)
            {
                var reduceOrderID = readingProducts.GroupBy(x => x.OrderID).OrderByDescending(x => x.Count()).First().Key;
                readProduct = readingProducts.FirstOrDefault(x => x.OrderID == reduceOrderID);
                logDetails = $"{readProduct.OrderID} ID'li siparişte {readProduct.ProductID} ID'li ürünü({readProduct.Barcode}) {(request.isMissingProduct ? ("bulunamadı olarak işaretlendi. ") : (request.ShelfID + " ID'li rafta buldu. "))}. Otomatik Sipariştir.";
                await _orderCollectionService.SetPreparingStatusCollectionProduct(
                    new SetPreparingStatusCollectionProductDto
                    {
                        ID = readProduct.ID,
                        PreparingStatus = 5,
                    }, cancellationToken);

                readProduct.PreparationStatus = 5;

                if (readProduct.BoxID == 0)
                {
                    var orderProducts = readingProducts.Where(x => x.OrderID == readProduct.OrderID);
                    int totalProductCount = orderProducts.Count();
                    int missingProductCount = orderProducts.Count(x => x.PreparationStatus == 5);
                    if (totalProductCount == missingProductCount)
                    {
                        var order = (await _orderService.GetList(new OrderGetListDto { OrderID = readProduct.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
                        if (order == null)
                            throw new NotFoundException("SiparisBulunamadi");

                        await _customerServicesManager.Add(
                            new CustomerServiceAddDto
                            {
                                CallByMemberName = order.Customer,
                                CallByMemberID = order.MemberID,
                                CallByPhoneNumber = order.CustomerTelephone,
                                StatusID = (int)CustomerServiceStatus.Bekliyor,
                                PaymentType = -1,
                                OrderID = order.ID,
                                OrderNo = order.OrderNo,
                                OrderDeliveryPhone = order.CustomerTelephone,
                                Type = (int)CustomerServiceType.EksikUrunBekleyenSiparisler,
                            }, cancellationToken);

                        logDetails += "Müşteri hizmetlerine otomatik yönlendirildi.";

                        await _orderService.SetPackagingStatus(new OrderSetPackagingStatusDto
                        {
                            IDs = new List<int> { order.ID },
                            Status = PackageStatus.EksikUrun,
                        }, cancellationToken);
                    }

                    var shelf = request.ShelfID > 0 ? (await _shelfService.GetList(new ShelfGetListDto { ID = request.ShelfID }, null, cancellationToken)).Model.FirstOrDefault() : null;
                    if (shelf != null)
                    {
                        logDetails += $"Rafın sıra bilgisi {shelf.Rank} 'dir. ";
                        logDetails += shelf.IsMissingProductShelf ? "Eksik Ürün rafıdır. " : "";
                    }

                    logType = "DEPOURUNTOPLAMAOTOMATIKSIPARIS";
                }
            }
            else
            {
                readProduct = readingProducts.FirstOrDefault();

                await _orderProductService.UpdateProductFind(
                    new UpdateProductFindDto
                    {
                        ID = readProduct.ID,
                        OrderProductID = readProduct.OrderProductID,
                        isMissingProduct = request.isMissingProduct,
                        Piece = piece,
                        SetNo = request.SetNo,
                        CarID = WebSiteInfo.User.Value.WarehouseCar.ID,
                    }, cancellationToken);

                if (request.ShelfID > 0)
                {
                    await _shelfProductService.ShelfProductReduce(new ShelfProductReduceDto
                    {
                        ProductID = request.ProductID,
                        ShelfID = request.ShelfID,
                        StockPiece = piece,
                        OrderID = readProduct.OrderID,
                    }, cancellationToken);

                    if (WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.RaftaUrunBulunamadigindaSiteStokGuncelle)
                    {
                        var reduceStockResult = new ReduceStockResponseDto();
                        var storeStockCount = (await _productService.StoreStockGetCount(new StoreStockFilter { StoreID = WebSiteInfo.User.Value.StoreID, ProductID = readProduct.ProductID }, cancellationToken)).Model;
                        if (storeStockCount > 0)
                            // stoğu güncellenecek ürün mağaza stokda varsa orada güncelliyoruz....
                            reduceStockResult = await _productService.ReduceStock(new ProductUpdateStockDto { ProductID = readProduct.ProductID, Piece = 1, StoreID = WebSiteInfo.User.Value.StoreID }, cancellationToken);
                        else // ürün stoğundan güncelliyoruz...
                            reduceStockResult = await _productService.ReduceStock(new ProductUpdateStockDto { ProductID = readProduct.ProductID, Piece = 1 }, cancellationToken);

                        if (reduceStockResult.SendStockSafetyEmail)
                        {
                            List<string> tos = new List<string>();
                            if (WebSiteInfo.User.Value.Settings.GuvenliStokMail.Contains(','))
                                tos.AddRange(WebSiteInfo.User.Value.Settings.GuvenliStokMail.Split(',').ToList());
                            else
                                tos.Add(WebSiteInfo.User.Value.Settings.GuvenliStokMail);

                            await _warehouseEmailService.SendSafetyStockMail(tos, reduceStockResult.Product.ID, reduceStockResult.Product.Barcode, reduceStockResult.Product.ProductName, reduceStockResult.Product.StockPiece, cancellationToken);
                        }
                    }
                }

                logDetails = $"{readProduct.OrderID} ID'li siparişte {readProduct.ProductID} ID'li ürünü({readProduct.Barcode}) {(request.isMissingProduct ? ("bulunamadı olarak işaretlendi. ") : (request.ShelfID + " ID'li rafta buldu. "))}";

                var shelf = request.ShelfID > 0 ? (await _shelfService.GetList(new ShelfGetListDto { ID = request.ShelfID }, null, cancellationToken)).Model.FirstOrDefault() : null;
                if (shelf != null)
                {
                    logDetails += $"Rafın sıra bilgisi {shelf.Rank}'dir. ";
                    logDetails += shelf.IsMissingProductShelf ? "Eksik Ürün rafıdır. " : "";
                }

                logType = "DEPOURUNTOPLAMAOTOMATIKSIPARIS";
            }

            if (WebSiteInfo.User.Value.Settings.ArabaModulAktif && WebSiteInfo.User.Value.WarehouseCar != null)
            {
                //Arabadan eksik olan ürünü düşüp eksik değilse eksik olmayan halini ekliyoruz. TODO: Silmeden ekle yapmadan direk update edebiliriz.
                await _warehouseCarProductService.Reduce(new WarehouseCarProductReduceDto
                { WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID, Piece = piece, ProductID = request.ProductID, OrderID = readProduct.OrderID, isMissingProduct = true }, cancellationToken);

                if (!request.isMissingProduct)
                    await _warehouseCarProductService.Add(new WarehouseCarProductAddDto
                    { WarehouseCarID = WebSiteInfo.User.Value.WarehouseCar.ID, Piece = piece, ProductID = request.ProductID, OrderID = readProduct.OrderID, Settings = new WarehouseCarProductSettings() }, cancellationToken);
            }

            _logger.LogTrace(JsonSerializerWrapper.Serialize(
                new TiciWmsLogDto
                {
                    Date = DateTime.Now,
                    Process = "EKSIKURUNBULUNDU",
                    PersonID = WebSiteInfo.User.Value.ID,
                    Details = logDetails,
                    LogType = logType,
                }));

            await _orderMovementService.AddAsync(
                new OrderMovementAddDto
                {
                    AgentID = WebSiteInfo.User.Value.ID,
                    isSystem = true,
                    Message = logDetails,
                    Name = WebSiteInfo.User.Value.Name,
                    OrderID = readProduct.OrderID,
                }, cancellationToken);

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvent.MissingProductCompleted, new MissingProductCompletedEventPayload(readProduct.OrderProductID, request.isMissingProduct, request.ShelfID, request.Piece, DateTime.Now.ToTimestamp())));
        }

        /// <summary>
        /// Bir set içindeki siparişlerin ürünlerinin tümü, ya product ya lot olabilir. Bu ürün tipine göre ürün ya da lot set, filtreler ile bulamazsa null döndürür.
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private async Task<OrderCollectionSet> GetProducts(OrderCollectionSetFilter filter, int? combineProductType = null, CancellationToken cancellationToken = default)
        {
            var carsOnSet = (await _warehouseCarService
                .GetCount(new WarehouseCarGetListDto { PersonID = WebSiteInfo.User.Value.ID }, cancellationToken)).Model;

            OrderCollectionSet set = (await _orderCollectionService.GetCollectionSet(new GetOrderCollectionSetDto
            {
                Filter = new OrderCollectionSetFilter
                {
                    SetNo = WebSiteInfo.User.Value.SetNo,
                    isGrouping = carsOnSet == 1 && combineProductType.HasValue &&
                                 combineProductType.Value != 5
                }
            }, cancellationToken)).Model;

            return set;
        }

        public async Task DistributionProductControlAsync(PickingProductServiceDistributionProductDto request, CancellationToken cancellationToken)
        {
            using (var redlockFactory = RedLockFactory.Create(new List<RedLockMultiplexer> { new RedLockMultiplexer(_redisConnect) }))
            {
                var resource = $"{Info.DomainName.Value}_DistributionProduct";
                var expiryTime = TimeSpan.FromSeconds(120);

                await using (var redLock = await redlockFactory.CreateLockAsync(resource, expiryTime))
                {
                    if (redLock.IsAcquired)
                    {
                        await DistributionProductAsync(request, cancellationToken);

                        await _transactionalCommandService.TransactionalExecuteAsync();
                    }
                    else
                        throw new BusinessException("PROCESS_USE_A_ANOTHER_USER");
                }
            }

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvents.Selected, new PickingProductSelectedEventPayload(request.Type, request.OrderId)));
        }

        public async Task<ErrorResponse> EditOrderProductOccurenciesPiece(EditOrderProductOccurenciesPieceDto request, CancellationToken cancellationToken)
        {
            var orderProduct = (await _orderProductService.GetProductList(new OrderProductFilter { OrderProductID = request.OrderProductID }, cancellationToken)).Model.FirstOrDefault();

            if (orderProduct == null)
                throw new NotFoundException("SiparisUrunuBulunamadi");

            if (orderProduct.Operation == 2)
                throw new BusinessException("SiparisUrunuIptalEdilmis");

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = orderProduct.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
                throw new NotFoundException("SiparisBulunamadi");

            if (order.Status < (int)OrderStatus.Paketleniyor && order.PackagingStatusID < (int)Entities.Enums.PackageStatus.Paketleniyor)
                throw new BusinessException("SiparisDurumuUygunDegil");

            await OrderOverPaymentCheck(orderProduct.OrderID, new List<OrderOverPaymentCheckModel>
            {
                new OrderOverPaymentCheckModel
                    { ProductCount = request.Piece, ProductId = orderProduct.ProductID }
            }, cancellationToken);

            await _orderProductService.UpdateOccurenciesPeice(request, cancellationToken);

            //Todo: Collection process update 

            await _orderService.CalculationAmount(order.ID, cancellationToken);
            double oldPrice = order.TotalAmount;

            order = (await _orderService.GetList(new OrderGetListDto { OrderID = order.ID }, null, cancellationToken)).Model.FirstOrDefault();

            _logger.LogTrace(JsonSerializerWrapper.Serialize(new OrderProductOccurenciesPieceLog
            {
                UserId = WebSiteInfo.User.Value.ID,
                Username = WebSiteInfo.User.Value.Username,
                OrderId = order.ID,
                ProductId = orderProduct.ProductID,
                OldQty = orderProduct.OccurrencesPiece,
                NewQty = request.Piece,
                OldPrice = oldPrice,
                NewPrice = order.TotalAmount,
                ProcessDate = DateTime.Now,
            }));

            await _orderMovementService.AddAsync(new OrderMovementAddDto
            {
                OrderID = order.ID,
                AgentID = WebSiteInfo.User.Value.ID,
                isSystem = true,
                Message = $"{order.ID} ID'li siparişte {orderProduct.Barcode} Barkodlu Üründen {orderProduct.OccurrencesPiece} hatalı bulunan adet düzenlendi, yeni bulunan adet {request.Piece}",
                Name = WebSiteInfo.User.Value.Username,
            }, cancellationToken);

            return new ErrorResponse();
        }

        public async Task<List<AllocateProductResponse>> DoAssignShelf(List<AllocateProductRequest> request, CancellationToken cancellationToken)
        {
            var items = request.Select(x => new AllocateProductResponse(x.ProductId, x.Quantity, x.WarehouseId)).ToList();
            foreach (var warehouseGroup in request.GroupBy(x => x.WarehouseId).ToList())
            {
                List<ShelfProductInPiece> shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto
                {
                    NotIncludingGoodsReceivingShelf = true,
                    WarehouseID = warehouseGroup.Key,
                    ProductIDList = warehouseGroup.Where(x => x.WarehouseId == warehouseGroup.Key).Select(x => x.ProductId).ToList(),
                    IsPickingOpened = true
                }, null, cancellationToken)).Model.ToList();

                var allocateQuantityControl = items.Where(x => x.Quantity > 1 || x.Quantity % 1 > 0).ToList();
                if (allocateQuantityControl.Count > 0)
                {
                    items.RemoveAll(x => x.Quantity > 1 || x.Quantity % 1 > 0);
                    foreach (var item in allocateQuantityControl)
                    {
                        for (int i = 0; i < Math.Floor(item.Quantity); i++)
                        {
                            items.Add(new AllocateProductResponse(item.ProductId, 1, item.WarehouseId));
                        }

                        if (item.Quantity % 1 > 0)
                            items.Add(new AllocateProductResponse(item.ProductId, Math.Round(item.Quantity % 1, 2), item.WarehouseId));
                    }
                }

                foreach (var product in items)
                {
                    List<int> denenenRaflar = new List<int>();
                    var shelfProduct = shelfProducts.FirstOrDefault(x => x.ProductID == product.ProductId);
rafStokDene:
                    if (shelfProduct != null)
                    {
                        var assignedTotalStock = items.Where(x => x.ShelfId == shelfProduct.ShelfID && x.ProductId == product.ProductId).Sum(x => x.Quantity);
                        if (shelfProduct.ShelfStock > assignedTotalStock)
                        {
                            product.ShelfId = shelfProduct.ShelfID;
                            product.ShelfName = shelfProduct.ShelfName;
                            product.ShelfBarcode = shelfProduct.ShelfBarcode;
                            product.ShelfRank = shelfProduct.ShelfRank;
                            product.WarehouseId = shelfProduct.WarehouseID;
                            product.WarehouseName = shelfProduct.WarehouseName;
                            product.WarehouseCode = shelfProduct.WarehouseCode;
                        }
                        else
                        {
                            denenenRaflar.Add(shelfProduct.ShelfID);
                            shelfProduct = shelfProducts.FirstOrDefault(x => !denenenRaflar.Contains(x.ShelfID) && x.ProductID == product.ProductId);
                            goto rafStokDene;
                        }
                    }
                }

                foreach (var nonShelfProduct in items.Where(x => x.ShelfId == 0))
                {
                    nonShelfProduct.ShelfRank = 999999;
                    nonShelfProduct.ShelfBarcode = "BULUNAMAYAN";
                    nonShelfProduct.ShelfName = "BULUNAMAYAN";
                }
            }

            return items;
        }

        private async Task OrderOverPaymentCheck(int orderId, List<OrderOverPaymentCheckModel> productModels, CancellationToken cancellationToken)
        {
            if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.SiparisUrunDegistirmeTutarOrani > 0)
            {
                var orderPayments = (await _orderPaymentService.GetList(new() { OrderID = orderId, isApproved = true }, cancellationToken)).Model;
                if (orderPayments.Count > 0)
                {
                    var productIds = productModels.Select(x => x.ProductId).ToList();
                    var approvedAmount = orderPayments.Sum(x => x.Amount);
                    var products = await _productService.GetListAsync(new ProductGetListDto
                    { IDList = productIds });

                    var productSumPieces = productModels.Sum(x => x.ProductCount);
                    var toBeAddProductAmount = products.Sum(x => x.Amount) * productSumPieces;
                    var maxOrderAmount = approvedAmount * (100 + WebSiteInfo.User.Value.Settings.UrunToplamaAyar.SiparisUrunDegistirmeTutarOrani) / 100;
                    var minOrderAmount = approvedAmount * (100 - WebSiteInfo.User.Value.Settings.UrunToplamaAyar.SiparisUrunDegistirmeTutarOrani) / 100;
                    var toBeRemovedProducts = (await _orderProductService.GetProductList(new OrderProductFilter
                    { OrderID = orderId }, cancellationToken)).Model
                        .Where(x => productIds.Contains(x.OrderProductID)).ToList();

                    var toBeRemovedProductAmount = (toBeRemovedProducts.Sum(x => x.Amount) / toBeRemovedProducts.Sum(x => x.Piece)) * productSumPieces;

                    var changeAfterOrderTotalAmount = approvedAmount + (toBeAddProductAmount - toBeRemovedProductAmount);
                    if (changeAfterOrderTotalAmount > maxOrderAmount || changeAfterOrderTotalAmount < minOrderAmount)
                        throw new BusinessException("SiparisDegistirmeTutarOraniHatasi", new KeyValuePair<string, string>("id", orderId.ToString()));
                }
            }
        }

        public async Task DoRejectStore(int orderId, int orderProductId, double piece, bool isPickingProduct = false, CancellationToken cancellationToken = default)
        {
            if (!WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                throw new BusinessException("OMNICHANNEL_SETTINGS_IS_NOT_OPENED");

            var store = (await _storeService.GetList(new StoreGetListDto() { StoreID = WebSiteInfo.User.Value.StoreID }, null, cancellationToken)).Model.FirstOrDefault();
            var orderProduct = (await _orderProductService.GetProductList(new OrderProductFilter() { OrderProductID = orderProductId }, cancellationToken)).Model.FirstOrDefault();

            if (orderProduct == null)
                throw new NotFoundException("ORDER_PRODUCT_NOT_FOUND");
            if (store == null)
                throw new NotFoundException("STORE_IS_NOT_FOUND", new KeyValuePair<string, string>("storeId", WebSiteInfo.User.Value.StoreID.ToString()));

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(OrderProductEvents.Rejected, new OrderProductRejectEventPayload(WebSiteInfo.User.Value.DomainName, store.StoreCode, store.ApiPassword, orderId, orderProductId, piece)));
            //await _orderProductService.UpdateStoreId(orderProductId, -1, cancellationToken);
            if (isPickingProduct)
                await _orderCollectionService.DeleteByOrderProductIdMissingProductForOneRow(orderProductId, cancellationToken);
            else
                await _orderCollectionService.DeleteByOrderProductIdForOneRow(orderProductId, cancellationToken);
            await _orderMovementService.AddAsync(new OrderMovementAddDto(orderId, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, $"{orderProduct.Barcode} barkodlu ürünü bulamadım ve başka bir mağazaya yönlendirdim.", true), cancellationToken);
        }

        public async Task CreateCollection(CreateOrderCollectionRequest request, CancellationToken cancellationToken)
        {
            request.UserId = request.UserId ?? 0;

            if (!request.OrderIds.Any())
                throw new BusinessException("ORDER_IDS_MUST_BE_FILLED");

            var warehouseCarCount = (await _warehouseCarService.GetCount(new WarehouseCarGetListDto() { PersonID = request.UserId.Value }, cancellationToken)).Model;
            if (warehouseCarCount > 0)
                throw new BusinessException("USER_ALREADY_HAVE_WAREHOUSE_CAR");

            var pickingProducts = (await GetDistributionProduct(new PickingProductGetDistiributionProductDto(
                new PickingProductFilter
                {
                    OrderStatus = OrderStatus.On,
                    PackageStatus = Core.Order.Entities.Enums.PackageStatus.Beklemede,
                    PriorityPackageStatus = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu,
                    OrderIDList = request.OrderIds
                }), null, cancellationToken)).Model;

            if (request.UserId == 0)
                pickingProducts.ForEach(x => x.ProductType = 1);

            if (!pickingProducts.Any())
                throw new BusinessException("DagitilacakUrunBulunamadi");

            var setNo = await _pickingProductDal.DistributionProductAsync(pickingProducts, cancellationToken);

            await _orderCollectionService.SetAssign(setNo, request.UserId.Value, cancellationToken);


            List<Task> waitingTask = new List<Task>();

            if (request.UserId.Value != 0)
            {
                string agentName = WebSiteInfo.User.Value.Name;
                if (request.UserId != WebSiteInfo.User.Value.ID)
                {
                    var agent = await _storeAgentService.GetList(new StoreAgentGetListDto() { ID = request.UserId.Value });
                    agentName = agent.Model.FirstOrDefault() != null ? agent.Model.FirstOrDefault().Name : "";
                }
                foreach (var orderId in pickingProducts.Select(x => x.OrderID).Distinct())
                {
                    waitingTask.Add(_orderMovementService.AddAsync(
                        new OrderMovementAddDto
                        {
                            AgentID = WebSiteInfo.User.Value.ID,
                            isSystem = true,
                            Message = $"Sipariş toplama {agentName} toplayıcısının setine({setNo}) atandı.",
                            Name = WebSiteInfo.User.Value.Name,
                            OrderID = orderId,
                        }, cancellationToken));
                }
            }


            foreach (var product in pickingProducts)
            {
                waitingTask.Add(_productMovementService.CreateMovement(
                    product.ProductID,
                    new CreateProductMovementRequest(ProductMovementProcessType.Allocated, product.Piece, ProductMovementMessage.Allocated(product.ShelfName, product.OrderID), null),
                    cancellationToken));
            }

            Task.WaitAll(waitingTask.ToArray(), cancellationToken);

            await _orderService.SetPackagingStatus(
                new OrderSetPackagingStatusDto
                {
                    IDs = pickingProducts.Select(x => x.OrderID).Distinct().ToList(),
                    Status = request.UserId > 0 ? PackageStatus.Paketleniyor : PackageStatus.Beklemede,
                }, cancellationToken);

            await _orderService.UpdateOrderPreparedID(
                new UpdateOrderPreparedIDDto
                {
                    OrderIDs = pickingProducts.Select(x => x.OrderID).Distinct().ToList(),
                    PreparedID = WebSiteInfo.User.Value.ID,
                }, cancellationToken);



            //TODO: eğer setNo oluştuysa stok ile ilgili eventı koyalım buraya.
            if (!string.IsNullOrEmpty(setNo))
            {
                List<ProductMovementStockControlInfo> productMovementStockList = new List<ProductMovementStockControlInfo>();

                var dbProductList = (await _productService.GetList(new ProductGetListDto() { IDList = pickingProducts.Select(x => x.ProductID).ToList() })).Model;
                var grouppedPickingProducts = pickingProducts.GroupBy(x => x.ProductID).Select(group => new { ProductId = group.Key, TotalPickingQuantity = group.Sum(x => x.Piece) });

                foreach (var groupPickingProduct in grouppedPickingProducts)
                {
                    var dbProduct = dbProductList.FirstOrDefault(x => x.ID == groupPickingProduct.ProductId);
                    if (dbProduct != null)
                    {
                        var shelfStock = (await _shelfProductService.GetList(new ShelfProductGetListDto() { ProductID = dbProduct.ID, WarehouseID = WebSiteInfo.User.Value.WarehouseID }, null, cancellationToken)).Model.Sum(x => x.ShelfStock);

                        ProductMovementStockControlInfo stockInfo = new ProductMovementStockControlInfo();
                        stockInfo.Piece = groupPickingProduct.TotalPickingQuantity;
                        stockInfo.WebStock = dbProduct.StockPiece;
                        stockInfo.ConsigmentStock = dbProduct.ConsignmentStockPiece;
                        stockInfo.ShelfStock = shelfStock - groupPickingProduct.TotalPickingQuantity;
                        stockInfo.OldWebStock = dbProduct.StockPiece;
                        stockInfo.OldConsigmentStock = dbProduct.ConsignmentStockPiece;
                        stockInfo.OldShelfStock = shelfStock;
                        stockInfo.ProductId = dbProduct.ID;
                        stockInfo.ProductName = dbProduct.ProductName;
                        stockInfo.DomainName = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.DomainName : "";
                        stockInfo.StoreId = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.StoreID : 0;
                        stockInfo.WarehouseId = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.WarehouseID : 0;
                        stockInfo.CreatedDate = DateTime.Now.ToTimestamp();
                        stockInfo.Type = ProductMovementStockControlTypeEnum.OrderSelectStockReduce.ToString();
                        stockInfo.ObjectId = string.Join(",", pickingProducts.Select(x => x.OrderID).Distinct().ToList());
                        productMovementStockList.Add(stockInfo);
                    }
                }

                WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvent.Select, new SelectProductEventPayload(productMovementStockList)));
                if (request.UserId != null)
                {
                    WebSiteInfo.User.Value.Events.Add(new DomainEvent(NotificationEvent.Created,
                    new CreateNotificationEventPayload(request.UserId.Value, "Set Ataması",
                    $"{WebSiteInfo.User.Value.Name} kullanıcısı tarafından üzerinize set atandı. Set No: {setNo}",
                    "<i class='fi fi-sr-basket-shopping-plus'></i>", false, false, false, NotificationTypes.CreateCollection)));
                }
            }
        }

        public async Task<List<OrderSelectionProductResponse>> GetOrderSelectionProducts(int? productId, CancellationToken cancellationToken)
        {
            var filter = new PickingProductFilter
            {
                OrderStatus = OrderStatus.On,
                PackageStatus = PackageStatus.Beklemede,
                PriorityPackageStatus = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu,
            };

            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                filter.OrderStatus = null;
                filter.PackageStatus = null;
                filter.OrderStatusList.AddRange(new List<OrderStatus>()
                {
                    OrderStatus.On,
                    OrderStatus.Onaylandi,
                    OrderStatus.Paketleniyor,
                    OrderStatus.KargoyaVerildi
                });
            }

            var result = await GetDistributionProduct(new PickingProductGetDistiributionProductDto(filter), null, cancellationToken);
            if (!result.Model.Any())
                throw new NotFoundException("SELECTION_PRODUCTS_ARE_NOT_FOUND");

            var productList = new List<PickingProduct>();
            foreach (var orderGroupped in result.Model.GroupBy(x => x.OrderID))
            {
                var differentProduct = orderGroupped.Select(x => x.StockCode).Distinct();
                if (differentProduct.Count() == 1)
                    productList.AddRange(orderGroupped.ToList());
            }

            if (productId.HasValue)
                productList = productList.Where(x => x.ProductID == productId).ToList();

            var productStockCodeGroups = productList.GroupBy(x => x.StockCode).ToList();

            var products = await _productService.GetListAsync(new ProductGetListDto() { StockCodeList = productStockCodeGroups.Select(x => x.Key).ToList(), AddSubQueries = false });
            var items = new List<OrderSelectionProductResponse>();
            foreach (var productGroup in productStockCodeGroups)
            {
                var product = products.FirstOrDefault(x => x.StockCode == productGroup.Key);
                if (product == null)
                    continue;

                var shelfNames = string.Join(", ", productGroup.Select(x => x.ShelfName).Distinct());

                items.Add(new OrderSelectionProductResponse(product.ID, product.ProductName, product.Barcode, product.Image, productGroup.Sum(x => x.Piece), productGroup.Key, shelfNames));
            }

            return items;
        }

        public async Task<List<OrderSelectionLocationResponse>> GetOrderSelectionLocations(int? shelfId, string? shelfBarcode, string? shelfName, bool? isMainShelf, bool? isSingleProduct, bool? isMultiProduct, CancellationToken cancellationToken)
        {
            bool isMainLocation = false;
            bool isSingle = false;
            bool isMulti = false;
            if (isMainShelf.HasValue)
                isMainLocation = isMainShelf.Value;
            if (isSingleProduct.HasValue)
                isSingle = isSingleProduct.Value;
            if (isMultiProduct.HasValue)
                isMulti = isMultiProduct.Value;

            var filter = new PickingProductFilter
            {
                OrderStatus = OrderStatus.On,
                PackageStatus = PackageStatus.Beklemede,
                PriorityPackageStatus = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu,
            };

            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                filter.OrderStatus = null;
                filter.PackageStatus = null;
                filter.OrderStatusList.AddRange(new List<OrderStatus>()
                {
                    OrderStatus.On,
                    OrderStatus.Onaylandi,
                    OrderStatus.Paketleniyor,
                    OrderStatus.KargoyaVerildi
                });
            }

            var result = await GetDistributionProduct(new PickingProductGetDistiributionProductDto(filter), null, cancellationToken);
            if (!result.Model.Any())
                throw new NotFoundException("SELECTION_PRODUCTS_ARE_NOT_FOUND");

            var shelfs = await _shelfService.GetList(new ShelfGetListDto() { });

            var productList = new List<PickingProduct>();
            foreach (var orderGroupped in result.Model.GroupBy(x => x.OrderID))
            {
                if (!isMainLocation)
                {
                    var differentShelf = orderGroupped.Select(x => x.ShelfID).Distinct();
                    if (differentShelf.Count() == 1 && differentShelf.Any(x => x != 0))
                        productList.AddRange(orderGroupped.ToList());
                }
                else
                {
                    List<int> orderGroupMainShelfIds = new List<int>();
                    foreach (var orderGrouppedItem in orderGroupped.ToList())
                    {
                        var parentShelfId = GetTopParentShelfId(shelfs.Model, orderGrouppedItem.ShelfID);
                        orderGroupMainShelfIds.Add(parentShelfId);
                    }
                    if (orderGroupMainShelfIds.Distinct().Count() == 1 && orderGroupMainShelfIds.Any(x => x != 0))
                    {
                        if (isSingle && orderGroupped.ToList().Sum(x => x.OrderProductPiece) == 1)
                            productList.AddRange(orderGroupped.ToList());
                        else if (isMulti && orderGroupped.ToList().Sum(x => x.OrderProductPiece) > 1)
                            productList.AddRange(orderGroupped.ToList());
                        else if (!isSingle && !isMulti)
                            productList.AddRange(orderGroupped.ToList());
                    }
                }

            }

            if (shelfId.HasValue)
                productList = productList.Where(x => x.ShelfID == shelfId).ToList();
            if (!string.IsNullOrWhiteSpace(shelfBarcode))
                productList = productList.Where(x => x.ShelfBarcode == shelfBarcode).ToList();
            if (!string.IsNullOrWhiteSpace(shelfName))
                productList = productList.Where(x => x.ShelfName == shelfName).ToList();

            var shelfIdGroups = productList.GroupBy(x => x.ShelfID).ToList();

            var items = new List<OrderSelectionLocationResponse>();

            if (!isMainLocation)
            {
                foreach (var shelfGroup in shelfIdGroups)
                {
                    items.Add(new OrderSelectionLocationResponse(shelfGroup.Key, shelfGroup.First().ShelfName, shelfGroup.First().ShelfBarcode, shelfGroup.Sum(x => x.Piece)));
                }
            }
            else
            {
                List<OrderSelectionLocationResponse> orderSelecitonLocationResponse = new List<OrderSelectionLocationResponse>();
                foreach (var shelfGroup in shelfIdGroups)
                {
                    int parentShelfId = GetTopParentShelfId(shelfs.Model, shelfGroup.Key);
                    var shelf = shelfs.Model.FirstOrDefault(x => x.ID == parentShelfId);
                    if (shelf != null)
                    {
                        if (!orderSelecitonLocationResponse.Any(x => x.Id == parentShelfId))
                            orderSelecitonLocationResponse.Add(new OrderSelectionLocationResponse(shelf.ID, shelf.Definition, shelf.Barcode, shelfGroup.Sum(x => x.Piece)));
                        else
                            orderSelecitonLocationResponse.First(x => x.Id == parentShelfId).TotalPiece += shelfGroup.Sum(x => x.Piece);
                    }
                }
                items = orderSelecitonLocationResponse;
            }

            items = items.OrderByDescending(x => x.TotalPiece).ToList();
            return items;
        }
        private int GetTopParentShelfId(List<Shelf> shelfList, int shelfId)
        {
            var shelf = shelfList.FirstOrDefault(p => p.ID == shelfId);
            if (shelf == null)
                return 0;


            if (shelf.ParentId == 0)
                return shelf.ID;


            return GetTopParentShelfId(shelfList, shelf.ParentId);
        }

        public async Task<List<OrderSelectionCountriesResponse>> GetOrderSelectionCountries(int? countryId, CancellationToken cancellationToken)
        {
            List<OrderSelectionCountriesResponse> response = new List<OrderSelectionCountriesResponse>();
            var filter = new PickingProductFilter
            {
                OrderStatus = OrderStatus.On,
                PackageStatus = PackageStatus.Beklemede,
                PriorityPackageStatus = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu,
            };

            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                filter.OrderStatus = null;
                filter.PackageStatus = null;
                filter.OrderStatusList.AddRange(new List<OrderStatus>()
                {
                    OrderStatus.On,
                    OrderStatus.Onaylandi,
                    OrderStatus.Paketleniyor,
                    OrderStatus.KargoyaVerildi
                });
            }

            var result = await GetDistributionProduct(new PickingProductGetDistiributionProductDto(filter), null, cancellationToken);
            if (!result.Model.Any())
                throw new NotFoundException("SELECTION_PRODUCTS_ARE_NOT_FOUND");

            var countries = await _countryCityDistrictDal.GetCountryList(new CountryFilter() { Active = 1 }, cancellationToken);
            //Burada bir siparişin countryID'si 0 ise default olarak CountryID'yi TR yapıyoruz.
            foreach (var pickingOrder in result.Model)
            {
                if (pickingOrder.CountryID == 0)
                    pickingOrder.CountryID = countries.Where(x => x.Currency == "try" && x.CountryCodeAlpha2 == "TR" && x.CountryCodeAlpha3 == "TUR").First().ID;
            }

            var grouppedOrders = result.Model.GroupBy(x => x.CountryID);

            if (countryId.HasValue)
                grouppedOrders = grouppedOrders.Where(x => x.Key == countryId.Value).ToList();

            foreach (var order in grouppedOrders)
            {
                var country = countries.FirstOrDefault(x => x.ID == order.Key);
                if (country == null)
                    continue;

                OrderSelectionCountriesResponse item =
                    new OrderSelectionCountriesResponse(order.Key, country.Definition, order.GroupBy(x => x.OrderID).Count(), order.Count());
                item.Type = "4200-" + order.Key;
                response.Add(item);
            }

            return response;
        }

        public async Task<OrderSelectionDetailResponse> GetOrderSelectionDetails(int orderId, CancellationToken cancellationToken)
        {
            OrderSelectionDetailResponse response = new OrderSelectionDetailResponse();
            var order = (await _orderService.GetList(new OrderGetListDto() { OrderID = orderId })).Model.FirstOrDefault();
            if (order == null)
                throw new NotFoundException("ORDER_NOT_FOUND");

            response.Date = order.Date;
            response.Status = order.Status;
            response.Balance = order.Balance;

            var orderProducts = await _orderProductService.GetProductList(new OrderProductFilter() { OrderID = orderId },
                                                                cancellationToken);



            if (orderProducts.Model.Count > 0)
            {
                var shelfProducts = await _shelfProductService.GetList(new ShelfProductGetListDto()
                {
                    ProductIDList = orderProducts.Model.Select(x => x.ProductID).ToList(),
                    IsStockAvailable = true,
                    IsPickingOpened = true,
                    IsSaleOpened = true
                });


                response.OrderProductSelectionStatusDetailItems = new List<OrderProductSelectionStatusDetailItem>();
                response.OrderProductSelectionStockDetailItems = new List<OrderProductSelectionStockDetailItem>();
                foreach (var orderProduct in orderProducts.Model)
                {
                    OrderProductSelectionStatusDetailItem orderProductStatusItem = new OrderProductSelectionStatusDetailItem();
                    OrderProductSelectionStockDetailItem orderProductStockItem = new OrderProductSelectionStockDetailItem();
                    orderProductStatusItem.Barcode = orderProduct.Barcode;
                    orderProductStatusItem.StockCode = orderProduct.StockCode;
                    orderProductStatusItem.Status = orderProduct.Status;
                    orderProductStatusItem.IsConsigmentProduct = orderProduct.ConsignmentProduct;
                    orderProductStockItem.Barcode = orderProduct.Barcode;
                    orderProductStockItem.StockCode = orderProduct.StockCode;
                    orderProductStockItem.Stock = shelfProducts.Model.Where(x => x.ProductID == orderProduct.ProductID)
                                                        .Select(x => x.ShelfStock).Sum();
                    response.OrderProductSelectionStatusDetailItems.Add(orderProductStatusItem);
                    response.OrderProductSelectionStockDetailItems.Add(orderProductStockItem);
                }
            }

            return response;
        }

        #region GetOrderSelectionExtraMethods

        /// <summary>
        /// Listenin Filtresine
        /// </summary>
        /// <param name="response"></param>
        /// <param name="result"></param>
        private async Task<List<OrderSelectionResponse>> GetFiltredAndPaginatedOrders(DataResult<List<PickingProduct>> result, CancellationToken cancellationToken)
        {
            var totalOrder = result.Model.ToList();

            OrderAndOrderProductCountDto orderAndOrderProductCountDto = await ProductBasedValues(totalOrder, cancellationToken);

            List<OrderSelectionResponse> listOrderSelection = await GetListOrderSelection(orderAndOrderProductCountDto, cancellationToken);
            return listOrderSelection;


        }
        private async Task<List<OrderSelectionResponse>> GetListOrderSelection(OrderAndOrderProductCountDto orderAndOrderProductCountDto, CancellationToken cancellationToken)
        {
            var response = new List<OrderSelectionResponse>();

            #region Normal

            response.Add(new OrderSelectionResponse()
            {
                Id = 1,
                Name = "Normal",
                Items = new List<OrderSelectionItem>
                {
                    new OrderSelectionItem
                    {
                        Type = "1-TekSiparisAl",
                        OrderCount = orderAndOrderProductCountDto.CountsBySelectSingleOrderProduct.OrderCount,
                        ProductCount = orderAndOrderProductCountDto.CountsBySelectSingleOrderProduct.ProductOrderCount,
                        Definition = "Tek Sipariş Al",
                        Icon = "fas fa-conveyor-belt",
                        AuthCode = "OrderManagement.OrderSelect.OneOrder",
                        OrderInfo = orderAndOrderProductCountDto.CountsBySelectSingleOrderProduct.OrderInfo
                    },
                    new OrderSelectionItem
                    {
                        Type = "2-TekUrunluSiparisAl",
                        OrderCount = orderAndOrderProductCountDto.CountsBySingleProduct.OrderCount,
                        ProductCount = orderAndOrderProductCountDto.CountsBySingleProduct.ProductOrderCount,
                        Definition = "Tek Ürünlü Sipariş Al",
                        Icon = "fas fa-conveyor-belt",
                        AuthCode = "OrderManagement.OrderSelect.OneProductOrder",
                        Class = "bg2",
                        OrderInfo = orderAndOrderProductCountDto.CountsBySingleProduct.OrderInfo
                    },
                    new OrderSelectionItem
                    {
                        Type = "3-CokUrunluSiparisAl",
                        OrderCount = orderAndOrderProductCountDto.CountsByMultipleProduct.OrderCount,
                        ProductCount = orderAndOrderProductCountDto.CountsByMultipleProduct.ProductOrderCount,
                        Definition = "Çok Ürünlü Sipariş Al",
                        Icon = "fas fa-conveyor-belt",
                        AuthCode = "OrderManagement.OrderSelect.ALotOfProductOrder",
                        Class = "bg3",
                        OrderInfo = orderAndOrderProductCountDto.CountsByMultipleProduct.OrderInfo
                    },
                    new OrderSelectionItem
                    {
                        Type = "5-KarisikSiparisAl",
                        OrderCount = orderAndOrderProductCountDto.CountsByMixedOrderProduct.OrderCount,
                        ProductCount = orderAndOrderProductCountDto.CountsByMixedOrderProduct.ProductOrderCount,
                        Definition = "Karışık Sipariş Al",
                        Icon = "far fa-shopping-basket",
                        AuthCode = "OrderManagement.OrderSelect.MixedOrder",
                        Class = "bg4",
                        OrderInfo = orderAndOrderProductCountDto.CountsByMixedOrderProduct.OrderInfo
                    }
                }
            });

            #endregion

            #region Marketplace Order

            var sourceItems = new List<OrderSelectionItem>();
            foreach (var sourceGroupped in orderAndOrderProductCountDto.CountsBySourceGroupSingleOrderProduct)
            {
                sourceItems.Add(new OrderSelectionItem
                {

                    Type = sourceGroupped.Type,
                    OrderCount = sourceGroupped.OrderCount,
                    ProductCount = sourceGroupped.ProductOrderCount,
                    Definition = sourceGroupped.TypeDefinition,
                    Icon = sourceGroupped.TypeIcons,
                    AuthCode = sourceGroupped.AuthCode,
                    Class = sourceGroupped.Name.ToLower()
                });
            }
            foreach (var sourceGroupped in orderAndOrderProductCountDto.CountsBySourceGroupMultipleOrderProduct)
            {
                sourceItems.Add(new OrderSelectionItem
                {

                    Type = sourceGroupped.Type,
                    OrderCount = sourceGroupped.OrderCount,
                    ProductCount = sourceGroupped.ProductOrderCount,
                    Definition = sourceGroupped.TypeDefinition,
                    Icon = sourceGroupped.TypeIcons,
                    AuthCode = sourceGroupped.AuthCode,
                    Class = sourceGroupped.Name.ToLower()
                });
            }

            foreach (var sourceGroupped in orderAndOrderProductCountDto.CountsBySourceGroupMixedOrderProduct)
            {
                sourceItems.Add(new OrderSelectionItem
                {

                    Type = sourceGroupped.Type,
                    OrderCount = sourceGroupped.OrderCount,
                    ProductCount = sourceGroupped.ProductOrderCount,
                    Definition = sourceGroupped.TypeDefinition,
                    Icon = sourceGroupped.TypeIcons,
                    AuthCode = sourceGroupped.AuthCode,
                    Class = sourceGroupped.Name.ToLower()
                });
            }

            response.Add(new OrderSelectionResponse()
            {
                Id = 3,
                Name = "MarketplaceOrder",
                Items = sourceItems
            });

            #endregion

            #region Carrier Groupped Order

            var items = new List<OrderSelectionItem>();
            foreach (var carrierGroupped in orderAndOrderProductCountDto.CountsByCarrierGroupSingleOrderProduct)
            {
                items.Add(new OrderSelectionItem
                {

                    Type = carrierGroupped.Type,
                    OrderCount = carrierGroupped.OrderCount,
                    ProductCount = carrierGroupped.ProductOrderCount,
                    Definition = carrierGroupped.TypeDefinition,
                    Icon = carrierGroupped.TypeIcons,
                    AuthCode = carrierGroupped.AuthCode,
                    Class = carrierGroupped.Name.ToLower()
                });
            }

            foreach (var carrierGroupped in orderAndOrderProductCountDto.CountsByCarrierGroupMultipleOrderProduct)
            {
                items.Add(new OrderSelectionItem
                {

                    Type = carrierGroupped.Type,
                    OrderCount = carrierGroupped.OrderCount,
                    ProductCount = carrierGroupped.ProductOrderCount,
                    Definition = carrierGroupped.TypeDefinition,
                    Icon = carrierGroupped.TypeIcons,
                    AuthCode = carrierGroupped.AuthCode,
                    Class = carrierGroupped.Name.ToLower()
                });
            }

            foreach (var carrierGroupped in orderAndOrderProductCountDto.CountsByCarrierGroupMixedOrderProduct)
            {
                items.Add(new OrderSelectionItem
                {

                    Type = carrierGroupped.Type,
                    OrderCount = carrierGroupped.OrderCount,
                    ProductCount = carrierGroupped.ProductOrderCount,
                    Definition = carrierGroupped.TypeDefinition,
                    Icon = carrierGroupped.TypeIcons,
                    AuthCode = carrierGroupped.AuthCode,
                    Class = carrierGroupped.Name.ToLower()
                });
            }

            response.Add(new OrderSelectionResponse()
            {
                Id = 4,
                Name = "KargoSiparisleri",
                Items = items
            });

            #endregion

            #region PaymentType Order

            var paymentTypeItems = new List<OrderSelectionItem>();
            foreach (var paymentTypeGroupped in orderAndOrderProductCountDto.CountsByPaymentTypeGroupOrderProduct)
            {
                paymentTypeItems.Add(new OrderSelectionItem
                {
                    Type = paymentTypeGroupped.Type,
                    OrderCount = paymentTypeGroupped.OrderCount,
                    ProductCount = paymentTypeGroupped.ProductOrderCount,
                    Definition = paymentTypeGroupped.TypeDefinition,
                    Icon = paymentTypeGroupped.TypeIcons,
                    AuthCode = paymentTypeGroupped.AuthCode,
                    Class = paymentTypeGroupped.Name.ToLower()
                });
            }

            response.Add(new OrderSelectionResponse()
            {
                Id = 5,
                Name = "PaymentTypeOrders",
                Items = paymentTypeItems
            });

            #endregion

            #region Warehouse Product Transfer

            var count = _warehouseProductTransferFileDal.Count(null, null, WarehouseProductTransferStatus.Created, null, null, null, null, false, CancellationToken.None).GetAwaiter().GetResult();
            response.Add(new OrderSelectionResponse()
            {
                Id = 6,
                Name = "WarehouseProductTransfer",
                Items = new List<OrderSelectionItem>
                {
                    new OrderSelectionItem
                    {
                        Definition = "Depolar Arası Bekleyenler",
                        Icon = "fas fa-truck-fast",
                        AuthCode = "OrderManagement.OrderSelect.OneOrder",
                        Class = "warehouseproducttransfer",
                        Url = "/product-management/warehouse-product-transfer",
                        Piece = count
                    }
                }
            });

            #endregion

            #region Groupped Picking

            response.Add(new OrderSelectionResponse()
            {
                Id = 7,
                Name = "GroupPicking",
                Items = new List<OrderSelectionItem>
                {
                    new OrderSelectionItem
                    {
                        Definition = "Ürün Bazlı Toplama",
                        Icon = "fas fa-book",
                        AuthCode = "OrderManagement.OrderSelect.OneOrder",
                        Class = "productbasedpicking",
                        Url = "/order-management/order-selecting/product",
                        Piece = orderAndOrderProductCountDto.CountsByDiffrentProduct
                    },
                    new OrderSelectionItem
                    {
                        Definition = "Lokasyon Bazlı Toplama",
                        Icon = "fas fa-shelves",
                        AuthCode = "OrderManagement.OrderSelect.OneOrder",
                        Class = "locationbasedpicking",
                        Url = "/order-management/order-selecting/location",
                        Piece = orderAndOrderProductCountDto.CountsByLocation
                    },
                    new OrderSelectionItem
                    {
                        Definition = "Ana Lokasyon Bazlı Toplama",
                        Icon = "fas fa-shelves",
                        AuthCode = "OrderManagement.OrderSelect.OneOrder",
                        Class = "locationbasedpicking",
                        Url = "/order-management/order-selecting/main-location",
                        Piece = orderAndOrderProductCountDto.CountsByMainLocation
                    },
                    new OrderSelectionItem
                    {
                        Definition = "Ana Lokasyon Bazlı Toplama (Tek Ürünlü)",
                        Icon = "fas fa-shelves",
                        AuthCode = "OrderManagement.OrderSelect.OneOrder",
                        Class = "locationbasedpicking",
                        Url = "/order-management/order-selecting/main-location-single-product",
                        Piece = orderAndOrderProductCountDto.CountsByMainLocationSingleProduct
                    },
                    new OrderSelectionItem
                    {
                        Definition = "Ana Lokasyon Bazlı Toplama (Çok Ürünlü)",
                        Icon = "fas fa-shelves",
                        AuthCode = "OrderManagement.OrderSelect.OneOrder",
                        Class = "locationbasedpicking",
                        Url = "/order-management/order-selecting/main-location-multi-product",
                        Piece = orderAndOrderProductCountDto.CountsByMainLocationMultiProduct
                    }
                }
            });

            #endregion


            #region Cargo Integraion Groupped Order

            var cargoIntegrationItems = new List<OrderSelectionItem>();
            foreach (var cargoIntegrationGroupped in orderAndOrderProductCountDto.CountsByCargoIntegrationGroupOrderProduct)
            {
                cargoIntegrationItems.Add(new OrderSelectionItem
                {
                    Type = cargoIntegrationGroupped.Type,
                    OrderCount = cargoIntegrationGroupped.OrderCount,
                    ProductCount = cargoIntegrationGroupped.ProductOrderCount,
                    Definition = cargoIntegrationGroupped.TypeDefinition,
                    Icon = cargoIntegrationGroupped.TypeIcons,
                    AuthCode = cargoIntegrationGroupped.AuthCode,
                });
            }

            response.Add(new OrderSelectionResponse()
            {
                Id = 8,
                Name = "KargoEntegrasyonSiparisleri",
                Items = cargoIntegrationItems
            });

            #endregion

            #region CountriesGrouppedOrderPicking

            response.Add(new OrderSelectionResponse()
            {
                Id = 9,
                Name = "UlkeBazliSiparisler",
                Items = new List<OrderSelectionItem>
                {
                    new OrderSelectionItem
                    {
                        Definition = "Ülke Bazlı Toplama",
                        Icon = "fas fa-book",
                        AuthCode = "OrderManagement.OrderSelect.CountriesOrder",
                        Class = "productbasedpicking",
                        Url = "/order-management/order-selecting/countries",
                        Piece = orderAndOrderProductCountDto.CountsByCountries
                    }
                }
            });
            #endregion

            #region ÜzerimeSetAl
            response.Add(new OrderSelectionResponse()
            {
                Id = 8,
                Name = "SetManagement",
                Items = new List<OrderSelectionItem>
                {
                    new OrderSelectionItem
                    {
                        Definition = "Üzerime Set Al",
                        Icon = "fas fa-book",
                        AuthCode = "OrderManagement.OrderSelect.OneOrder",
                        Class = "productbasedpicking",
                        Url = "/order-management/order-selecting/set-management",
                        Piece = orderAndOrderProductCountDto.SetManagementSetCount
                    }
                }
            });
            #endregion

            return response;
        }


        public async Task<DataResult<List<OrderSelectionMountlyDto>>> GetOrderSelectionMountly(PickingProductGetDistiributionProductDto request, CancellationToken cancellationToken)
        {
            DataResult<List<OrderSelectionMountlyDto>> response = new DataResult<List<OrderSelectionMountlyDto>>();
            var result = await GetDistributionProduct(request, null, cancellationToken);

            var grouppedPickingProducts = result.Model.GroupBy(x => new
            {
                x.OrderID,
                x.OrderProductPiece,
                x.OrderDate
            }).ToList();

            foreach (var pickingOrder in grouppedPickingProducts)
            {
                OrderSelectionMountlyDto item = new OrderSelectionMountlyDto();
                item.OrderId = pickingOrder.Key.OrderID;
                item.OrderProductPiece = pickingOrder.Key.OrderProductPiece;
                item.OrderDate = pickingOrder.Key.OrderDate;
                response.Model.Add(item);
            }
            response.Count = response.Model.Count;
            return response;
        }

        /// <summary>
        /// Ürün Bazlı Değerlerin Hesaplaması İçin
        /// </summary>
        /// <param name="totalOrder">Ham Sipariş Listesi</param>
        /// <returns>Tüm Hesaplamaların Tamamlanıp Hepsinin Property Olarak Tutulduğu Model Döner</returns>
        private async Task<OrderAndOrderProductCountDto> ProductBasedValues(List<PickingProduct> totalOrder, CancellationToken cancellationToken)
        {
            OrderAndOrderProductCountDto countsDto = new OrderAndOrderProductCountDto();

            var cagoIntegrations = await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto() { Active = 1 });

            var oneProductOrder = totalOrder.Where(x => x.OrderProductPiece == 1).ToList();
            var aLotsOfProductOrder = totalOrder.Where(x => x.OrderProductPiece > 1).ToList();
            var carrierGroupped = totalOrder.GroupBy(x => new { x.CarrierId, x.CarrierName }).ToList();
            var cargoIntegrationGroupped = totalOrder.GroupBy(x => new { x.CargoIntegrationID });
            var sourceGroupped = totalOrder.GroupBy(x => x.OrderSource).ToList();
            var paymentTypeGroupped = totalOrder.GroupBy(x => x.PaymentType).ToList();

            var countries = await _countryCityDistrictDal.GetCountryList(new CountryFilter() { Active = 1 }, cancellationToken);
            //Burada eğer siparişin countryID'si 0 geliyorsa default olarak tr yapmış olduk. countries hiçbir şekilde null gelmeyeceği düşünülüyor.
            foreach (var order in totalOrder)
            {
                if (order.CountryID == 0)
                {
                    order.CountryID = countries.
                            Where(x => x.CountryCodeAlpha2 == "TR" && x.CountryCodeAlpha3 == "TUR" && x.Currency == "try").First().ID;
                }

            }
            var countriesGroupped = totalOrder.GroupBy(x => x.CountryID).ToList();
            countsDto.CountsByCountries = countriesGroupped.Select(x => x.Key).Distinct().Count();

            foreach (var orderGroupped in totalOrder.GroupBy(x => x.OrderID))
            {
                var differentProductCount = orderGroupped.Select(x => x.ProductID).Distinct().Count();
                if (differentProductCount == 1)
                    countsDto.CountsByDiffrentProduct++;

                var differentLocation = orderGroupped.Select(x => x.ShelfID).Distinct();
                if (differentLocation.Count() == 1 && differentLocation.Any(x => x != 0))
                    countsDto.CountsByLocation++;

                if (orderGroupped.Sum(x => x.OrderProductPiece) == 1)
                    countsDto.CountsByMainLocationSingleProduct++;
                else if (orderGroupped.Sum(x => x.OrderProductPiece) > 1)
                    countsDto.CountsByMainLocationMultiProduct++;

            }

            countsDto.CountsByMainLocation = countsDto.CountsByLocation;

            // Tek Sipariş alanındaki Sipariş ve Siparişe ait Ürün adet değerleri
            countsDto.CountsBySingleProduct.OrderCount = oneProductOrder.Count();
            countsDto.CountsBySingleProduct.ProductOrderCount = oneProductOrder.Count();
            countsDto.CountsBySingleProduct.OrderInfo = oneProductOrder.OrderBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(g => new OrderSelectionOrderInfoItem { OrderId = g.Key, OrderDate = g.First().OrderDate }).ToList();

            // Tek Ürünlü alanındaki Sipariş ve Siparişe ait Ürün adet değerleri
            countsDto.CountsByMultipleProduct.OrderCount = aLotsOfProductOrder.DistinctBy(x => x.OrderID).Count();
            countsDto.CountsByMultipleProduct.ProductOrderCount = aLotsOfProductOrder.Count();
            countsDto.CountsByMultipleProduct.OrderInfo = aLotsOfProductOrder.OrderBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(g => new OrderSelectionOrderInfoItem { OrderId = g.Key, OrderDate = g.First().OrderDate }).ToList();

            // Çok Ürünlü alanındaki Sipariş ve Siparişe ait Ürün adet değerleri
            countsDto.CountsBySelectSingleOrderProduct.OrderCount = totalOrder.DistinctBy(x => x.OrderID).Count();
            countsDto.CountsBySelectSingleOrderProduct.ProductOrderCount = totalOrder.Count();
            countsDto.CountsBySelectSingleOrderProduct.OrderInfo = totalOrder.OrderBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(g => new OrderSelectionOrderInfoItem { OrderId = g.Key, OrderDate = g.First().OrderDate }).ToList();

            // Karışık alanındaki Sipariş ve Siparişe Ait Ürün adet değerleri
            countsDto.CountsByMixedOrderProduct.OrderCount = totalOrder.DistinctBy(x => x.OrderID).Count();
            countsDto.CountsByMixedOrderProduct.ProductOrderCount = totalOrder.Count();
            countsDto.CountsByMixedOrderProduct.OrderInfo = totalOrder.OrderBy(x => x.OrderID).GroupBy(x => x.OrderID).Select(g => new OrderSelectionOrderInfoItem { OrderId = g.Key, OrderDate = g.First().OrderDate }).ToList();

            //Set yönetiminde hazırlayan kişinin belirlenmediği setlerin içerisindeki siparişler
            var collections = await _orderCollectionService.OrderCollectionPage(new SetGetListFilter()
            {
                PreparedID = 0,
                isGetCount = true
            });
            countsDto.SetManagementSetCount = collections.Count;

            foreach (var carrierOrder in carrierGroupped)
            {
                var orderGroupped = carrierOrder.GroupBy(x => x.OrderID).Select(g => new
                {
                    OrderId = g.Key,
                    OrderCount = g.Count(),
                    OrderProductCount = g.Sum(x => x.Piece)
                }).ToList();

                var orderSingleGroupped = orderGroupped.Where(x => x.OrderProductCount == 1).ToList();
                var orderMultipleGroupped = orderGroupped.Where(x => x.OrderProductCount > 1).ToList();

                countsDto.CountsByCarrierGroupSingleOrderProduct.Add(new OrderAndOrderProductDto
                {
                    Name = carrierOrder.Key.CarrierName,
                    OrderCount = orderSingleGroupped.Count,
                    ProductOrderCount = orderSingleGroupped.Sum(x => x.OrderProductCount).ToInt32(),
                    AuthCode = "OrderManagement.OrderSelect.CarrierOrders",
                    Type = $"1000-Tekli-{carrierOrder.Key.CarrierId}",
                    TypeDefinition = $"{carrierOrder.Key.CarrierName} Siparişi Al (Tekli)",
                    TypeIcons = "bi bi-border-width",
                });

                countsDto.CountsByCarrierGroupMultipleOrderProduct.Add(new OrderAndOrderProductDto
                {
                    Name = carrierOrder.Key.CarrierName,
                    OrderCount = orderMultipleGroupped.Count,
                    ProductOrderCount = orderMultipleGroupped.Sum(x => x.OrderProductCount).ToInt32(),
                    AuthCode = "OrderManagement.OrderSelect.CarrierOrders",
                    Type = $"1000-Coklu-{carrierOrder.Key.CarrierId}",
                    TypeDefinition = $"{carrierOrder.Key.CarrierName} Siparişi Al (Çoklu)",
                    TypeIcons = "bi bi-border-width",
                });

                countsDto.CountsByCarrierGroupMixedOrderProduct.Add(new OrderAndOrderProductDto
                {
                    Name = carrierOrder.Key.CarrierName,
                    OrderCount = orderGroupped.Count,
                    ProductOrderCount = orderGroupped.Sum(x => x.OrderProductCount).ToInt32(),
                    AuthCode = "OrderManagement.OrderSelect.CarrierOrders",
                    Type = $"1000-Karisik-{carrierOrder.Key.CarrierId}",
                    TypeDefinition = $"{carrierOrder.Key.CarrierName} Siparişi Al (Karışık)",
                    TypeIcons = "bi bi-border-width",
                });
            }

            foreach (var integration in cargoIntegrationGroupped)
            {
                var cargoIntegration = cagoIntegrations.Model.FirstOrDefault(x => x.ID == integration.Key.CargoIntegrationID);
                if (cargoIntegration != null)
                {
                    countsDto.CountsByCargoIntegrationGroupOrderProduct.Add(new OrderAndOrderProductDto
                    {
                        Name = cargoIntegration.Description,
                        OrderCount = integration.DistinctBy(x => x.OrderID).Count(),
                        ProductOrderCount = integration.Count(),
                        AuthCode = "OrderManagement.OrderSelect.CarrierOrders",
                        Type = $"4100-{cargoIntegration.ID}",
                        TypeDefinition = $"{cargoIntegration.Description} Siparişi Al",
                        TypeIcons = "bi bi-text-center",
                    });
                }
            }

            foreach (var sourceOrder in sourceGroupped)
            {
                var orderGroupped = sourceOrder.GroupBy(x => x.OrderID).Select(g => new
                {
                    OrderId = g.Key,
                    OrderCount = g.Count(),
                    OrderProductCount = g.Sum(x => x.Piece)
                }).ToList();

                var orderSingleGroupped = orderGroupped.Where(x => x.OrderProductCount == 1).ToList();
                var orderMultipleGroupped = orderGroupped.Where(x => x.OrderProductCount > 1).ToList();

                countsDto.CountsBySourceGroupSingleOrderProduct.Add(new OrderAndOrderProductDto
                {
                    Name = sourceOrder.Key,
                    OrderCount = orderSingleGroupped.Count,
                    ProductOrderCount = orderSingleGroupped.Sum(x => x.OrderProductCount).ToInt32(),
                    AuthCode = "OrderManagement.OrderSelect.SourceOrders",
                    Type = $"2000-Tekli-{sourceOrder.Key}",
                    TypeDefinition = $"{sourceOrder.Key} Siparişi Al (Tekli)",
                    TypeIcons = "fas fa-shopping-cart",
                });


                countsDto.CountsBySourceGroupMultipleOrderProduct.Add(new OrderAndOrderProductDto
                {
                    Name = sourceOrder.Key,
                    OrderCount = orderMultipleGroupped.Count,
                    ProductOrderCount = orderMultipleGroupped.Sum(x => x.OrderProductCount).ToInt32(),
                    AuthCode = "OrderManagement.OrderSelect.SourceOrders",
                    Type = $"2000-Coklu-{sourceOrder.Key}",
                    TypeDefinition = $"{sourceOrder.Key} Siparişi Al (Çoklu)",
                    TypeIcons = "fas fa-shopping-cart",
                });

                countsDto.CountsBySourceGroupMixedOrderProduct.Add(new OrderAndOrderProductDto
                {
                    Name = sourceOrder.Key,
                    OrderCount = orderGroupped.Count,
                    ProductOrderCount = orderGroupped.Sum(x => x.OrderProductCount).ToInt32(),
                    AuthCode = "OrderManagement.OrderSelect.SourceOrders",
                    Type = $"2000-Karisik-{sourceOrder.Key}",
                    TypeDefinition = $"{sourceOrder.Key} Siparişi Al (Karışık)",
                    TypeIcons = "fas fa-shopping-cart",
                });
            }

            foreach (var paymentTypeOrder in paymentTypeGroupped)
            {
                var name = ((PaymentType)paymentTypeOrder.Key).ToString();
                countsDto.CountsByPaymentTypeGroupOrderProduct.Add(new OrderAndOrderProductDto
                {
                    Name = name,
                    OrderCount = paymentTypeOrder.DistinctBy(x => x.OrderID).Count(),
                    ProductOrderCount = paymentTypeOrder.Count(),
                    AuthCode = "OrderManagement.OrderSelect.PaymentTypeOrders",
                    Type = $"3000-{paymentTypeOrder.Key}",
                    TypeDefinition = $"{name} Siparişi Al",
                    TypeIcons = "fas fa-credit-cart",
                });
            }

            return countsDto;
        }


        #endregion GetOrderSelectionExtraMethods

        public class OrderOverPaymentCheckModel
        {
            public int ProductId { get; set; }

            public double ProductCount { get; set; }
        }
    }
}