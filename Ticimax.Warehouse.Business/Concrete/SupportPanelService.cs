using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract.PostgreSQL;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class SupportPanelService : BaseService, ISupportPanelService
    {
        private readonly IUserInformationDal _userInformationDal;
        private readonly ICustomerAccessDal _customerAccessDal;
        public SupportPanelService(IUserInformationDal userInformationDal, ICustomerAccessDal customerAccessDal)
        {
            _userInformationDal = userInformationDal;
            _customerAccessDal = customerAccessDal;
        }

        public async Task<List<CustomerUserCountsDto>> GetCustomerUserCountsReport(string domainName, bool filterTestDomain, CancellationToken cancellationToken)
        {
            var result = await _userInformationDal.GetCustomerUserCountsReport(domainName, filterTestDomain, cancellationToken);
            return result;
        }

        public async Task<CustomerCountReportDto> GetCustomerCountReport(string domainName, bool filterTestDomain, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            if (pageSize == 0) pageSize = 20;
            if (pageIndex == 0) pageIndex = 1;

            var average = await _customerAccessDal.GetAverageCustomerCount(domainName, filterTestDomain, cancellationToken);
            var records = await _customerAccessDal.GetCustomerCountReport(domainName, filterTestDomain, pageSize, pageIndex, cancellationToken);
            var count = await _customerAccessDal.GetCustomerCountReportCount(domainName, filterTestDomain, cancellationToken);

            var report = new Pageable<CustomerCountsDto>(pageIndex, pageSize, count, records);

            return new CustomerCountReportDto(average, report);
        }

        public async Task<bool> ChangeCustomerStatus(int id, bool status, CancellationToken cancellationToken)
        {
            var result = await _customerAccessDal.ChangeActive(id, status, cancellationToken);
            return result;
        }

        public async Task<int> GetUserCount(bool filterTestDomain, CancellationToken cancellationToken)
        {
            var result = await _userInformationDal.GetUserCount(filterTestDomain, cancellationToken);
            return result;
        }
        public async Task<int> GetCustomerCount(bool filterTestDomain, CancellationToken cancellationToken)
        {
            var result = await _customerAccessDal.GetCustomerCount(filterTestDomain, cancellationToken);
            return result;
        }

    }
}
