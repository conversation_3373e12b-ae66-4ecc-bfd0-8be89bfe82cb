using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Suppliers.Enums;
using Ticimax.Warehouse.Business.Concrete.Suppliers.Events;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class SupplierService : BaseService, ISupplierService
    {
        private readonly ISupplierDal _supplierDal;

        public SupplierService(ISupplierDal supplierDal)
        {
            _supplierDal = supplierDal;
        }

        public async Task<Supplier> GetAsync(int id, CancellationToken cancellationToken)
        {
            var result = await _supplierDal.GetAsync(id, cancellationToken);
            return result;
        }

        public async Task<DataResult<List<Supplier>>> GetList(SupplierGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<Supplier>> result = new DataResult<List<Supplier>>();

            var filter = new SupplierDtoFilter { ID = request.ID, Definition = request.Definition, MailAddress = request.MailAddress, TelephoneNumber = request.TelephoneNumber, isGetCount = request.isGetCount, isActive = request.isActive,SupplierCode =request.SupplierCode };
            result.Model = (await _supplierDal.GetListAsync(filter, paging != null ? new SupplierPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();
            if (request.isGetCount)
                result.Count = (await GetCount(request, cancellationToken)).Model;

            return result;
        }

        public async Task<DataResult<List<SimpleSupplierDto>>> SimpleSupplierGetList(SupplierGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<List<SimpleSupplierDto>> result = new DataResult<List<SimpleSupplierDto>>();

            var filter = new SupplierDtoFilter { ID = request.ID, Definition = request.Definition, MailAddress = request.MailAddress, TelephoneNumber = request.TelephoneNumber, isGetCount = request.isGetCount, isActive = request.isActive, SupplierCode = request.SupplierCode };
            var suppliers = (await _supplierDal.GetListAsync(filter, null, cancellationToken)).ToList();
            result.Model = suppliers.Select(x => x.SimpleSupplierEntity()).ToList();

            return result;
        }

        public async Task<DataResult<int>> GetCount(SupplierGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            var filter = new SupplierDtoFilter { ID = request.ID, Definition = request.Definition, MailAddress = request.MailAddress, TelephoneNumber = request.TelephoneNumber, isGetCount = request.isGetCount, isActive = request.isActive, SupplierCode = request.SupplierCode };
            response.Model = await _supplierDal.GetCountAsync(filter, cancellationToken);

            return response;
        }

        public async Task<ErrorResponse> Update(SupplierEditDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var supplier = (await _supplierDal.GetListAsync(new SupplierDtoFilter { ID = request.ID }, null, cancellationToken)).FirstOrDefault();
            if (supplier != null)
            {
                var requestSupplier = request.ToEntity();
                requestSupplier.ModifiedUserID = WebSiteInfo.User.Value.ID;
                await _supplierDal.UpdateAsync(requestSupplier, cancellationToken);
            }
            else
            {
                throw new NotFoundException("TedarikciBulunamadi");
            }

            return response;
        }

        public async Task<ErrorResponse> Delete(SupplierDeleteDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var supplier = (await _supplierDal.GetListAsync(new SupplierDtoFilter { ID = request.ID }, null, cancellationToken)).FirstOrDefault();
            if (supplier != null)
            {
                var model = request.ToEntity();
                model.ModifiedUserID = WebSiteInfo.User.Value.ID;
                await _supplierDal.DeleteAsync(model, cancellationToken);
            }
            else
            {
                throw new NotFoundException("TedarikciBulunamadi");
            }

            return response;
        }

        public async Task<int> Add(SupplierAddDto request, CancellationToken cancellationToken)
        {
            if (request == null)
                throw new NotFoundException("IstekModeliBos");

            var supplier = request.ToEntity();
            supplier.CreateUserID = WebSiteInfo.User.Value.ID;
            supplier.ModifiedUserID = WebSiteInfo.User.Value.ID;
            var result = await _supplierDal.AddIdAsync(supplier, cancellationToken);

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(SupplierEvents.Created, new SupplierEventPayload(supplier)));

            return result;
        }

        public async Task<ErrorResponse> MultipleAdd(MultipleSupplierAddDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            if (request.Suppliers.Count > 0)
            {
                var suppliers = request.Suppliers.Select(x => x.ToEntity()).ToList();
                await _supplierDal.MultipleAddAsync(suppliers, cancellationToken);
            }
            else
                throw new NotFoundException("IstekListesiBos");

            return response;
        }
    }
}