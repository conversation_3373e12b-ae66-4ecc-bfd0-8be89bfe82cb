using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Extensions.Configuration;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.WarehouseReview.Models.Requests;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.Entities.Models.Requests.WarehouseReview;
using Ticimax.Warehouse.Entities.Static;
using Ticimax.Warehouse.Business.Concrete.WarehouseChatHub.Models;
using System.Linq;
using System.Collections.Generic;
using System.Text;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class WarehouseReviewService : IWarehouseReviewService
    {
        private readonly string _reviewUrl;
        private readonly IStoreAgentService _storeAgentService;

        public WarehouseReviewService(IConfiguration configuration, IStoreAgentService storeAgentService)
        {
            _reviewUrl = configuration.GetValue<string>(ConfigKeys.ReviewUrl);
            _storeAgentService = storeAgentService;
        }

        public async Task Create(CreateWarehouseReviewRequest request, CancellationToken cancellationToken)
        {
            using (HttpClient client = new HttpClient())
            {
                client.BaseAddress = new Uri(_reviewUrl);
                var response = await client.PostAsync("warehouse-reviews", new CreateWarehouseReviewClientRequest(request).ToJsonContent());

                await response.HandleKnownExceptions(cancellationToken);
            }
        }
        public async Task<Nextable<WarehouseChatResponse>> GetWarehouseChatHistory(string domainName, Guid connectionId, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["connectionId"] = connectionId.ToString();
            query["pageSize"] = pageSize.ToString();
            query["pageIndex"] = pageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", domainName);
                client.BaseAddress = new Uri(_reviewUrl);
                var response = await client.GetAsync("warehouse-chat?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                return await JsonSerializerWrapper.Deserialize<Nextable<WarehouseChatResponse>>(response.Content, cancellationToken);
            }
        }

        public async Task WarehouseChatRead(WarehouseChatReadRequest request, CancellationToken cancellationToken)
        {
            using (HttpClient client = new HttpClient())
            {
                client.BaseAddress = new Uri(_reviewUrl);
                var response = await client.PutAsync("warehouse-chat/read-messages", request.ToJsonContent(), cancellationToken);
                response.EnsureSuccessStatusCode();
            }
        }
        public async Task<WarehouseChatUserUnDeliveredCountResponse> GetUserChatUnReadCount(CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["userId"] = WebSiteInfo.User.Value.ID.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reviewUrl);
                var response = await client.GetAsync("warehouse-chat/unread?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                return await JsonSerializerWrapper.Deserialize<WarehouseChatUserUnDeliveredCountResponse>(response.Content, cancellationToken);
            }
        }

        public async Task<Nextable<WarehouseChatGrouppedResponse>> GetWarehouseChatGroupped(string domainName, int userId, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);

            query["userId"] = userId.ToString();

            query["pageSize"] = pageSize.ToString();
            query["pageIndex"] = pageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", domainName);
                client.BaseAddress = new Uri(_reviewUrl);
                var response = await client.GetAsync("warehouse-chat/groupped?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                var result = await JsonSerializerWrapper.Deserialize<Nextable<WarehouseChatGrouppedResponse>>(response.Content, cancellationToken);
                if (result.Contents.Count > 0)
                {
                    List<int> allUserIds = result.Contents.SelectMany(x => x.UserIds).Distinct().ToList();

                    var agents = await _storeAgentService.GetList(new Entities.Dtos.StoreAgentGetListDto() { IDs = allUserIds });
                    foreach (var item in result.Contents)
                    {
                        if (item.UserIds != null && item.UserIds.Count > 0)
                        {
                            foreach (var userChatId in item.UserIds)
                            {
                                var agent = agents.Model.FirstOrDefault(x => x.ID == userChatId);
                                if (agent != null)
                                    item.UserInfo.Add(new WarehouseChatGrouppedUserInfo() { UserId = agent.ID, UserName = agent.Username });
                            }
                        }
                    }
                }
                return result;
            }
        }
        public async Task<CreateWarehouseChatConnectionResponse> CreateWarehouseChatConnection(CreateWarehouseChatConnectionRequest request, CancellationToken cancellationToken)
        {
            request.SenderId = WebSiteInfo.User.Value.ID;
            var payload = JsonSerializerWrapper.Serialize(request);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_reviewUrl}warehouse-chat-connection"),
                Method = HttpMethod.Post,
                Content = new StringContent(payload, Encoding.UTF8, "application/json")
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new HttpClient())
            {
                var response = await client.SendAsync(message, cancellationToken);
                await response.HandleKnownExceptions();
                if (response.IsSuccessStatusCode)
                    return await JsonSerializerWrapper.Deserialize<CreateWarehouseChatConnectionResponse>(response.Content);

                await response.HandleKnownExceptions();
                response.EnsureSuccessStatusCode();
                return null;
            }
        }
    }
}