using Castle.Core.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class LocalizationService : BaseService, ILocalizationService
    {
        private readonly string _reviewApiUrl;
        private readonly ILogger<LocalizationService> _logger;
        public LocalizationService(IConfiguration configuration, ILogger<LocalizationService> logger)
        {
            _reviewApiUrl = configuration.GetValue<string>(ConfigKeys.ReviewUrl);
            _logger = logger;
        }

        public async Task<List<LocalizationDto>> GetLocalizations(string language, CancellationToken cancellationToken)
        {
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_reviewApiUrl}localizations?language={language}"),
                Method = HttpMethod.Get
            };

            var client = new HttpClient();
            var response = await client.SendAsync(message, cancellationToken);
            if (response.IsSuccessStatusCode)
                return await JsonSerializerWrapper.Deserialize<List<LocalizationDto>>(response.Content, cancellationToken);

            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogError($"[error] localizationService get language, statusCode: {response.StatusCode} content: {errorContent}");

            await response.HandleKnownExceptions(cancellationToken);
            response.EnsureSuccessStatusCode();
            return null;
        }

    }
}
