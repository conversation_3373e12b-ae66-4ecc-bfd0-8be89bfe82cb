using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Business.Concrete.Review.Request
{
    public class FilterNotificationRequest
    {
        public bool? IsConfirmationRequied { get; set; }
        public bool? IsConfirmed { get; set; }
        public bool? IsRead { get; set; }
        public int PageSize { get; set; } = 20;
        public int PageIndex { get; set; } = 1;
    }
}
