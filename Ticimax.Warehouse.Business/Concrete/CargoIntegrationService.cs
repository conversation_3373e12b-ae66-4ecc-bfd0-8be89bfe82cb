using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class CargoIntegrationService : BaseService, ICargoIntegrationService
    {
        private readonly ICargoIntegrationDal _cargoIntegrationDal;

        public CargoIntegrationService(ICargoIntegrationDal cargoIntegrationDal)
        {
            _cargoIntegrationDal = cargoIntegrationDal;
        }

        public async Task<DataResult<int>> GetCount(CargoIntegrationGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _cargoIntegrationDal.GetCountAsync(request.ToFilter(), cancellationToken);

            return response;
        }

        public async Task<DataResult<List<CargoIntegration>>> GetList(CargoIntegrationGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<CargoIntegration>> response = new DataResult<List<CargoIntegration>>();

            response.Model = (await _cargoIntegrationDal.GetListAsync(request.ToFilter(), paging != null ? new CargoIntegrationPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();
            if (request.isGetCount)
                response.Count = (await GetCount(request, cancellationToken)).Model;

            return response;
        }

        public async Task<ErrorResponse> UpdateWarehouseSettings(CargoIntegrationUpdateWarehouseSettingsDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            await _cargoIntegrationDal.UpdateWarehouseSettings(new CargoIntegration { ID = request.ID, WarehouseSettings = request.WarehouseSettings }, cancellationToken);

            return response;
        }
    }
}