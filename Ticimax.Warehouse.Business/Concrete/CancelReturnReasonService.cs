using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class CancelReturnReasonService : BaseService, ICancelReturnReasonService
    {
        private readonly ICancelReturnReasonDal _cancelReturnReasonDal;

        public CancelReturnReasonService(ICancelReturnReasonDal cancelReturnReasonDal)
        {
            _cancelReturnReasonDal = cancelReturnReasonDal;
        }

        public async Task<DataResult<List<CancelReturnReason>>> GetList(CancelReturnReasonGetListDto request, WarehouseOperationRulesPaging paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<CancelReturnReason>> response = new DataResult<List<CancelReturnReason>>();

            response.Model = (await _cancelReturnReasonDal.GetListAsync(request.ToFilter(), paging != null ? new CancelReturnReasonPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();

            return response;
        }
    }
}