using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class CategoryService : BaseService, ICategoryService
    {
        private readonly ICategoryDal _categoryDal;

        public CategoryService(ICategoryDal categoryDal)
        {
            _categoryDal = categoryDal;
        }

        public async Task<DataResult<List<Category>>> GetList(CategoryGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<Category>> response = new DataResult<List<Category>>();

            response.Model = (await _categoryDal.GetListAsync(request.ToFilter(), paging != null ? new CategoryPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();

            return response;
        }

        public async Task<DataResult<List<Category>>> GetAllChildAndParentCategories(int categoryId, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<Category>> response = new DataResult<List<Category>>();
            response.Model = (await _categoryDal.GetAllChildAndParentCategoriesAsync(categoryId, paging != null ? new CategoryPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();
            return response;
        }
    }
}