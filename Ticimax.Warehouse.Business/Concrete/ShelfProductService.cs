using System;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums;
using Ticimax.Core.Product.Entities.Dtos;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Report.ProductExtraction.ValueObjects;
using Ticimax.Warehouse.Business.Concrete.ShelfProductConcrete.Enums;
using Ticimax.Warehouse.Business.Concrete.ShelfProductConcrete.Events;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.Business.Concrete.WarehouseEmail;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.ShelfProductDtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;
using Ticimax.Warehouse.Business.Concrete.Report.Enum;
using Ticimax.Core.Extensions;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.Product.Entities.Concrete;
using Ticimax.Core.Product.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class ShelfProductService : BaseService, IShelfProductService
    {
        private readonly IShelfProductDal _shelfProductDal;
        private readonly IShelfService _shelfService;
        private readonly IProductService _productService;
        private readonly ILogger<ShelfProductService> _logger;
        private readonly IOrderCollectionSetDal _orderCollectionSetDal;
        private readonly IProductMovementService _productMovementService;
        private readonly IWarehouseEmailService _warehouseEmailService;
        private readonly IConsignmentProductService _consignmentProductService;
        private readonly IOrderProductService _orderProductService;
        private readonly IServiceProvider _services;

        public ShelfProductService(
            IShelfProductDal shelfProductDal
            , IShelfService shelfService
            , IProductService productService
            , ILogger<ShelfProductService> logger
            , IOrderCollectionSetDal orderCollectionSetDal
            , IProductMovementService productMovementService
            , IWarehouseEmailService warehouseEmailService
            , IConsignmentProductService consignmentProductService
            , IOrderProductService orderProductService, IServiceProvider services)
        {
            _shelfProductDal = shelfProductDal;
            _productService = productService;
            _shelfService = shelfService;
            _logger = logger;
            _orderCollectionSetDal = orderCollectionSetDal;
            _productMovementService = productMovementService;
            _warehouseEmailService = warehouseEmailService;
            _consignmentProductService = consignmentProductService;
            _orderProductService = orderProductService;
            _services = services;
        }

        public async Task Add(ShelfProductAddDto request, CancellationToken cancellationToken)
        {
            List<ShelfProductAddItemDto> errorRequest = new List<ShelfProductAddItemDto>();

            if (string.IsNullOrEmpty(request.Type))
                request.Type = ProductMovementStockControlTypeEnum.ShelfProductAdd.ToString();


            var products = (await _productService.GetList(new Core.Product.Entities.Dtos.ProductGetListDto { IDList = request.ShelfItems.Select(x => x.ProductID).ToList(), AddSubQueries = false }, null, cancellationToken)).Model;
            bool isEmptyShelf = false;
            if (request.ShelfItems.All(x => x.isEmptyShelf) || request.ShelfItems.All(x => !x.isEmptyShelf))
                isEmptyShelf = request.ShelfItems.Select(x => x.isEmptyShelf).FirstOrDefault();

            var shelfs = (await _shelfService.GetList(new ShelfGetListDto() { IDList = request.ShelfItems.Select(x => x.ShelfID).ToList(), IsEmptyShelf = isEmptyShelf }, null, cancellationToken)).Model;

            if (products != null && products.Count == 0)
                throw new BusinessException("Rafa eklenmek istenen bazı ürünler hatalıdır. Ürünler şu şekilde; " + string.Join(",", request.ShelfItems.Select(x => x.ProductID).ToList()));

            if (products != null && products.Count > 0 && request.ShelfItems.Any(x => x.ProductID > 0 && !products.Any(y => y.ID == x.ProductID)))
                throw new BusinessException("Rafa eklenmek istenen bazı ürünler hatalıdır. Ürünler şu şekilde; " + string.Join(",", request.ShelfItems.Where(x => !products.Any(y => y.ID == x.ProductID)).Select(x => x.ProductID).ToList()));

            var toBePickOrders = new List<ToBePickOrderDto>();
            if (WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.UrunYerlestirirkenEksikVarsaSiteyeStokEkleme && request.AddStockWebSite)
                toBePickOrders.AddRange((await _services.GetRequiredService<IReportService>().OffTheShelfProductsAsync(new GetToBePickOrderFilterDto() { ProductIds = request.ShelfItems.Select(x => x.ProductID).ToList() }, cancellationToken)).Model);

            foreach (var shelfItem in request.ShelfItems)
            {
                Shelf shelfControl = shelfs.FirstOrDefault(x => x.ID == shelfItem.ShelfID);
                if (shelfControl != null)
                {
                    if (!errorRequest.Any(x => x == shelfItem))
                    {
                        ShelfProduct shelfProduct = shelfItem.ToEntity();
                        shelfProduct.WarehouseID = shelfProduct.WarehouseID > 0 ? shelfProduct.WarehouseID : WebSiteInfo.User.Value.WarehouseID;
                        shelfProduct.StoreID = shelfProduct.StoreID > 0 ? shelfProduct.StoreID : WebSiteInfo.User.Value.StoreID;
                        await _shelfProductDal.AddAsync(shelfProduct, cancellationToken);

                        var product = products.FirstOrDefault(x => x.ID == shelfItem.ProductID);
                        if (product != null)
                        {
                            shelfProduct.ProductName = product.ProductName;
                            shelfProduct.ProductBarcode = product.Barcode;
                            shelfProduct.ProductStockCode = product.StockCode;
                        }

                        shelfProduct.ShelfName = shelfControl.Definition;
                        shelfProduct.Barcode = shelfControl.Barcode;

                        double reduceConsigmentStock = 0;
                        double addToWebStock = 0;
                        double totalAddedStock = shelfItem.ShelfStock;
                        if (shelfProduct.ProductID > 0)
                        {
                            if (request.AddStockWebSite && WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.YerlestirmedeStokEkle && shelfControl.IsOpenForSale)
                            {
                                var approvedAddToStock = true;
                                if (toBePickOrders.Any() && WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.UrunYerlestirirkenEksikVarsaSiteyeStokEkleme)
                                {
                                    var alreadySoldButNotHaveShelfProductPiece = toBePickOrders.Where(x => x.ProductID == shelfItem.ProductID).Sum(x => x.Piece);
                                    if (alreadySoldButNotHaveShelfProductPiece >= shelfItem.ShelfStock)
                                        approvedAddToStock = false;
                                    else
                                        shelfItem.ShelfStock -= alreadySoldButNotHaveShelfProductPiece;

                                }

                                double addtoWebSitePiece = shelfItem.ShelfStock;

                                if (WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive)
                                {
                                    var consignments = (await _consignmentProductService.GetList(new ConsignmentProductGetListDto() { ProductID = shelfItem.ProductID, isCompleted = false }, null, cancellationToken)).Model;
                                    foreach (var consignment in consignments)
                                    {
                                        double consignmentWaitingPiece = consignment.Stock - consignment.InComingStock;
                                        double consignmentAddedPiece = totalAddedStock > consignmentWaitingPiece ? consignmentWaitingPiece : totalAddedStock;
                                        consignment.InComingStock += consignmentAddedPiece;
                                        await _consignmentProductService.UpdateInComingStock(new ConsignmentProductUpdateInComingStockDto() { ID = consignment.ID, InComingStock = consignment.InComingStock, ProductId = consignment.ProductID }, cancellationToken);
                                        //DBSiparis.SiparisKonsinyeDusur(ref cnn, konsinye.Id, konsinye.UrunId, konsinyeEklenecekStok);
                                        var piece = await _orderProductService.OrderConsignmentReduce(consignment.ID, consignment.ProductID, consignmentAddedPiece, cancellationToken);
                                        totalAddedStock -= consignmentAddedPiece;
                                        if (piece > 0)
                                        {
                                            await _productService.AddStock(
                                                new ProductAddStockDto
                                                {
                                                    ProductID = shelfProduct.ProductID,
                                                    Piece = piece,
                                                    ConsignmentModuleActive = WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive,
                                                    StoreID = WebSiteInfo.User.Value.StoreModuleSettings.MagazaStokSatis.Aktif ? WebSiteInfo.User.Value.StoreID : 0,
                                                },
                                                cancellationToken);

                                            await _productService.ReduceConsignmentStock(consignment.ProductID, piece, true, cancellationToken);
                                            reduceConsigmentStock = piece;
                                        }

                                        if (totalAddedStock == 0)
                                        {
                                            addtoWebSitePiece -= consignmentAddedPiece;
                                            break;
                                        }

                                        addtoWebSitePiece -= consignmentAddedPiece;
                                    }
                                }

                                if (approvedAddToStock && addtoWebSitePiece > 0)
                                {
                                    await _productService.AddStock(
                                        new ProductAddStockDto
                                        {
                                            ProductID = shelfProduct.ProductID,
                                            Piece = addtoWebSitePiece,
                                            ConsignmentModuleActive = WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive,
                                            StoreID = WebSiteInfo.User.Value.StoreModuleSettings.MagazaStokSatis.Aktif ? WebSiteInfo.User.Value.StoreID : 0,
                                        },
                                        cancellationToken);

                                    if (!WebSiteInfo.User.Value.StoreModuleSettings.MagazaStokSatis.Aktif)
                                        addToWebStock = addtoWebSitePiece;
                                }

                            }
                        }

                        _logger.LogTrace(JsonSerializerWrapper.Serialize(new ProductPlacementLogDto
                        {
                            PersonName = WebSiteInfo.User.Value.Name,
                            PersonID = WebSiteInfo.User.Value.ID,
                            WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                            WarehouseName = WebSiteInfo.User.Value.Warehouse,
                            ProductID = shelfItem.ProductID,
                            ShelfID = shelfItem.ShelfID,
                            Piece = shelfItem.ShelfStock,
                        }));



                        if (request.ShelfCounting)
                        {
                            await _productMovementService.CreateMovement(
                                shelfItem.ProductID,
                                new CreateProductMovementRequest(ProductMovementProcessType.ShelfCountingRegulation, shelfItem.ShelfStock, ProductMovementMessage.ShelfCountingRegulation(shelfControl.Definition), request.Note),
                                cancellationToken);
                        }
                        else
                        {
                            await _productMovementService.CreateMovement(
                                shelfItem.ProductID,
                                new CreateProductMovementRequest(ProductMovementProcessType.ProductPlacementToShelf, shelfItem.ShelfStock, ProductMovementMessage.ProductPlacementToShelf(shelfControl.Definition), request.Note),
                                cancellationToken);
                        }

                        ProductMovementStockControlInfo stockInfo = new ProductMovementStockControlInfo();
                        if (product != null)
                        {
                            var shelfStock = (await GetList(new ShelfProductGetListDto() { ProductID = product.ID, WarehouseID = WebSiteInfo.User.Value.WarehouseID }, null, cancellationToken)).Model.Sum(x => x.ShelfStock);

                            stockInfo.WebStock = product.StockPiece + addToWebStock + reduceConsigmentStock;
                            stockInfo.ConsigmentStock = product.ConsignmentStockPiece - reduceConsigmentStock;
                            stockInfo.ShelfStock = shelfStock + shelfItem.ShelfStock;
                            stockInfo.OldWebStock = product.StockPiece;
                            stockInfo.OldConsigmentStock = product.ConsignmentStockPiece;
                            stockInfo.OldShelfStock = shelfStock;
                            stockInfo.CreatedDate = DateTime.Now.ToTimestamp();
                            stockInfo.Type = request.Type;
                            stockInfo.ObjectId = request.ObjectId;
                            if (request.Type == ProductMovementStockControlTypeEnum.PostAcceptanceShelving.ToString())
                            {
                                stockInfo.ShelfStock = shelfStock;
                                stockInfo.OldShelfStock = shelfStock - shelfItem.ShelfStock;
                                stockInfo.OldWebStock = product.StockPiece - shelfItem.ShelfStock;
                            }
                        }

                        WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfProductEvent.Added,
                        new ShelfProductEventPayload(WebSiteInfo.User.Value.DomainName, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, new ProductExtractionProductInfo(shelfProduct.ProductID, shelfProduct.ProductName, shelfProduct.ProductBarcode, shelfProduct.ProductStockCode, shelfItem.ShelfStock, product.ProductCartSupplierDefinition, product.ProductCartSupplierId, request.AddStockWebSite), new ProductExtractionShelfInfo(shelfProduct.ShelfID, shelfProduct.ShelfName, shelfProduct.Barcode),
                            WebSiteInfo.User.Value.WarehouseID, WebSiteInfo.User.Value.StoreID, stockInfo)));
                    }
                }
                else
                    errorRequest.Add(shelfItem);
            }

            if (errorRequest.Count > 0)
                throw new BusinessException("Rafa eklenmek istenen bazı ürünler hatalıdır. Ürünler şu şekilde; " + string.Join(",", errorRequest.Select(x => x.ProductID)),
                    errorRequest.Select(x => new KeyValuePair<string, string>("ProductID", x.ProductID.ToString())).ToArray());

        }

        public async Task<ErrorResponse> Delete(ShelfProductDeleteDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            ShelfProduct shelfProduct = request.ToEntity();
            await _shelfProductDal.DeleteAsync(shelfProduct, cancellationToken);

            return response;
        }

        public async Task<ErrorResponse> DeleteByProductAndShelfID(ShelfProductDeleteByProductIDAndShelfIDDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            await _shelfProductDal.DeleteByProductAndShelfIDAsync(request.ToEntity(), cancellationToken);

            return response;
        }

        public async Task<DataResult<List<ShelfProductInPiece>>> GetList(ShelfProductGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<ShelfProductInPiece>> response = new DataResult<List<ShelfProductInPiece>>();

            double shelfCount = 0;

            var filter = new ShelfProductFilter
            {
                ID = request.ID,
                ProductID = request.ProductID,
                ProductBarcode = request.ProductBarcode,
                ProductStockCode = request.ProductStockCode,
                ShelfID = request.ShelfID,
                WarehouseID = request.WarehouseID,
                WarehouseIDList = request.WarehouseIDList,
                Stock = request.Stock,
                IsMultipleBarcode = request.isMultipleBarcode,
                ProductIDList = request.ProductIDList,
                IsStockAvailable = request.IsStockAvailable,
                IsEmptyShelf = request.IsEmptyShelf,
                ShelfIds = request.ShelfIds,
                IsSaleOpened = request.IsSaleOpened,
                NotIncludingGoodsReceivingShelf = request.NotIncludingGoodsReceivingShelf,
                IsPickingOpened = request.IsPickingOpened
            };

            var pagingInfo = paging != null ? new ShelfProductPaging(paging.PageNo, paging.RecordNumber) : null;

            var products = (await _shelfProductDal.GetListAsync(filter, pagingInfo, cancellationToken)).ToList();

            if (request.IsGetCount)
                response.Count = (await GetCount(request, cancellationToken)).Model;

            response.Model = products.Select(x => new ShelfProductInPiece(x)).ToList();

            return response;
        }

        public async Task<ErrorResponse> Update(ShelfProductUpdateDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            int count = await _shelfProductDal.GetCountAsync(new ShelfProductFilter { ID = request.ID }, cancellationToken);
            if (count > 0)
            {
                var shelfProduct = request.ToEntity();
                if (shelfProduct.ProductID > 0)
                {
                    await _shelfProductDal.UpdateAsync(shelfProduct, cancellationToken);
                }
                else
                {
                    response.IsError = true;
                    response.ErrorMessage = "UrunIDBosOlamaz";
                }
            }
            else
            {
                response.IsError = true;
                response.ErrorMessage = "RafUrunBulunamadi";
            }

            return response;
        }

        public async Task<ErrorResponse> UpdateStock(ShelfProductUpdateStockDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            ShelfProduct shelfProduct = request.ToEntity();
            await _shelfProductDal.UpdateStockAsync(shelfProduct, cancellationToken);

            return response;
        }

        public async Task ShelfProductReduce(ShelfProductReduceDto request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(request.Type))
                request.Type = ProductMovementStockControlTypeEnum.ShelfProductReduce.ToString();
            if ((!string.IsNullOrEmpty(request.ShelfBarcode) || request.ShelfID > 0) && (!string.IsNullOrEmpty(request.ProductBarcode) || request.ProductID > 0))
            {
                var oldProduct = (await _productService.GetList(new Core.Product.Entities.Dtos.ProductGetListDto { ID = request.ProductID }, null, cancellationToken)).Model.FirstOrDefault();

                Shelf shelf = (await _shelfService.GetList(
                    new ShelfGetListDto
                    {
                        Barcode = request.ShelfBarcode,
                        ID = request.ShelfID
                    }, null, cancellationToken)).Model.FirstOrDefault();

                ShelfProduct shelfProduct = (await _shelfProductDal.GetListAsync(
                    new ShelfProductFilter
                    {
                        ProductBarcode = request.ProductBarcode,
                        ProductID = request.ProductID,
                        ShelfID = request.ShelfID,
                        WarehouseID = WebSiteInfo.User.Value.WarehouseID
                    }, null, cancellationToken)).FirstOrDefault();

                if (shelf != null && shelfProduct != null)
                {
                    if (!string.IsNullOrEmpty(request.ShelfBarcode))
                    {
                        request.ShelfID = shelf.ID;
                    }

                    if (!string.IsNullOrEmpty(request.ProductBarcode))
                    {
                        request.ProductID = shelfProduct.ProductID;
                    }

                    shelfProduct.ShelfStock = shelfProduct.ShelfStock - request.StockPiece;
                    if (shelfProduct.ShelfStock < 0)
                    {
                        shelfProduct.ShelfStock = 0;
                    }

                    if (request.StockPiece == 0 || shelfProduct.ShelfStock == 0)
                    {
                        if (request.ShelfID > 0)
                        {
                            await DeleteByProductAndShelfID(
                                new ShelfProductDeleteByProductIDAndShelfIDDto
                                {
                                    ShelfID = request.ShelfID,
                                    ProductID = request.ProductID,
                                }, cancellationToken);
                        }
                    }
                    else
                    {
                        await UpdateStock(
                            new ShelfProductUpdateStockDto
                            {
                                ID = shelfProduct.ID,
                                ShelfStock = shelfProduct.ShelfStock,
                            }, cancellationToken);
                    }

                    double totalStock = (await _shelfProductDal.GetListAsync(new ShelfProductFilter { ProductID = request.ProductID, ProductBarcode = request.ProductBarcode, WarehouseID = WebSiteInfo.User.Value.WarehouseID }, null, cancellationToken)).Sum(x => x.ShelfStock);
                    if (shelfProduct.ProductID == request.ProductID && shelfProduct.ShelfStock == 0)
                        totalStock = 0;

                    if (WebSiteInfo.User.Value.Settings.GuvenliStokAdedi > 0 && !string.IsNullOrEmpty(WebSiteInfo.User.Value.Settings.GuvenliStokMail) && WebSiteInfo.User.Value.Settings.GuvenliStokAdedi >= totalStock)
                    {
                        var product = (await _productService.GetList(new Core.Product.Entities.Dtos.ProductGetListDto { ID = request.ProductID, Barcode = request.ProductBarcode }, null, cancellationToken)).Model.FirstOrDefault();
                        List<string> tos = new List<string>();
                        if (WebSiteInfo.User.Value.Settings.GuvenliStokMail.Contains(','))
                            tos.AddRange(WebSiteInfo.User.Value.Settings.GuvenliStokMail.Split(',').ToList());
                        else
                            tos.Add(WebSiteInfo.User.Value.Settings.GuvenliStokMail);

                        await _warehouseEmailService.SendSafetyStockMail(tos, product.ID, product.Barcode, product.ProductName, totalStock, cancellationToken);
                    }

                    double reduceWebSiteStock = 0;
                    if (WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.YerlestirmedeStokEkle && request.ReduceStockWebSite)
                    {
                        await _productService.AddStock(
                            new Core.Product.Entities.Dtos.ProductAddStockDto
                            {
                                ProductID = shelfProduct.ProductID,
                                Piece = -1 * request.StockPiece,
                                ConsignmentModuleActive = WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive,
                                StoreID = WebSiteInfo.User.Value.StoreModuleSettings.MagazaStokSatis.Aktif ? WebSiteInfo.User.Value.StoreID : 0,
                            }, cancellationToken);
                        reduceWebSiteStock = request.StockPiece;
                    }

                    _logger.LogTrace(JsonSerializerWrapper.Serialize(new ProductPlacementLogDto
                    {
                        PersonName = WebSiteInfo.User.Value.Name,
                        PersonID = WebSiteInfo.User.Value.ID,
                        WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                        WarehouseName = WebSiteInfo.User.Value.Warehouse,
                        ProductID = shelfProduct.ProductID,
                        ShelfID = shelfProduct.ShelfID,
                        Piece = -1 * request.StockPiece,
                        OrderID = request.OrderID,
                    }));


                    await _productMovementService.CreateMovement(
                        shelfProduct.ProductID,
                        new CreateProductMovementRequest(ProductMovementProcessType.RemoveProductFromTheShelf, -1 * request.StockPiece, ProductMovementMessage.RemoveProductFromTheShelf(shelf.Definition), request.Note),
                        cancellationToken);

                    ProductMovementStockControlInfo stockInfo = new ProductMovementStockControlInfo();
                    string supplierDefinition = "";
                    int supplierID = 0;
                    if (oldProduct != null)
                    {
                        var shelfStock = (await GetList(new ShelfProductGetListDto() { ProductID = oldProduct.ID, WarehouseID = WebSiteInfo.User.Value.WarehouseID }, null, cancellationToken)).Model.Sum(x => x.ShelfStock);

                        stockInfo.WebStock = oldProduct.StockPiece - reduceWebSiteStock;
                        stockInfo.ConsigmentStock = oldProduct.ConsignmentStockPiece;
                        stockInfo.ShelfStock = shelfStock - request.StockPiece;
                        stockInfo.OldWebStock = oldProduct.StockPiece;
                        stockInfo.OldConsigmentStock = oldProduct.ConsignmentStockPiece;
                        stockInfo.OldShelfStock = shelfStock;
                        stockInfo.CreatedDate = DateTime.Now.ToTimestamp();
                        stockInfo.Type = request.Type;
                        stockInfo.ObjectId = request.ObjectId;
                        supplierDefinition = oldProduct.ProductCartSupplierDefinition;
                        supplierID = oldProduct.ProductCartSupplierId;
                    }

                    WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfProductEvent.Reduced,
                        new ShelfProductEventPayload(WebSiteInfo.User.Value.DomainName, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, new ProductExtractionProductInfo(shelfProduct.ProductID, shelfProduct.ProductName, shelfProduct.Barcode, shelfProduct.ProductStockCode, request.StockPiece, supplierDefinition, supplierID, false), new ProductExtractionShelfInfo(shelf.ID, shelf.Definition, shelf.Barcode),
                            WebSiteInfo.User.Value.WarehouseID, WebSiteInfo.User.Value.StoreID, stockInfo)));
                }
                else
                    throw new NotFoundException("UrunYadaRafBulunamadi");
            }
            else
                throw new BusinessException("IstekBosOlamaz");
        }

        public async Task<ErrorResponse> ShelfProductReduceListControl(ShelfProductReduceListDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            foreach (var product in request.Products)
            {
                ShelfProduct shelfProduct = null;


                shelfProduct = (await _shelfProductDal.GetListAsync(new ShelfProductFilter
                {
                    ProductID = product.ProductID,
                    ShelfID = request.ShelfID,
                    WarehouseID = WebSiteInfo.User.Value.WarehouseID
                }, null, cancellationToken)).FirstOrDefault();

                if (shelfProduct == null)
                {
                    response.IsError = true;
                    response.ErrorMessage = "RafBulunamadi";
                    return response;
                }

                var shelfProductSetControlCount = (await _orderCollectionSetDal.GetSetProductsAsync(new OrderCollectionSetFilter { ShelfID = request.ShelfID, ProductID = product.ProductID, FindStatus = false, isGrouping = false }, cancellationToken)).Products.Count();
                if (shelfProductSetControlCount > 0)
                {
                    if (product.StockPiece > shelfProduct.ShelfStock) // request de bulunan aktarılmak istenen ürün adedi müsait durum adedinden büyük ise...
                    {
                        response.IsError = true;
                        response.ErrorMessage = $"{request.ShelfID}'ID li rafta bulunan {shelfProduct.ProductID}'ID li Üründen {shelfProduct.ShelfStock} adetini raftan çıkarabilirsiniz.";
                        response.ErrorMessage += shelfProductSetControlCount == 0 ? "" : $"Raftan çıkarılamayan {shelfProductSetControlCount} adet Ürün kullanımdadır.";
                        return response;
                    }
                }
                else
                {
                    if (product.StockPiece > shelfProduct.ShelfStock) // request de bulunan aktarılmak istenen ürün adedi müsait durum adedinden büyük ise...
                    {
                        response.IsError = true;
                        response.ErrorMessage = $"{request.ShelfID}'ID li rafta bulunan {shelfProduct.ProductID}'ID li Üründen {shelfProduct.ShelfStock} adetini raftan çıkarabilirsiniz.";
                        return response;
                    }
                }
            }

            return response;
        }

        public async Task ShelfProductReduceList(ShelfProductReduceListDto request, CancellationToken cancellationToken)
        {
            var shelfProductReduceListControlResponse = await ShelfProductReduceListControl(request, cancellationToken);
            if (shelfProductReduceListControlResponse.IsError)
                throw new BusinessException(shelfProductReduceListControlResponse.ErrorMessage);

            foreach (var product in request.Products)
            {
                await ShelfProductReduce(
                    new ShelfProductReduceDto
                    {
                        ProductBarcode = product.ProductBarcode,
                        ProductID = product.ProductID,
                        ShelfBarcode = request.ShelfBarcode,
                        ShelfID = request.ShelfID,
                        StockPiece = product.StockPiece,
                        ReduceStockWebSite = request.ReduceStockWebSite,
                        Note = request.Note,
                    }, cancellationToken);
            }
        }

        public async Task<ErrorResponse> ProductShelfChangeControl(ProductShelfChangeDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            Shelf shelf = null;
            Shelf transferShelf = null;

            // Çıkarılacak rafı buluyoruz....
            if (request.ShelfID <= 0)
            {
                if (!string.IsNullOrEmpty(request.ShelfBarcode))
                {
                    shelf = (await _shelfService.GetList(new ShelfGetListDto { Barcode = request.ShelfBarcode }, null, cancellationToken)).Model.FirstOrDefault();
                    if (shelf == null)
                    {
                        response.IsError = true;
                        response.ErrorMessage = "RafBilgisineUlasilamiyor";
                        return response;
                    }
                }
                else
                {
                    response.IsError = true;
                    response.ErrorMessage = "RafIdVeyaRafBarkodBilgisiBosOlamaz";
                    return response;
                }
            }
            else
            {
                shelf = (await _shelfService.GetList(new ShelfGetListDto { ID = request.ShelfID }, null, cancellationToken)).Model.FirstOrDefault();
                if (shelf == null)
                {
                    response.IsError = true;
                    response.ErrorMessage = "RafBilgisineUlasilamiyor";
                    return response;
                }
            }

            // Transfer edilecek rafı buluyrouz....
            if (request.TransferShelfID <= 0)
            {
                if (!string.IsNullOrEmpty(request.TransferShelfBarcode))
                {
                    transferShelf = (await _shelfService.GetList(new ShelfGetListDto { Barcode = request.TransferShelfBarcode }, null, cancellationToken)).Model.FirstOrDefault();
                    if (transferShelf == null)
                    {
                        response.IsError = true;
                        response.ErrorMessage = "TransferYapilacakRafBilgisineUlasilamiyor";
                        return response;
                    }
                }
                else
                {
                    response.IsError = true;
                    response.ErrorMessage = "TransferRafIdVeyaRafBarkodBilgisiBosOlamaz";
                    return response;
                }
            }
            else
            {
                transferShelf = (await _shelfService.GetList(new ShelfGetListDto { ID = request.TransferShelfID }, null, cancellationToken)).Model.FirstOrDefault();
                if (transferShelf == null)
                {
                    response.IsError = true;
                    response.ErrorMessage = "TransferYapilacakRafBilgisineUlasilamiyor";
                    return response;
                }
            }

            // transfer edilecek ürünleri dönüyoruz...
            foreach (var product in request.Products)
            {
                ShelfProduct shelfProduct = null;

                if (product.ProductID > 0 || !string.IsNullOrEmpty(product.ProductBarcode)) // ürün ise 
                {
                    if (product.ProductID <= 0)
                    {
                        if (!string.IsNullOrEmpty(product.ProductBarcode))
                        {
                            shelfProduct = (await _shelfProductDal.GetListAsync(new ShelfProductFilter
                            {
                                ProductBarcode = product.ProductBarcode,
                                ShelfID = shelf.ID,
                                WarehouseID = WebSiteInfo.User.Value.WarehouseID
                            }, null, cancellationToken)).FirstOrDefault();

                            if (shelfProduct == null)
                            {
                                response.IsError = true;
                                response.ErrorMessage = "UrunBulunamadi";
                                return response;
                            }
                        }
                    }
                    else
                    {
                        shelfProduct = (await _shelfProductDal.GetListAsync(new ShelfProductFilter
                        {
                            ProductID = product.ProductID,
                            ShelfID = shelf.ID,
                            WarehouseID = WebSiteInfo.User.Value.WarehouseID
                        }, null, cancellationToken)).FirstOrDefault();

                        if (shelfProduct == null)
                        {
                            response.IsError = true;
                            response.ErrorMessage = "UrunBulunamadi";
                            return response;
                        }
                    }

                    var shelfProductSetControlCount = (await _orderCollectionSetDal.GetSetProductsAsync(new OrderCollectionSetFilter { ShelfID = shelf.ID, ProductID = shelfProduct.ProductID, FindStatus = false, isGrouping = false }, cancellationToken)).Products.Count(); // sete atanmıs ancak henüz hiçbir işlem yapılmamış ürünlerin sayılarını buluyoruz....
                    if (shelfProductSetControlCount > 0)
                    {
                        if (product.StockPiece > shelfProduct.ShelfStock) // requet de bulunan aktarılmak istenen ürün adedi müsait durum adedinden büyük ise 
                        {
                            response.IsError = true;
                            response.ErrorMessage = $"{shelf.ID}'ID li rafta bulunan {shelfProduct.ProductID}'ID li Üründen {shelfProduct.ShelfStock} adetini başka bir rafa aktarabilirsiniz.";
                            response.ErrorMessage += shelfProductSetControlCount == 0 ? "" : $"Aktarılamayan {shelfProductSetControlCount} adet Ürün kullanımdadır.";
                            return response;
                        }
                    }
                    else
                    {
                        if (product.StockPiece > shelfProduct.ShelfStock) // requet de bulunan aktarılmak istenen ürün adedi müsait durum adedinden büyük ise 
                        {
                            response.IsError = true;
                            response.ErrorMessage = $"{shelf.ID}'ID li rafta bulunan {shelfProduct.ProductID}'ID li Üründen {shelfProduct.ShelfStock} adetini başka bir rafa aktarabilirsiniz.";
                            return response;
                        }
                    }
                }
            }

            return response;
        }

        public async Task ProductShelfChange(ProductShelfChangeDto request, CancellationToken cancellationToken)
        {
            Shelf shelf = null;
            Shelf transferShelf = null;

            var productShelfChangeControlResponse = await ProductShelfChangeControl(request, cancellationToken);
            if (productShelfChangeControlResponse.IsError)
                throw new BusinessException(productShelfChangeControlResponse.ErrorMessage);

            // Çıkarılacak rafı buluyoruz....
            if (request.ShelfID <= 0)
            {
                if (!string.IsNullOrEmpty(request.ShelfBarcode))
                {
                    shelf = (await _shelfService.GetList(new ShelfGetListDto { Barcode = request.ShelfBarcode }, null, cancellationToken)).Model.FirstOrDefault();
                }
            }
            else
            {
                shelf = (await _shelfService.GetList(new ShelfGetListDto { ID = request.ShelfID }, null, cancellationToken)).Model.FirstOrDefault();
            }

            // Transfer edilecek rafı buluyrouz....
            if (request.TransferShelfID <= 0)
            {
                if (!string.IsNullOrEmpty(request.TransferShelfBarcode))
                {
                    transferShelf = (await _shelfService.GetList(new ShelfGetListDto { Barcode = request.TransferShelfBarcode }, null, cancellationToken)).Model.FirstOrDefault();
                }
            }
            else
            {
                transferShelf = (await _shelfService.GetList(new ShelfGetListDto { ID = request.TransferShelfID }, null, cancellationToken)).Model.FirstOrDefault();
            }

            var oldProducts = await _productService.GetList(new ProductGetListDto() { IDList = request.Products.Select(x => x.ProductID).ToList() });

            // transfer edilecek ürünleri dönüyoruz...
            foreach (var product in request.Products)
            {
                ShelfProduct shelfProduct = null;

                if (product.ProductID > 0 || !string.IsNullOrEmpty(product.ProductBarcode)) // ürün ise 
                {
                    if (product.ProductID <= 0)
                    {
                        if (!string.IsNullOrEmpty(product.ProductBarcode))
                        {
                            shelfProduct = (await _shelfProductDal.GetListAsync(new ShelfProductFilter
                            {
                                ProductBarcode = product.ProductBarcode,
                                ShelfID = shelf.ID,
                                WarehouseID = WebSiteInfo.User.Value.WarehouseID
                            }, null, cancellationToken)).FirstOrDefault();
                        }
                    }
                    else
                    {
                        shelfProduct = (await _shelfProductDal.GetListAsync(new ShelfProductFilter
                        {
                            ProductID = product.ProductID,
                            ShelfID = shelf.ID,
                            WarehouseID = WebSiteInfo.User.Value.WarehouseID
                        }, null, cancellationToken)).FirstOrDefault();
                    }

                    await ShelfProductReduce(
                        new ShelfProductReduceDto
                        {
                            ProductBarcode = shelfProduct.Barcode,
                            ProductID = shelfProduct.ProductID,
                            ShelfBarcode = shelf.Barcode,
                            ShelfID = shelf.ID,
                            StockPiece = product.StockPiece,
                            ReduceStockWebSite = request.ReduceStockWebSite,
                            Type = ProductMovementStockControlTypeEnum.ShelfProductChange.ToString()
                        }, cancellationToken);

                    await _shelfProductDal.AddAsync(new ShelfProduct
                    {
                        ProductID = shelfProduct.ProductID,
                        ShelfID = transferShelf.ID,
                        WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                        StoreID = WebSiteInfo.User.Value.StoreID,
                        ShelfStock = product.StockPiece
                    }, cancellationToken);

                    await _productMovementService.CreateMovement(shelfProduct.ProductID,
                        new CreateProductMovementRequest(ProductMovementProcessType.ProductPlacementToShelf
                        , product.StockPiece
                        , ProductMovementMessage.ProductPlacementToShelf(transferShelf.Definition), null)
                    , cancellationToken);

                    //await ShelfProductReduce içerisinde stok düşümünü rapora yazıyor. Burada da rafa eklenen değeri yazıyoruz.
                    ProductMovementStockControlInfo stockInfo = new ProductMovementStockControlInfo();
                    var oldProduct = oldProducts.Model.FirstOrDefault(x => x.ID == product.ProductID);
                    int supplierId = 0;
                    string supplierDefinition = "";
                    if (oldProduct != null)
                    {
                        var shelfStock = (await GetList(new ShelfProductGetListDto() { ProductID = oldProduct.ID, WarehouseID = WebSiteInfo.User.Value.WarehouseID }, null, cancellationToken)).Model.Sum(x => x.ShelfStock);

                        stockInfo.WebStock = oldProduct.StockPiece;
                        stockInfo.ConsigmentStock = oldProduct.ConsignmentStockPiece;
                        //stockInfo.ShelfStock = oldProduct.ShelfPiece + product.StockPiece;
                        stockInfo.ShelfStock = shelfStock;
                        stockInfo.OldWebStock = oldProduct.StockPiece;
                        stockInfo.OldConsigmentStock = oldProduct.ConsignmentStockPiece;
                        stockInfo.OldShelfStock = shelfStock - product.StockPiece;
                        stockInfo.CreatedDate = DateTime.Now.ToTimestamp();
                        stockInfo.Type = ProductMovementStockControlTypeEnum.ShelfProductChange.ToString();
                        supplierId = oldProduct.ProductCartSupplierId;
                        supplierDefinition = oldProduct.ProductCartSupplierDefinition;
                    }

                    WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfProductEvent.Changed,
                        new ShelfProductEventPayload(WebSiteInfo.User.Value.DomainName, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, new ProductExtractionProductInfo(shelfProduct.ProductID, shelfProduct.ProductName, shelfProduct.Barcode, shelfProduct.ProductStockCode, product.StockPiece, supplierDefinition, supplierId, false), new ProductExtractionShelfInfo(shelfProduct.ShelfID, shelfProduct.ShelfName, shelfProduct.Barcode),
                            WebSiteInfo.User.Value.WarehouseID, WebSiteInfo.User.Value.StoreID, stockInfo)));

                    WebSiteInfo.User.Value.Events.Add(new DomainEvent(ShelfProductEvent.Added,
                     new ShelfProductEventPayload(WebSiteInfo.User.Value.DomainName, WebSiteInfo.User.Value.ID,
                     WebSiteInfo.User.Value.Name,
                     new ProductExtractionProductInfo(shelfProduct.ProductID, shelfProduct.ProductName,
                     shelfProduct.Barcode, shelfProduct.ProductStockCode,
                     product.StockPiece, oldProduct.ProductCartSupplierDefinition,
                     oldProduct.ProductCartSupplierId, false),
                     new ProductExtractionShelfInfo(transferShelf.ID, transferShelf.Definition, transferShelf.Barcode),
                      WebSiteInfo.User.Value.WarehouseID, WebSiteInfo.User.Value.StoreID, null)));
                }
            }
        }

        public async Task<ShelfStockResponse> GetStockCount(ShelfProductGetListDto request, CancellationToken cancellationToken)
        {
            var stock = await GetShelfChildrenStock(request.ShelfID, cancellationToken);
            return new ShelfStockResponse(stock);
        }

        public async Task<double> GetShelfChildrenStock(int shelfId, CancellationToken cancellationToken)
        {
            var stock = await _shelfProductDal.GetShelfChildrenStock(shelfId, cancellationToken);
            return stock;
        }

        public async Task<double> GetShelfParentStock(int shelfId, CancellationToken cancellationToken)
        {
            var stock = await _shelfProductDal.GetShelfParentStock(shelfId, cancellationToken);
            return stock;
        }

        private void ShelfChildrenIdListFill(int shelfId, List<Shelf> shelfs, List<int> shelfIdList)
        {
            shelfIdList.Add(shelfId);
            var chidShelfs = shelfs.Where(x => x.ParentId == shelfId).ToList();
            foreach (var childShelf in chidShelfs)
            {
                ShelfChildrenIdListFill(childShelf.ID, shelfs, shelfIdList);
            }
        }

        public async Task<DataResult<int>> GetCount(ShelfProductGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _shelfProductDal.GetCountAsync(new ShelfProductFilter
            {
                ID = request.ID,
                ProductID = request.ProductID,
                ProductBarcode = request.ProductBarcode,
                ProductStockCode = request.ProductStockCode,
                ShelfID = request.ShelfID,
                WarehouseID = request.WarehouseID,
                WarehouseIDList = request.WarehouseIDList,
                Stock = request.Stock,
                IsMultipleBarcode = request.isMultipleBarcode,
                ProductIDList = request.ProductIDList,
                IsStockAvailable = request.IsStockAvailable,
                IsEmptyShelf = request.IsEmptyShelf,
                ShelfIds = request.ShelfIds
            }, cancellationToken);


            return response;
        }

        public async Task<ErrorResponse> DeleteByShelfID(ShelfProductDeleteByShelfIDDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var shelfIds = await _shelfService.ChildrenShelfIdsGetListAsync(request.ShelfID, cancellationToken);

            if (request.ReduceStockWebSite)
            {
                var shelfAllProducts = await _shelfProductDal.GetListAsync(new ShelfProductFilter() { ShelfIds = shelfIds });
                List<int> webSiteTockReduceProductIds = new List<int>();
                foreach (var shelfProduct in shelfAllProducts)
                {
                    if (webSiteTockReduceProductIds.Contains(shelfProduct.ProductID))
                        continue;

                    var productAllStock = shelfAllProducts.Where(x => x.ProductID == shelfProduct.ProductID).Sum(x => x.ShelfStock);
                    if (WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.YerlestirmedeStokEkle)
                    {
                        await _productService.AddStock(
                            new Core.Product.Entities.Dtos.ProductAddStockDto
                            {
                                ProductID = shelfProduct.ProductID,
                                Piece = -1 * productAllStock,
                                StoreID = WebSiteInfo.User.Value.StoreModuleSettings.MagazaStokSatis.Aktif ? WebSiteInfo.User.Value.StoreID : 0,
                            }, cancellationToken);
                        webSiteTockReduceProductIds.Add(shelfProduct.ProductID);
                    }
                }
            }


            foreach (var shelfId in shelfIds)
            {

                request.ShelfID = shelfId;
                await _shelfProductDal.DeleteByShelfIDAsync(request.ToEntity(), cancellationToken);

                var shelfProducts = await GetList(new ShelfProductGetListDto() { ShelfID = request.ShelfID });
                foreach (var shelfProduct in shelfProducts.Model)
                {
                    await _productMovementService.CreateMovement(
                     shelfProduct.ProductID,
                     new CreateProductMovementRequest(ProductMovementProcessType.RemoveProductFromTheShelf, -1 * shelfProduct.ShelfStock, ProductMovementMessage.RemoveProductFromTheShelf(shelfProduct.ShelfName), null),
                     cancellationToken);
                }
            }


            return response;
        }

        public async Task<DataResult<List<CriticalStockShelfProductResponse>>> GetCriticalStockProductsAsync(CriticalStockShelfProductPaging paging = null, CancellationToken cancellationToken = default)
        {
            if (WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.CriticStock <= 0)
                throw new BusinessException("ENTER_VALID_CRITICAL_STOCK_QUANTITY");

            DataResult<List<CriticalStockShelfProductResponse>> response = new DataResult<List<CriticalStockShelfProductResponse>>();
            CriticalStockShelfProductFilter filter = new CriticalStockShelfProductFilter(WebSiteInfo.User.Value.WarehouseID, WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.CriticStock);
            response.Model = await _shelfProductDal.GetCriticalStockProducts(filter, paging != null ? new CriticalStockShelfProductPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken);
            response.Count = await GetCriticalStockProductsCountAsync(cancellationToken);
            return response;
        }

        public async Task<int> GetCriticalStockProductsCountAsync(CancellationToken cancellationToken = default)
        {
            CriticalStockShelfProductFilter filter = new CriticalStockShelfProductFilter(WebSiteInfo.User.Value.WarehouseID, WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.CriticStock);
            int count = await _shelfProductDal.GetCriticalStockProductsCount(filter, cancellationToken);
            return count;
        }

    }
}