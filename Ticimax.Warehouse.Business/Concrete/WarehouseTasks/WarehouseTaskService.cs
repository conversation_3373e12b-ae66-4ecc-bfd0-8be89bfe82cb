using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models;
using Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models.Reponse;
using Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models.Request;
using Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models.Request.ClientRequest;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseTasks
{
    public class WarehouseTaskService : IWarehouseTaskService
    {
        private readonly string _reviewUrl;
        private readonly ILogger<WarehouseTaskService> _logger;

        public WarehouseTaskService(IConfiguration configuration, ILogger<WarehouseTaskService> logger)
        {
            _reviewUrl = configuration.GetValue<string>(ConfigKeys.ReviewUrl);
            _logger = logger;
        }

        public async Task<WarehouseTaskModel> FindWarehouseTask(Guid id, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewUrl}warehouse-tasks/{id}";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Get
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return await JsonSerializerWrapper.Deserialize<WarehouseTaskModel>(response.Content, cancellationToken);

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] WarehouseTaskService FindWarehouseTask, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return null;
            }
        }

        public async Task<Pageable<WarehouseTaskModel>> FilterWarehouseTask(FilterWarehouseTasksRequest filter, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["pageSize"] = filter.PageSize.ToString();
            query["pageIndex"] = filter.PageIndex.ToString();

            if (!string.IsNullOrEmpty(filter.Keyword))
                query["keyword"] = filter.Keyword;

            if (filter.TaskStatus.HasValue)
                query["taskStatus"] = filter.TaskStatus.ToString();

            if (filter.AssignedUserId.HasValue)
                query["assignedUserId"] = filter.AssignedUserId.ToString();

            if (filter.CreatedUserId.HasValue)
                query["createdUserId"] = filter.CreatedUserId.ToString();

            if (filter.StartDate.HasValue)
                query["startDate"] = filter.StartDate.ToString();

            if (filter.EndDate.HasValue)
                query["endDate"] = filter.EndDate.ToString();

            string queryString = query.ToString();

            var endpoint = $"{_reviewUrl}warehouse-tasks?{queryString}";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Get
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return await JsonSerializerWrapper.Deserialize<Pageable<WarehouseTaskModel>>(response.Content, cancellationToken);

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] WarehouseTaskService FilterWarehouseTask, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return null;
            }
        }

        public async Task<bool> CreateWarehouseTask(CreateWarehouseTaskRequest request, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewUrl}warehouse-tasks";
            var payload = new CreateWarehouseTaskClientRequest(request.Title, request.Description, request.AssignedUserId, WebSiteInfo.User.Value.ID);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Post,
                Content = payload.ToJsonContent()
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] WarehouseTaskService CreateWarehouseTask, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }

        public async Task<bool> UpdateWarehouseTask(Guid id, UpdateWarehouseTaskRequest request, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewUrl}warehouse-tasks/{id}";
            var payload = new UpdateWarehouseTaskClientRequest(request.Title, request.Description, request.AssignedUserId, WebSiteInfo.User.Value.ID);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Patch,
                Content = payload.ToJsonContent()
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] WarehouseTaskService UpdateWarehouseTask, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }

        public async Task<bool> ChangeWarehouseTaskStatus(Guid id, ChangeWarehouseTaskStatusRequest request, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewUrl}warehouse-tasks/{id}";
            var payload = new ChangeWarehouseTaskStatusClientRequest(request.TaskStatus, WebSiteInfo.User.Value.ID);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Put,
                Content = payload.ToJsonContent()
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] WarehouseTaskService ChangeWarehouseTaskStatus, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }

        public async Task<bool> DeleteWarehouseTask(Guid id, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewUrl}warehouse-tasks/{id}";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Delete
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] WarehouseTaskService DeleteWarehouseTask, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }

        public async Task<WarehouseTaskStatusResponse> FilterWarehouseTaskStatuses(WarehouseTaskStatusRequest filter, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);

            if (!string.IsNullOrEmpty(filter.Name))
                query["name"] = filter.Name;

            string queryString = query.ToString();
            if (!string.IsNullOrEmpty(filter.Ids))
            {
                var ids = filter.Ids.Split(',').Where(x => !string.IsNullOrEmpty(x)).Select(x => $"ids={x}").ToList();
                queryString += "&" + string.Join("&", ids);
            }

            var endpoint = $"{_reviewUrl}warehouse-tasks/statuses?{queryString}";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Get
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return await JsonSerializerWrapper.Deserialize<WarehouseTaskStatusResponse>(response.Content, cancellationToken);

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] WarehouseTaskService FilterWarehouseTaskStatuses, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return null;
            }
        }

        public async Task<List<WarehouseTaskHistoryModel>> FindWarehouseTaskHistory(Guid id, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewUrl}warehouse-tasks/{id}/history";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Get
            };

            message.Headers.Add("domainName", WebSiteInfo.User.Value.DomainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return await JsonSerializerWrapper.Deserialize<List<WarehouseTaskHistoryModel>>(response.Content, cancellationToken);

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] WarehouseTaskService FindWarehouseTaskHistory, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return null;
            }
        }
    }
}
