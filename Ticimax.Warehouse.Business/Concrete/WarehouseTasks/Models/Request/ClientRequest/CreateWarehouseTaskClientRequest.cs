namespace Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models.Request.ClientRequest
{
    public class CreateWarehouseTaskClientRequest
    {
        public CreateWarehouseTaskClientRequest() { }
        public CreateWarehouseTaskClientRequest(string title, string description, int assignedUserId, int createdUserId)
        {
            Title = title;
            Description = description;
            AssignedUserId = assignedUserId;
            CreatedUserId = createdUserId;
        }

        public string Title { get; set; }
        public string Description { get; set; }
        public int AssignedUserId { get; set; }
        public int CreatedUserId { get; set; }
    }
}
