using System;
using Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Enums;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseTasks.Models
{
    public class WarehouseTaskHistoryModel
    {
        public Guid Id { get; set; }
        public Guid WarehouseTaskId { get; set; }
        public int UserId { get; set; }
        public string? FieldName { get; set; }
        public string? OldValue { get; set; }
        public string? NewValue { get; set; }
        public HistoryLogType HistoryLogType { get; set; }
        public long CreatedDate { get; set; }
        public long LastModifiedDate { get; set; }
        public long Version { get; set; }
    }
}
