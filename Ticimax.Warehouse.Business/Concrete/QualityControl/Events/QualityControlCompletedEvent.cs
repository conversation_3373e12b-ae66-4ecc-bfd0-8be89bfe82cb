using System;
using System.Collections.Generic;
using System.Linq;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.KargoHelper.DTO.Models;
using Ticimax.Warehouse.Business.Concrete.Report.ProductExtraction.ValueObjects;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.QualityControl.Events
{
    public class QualityControlCompletedEvent : BaseDomainEvent
    {
        public QualityControlCompletedEvent(Order order, BindPacketResponse packet)
        {
            OrderId = order.ID;
            InvoiceNo = order.InvoiceNo;
            DomainName = WebSiteInfo.User.Value.DomainName;
            IsOmnichannelActive = WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive;
            OmnichannelWaitingChangeStatus = WebSiteInfo.User.Value.Settings.OmnichannelSettings.CompletedProductStatus;
            OmnichannelWaitingChangeProcess = (int)ProductStatus.KargoyaVerildi;
            Token = WebSiteInfo.User.Value.WarehouseToken;
            SendSms = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.SmsBilgilendir;
            SendMail = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.MailBilgilendir;
            Packet = packet != null ? new QualityControlCompletedPacketInformation(packet.SiparisKargoPaketi.ID, packet.SiparisKargoPaketi.Kargo_Entegrasyon_Tanim, packet.KargoPaketi.ShipmentBarcode, order.Customer, packet.SiparisKargoPaketi.Urunler.Count, packet.SiparisKargoPaketi.Kargo_Entegrasyon_ID) : null;
            PersonId = WebSiteInfo.User.Value.ID;
            PersonName = WebSiteInfo.User.Value.Name;
            TableId = WebSiteInfo.User.Value.TableID;
            TableName = WebSiteInfo.User.Value.Table;
            Desi = order.TotalDesi;
            WarehouseId = WebSiteInfo.User.Value.WarehouseID;
            StoreId = WebSiteInfo.User.Value.StoreID;
            UserId = WebSiteInfo.User.Value.ID;
            Username = WebSiteInfo.User.Value.Username;
            Products = order.Products;
            OrderSource = order.OrderSource;
            ProductCount = order.ProductCount;
            ProductsAmount = order.Products.Sum(x => x.Amount);
            CargoAmount = order.CargoAmount;
            TotalAmount = order.TotalAmount;
            CustomerName = order.Customer;
            Year = DateTime.Now.Year;
            Month = DateTime.Now.Month;
            QualityControlDesi = order.QualityControlDesi;
            OrderDate = order.Date.ToTimestamp();
        }

        public int OrderId { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public double QualityControlDesi { get; set; }

        public string InvoiceNo { get; set; }

        public string DomainName { get; set; }
        public string CustomerName { get; set; }

        public bool IsOmnichannelActive { get; set; }

        public int OmnichannelWaitingChangeStatus { get; set; }

        public int OmnichannelWaitingChangeProcess { get; set; }

        public string Token { get; set; }

        public bool SendSms { get; set; }
        public long OrderDate { get; set; }

        public bool SendMail { get; set; }

        public int PersonId { get; set; }

        public string PersonName { get; set; }

        public int TableId { get; set; }

        public string TableName { get; set; }

        public double Desi { get; set; }
        public double CargoAmount { get; set; }
        public double TotalAmount { get; set; }
        public double ProductsAmount { get; set; }

        public int WarehouseId { get; set; }

        public int StoreId { get; set; }

        public int UserId { get; set; }

        public string Username { get; set; }

        public string OrderSource { get; set; }
        public int ProductCount { get; set; }

        public QualityControlCompletedPacketInformation Packet { get; set; }

        public List<OrderProduct> Products { get; set; }
        public List<ProductMovementStockControlInfo> ProductMovementStockControlInfo { get; set; }
    }

    public class QualityControlCompletedPacketInformation
    {
        public QualityControlCompletedPacketInformation(int ıd, string cargoCompany, string barcode, string customerName, int productCount, int cargoEntegrationId)
        {
            Id = ıd;
            CargoCompany = cargoCompany;
            Barcode = barcode;
            CustomerName = customerName;
            ProductCount = productCount;
            CargoEntegrationId = cargoEntegrationId;
        }

        public int Id { get; set; }

        public string CargoCompany { get; set; }

        public string Barcode { get; set; }

        public string CustomerName { get; set; }

        public int ProductCount { get; set; }
        public int CargoEntegrationId { get; set; }
    }
}