using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.QualityControl.Events
{
    public class QualityControlInvoiceCreateEvent : BaseDomainEvent
    {
        public QualityControlInvoiceCreateEvent(int orderId, long orderDate)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            OrderId = orderId;
            OrderDate = orderDate;
            Username = WebSiteInfo.User.Value.Username;
            Password = WebSiteInfo.User.Value.Password;
            Token = WebSiteInfo.User.Value.WarehouseToken;
            TokenExpire = WebSiteInfo.User.Value.WarehouseTokenExpire;
            TokenServer = WebSiteInfo.User.Value.WarehouseTokenServer;
            WWWActive = WebSiteInfo.User.Value.SiteSettings.SeoAyar?.WwwAktif ?? true;
        }

        public string DomainName { get; set; }

        public int OrderId { get; set; }
        public long OrderDate { get; set; }

        public string Username { get; set; }

        public string Password { get; set; }

        public string Token { get; set; }

        public DateTime TokenExpire { get; set; }

        public string TokenServer { get; set; }

        public bool WWWActive { get; set; }
    }
}