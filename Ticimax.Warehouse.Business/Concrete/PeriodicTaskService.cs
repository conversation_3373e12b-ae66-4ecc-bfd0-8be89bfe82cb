using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.DataAccessLayer.Abstract;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class PeriodicTaskService : IPeriodicTaskService
    {
        private readonly IPeriodicTaskDal _periodicTaskDal;
        private readonly string _wmsInternalApiUrl;
        public PeriodicTaskService(IPeriodicTaskDal periodicTaskDal, IConfiguration configuration)
        {
            _periodicTaskDal = periodicTaskDal;
            _wmsInternalApiUrl = configuration.GetValue<string>(ConfigKeys.WmsInternalUrl);
        }

        public async Task ClearEmptyStocks(string domainName, CancellationToken cancellationToken)
        {
            Info.DomainName.Value = domainName;

            await _periodicTaskDal.ClearEmptyStocks(cancellationToken);
        }

        public async Task ClearEmptyStocksSend(string domainName, CancellationToken cancellationToken)
        {
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_wmsInternalApiUrl}periodic-tasks/clear-empty-stock-internal"),
                Method = HttpMethod.Post
            };
            message.Headers.Add("domainName", domainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (!response.IsSuccessStatusCode)
                {
                    await response.HandleKnownExceptions(cancellationToken);
                    response.EnsureSuccessStatusCode();
                }
            }
        }
    }
}
