using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Business.Concrete.Order.Enums;
using Ticimax.Core.Order.Business.Concrete.Order.Events;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.OrderWaiting.Models.Request;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete.OrderWaiting;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.OrderWaiting.Models
{
#nullable enable
    public class WaitingOrderService : IWaitingOrderService
    {
        private readonly IWaitingOrderDal _waitingOrderDal;
        private readonly IOrderMovementService _orderMovementService;

        public WaitingOrderService(IWaitingOrderDal waitingOrderDal, IOrderMovementService orderMovementService)
        {
            _waitingOrderDal = waitingOrderDal;
            _orderMovementService = orderMovementService;
        }

        public async Task Completed(int id, CancellationToken cancellationToken)
        {
            var aggregate = await _waitingOrderDal.GetByOrderId(id, cancellationToken);
            if (aggregate == null)
                throw new NotFoundException("WAITING_ORDER_IS_NOT_FOUND");

            aggregate.Complete();
            await _waitingOrderDal.Completed(aggregate, cancellationToken);
        }

        public async Task<WaitingOrderAggregate> Create(int orderId, WaitingOrderRequest request, CancellationToken cancellationToken)
        {
            var aggregate = new WaitingOrderAggregate(orderId, request.Message);

            await _waitingOrderDal.Create(aggregate, cancellationToken);
            await _orderMovementService.AddAsync(new OrderMovementAddDto(orderId, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, "Sipariş bekleme moduna alındı.", true), cancellationToken);

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(OrderEvents.Waiting, new WaitingOrderCreatedEventPayload(aggregate)));

            return aggregate;
        }

        public async Task<WaitingOrderAggregate> GetByOrderId(int orderId, CancellationToken cancellationToken)
        {
            return await _waitingOrderDal.GetByOrderId(orderId, cancellationToken);
        }

        public async Task<Pageable<WaitingOrderAggregate>> Get(int? orderId, string? status, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _waitingOrderDal.Get(orderId, status, pageSize, pageIndex, cancellationToken);
            var count = await  _waitingOrderDal.Count(orderId, status, cancellationToken);

            return new Pageable<WaitingOrderAggregate>(pageIndex, pageSize, count, contents);
        }
    }
}