using System;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Warehouse.Entities.Concrete.OrderWaiting;

namespace Ticimax.Warehouse.Business.Concrete.OrderWaiting.Models.Response
{
    public class WaitingOrderDetailed
    {
        public WaitingOrderDetailed()
        {
        }

        public WaitingOrderDetailed(WaitingOrderAggregate aggregate, Order order)
        {
            Id = aggregate.Id;
            Message = aggregate.Message;
            Status = aggregate.Status;
            OrderId = aggregate.OrderId;
            OrderNo = order.OrderNo;
            MemberName = order.Customer;
            Telephone = order.CustomerTelephone;
            TotalAmount = order.TotalAmount;
            OrderSource = order.OrderSource;
            OrderDate = order.Date;
            CreatedDate = aggregate.CreatedDate;
            LastModifiedDate = aggregate.LastModifiedDate;
        }

        public Guid Id { get; set; }

        public string Message { get; set; }

        public string Status { get; set; }

        public int OrderId { get; set; }

        public string OrderNo { get; set; }

        public string MemberName { get; set; }

        public string Telephone { get; set; }

        public double TotalAmount { get; set; }

        public string OrderSource { get; set; }

        public DateTime OrderDate { get; set; }

        public long CreatedDate { get; set; }

        public long LastModifiedDate { get; set; }
    }
}