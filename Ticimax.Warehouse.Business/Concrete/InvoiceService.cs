using HandlebarsDotNet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Utilities.Service;
using Ticimax.Invoice.BLL;
using Ticimax.Invoice.DTO.Messages;
using Ticimax.Invoice.DTO.Models.Invoice;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.InvoiceThemeConcrete.Enums;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class InvoiceService : BaseService, IInvoiceService
    {
        private readonly IInvoiceDal _invoiceDal;
        private readonly IInvoiceThemeService _invoiceThemeService;
        private readonly IInvoiceSettingsService _invoiceSettingsService;
        private readonly ICargoCompanyService _cargoCompanyService;
        private readonly IOrderService _orderService;
        private readonly IOrderPaymentService _orderPaymentService;
        private readonly IOrderProductService _orderProductService;
        private readonly IImageService _imageService;

        public InvoiceService(
            IInvoiceDal invoiceDal
            , IInvoiceThemeService invoiceThemeService
            , IInvoiceSettingsService invoiceSettingsService
            , ICargoCompanyService cargoCompanyService
            , IOrderService orderService
            , IOrderPaymentService orderPaymentService
            , IOrderProductService orderProductService
            , IImageService imageService)
        {
            _invoiceDal = invoiceDal;
            _invoiceThemeService = invoiceThemeService;
            _invoiceSettingsService = invoiceSettingsService;
            _cargoCompanyService = cargoCompanyService;
            _orderService = orderService;
            _orderPaymentService = orderPaymentService;
            _orderProductService = orderProductService;
            _imageService = imageService;
        }

        public async Task<DataResult<InvoiceCreateResponseDto>> CreateInvoice(CreateInvoiceRequestDto request, CancellationToken cancellationToken)
        {
            DataResult<InvoiceCreateResponseDto> response = new DataResult<InvoiceCreateResponseDto>();

            var orderInv = (await _invoiceDal.GetListAsync(new InvoiceFilter { OrderID = request.Order.ID, isCancel = false }, null, cancellationToken)).FirstOrDefault();
            if (orderInv != null && !WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                Base content = orderInv.Content.ToJsonDeserialize<Base>();
                Settings templateSettings = orderInv.TemplateSettings;
                templateSettings.TemplateString = orderInv.Template;

                var renderedContents = GenerateInvoicePdfBySelectPdf(
                    new GenerateInvoicePdfRequest
                    {
                        Invoice = content,
                        Settings = templateSettings
                    });

                response.Model.Invoice = content;
                response.Model.InvoiceHtml = renderedContents;
                return response;
            }

            var invoiceTemplate = (await _invoiceThemeService.GetList(new InvoiceThemeGetListDto { IsDefault = true, Type = InvoiceThemeType.Invoice }, null, cancellationToken)).Model.FirstOrDefault();
            if (invoiceTemplate == null)
                throw new NotFoundException("VarsayilanTemaBulunamadi");

            var template = await _invoiceThemeService.RenderTemplate(
                new InvoiceThemeRenderTemplateDto
                {
                    Css = invoiceTemplate.Css,
                    Html = invoiceTemplate.Html,
                    JavaScript = invoiceTemplate.Javascript,
                    IsUseBootstrap = invoiceTemplate.Settings.IsUseBootstrap
                }, cancellationToken);

            var invoiceSettings = (await _invoiceSettingsService.GetList(new InvoiceSettingsGetListDto(), null, cancellationToken)).Model.FirstOrDefault();
            if (invoiceSettings == null)
                throw new NotFoundException("INVOICE_SETTINGS_IS_NOT_FOUND");

            var cargoCompany = (await _cargoCompanyService.GetList(new CargoCompanyGetListDto { ID = request.Order.CargoCompanyID }, null, cancellationToken)).Model.FirstOrDefault();
            var invoiceClient = new InvoiceClient(new CurrencyInfo { Symbol = request.Order.CurrencyCode });
            var invoice = new Base();
            string series = invoiceSettings.Series;
            int length = invoiceSettings.InvoiceNoLength;
            int padLeftLength = length - series.Length;
            int rankNumber = 0;
            DateTime invoiceDate = DateTime.Now;

            if (request.Order.InvoiceNo?.Length != invoiceSettings.InvoiceNoLength || WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                var seriesInvoice = (await _invoiceDal.GetListAsync(new InvoiceFilter { Series = series }, new InvoicePaging { RecordNumber = 1, SortingValue = "SIRA", SortingDirection = "DESC" }, cancellationToken)).FirstOrDefault();
                rankNumber = (seriesInvoice?.Rank + 1) ?? 1;
                
                var payments = request.Order.Payments.Where(x => x.Approved == 1 && x.Amount > 0);
                double returnPaymentAmount = payments.Sum(x => x.Amount);
                Invoice.DTO.Models.Order.Base order = new Invoice.DTO.Models.Order.Base();
                var additionalAmountSetting = new Invoice.DTO.Models.Order.AdditionalAmountInfo { ApplyAsProduct = true, VatRate = 18 };

                order.OrderNumberBarcode = (await _imageService.CreateBarcode(request.Order.OrderNo)).Image;
                order.OrderIdBarcode = (await _imageService.CreateBarcode(request.Order.ID.ToString())).Image;

                order.InvoiceNumber = string.IsNullOrWhiteSpace(request.Order.InvoiceNo) ? series + rankNumber.ToString().PadLeft(padLeftLength, '0') : request.Order.InvoiceNo;
                order.InvoiceDate = invoiceDate;
                order.MemberId = request.Order.MemberID;
                order.MemberName = request.Order.Customer;
                order.OrderDate = request.Order.Date;
                order.OrderId = request.Order.ID;
                order.OrderNote = request.Order.OrderNote;
                order.OrderAdminNote = request.Order.OrderPanelNote;
                order.OrderNumber = request.Order.OrderNo;
                order.OrderSource = request.Order.OrderSource;
                order.OrderReferenceNumber = "";
                order.CargoCompany = cargoCompany != null ? cargoCompany.Description : "";
                order.CargoTaxNumber = cargoCompany != null ? cargoCompany.CompanyVKN : "";
                order.CargoDesi = request.Order.TotalDesi;

                

                order.CargoCode = request.Order.CargoCode;

                if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                    order.CargoCode = request.Order.CargoPacketID2.ToString();

                order.CargoTrackingNumber = string.IsNullOrEmpty(request.TrackingNumber) ? (await _orderService.GetList(new OrderGetListDto { OrderNo = order.OrderNumber }, null, cancellationToken)).Model.FirstOrDefault()?.CargoTrackingNumber : request.TrackingNumber;

                order.GiftPackageNote = request.Order.GiftPackageNote;
                order.IsExistsGiftPackage = request.Order.isGiftPackage;

                if (!string.IsNullOrEmpty(order.CargoCode))
                    order.CargoCodeBarcode = (await _imageService.CreateBarcode(order.CargoCode)).Image;

                if (!string.IsNullOrEmpty(order.CargoTrackingNumber))
                    order.CargoTrackingNumberBarcode = (await _imageService.CreateBarcode(order.CargoTrackingNumber)).Image;

                order.MarketplaceCampaignCode = request.Order.AdditionalInfo.MarketplaceCampaignCode;
                if (!string.IsNullOrEmpty(order.MarketplaceCampaignCode))
                    order.MarketplaceCampaignCodeBarcode = (await _imageService.CreateBarcode(order.MarketplaceCampaignCode)).Image;

                order.ETTN = request.Order.OrderCode;

                order.InvoiceAddress = new Invoice.DTO.Models.Shared.AddressInfo();
                order.InvoiceAddress.Address = request.Order.InvoiceAddress.Adres;
                order.InvoiceAddress.City = request.Order.InvoiceAddress.Sehir;
                order.InvoiceAddress.District = request.Order.InvoiceAddress.Ilce;
                order.InvoiceAddress.Country = request.Order.InvoiceAddress.Ulke;
                order.InvoiceAddress.Name = request.Order.InvoiceAddress.AliciAdi;
                order.InvoiceAddress.Phone = request.Order.InvoiceAddress.AliciTelefon;
                order.InvoiceAddress.PostalCode = request.Order.InvoiceAddress.PostaKodu;
                order.InvoiceAddress.TaxNumber = request.Order.InvoiceAddress.VergiNo;
                order.InvoiceAddress.TaxOffice = request.Order.InvoiceAddress.VergiDairesi;
                order.InvoiceAddress.FirstName = !string.IsNullOrEmpty(request.Order.InvoiceAddress.AliciAdi) ? request.Order.InvoiceAddress.AliciAdi : request.Order.InvoiceAddress.FirmaAdi;

                order.ShippingAddress = new Invoice.DTO.Models.Shared.AddressInfo();
                order.ShippingAddress.Address = request.Order.DeliveryAddress.Adres;
                order.ShippingAddress.City = request.Order.DeliveryAddress.Sehir;
                order.ShippingAddress.District = request.Order.DeliveryAddress.Ilce;
                order.ShippingAddress.Country = request.Order.DeliveryAddress.Ulke;
                order.ShippingAddress.Name = request.Order.DeliveryAddress.AliciAdi;
                order.ShippingAddress.Phone = request.Order.DeliveryAddress.AliciTelefon;
                order.ShippingAddress.PostalCode = request.Order.DeliveryAddress.PostaKodu;
                order.ShippingAddress.TaxNumber = request.Order.DeliveryAddress.VergiNo;
                order.ShippingAddress.TaxOffice = request.Order.DeliveryAddress.VergiDairesi;

                order.BankComissionAmount = payments.Sum(x => x.BankCommission);
                order.PayAtTheDoorPrice = payments.Sum(x => x.PayDoorAmount);
                order.PaymentDiscountAmount = payments.Sum(x => x.PaymentDiscount);
                order.AmountToBeCharged = payments.Where(x => x.PaymentType == 2 || x.PaymentType == 3).Sum(x => x.Amount) + returnPaymentAmount;
                order.AmountToBeCharged = order.AmountToBeCharged < 0 ? 0 : order.AmountToBeCharged;
                order.CartCampaignDiscountAmount = request.Order.BasketDiscounthAmount;
                order.GiftVoucherAmount = request.Order.GiftVoucherAmount;
                order.GiftPackageAmount = request.Order.GiftPackageAmount;
                order.PaymentType = request.Order.PaymentType;
                order.ShipmentAmount = request.Order.CargoAmount;
                order.TotalAmount = request.Order.TotalAmount;

                order.Products = request.Order.Products.Select(x => new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                {
                    Id = x.ProductID,
                    VatRate = x.KDVRate,
                    UnitPrice = x.Amount,
                    Name = x.ProductName + " " + x.AdditionalOptions,
                    Quantity = x.Piece,
                    SaleUnit = x.SalesUnit,
                    StockCode = x.StockCode,
                    Barcode = x.Barcode,
                    Supplier = x.Supplier,
                    Brand = x.Brand,
                    TotalPrice = x.KDVIncludeAmount
                }).ToList();

                if (additionalAmountSetting.ApplyAsProduct)
                {
                    if (order.BankComissionAmount > 0)
                    {
                        order.Products.Add(new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                        {
                            Name = "Banka Komisyonu",
                            UnitPrice = Math.Round(order.BankComissionAmount / ((100 + additionalAmountSetting.VatRate) / 100f), 2),
                            SaleUnit = "Adet",
                            Quantity = 1,
                            VatRate = additionalAmountSetting.VatRate,
                            IsAdditionalAmount = true,
                        });
                    }

                    if (order.ShipmentAmount > 0)
                    {
                        order.Products.Add(new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                        {
                            Name = "Kargo Ücreti",
                            UnitPrice = Math.Round(order.ShipmentAmount / ((100 + additionalAmountSetting.VatRate) / 100f), 2),
                            SaleUnit = "Adet",
                            Quantity = 1,
                            VatRate = additionalAmountSetting.VatRate,
                            IsAdditionalAmount = true
                        });
                    }

                    if (order.PayAtTheDoorPrice > 0)
                    {
                        order.Products.Add(new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                        {
                            Name = "Kapıda Ödeme Ücreti",
                            UnitPrice = Math.Round(order.PayAtTheDoorPrice / ((100 + additionalAmountSetting.VatRate) / 100f), 2),
                            SaleUnit = "Adet",
                            Quantity = 1,
                            VatRate = additionalAmountSetting.VatRate,
                            IsAdditionalAmount = true
                        });

                        // Bu kısım ödeme yönteminin toplam tutarının, toplam tutara eklenmesi için yapılmıştır. İlgili method tarafından bu işlemin yapılmadığı görülmektedir. 
                        // https://ticimax.atlassian.net/browse/WMS-995 Maddesi ile İki kere fiyatın dahil edilmesi hatası üzerine kaldırılmıştır.
                        //var paymentMethod = order.Products.FirstOrDefault(x => x.Name == "Kapıda Ödeme Ücreti");
                        //double vatRate = paymentMethod.VatRate * 0.01 + 1; // Kdv haric tutarı hesaplamak için KDV oranını bu şekil de düzenliyoruz
                        //var paymentMethodTotalAmount = paymentMethod.UnitPrice * vatRate;
                        //order.TotalAmount = order.TotalAmount + paymentMethodTotalAmount;
                    }

                    if (order.GiftPackageAmount > 0)
                    {
                        order.Products.Add(new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                        {
                            Name = "Hediye Paket Ücreti",
                            UnitPrice = Math.Round(order.GiftPackageAmount / ((100 + additionalAmountSetting.VatRate) / 100f), 2),
                            SaleUnit = "Adet",
                            Quantity = 1,
                            VatRate = additionalAmountSetting.VatRate,
                            IsAdditionalAmount = true
                        });
                    }
                }

                // ayar açık ise ve sipariş de hediye notu var ise irsaliyede fiyat bilgisinin gösterilmesini engelliyoruz...
                if (WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.HediyeIrsaliyeAktif && request.Order.isGiftPackage)
                {
                    order.TotalAmount = 0;
                    order.Products.ForEach(x =>
                    {
                        x.VatIncludedUnitPrice = 0;
                        x.UnitPrice = 0;
                        x.TotalPrice = 0;
                        x.VatRate = 0;
                    });

                    additionalAmountSetting.VatRate = 0;
                }

                invoice = invoiceClient.CreateInvoice(new CreateInvoiceRequest
                {
                    AdditionalAmountSetting = additionalAmountSetting,
                    Order = order,
                    IsCreateCargoBarcode = false,
                    IsCreateMarketplaceBarcode = false,
                    IsCreateCargoDesiBarcode = false,
                    IsCreateOrderBarcode = false
                });
            }
            else
            {
                int rank = request.Order.InvoiceNo.Substring(0, series.Length).ToInt32();
                var orderInvoice = (await _invoiceDal.GetListAsync(new InvoiceFilter { Series = series, OrderID = request.Order.ID, Rank = rank, isCancel = false }, null, cancellationToken)).FirstOrDefault();
                if (orderInvoice != null)
                    invoice = orderInvoice.Content.ToJsonDeserialize<Base>();
                else
                {
                    request.Order.InvoiceNo = "";
                    return await CreateInvoice(new CreateInvoiceRequestDto { Order = request.Order }, cancellationToken);
                }
            }

            int productLimit = 10;
            var renderedContent = string.Empty;
            var invoiceProducts = invoice.Products;
            var settings = new Settings
            {
                PageOrientation = invoiceTemplate.Settings.Landscape ? Invoice.DTO.Models.Shared.Enums.PageOrientation.Landscape : Invoice.DTO.Models.Shared.Enums.PageOrientation.Portrait,
                TemplateString = template.Model.ToString(),
                TemplateType = Invoice.DTO.Models.Shared.Enums.TemplateType.Html,
                MarginBottom = invoiceTemplate.Settings.MarginBottom,
                MarginLeft = invoiceTemplate.Settings.MarginLeft,
                MarginRight = invoiceTemplate.Settings.MarginRight,
                MarginTop = invoiceTemplate.Settings.MarginTop
            };

            if (invoiceProducts != null && invoiceProducts.Count > productLimit)
            {
                var htmlList = new List<string>();
                var loopCount = invoiceProducts.Count / productLimit;
                if (invoiceProducts.Count % productLimit > 0)
                {
                    loopCount++;
                }

                for (int i = 0; i < loopCount; i++)
                {
                    invoice.Products = invoiceProducts.Skip(i * productLimit).Take(productLimit).ToList();
                    invoice.HasNextPage = i + 1 != loopCount;

                    var htmlResponse = GenerateInvoicePdfBySelectPdf(
                        new GenerateInvoicePdfRequest
                        {
                            Invoice = invoice,
                            Settings = settings
                        });

                    htmlList.Add(htmlResponse);
                }

                renderedContent = string.Join(' ', htmlList);
            }
            else
            {
                renderedContent = GenerateInvoicePdfBySelectPdf(
                    new GenerateInvoicePdfRequest
                    {
                        Invoice = invoice,
                        Settings = settings
                    });
            }

            response.Model.Invoice = invoice;
            response.Model.InvoiceHtml = renderedContent;

            if (request.Order.InvoiceNo?.Length != invoiceSettings.InvoiceNoLength)
            {
                settings.TemplateString = null;
                await _invoiceDal.CreateAsync(new Entities.Concrete.Invoice
                {
                    OrderID = request.Order.ID,
                    Content = invoice.ToJsonSerialize(),
                    Date = invoiceDate,
                    Template = template.Model.ToString(),
                    TemplateSettings = settings,
                    Rank = rankNumber,
                    Series = series,
                    isCancel = false,
                }, cancellationToken);
            }

            return response;
        }

        public async Task<DataResult<List<Entities.Concrete.Invoice>>> GetList(InvoiceGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<Entities.Concrete.Invoice>> response = new DataResult<List<Entities.Concrete.Invoice>>();

            response.Model = (await _invoiceDal.GetListAsync(request.ToFilter(), paging != null ? new InvoicePaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();
            if (request.isGetCount)
                response.Count = (await GetCount(request, cancellationToken)).Model;

            return response;
        }

        public async Task<DataResult<int>> GetCount(InvoiceGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _invoiceDal.GetCountAsync(request.ToFilter(), cancellationToken);

            return response;
        }

        public async Task<DataResult<InvoiceCreateResponseDto>> ReCreateInvoice(ReOrderInvoicePrintDto request, CancellationToken cancellationToken)
        {
            bool isOrderNotFound = false;
            Order order = null;

            order = (await _orderService.GetList(new OrderGetListDto { OrderID = request.OrderID }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
                isOrderNotFound = true;
            else if (order.PackagingStatusID != (int)PackageStatus.FaturaKesildi)
                throw new BusinessException("FaturasiKesilmemisSiparisteBuIslemiYapamazsiniz");

            var orderInvoice = (await _invoiceDal.GetListAsync(new InvoiceFilter { OrderID = request.OrderID, isCancel = false }, new InvoicePaging { RecordNumber = 1, SortingDirection = "DESC", SortingValue = "ID" }, cancellationToken)).FirstOrDefault();
            if (orderInvoice == null)
                throw new BusinessException("FaturasiKesilmemisSiparisteBuIslemiYapamazsiniz");

            if (order != null)
            {
                order.Payments = (await _orderPaymentService.GetList(new OrderPaymentGetListDto { OrderID = request.OrderID, isApproved = true }, cancellationToken)).Model;
                order.Products = (await _orderProductService.GetProductList(new Core.Order.Entities.Filters.OrderProductFilter { OrderID = request.OrderID }, cancellationToken)).Model.Where(x => x.Status != 2).ToList();
            }

            if (isOrderNotFound)
                throw new NotFoundException("SiparisBulunamadi");

            DataResult<InvoiceCreateResponseDto> response = new DataResult<InvoiceCreateResponseDto>();

            var invoiceTemplate = (await _invoiceThemeService.GetList(new InvoiceThemeGetListDto { IsDefault = true, Type = InvoiceThemeType.Invoice }, null, cancellationToken)).Model.FirstOrDefault();
            if (invoiceTemplate == null)
                throw new NotFoundException("VarsayilanTemaBulunamadi");

            await _invoiceDal.UpdateCancelAsync(new Entities.Concrete.Invoice { OrderID = order.ID, isCancel = true }, cancellationToken);

            var template = await _invoiceThemeService.RenderTemplate(
                new InvoiceThemeRenderTemplateDto
                {
                    Css = invoiceTemplate.Css,
                    Html = invoiceTemplate.Html,
                    JavaScript = invoiceTemplate.Javascript,
                    IsUseBootstrap = invoiceTemplate.Settings.IsUseBootstrap
                }, cancellationToken);

            var invoiceSettings = (await _invoiceSettingsService.GetList(new InvoiceSettingsGetListDto(), null, cancellationToken)).Model.FirstOrDefault();
            var cargoCompany = (await _cargoCompanyService.GetList(
                new CargoCompanyGetListDto
                {
                    ID = order.CargoCompanyID
                }, null, cancellationToken)).Model.FirstOrDefault();

            var invoiceClient = new InvoiceClient(new CurrencyInfo { Symbol = order.CurrencyCode });
            var invoice = new Base();

            string series = invoiceSettings.Series;
            int length = invoiceSettings.InvoiceNoLength;
            int padLeftLength = length - series.Length;
            int rankNumber;

            DateTime invoiceDate = DateTime.Now;

            var seriesInvoice = (await _invoiceDal.GetListAsync(new InvoiceFilter { Series = series, isCancel = false }, new InvoicePaging { RecordNumber = 1, SortingDirection = "DESC", SortingValue = "SIRA" }, cancellationToken)).FirstOrDefault();

            rankNumber = (seriesInvoice?.Rank + 1 ?? 1);

            if (order.Payments == null || order.Payments.Count == 0)
                throw new BusinessException("OdemesiYapilmamisSiparisinFaturasiOlusturulamaz");

            var payments = order.Payments.Where(x => x.Approved == 1 && x.Amount > 0);

            double returnPaymentAmount = payments.Sum(x => x.Amount);
            Invoice.DTO.Models.Order.Base baseOrder = new Invoice.DTO.Models.Order.Base();
            var additionalAmountSetting = new Invoice.DTO.Models.Order.AdditionalAmountInfo { ApplyAsProduct = true, VatRate = 18 };

            baseOrder.OrderNumberBarcode = (await _imageService.CreateBarcode(order.OrderNo)).Image;
            baseOrder.OrderIdBarcode = (await _imageService.CreateBarcode(order.ID.ToString())).Image;

            baseOrder.InvoiceNumber = series + rankNumber.ToString().PadLeft(padLeftLength, '0');
            baseOrder.InvoiceDate = invoiceDate;
            baseOrder.MemberId = order.MemberID;
            baseOrder.MemberName = order.Customer;
            baseOrder.OrderDate = order.Date;
            baseOrder.OrderId = order.ID;
            baseOrder.OrderNote = order.OrderNote;
            baseOrder.OrderAdminNote = order.OrderPanelNote;
            baseOrder.OrderNumber = order.OrderNo;
            baseOrder.OrderSource = order.OrderSource;
            baseOrder.OrderReferenceNumber = "";
            baseOrder.CargoCompany = cargoCompany != null ? cargoCompany.Description : "";
            baseOrder.CargoTaxNumber = cargoCompany != null ? cargoCompany.CompanyVKN : "";
            baseOrder.CargoDesi = order.TotalDesi;
            baseOrder.CargoCode = order.CargoCode;

            baseOrder.CargoTrackingNumber = (await _orderService.GetList(new OrderGetListDto { OrderNo = baseOrder.OrderNumber }, null, cancellationToken)).Model.FirstOrDefault()?.CargoTrackingNumber;

            baseOrder.GiftPackageNote = order.GiftPackageNote;
            baseOrder.IsExistsGiftPackage = order.isGiftPackage;

            if (!string.IsNullOrEmpty(baseOrder.CargoCode))
                baseOrder.CargoCodeBarcode = _imageService.CreateBarcode(order.CargoCode).GetAwaiter().GetResult().Image;

            if (!string.IsNullOrEmpty(baseOrder.CargoTrackingNumber))
                baseOrder.CargoTrackingNumberBarcode = _imageService.CreateBarcode(order.CargoTrackingNumber).GetAwaiter().GetResult().Image;

            baseOrder.MarketplaceCampaignCode = order.AdditionalInfo.MarketplaceCampaignCode;
            if (!string.IsNullOrEmpty(baseOrder.MarketplaceCampaignCode))
                baseOrder.MarketplaceCampaignCodeBarcode = _imageService.CreateBarcode(order.AdditionalInfo.MarketplaceCampaignCode).GetAwaiter().GetResult().Image;

            baseOrder.ETTN = order.OrderCode;

            baseOrder.InvoiceAddress = new Invoice.DTO.Models.Shared.AddressInfo();
            baseOrder.InvoiceAddress.Address = order.InvoiceAddress.Adres;
            baseOrder.InvoiceAddress.City = order.InvoiceAddress.Sehir;
            baseOrder.InvoiceAddress.District = order.InvoiceAddress.Ilce;
            baseOrder.InvoiceAddress.Country = order.InvoiceAddress.Ulke;
            baseOrder.InvoiceAddress.Name = order.InvoiceAddress.AliciAdi;
            baseOrder.InvoiceAddress.Phone = order.InvoiceAddress.AliciTelefon;
            baseOrder.InvoiceAddress.PostalCode = order.InvoiceAddress.PostaKodu;
            baseOrder.InvoiceAddress.TaxNumber = order.InvoiceAddress.VergiNo;
            baseOrder.InvoiceAddress.TaxOffice = order.InvoiceAddress.VergiDairesi;
            baseOrder.InvoiceAddress.FirstName = !string.IsNullOrEmpty(order.InvoiceAddress.AliciAdi) ? order.InvoiceAddress.AliciAdi : order.InvoiceAddress.FirmaAdi;

            baseOrder.ShippingAddress = new Invoice.DTO.Models.Shared.AddressInfo();
            baseOrder.ShippingAddress.Address = order.DeliveryAddress.Adres;
            baseOrder.ShippingAddress.City = order.DeliveryAddress.Sehir;
            baseOrder.ShippingAddress.District = order.DeliveryAddress.Ilce;
            baseOrder.ShippingAddress.Country = order.DeliveryAddress.Ulke;
            baseOrder.ShippingAddress.Name = order.DeliveryAddress.AliciAdi;
            baseOrder.ShippingAddress.Phone = order.DeliveryAddress.AliciTelefon;
            baseOrder.ShippingAddress.PostalCode = order.DeliveryAddress.PostaKodu;
            baseOrder.ShippingAddress.TaxNumber = order.DeliveryAddress.VergiNo;
            baseOrder.ShippingAddress.TaxOffice = order.DeliveryAddress.VergiDairesi;

            baseOrder.BankComissionAmount = payments.Sum(x => x.BankCommission);
            baseOrder.PayAtTheDoorPrice = payments.Sum(x => x.PayDoorAmount);
            baseOrder.PaymentDiscountAmount = payments.Sum(x => x.PaymentDiscount);
            baseOrder.AmountToBeCharged = payments.Where(x => x.PaymentType == 2 || x.PaymentType == 3).Sum(x => x.Amount) + returnPaymentAmount;
            baseOrder.AmountToBeCharged = baseOrder.AmountToBeCharged < 0 ? 0 : baseOrder.AmountToBeCharged;
            baseOrder.CartCampaignDiscountAmount = order.BasketDiscounthAmount;
            baseOrder.GiftVoucherAmount = order.GiftVoucherAmount;
            baseOrder.GiftPackageAmount = order.GiftPackageAmount;
            baseOrder.PaymentType = order.PaymentType;
            baseOrder.ShipmentAmount = order.CargoAmount;
            baseOrder.TotalAmount = order.TotalAmount;

            baseOrder.Products = order.Products.Select(x => new Ticimax.Invoice.DTO.Models.Order.ProductInfo
            {
                Id = x.ProductID,
                VatRate = x.KDVRate,
                UnitPrice = x.Amount,
                Name = x.ProductName + " " + x.AdditionalOptions,
                Quantity = x.Piece,
                SaleUnit = x.SalesUnit,
                StockCode = x.StockCode,
                Barcode = x.Barcode,
                Supplier = x.Supplier,
                Brand = x.Brand
            }).ToList();

            if (additionalAmountSetting.ApplyAsProduct)
            {
                if (baseOrder.BankComissionAmount > 0)
                {
                    baseOrder.Products.Add(new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                    {
                        Name = "Banka Komisyonu",
                        UnitPrice = Math.Round(baseOrder.BankComissionAmount / ((100 + additionalAmountSetting.VatRate) / 100f), 2),
                        SaleUnit = "Adet",
                        Quantity = 1,
                        VatRate = additionalAmountSetting.VatRate,
                        IsAdditionalAmount = true
                    });
                }

                if (baseOrder.ShipmentAmount > 0)
                {
                    baseOrder.Products.Add(new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                    {
                        Name = "Kargo Ücreti",
                        UnitPrice = Math.Round(baseOrder.ShipmentAmount / ((100 + additionalAmountSetting.VatRate) / 100f), 2),
                        SaleUnit = "Adet",
                        Quantity = 1,
                        VatRate = additionalAmountSetting.VatRate,
                        IsAdditionalAmount = true
                    });
                }

                if (baseOrder.PayAtTheDoorPrice > 0)
                {
                    baseOrder.Products.Add(new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                    {
                        Name = "Kapıda Ödeme Ücreti",
                        UnitPrice = Math.Round(baseOrder.PayAtTheDoorPrice / ((100 + additionalAmountSetting.VatRate) / 100f), 2),
                        SaleUnit = "Adet",
                        Quantity = 1,
                        VatRate = additionalAmountSetting.VatRate,
                        IsAdditionalAmount = true
                    });

                    // Bu kısım ödeme yönteminin toplam tutarının, toplam tutara eklenmesi için yapılmıştır. İlgili method tarafından bu işlemin yapılmadığı görülmektedir. 
                    var paymentMethod = baseOrder.Products.Where(x => x.Name == "Kapıda Ödeme Ücreti").FirstOrDefault();
                    double vatRate = paymentMethod.VatRate * 0.01 + 1; // Kdv haric tutarı hesaplamak için KDV oranını bu şekil de düzenliyoruz
                    var paymentMethodTotalAmount = paymentMethod.UnitPrice * vatRate;
                    baseOrder.TotalAmount = baseOrder.TotalAmount + paymentMethodTotalAmount;
                }

                if (baseOrder.GiftPackageAmount > 0)
                {
                    baseOrder.Products.Add(new Ticimax.Invoice.DTO.Models.Order.ProductInfo
                    {
                        Name = "Hediye Paket Ücreti",
                        UnitPrice = Math.Round(baseOrder.GiftPackageAmount / ((100 + additionalAmountSetting.VatRate) / 100f), 2),
                        SaleUnit = "Adet",
                        Quantity = 1,
                        VatRate = additionalAmountSetting.VatRate,
                        IsAdditionalAmount = true
                    });
                }
            }

            invoice = invoiceClient.CreateInvoice(new CreateInvoiceRequest
            {
                AdditionalAmountSetting = additionalAmountSetting,
                Order = baseOrder,
                IsCreateCargoBarcode = false,
                IsCreateMarketplaceBarcode = false,
                IsCreateCargoDesiBarcode = false,
                IsCreateOrderBarcode = false
            });

            int productLimit = 10;
            var renderedContent = string.Empty;
            var invoiceProducts = invoice.Products;
            var settings = new Settings
            {
                PageOrientation = invoiceTemplate.Settings.Landscape ? Invoice.DTO.Models.Shared.Enums.PageOrientation.Landscape : Invoice.DTO.Models.Shared.Enums.PageOrientation.Portrait,
                TemplateString = template.Model.ToString(),
                TemplateType = Invoice.DTO.Models.Shared.Enums.TemplateType.Html,
                MarginBottom = invoiceTemplate.Settings.MarginBottom,
                MarginLeft = invoiceTemplate.Settings.MarginLeft,
                MarginRight = invoiceTemplate.Settings.MarginRight,
                MarginTop = invoiceTemplate.Settings.MarginTop
            };

            if (invoiceProducts != null && invoiceProducts.Count > productLimit)
            {
                var htmlList = new List<string>();
                var loopCount = invoiceProducts.Count / productLimit;
                if (invoiceProducts.Count % productLimit > 0)
                {
                    loopCount++;
                }

                for (int i = 0; i < loopCount; i++)
                {
                    invoice.Products = invoiceProducts.Skip(i * productLimit).Take(productLimit).ToList();
                    invoice.HasNextPage = i + 1 != loopCount;

                    var htmlContent = GenerateInvoicePdfBySelectPdf(
                        new GenerateInvoicePdfRequest
                        {
                            Invoice = invoice,
                            Settings = settings
                        });

                    htmlList.Add(htmlContent);
                }

                renderedContent = string.Join(' ', htmlList);
            }
            else
            {
                renderedContent = GenerateInvoicePdfBySelectPdf(
                    new GenerateInvoicePdfRequest
                    {
                        Invoice = invoice,
                        Settings = settings
                    });
            }

            response.Model.Invoice = invoice;
            response.Model.InvoiceHtml = renderedContent;

            settings.TemplateString = null;
            await _invoiceDal.CreateAsync(new Entities.Concrete.Invoice
            {
                OrderID = request.OrderID,
                Content = invoice.ToJsonSerialize(),
                Date = invoiceDate,
                Template = template.Model.ToString(),
                TemplateSettings = settings,
                Series = series,
                isCancel = false,
            }, cancellationToken);

            return response;
        }


        private string GenerateInvoicePdfBySelectPdf(GenerateInvoicePdfRequest request)
        {
            var settings = request.Settings;

            Handlebars.RegisterHelper("dateFormat", (writer, context, args) =>
            {
                writer.WriteSafeString(args[0].ParseToDateTime().ToString("dd.MM.yyyy HH:mm"));
            });

            Handlebars.RegisterHelper("currencyFormat", (writer, context, args) =>
            {
                writer.WriteSafeString(Math.Round(args[0].ToDouble(), 2));
            });

            Func<object, string> template = Handlebars.Compile(settings.TemplateString);

            if (string.IsNullOrEmpty(request.Invoice.CurrencyInfo.Symbol))
                request.Invoice.CurrencyInfo.Symbol = "₺";

            string renderedContent = template(request.Invoice);
            string js = "<script type='text/javascript'> window.onload = function () { window.print() } </script>";

            return renderedContent + js;
        }

    }
}