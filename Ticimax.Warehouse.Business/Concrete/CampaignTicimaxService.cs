using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class CampaignTicimaxService : BaseService, ICampaignTicimaxService
    {
        private readonly ICampaignTicimaxDal _campaignTicimaxDal;

        public CampaignTicimaxService(ICampaignTicimaxDal campaignTicimaxDal)
        {
            _campaignTicimaxDal = campaignTicimaxDal;
        }

        public async Task<DataResult<int>> GetCount(CampaignTicimaxFilter filter, CancellationToken cancellationToken)
        {
            return new DataResult<int> { Model = await _campaignTicimaxDal.GetCountAsync(filter, cancellationToken) };
        }

        public async Task<DataResult<List<CampaignTicimaxDto>>> GetList(CampaignTicimaxFilter filter, CampaignTicimaxPaging paging, CancellationToken cancellationToken)
        {
            var response = new DataResult<List<CampaignTicimaxDto>>();

            var list = await _campaignTicimaxDal.GetListAsync(filter, paging, cancellationToken);
            if (filter?.isGetCount ?? false)
                response.Count = list.Count;

            return response;
        }
    }
}