using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class DynamicFormFileService : BaseService, IDynamicFormFileService
    {
        private readonly IDynamicFormFileDal _dynamicFormFileDal;

        public DynamicFormFileService(IDynamicFormFileDal dynamicFormFileDal)
        {
            _dynamicFormFileDal = dynamicFormFileDal;
        }

        public async Task<DataResult<int>> GetCount(DynamicFormFileGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _dynamicFormFileDal.GetCountAsync(request.ToFilter(), cancellationToken);

            return response;
        }

        public async Task<DataResult<List<DynamicFormFile>>> GetList(DynamicFormFileGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<DynamicFormFile>> response = new DataResult<List<DynamicFormFile>>();

            response.Model = (await _dynamicFormFileDal.GetListAsync(request.ToFilter(), paging != null ? new DynamicFormFilePaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken )).ToList();
            if (request.isGetCount)
                response.Count = (await GetCount(request, cancellationToken)).Model;

            return response;
        }
    }
}