using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Concrete.Report.ProductExtraction.ValueObjects;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Events
{
    public class WarehouseProductTransferFileEventPayload : BaseDomainEvent
    {
        public WarehouseProductTransferFileEventPayload(WarehouseProductTransferFileAggregate aggregate)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            FileNo = aggregate.FileNo;
            Name = aggregate.Name;
            Note = aggregate.Note;
            WarehouseCarId = aggregate.WarehouseCarId;
            UserId = aggregate.UserId;
            TotalProductQuantity = aggregate.TotalProductQuantity;
            Status = aggregate.Status;
            StoreId = aggregate.StoreId;
            WarehouseId = aggregate.WarehouseId;
            TransferStoreId = aggregate.TransferStoreId;
            TransferWarehouseId = aggregate.TransferWarehouseId;
        }

        public string DomainName { get; set; }
        public string FileNo { get; set; }
        public string Name { get; set; }
        public string Note { get; set; }
        public int WarehouseCarId { get; set; }
        public int UserId { get; set; }
        public double TotalProductQuantity { get; set; }
        public string Status { get; set; }
        public int StoreId { get; set; }
        public int WarehouseId { get; set; }
        public int TransferStoreId { get; set; }
        public int TransferWarehouseId { get; set; }
        public List<ProductMovementStockControlInfo> ProductMovementStockControlInfo { get; set; }
    }
}
