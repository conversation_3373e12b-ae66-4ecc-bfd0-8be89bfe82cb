using iText.Layout.Properties;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.CrossCuttingConcerns.Caching;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums;
using Ticimax.Core.Product.Entities.Dtos;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Models.Requests;
using Ticimax.Warehouse.Business.Concrete.Report.Enum;
using Ticimax.Warehouse.Business.Concrete.Report.ProductExtraction.ValueObjects;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Enums;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Events;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Models.Requests;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Models.Responses;
using Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.File;
using Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.Item;
using Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.Movement;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File.Enums;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Item;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Movement;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer
{
#nullable enable
    public class WarehouseProductTransferService : IWarehouseProductTransferService
    {
        private readonly IWarehouseProductTransferFileDal _warehouseProductTransferFileDal;
        private readonly IWarehouseProductTransferItemDal _warehouseProductTransferItemDal;
        private readonly IWarehouseProductTransferMovementDal _warehouseProductTransferMovementDal;
        private readonly IWarehouseService _warehouseService;
        private readonly IPickingProductService _pickingProductService;
        private readonly IShelfProductService _shelfProductService;
        private readonly IWarehouseCarProductService _warehouseCarProductService;
        private readonly IShelfService _shelfService;
        private readonly IProductService _productService;
        private readonly IProductMovementService _productMovementService;
        private readonly ICacheManager _cacheManager;

        public WarehouseProductTransferService(
            IWarehouseProductTransferFileDal warehouseProductTransferFileDal,
            IWarehouseProductTransferItemDal warehouseProductTransferItemDal,
            IWarehouseProductTransferMovementDal warehouseProductTransferMovementDal,
            IWarehouseService warehouseService,
            IPickingProductService pickingProductService,
            IShelfProductService shelfProductService,
            IWarehouseCarProductService warehouseCarProductService,
            IShelfService shelfService,
            IProductService productService, IProductMovementService productMovementService, ICacheManager cacheManager)
        {
            _warehouseProductTransferFileDal = warehouseProductTransferFileDal;
            _warehouseProductTransferItemDal = warehouseProductTransferItemDal;
            _warehouseProductTransferMovementDal = warehouseProductTransferMovementDal;
            _warehouseService = warehouseService;
            _pickingProductService = pickingProductService;
            _shelfProductService = shelfProductService;
            _warehouseCarProductService = warehouseCarProductService;
            _shelfService = shelfService;
            _productService = productService;
            _productMovementService = productMovementService;
            _cacheManager = cacheManager;
        }

        public async Task<WarehouseProductTransferFileAggregate> GetById(Guid id, CancellationToken cancellationToken)
        {
            var aggregate = await _warehouseProductTransferFileDal.GetById(id, cancellationToken);
            if (aggregate == null)
                throw new NotFoundException("WAREHOUSE_PRODUCT_TRANSFER_FILE_IS_NOT_FOUND", new KeyValuePair<string, string>("id", id.ToString()));

            return aggregate;
        }

        public async Task<WarehouseProductTransferFileAggregate> CreateFile(CreateWarehouseProductTransferRequest request, CancellationToken cancellationToken)
        {
            var id = Guid.NewGuid();

            var warehouse = (await _warehouseService.GetList(new WarehouseGetListDto
            { ID = request.WarehouseId }, null, cancellationToken)).Model.FirstOrDefault();

            if (warehouse == null)
                throw new BusinessException("WAREHOUSE_NOT_FOUND", new KeyValuePair<string, string>("warehouseId", request.WarehouseId.ToString()));

            var transferWarehouse = (await _warehouseService.GetList(new WarehouseGetListDto
            { ID = request.TransferWarehouseId }, null, cancellationToken)).Model.FirstOrDefault();

            if (transferWarehouse == null)
                throw new BusinessException("TRANSFER_WAREHOUSE_NOT_FOUND", new KeyValuePair<string, string>("warehouseId", request.TransferWarehouseId.ToString()));

            if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.StockControlInTransfer)
            {
                var products = await _productService.GetListAsync(new ProductGetListDto() { IDList = request.Products.Select(x => x.ProductId).Distinct().ToList(), AddSubQueries = false });
                List<string> stokErrorProducts = new List<string>();
                foreach (var product in request.Products.ToList())
                {
                    var dbProduct = products.Where(x => x.ID == product.ProductId).FirstOrDefault();
                    if (dbProduct == null)
                        throw new BusinessException("PRODUCT_NOT_FOUND");

                    if (dbProduct.StockPiece < product.Quantity)
                    {
                        stokErrorProducts.Add(dbProduct.Barcode);
                        request.Products.Remove(product);
                    }
                }
                if (stokErrorProducts.Count > 0)
                    await _warehouseProductTransferMovementDal.Create(new WarehouseProductTransferMovementAggregate(id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, $"{string.Join(",", stokErrorProducts)} ürünlerini transfer adedi kadar stok olmadığı için alamadım.", true), cancellationToken);

            }

            var assignedProducts = await _pickingProductService.DoAssignShelf(request.Products.Select(x => new AllocateProductRequest(x.ProductId, x.Quantity, warehouse.ID)).ToList(), cancellationToken);

            var file = new WarehouseProductTransferFileAggregate(id, request.Name, request.Note, assignedProducts.Where(x => x.ShelfId != 0).Sum(x => x.Quantity), warehouse.StoreID,
                            warehouse.ID, transferWarehouse.StoreID, transferWarehouse.ID, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name);
            var items = assignedProducts.Where(x => x.ShelfId != 0).Select(x => new WarehouseProductTransferItemAggregate(id, x.ProductId, x.Quantity, x.ShelfId)).ToList();

            var notAssignProductIds = assignedProducts.Where(x => x.ShelfId == 0).Select(x => x.ProductId).Distinct().ToList();
            if (request.Products.Count == notAssignProductIds.Count || items.Count == 0)
                throw new BusinessException("SELECTED_PRODUCTS_NOT_HAVE_TO_SELECTED_WAREHOUSE");

            await _warehouseProductTransferFileDal.Create(file, cancellationToken);
            await _warehouseProductTransferItemDal.CreateBulk(items, cancellationToken);
            var productsOldList = await _productService.GetList(new ProductGetListDto() { IDList = items.Select(x => x.ProductId).ToList(), AddSubQueries = false });
            List<ProductMovementStockControlInfo> productMovementStockList = new List<ProductMovementStockControlInfo>();

            var shelfs = await _shelfService.GetList(new ShelfGetListDto() { IDList = items.Select(x => x.ShelfId).ToList() });

            var groupedItems = items.GroupBy(x => new { x.ShelfId, x.ProductId }).Select(group => new
            {
                ProductId = group.Key.ProductId,
                ShelfId = group.Key.ShelfId,
                Quantity = group.Sum(x => x.Quantity)
            });


            foreach (var item in groupedItems)
            {
                string shelfName = "";
                var shelf = shelfs.Model.FirstOrDefault(x => x.ID == item.ShelfId);
                if (shelf != null)
                    shelfName = shelf.Definition;
                var storeId = WebSiteInfo.User.Value.IsOneStore ? 0 : warehouse.StoreID;
                await _productService.ReduceStock(new ProductUpdateStockDto() { ConsignmentModuleActive = WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive, ProductID = item.ProductId, Piece = item.Quantity, StoreID = storeId }, cancellationToken);
                await _productMovementService.CreateMovement(item.ProductId, new CreateProductMovementRequest(ProductMovementProcessType.Allocated, item.Quantity, ProductMovementMessage.AllocatedForWarehouseTransfer(shelfName, file.FileNo), null), cancellationToken);
            }
            await _warehouseProductTransferMovementDal.Create(new WarehouseProductTransferMovementAggregate(id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, "Talep Dosyası oluşturdum.", true), cancellationToken);
            if (notAssignProductIds.Count > 0)
                await _warehouseProductTransferMovementDal.Create(new WarehouseProductTransferMovementAggregate(id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, $"{string.Join(',', notAssignProductIds)} Id'li ürünler rafta bulunamadığı için talebe eklenemedi.", true), cancellationToken);


            var groupResult = items.GroupBy(x => x.ProductId).Select(group => new { ProductId = group.Key, TotalTransferQuantity = group.Sum(x => x.Quantity) });


            foreach (var item in groupResult)
            {
                var oldProduct = productsOldList.Model.FirstOrDefault(x => x.ID == item.ProductId);
                if (oldProduct != null)
                {
                    var shelfStock = (await _shelfProductService.GetList(new ShelfProductGetListDto() { ProductID = oldProduct.ID, WarehouseID = WebSiteInfo.User.Value.WarehouseID }, null, cancellationToken)).Model.Sum(x => x.ShelfStock);

                    ProductMovementStockControlInfo stockInfo = new ProductMovementStockControlInfo();
                    stockInfo.Piece = item.TotalTransferQuantity;
                    stockInfo.WebStock = oldProduct.StockPiece;
                    stockInfo.ConsigmentStock = oldProduct.ConsignmentStockPiece;
                    stockInfo.ShelfStock = shelfStock - item.TotalTransferQuantity;
                    stockInfo.OldWebStock = oldProduct.StockPiece;
                    stockInfo.OldConsigmentStock = oldProduct.ConsignmentStockPiece;
                    stockInfo.OldShelfStock = shelfStock;
                    stockInfo.CreatedDate = DateTime.Now.ToTimestamp();
                    stockInfo.Type = ProductMovementStockControlTypeEnum.WarehouseTransferStockReduce.ToString();
                    stockInfo.ObjectId = file.FileNo;
                    stockInfo.ProductId = oldProduct.ID;
                    stockInfo.ProductName = oldProduct.ProductName;
                    stockInfo.DomainName = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.DomainName : "";
                    stockInfo.StoreId = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.StoreID : 0;
                    stockInfo.WarehouseId = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.WarehouseID : 0;
                    productMovementStockList.Add(stockInfo);
                }
            }

            var fileEvent = new WarehouseProductTransferFileEventPayload(file);
            fileEvent.ProductMovementStockControlInfo = productMovementStockList;
            WebSiteInfo.User.Value.Events.Add(new DomainEvent(WarehouseProductTransferFileEvents.Created, fileEvent));

            return file;
        }

        public async Task<WarehouseProductTransferFileAggregate> UpdateFile(Guid id, UpdateWarehouseProductTransferRequest request, CancellationToken cancellationToken)
        {
            var aggregate = await GetById(id, cancellationToken);

            aggregate.Update(request.Name, request.Note);

            await _warehouseProductTransferFileDal.Update(aggregate, cancellationToken);
            return aggregate;
        }

        public async Task UpdateFileStatus(Guid id, string status, CancellationToken cancellationToken)
        {
            var aggregate = await GetById(id, cancellationToken);

            aggregate.ChangeStatus(status);

            if (status == WarehouseProductTransferStatus.Cancelled)
            {
                var items = await _warehouseProductTransferItemDal.Get(id, null, null, null, null, null, null, int.MaxValue, 0, cancellationToken);
                var notCompletedItems = items.Where(x => x.Quantity - x.PickQuantity > 0).ToList();


                //Tamamlanmayan itemları raflara geri ekliyoruz.
                await _shelfProductService.Add(new ShelfProductAddDto
                {
                    Type = ProductMovementStockControlTypeEnum.WarehouseTransferCanceledStockAddExclusion.ToString(),
                    ObjectId = aggregate.FileNo,
                    //Depolar arası transfer dosyası oluştururken IsOneStore true ise websitestock'dan düşüyor.
                    //Bu sebeple iptal edildiğinde de IsOneStore true ise websitestock'a geri eklemeli.

                    //WarehouseTransferWebStockReduce ayarı açıksa transfer dosyası oluşurken her şekilde web stoktan düşüyor. Bu sebeple iptal edilirkende aynı senaryoyu uyguluyoruz
                    AddStockWebSite = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.WarehouseTransferWebStockReduce ? true : WebSiteInfo.User.Value.IsOneStore,
                    ShelfItems = notCompletedItems.Select(x => new ShelfProductAddItemDto
                    {
                        ShelfID = x.ShelfId,
                        ProductID = x.ProductId,
                        ShelfStock = x.Quantity - x.PickQuantity,
                        WarehouseID = aggregate.WarehouseId
                    }).ToList()
                }, cancellationToken);

                var groupResult = notCompletedItems.GroupBy(x => x.ProductId).Select(group => new { ProductId = group.Key, TotalTransferCancelQuantity = group.Sum(x => x.Quantity - x.PickQuantity) });
                List<ProductMovementStockControlInfo> productMovementStockList = new List<ProductMovementStockControlInfo>();
                var productsOldList = await _productService.GetList(new ProductGetListDto() { IDList = notCompletedItems.Select(x => x.ProductId).ToList(), AddSubQueries = false });

                foreach (var item in groupResult)
                {
                    var oldProduct = productsOldList.Model.FirstOrDefault(x => x.ID == item.ProductId);
                    if (oldProduct != null)
                    {
                        var shelfStock = (await _shelfProductService.GetList(new ShelfProductGetListDto() { ProductID = oldProduct.ID, WarehouseID = WebSiteInfo.User.Value.WarehouseID }, null, cancellationToken)).Model.Sum(x => x.ShelfStock);

                        ProductMovementStockControlInfo stockInfo = new ProductMovementStockControlInfo();
                        stockInfo.Piece = item.TotalTransferCancelQuantity;
                        stockInfo.WebStock = oldProduct.StockPiece;
                        stockInfo.ConsigmentStock = oldProduct.ConsignmentStockPiece;
                        stockInfo.ShelfStock = shelfStock + item.TotalTransferCancelQuantity;
                        stockInfo.OldWebStock = oldProduct.StockPiece;
                        stockInfo.OldConsigmentStock = oldProduct.ConsignmentStockPiece;
                        stockInfo.OldShelfStock = shelfStock;
                        stockInfo.CreatedDate = DateTime.Now.ToTimestamp();
                        stockInfo.Type = ProductMovementStockControlTypeEnum.WarehouseTransferCanceledStockAdd.ToString();
                        stockInfo.ObjectId = aggregate.FileNo;
                        stockInfo.ProductId = oldProduct.ID;
                        stockInfo.ProductName = oldProduct.ProductName;
                        stockInfo.DomainName = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.DomainName : "";
                        stockInfo.StoreId = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.StoreID : 0;
                        stockInfo.WarehouseId = WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.WarehouseID : 0;
                        productMovementStockList.Add(stockInfo);
                    }
                }
                WebSiteInfo.User.Value.Events.Add(new DomainEvent(WarehouseProductTransferFileEvents.Canceled, new WarehouseProductTransferFileCanceledEventPayload(productMovementStockList)));

            }

            await _warehouseProductTransferFileDal.Update(aggregate, cancellationToken);
            await _warehouseProductTransferMovementDal.Create(new WarehouseProductTransferMovementAggregate(aggregate.Id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, "Dosyanın statüsünü güncelledim.", true), cancellationToken);
        }

        public async Task<Pageable<WarehouseProductTransferFileAggregate>> Get(string? name, string? fileNo, string? status, int? createdUserId, string? productIds, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            List<int> productIdList = null;
            if (!string.IsNullOrEmpty(productIds))
            {
                productIdList = new List<int>();
                var productIdStrList = productIds.Split(',');
                productIdList.AddRange(productIdStrList.Select(x => x.ToInt32()));
            }

            var contents = await _warehouseProductTransferFileDal.Get(name, fileNo, status, createdUserId, productIdList, null, null, pageSize, pageIndex, cancellationToken);
            var count = await _warehouseProductTransferFileDal.Count(name, fileNo, status, createdUserId, productIdList, null, null, false, cancellationToken);
            if (!string.IsNullOrEmpty(fileNo))
            {
                var file = contents.FirstOrDefault();
                if(file != null)
                {
                    var fileItems = await _warehouseProductTransferItemDal.Get(file.Id, null, null, null, null, null,
                                        null, int.MaxValue, 1, cancellationToken);
                    file.IsMissingProductContains = fileItems.Any(x => x.MissingQuantity > 0);
                }
            }

            return new Pageable<WarehouseProductTransferFileAggregate>(pageIndex, pageSize, count, contents);
        }


        public async Task<int> Count(string? name, string? fileNo, string? status, int? createdUserId, string? productIds, CancellationToken cancellationToken)
        {
            List<int> productIdList = null;
            if (!string.IsNullOrEmpty(productIds))
            {
                productIdList = new List<int>();
                var productIdStrList = productIds.Split(',');
                productIdList.AddRange(productIdStrList.Select(x => x.ToInt32()));
            }
            return await _warehouseProductTransferFileDal.Count(name, fileNo, status, createdUserId, productIdList, null, null, false, cancellationToken);
        }

        public async Task<Pageable<ProductDetailedWarehouseProductTransferResponse>> GetItems(Guid fileId, int? shelfId, int? productId, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _warehouseProductTransferItemDal.Get(fileId, shelfId, productId, null, null, null, null, pageSize, pageIndex, cancellationToken);
            var count = await _warehouseProductTransferItemDal.Count(fileId, shelfId, productId, null, null, null, cancellationToken);

            var response = new List<ProductDetailedWarehouseProductTransferResponse>();
            if (contents.Count > 0)
            {
                var productIds = contents.Select(x => x.ProductId).ToList();
                var products = (await _productService.GetList(new ProductGetListDto() { IDList = productIds }, null, cancellationToken)).Model;
                foreach (var item in contents)
                {
                    var product = products.First(x => x.ID == item.ProductId);
                    response.Add(new ProductDetailedWarehouseProductTransferResponse(item, product.Image, product.Barcode, product.ProductName));
                }
            }

            return new Pageable<ProductDetailedWarehouseProductTransferResponse>(pageIndex, pageSize, count, response);
        }

        public async Task<Pageable<ProductDetailedWarehouseProductTransferResponse>> GetGroupped(Guid fileId, int? productId, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _warehouseProductTransferItemDal.GetGroupped(fileId, productId, pageSize, pageIndex, cancellationToken);
            var count = await _warehouseProductTransferItemDal.CountGroupped(fileId, productId, cancellationToken);

            var response = new List<ProductDetailedWarehouseProductTransferResponse>();
            if (contents.Count > 0)
            {
                var productIds = contents.Select(x => x.ProductId).ToList();
                var products = (await _productService.GetList(new ProductGetListDto() { IDList = productIds, AddSubQueries = false }, null, cancellationToken)).Model;
                foreach (var item in contents)
                {
                    var product = products.First(x => x.ID == item.ProductId);
                    response.Add(new ProductDetailedWarehouseProductTransferResponse(item, product.Image, product.Barcode, product.ProductName));
                }
            }
            response = response.OrderBy(x => x.Name).ToList();
            return new Pageable<ProductDetailedWarehouseProductTransferResponse>(pageIndex, pageSize, count, response);
        }

        public async Task<List<WarehouseProductTransferMovementAggregate>> GetMovements(Guid fileId, CancellationToken cancellationToken)
        {
            return await _warehouseProductTransferMovementDal.Get(fileId, cancellationToken);
        }

        public async Task CreateMovements(Guid fileId, string message, CancellationToken cancellationToken)
        {
            var aggregate = await GetById(fileId, cancellationToken);

            var movementAggregate = new WarehouseProductTransferMovementAggregate(aggregate.Id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, message, false);

            await _warehouseProductTransferMovementDal.Create(movementAggregate, cancellationToken);
        }

        public async Task Pick(Guid fileId, int shelfId, int productId, double quantity, bool isMissing, CancellationToken cancellationToken)
        {
            var file = await GetById(fileId, cancellationToken);
            if (file.Status != WarehouseProductTransferStatus.Picking)
            {
                file.ChangeStatus(WarehouseProductTransferStatus.Picking);
                await _warehouseProductTransferFileDal.Update(file, cancellationToken);
            }

            var item = (await _warehouseProductTransferItemDal.Get(file.Id, shelfId, productId, null, null, null, null, int.MaxValue, 0, cancellationToken)).FirstOrDefault(x => x.Quantity > x.PickQuantity + x.MissingQuantity);
            if (item == null)
                throw new NotFoundException("WAREHOUSE_PRODUCT_TRANSFER_ITEM_NOT_FOUND",
                    new KeyValuePair<string, string>("fileId", fileId.ToString()),
                    new KeyValuePair<string, string>("shelfId", shelfId.ToString()),
                    new KeyValuePair<string, string>("productId", productId.ToString()));

            if (WebSiteInfo.User.Value.Settings.ArabaModulAktif)
            {
                if (file.WarehouseCarId == 0)
                {
                    if (WebSiteInfo.User.Value.WarehouseCar == null)
                        throw new NotFoundException("YOU_HAVE_TO_ASSIGN_A_CAR_FIRST");

                    file.Assign(WebSiteInfo.User.Value.WarehouseCar.ID, WebSiteInfo.User.Value.ID);
                    await _warehouseProductTransferFileDal.Update(file, cancellationToken);
                }

                if (file.WarehouseCarId != WebSiteInfo.User.Value.WarehouseCar.ID)
                    throw new BusinessException("YOUR_WAREHOUSE_CAR_IS_NOT_EQUALS_TO_FILE_WAREHOUSE_CAR");

                await _warehouseCarProductService.Add(new WarehouseCarProductAddDto
                {
                    Piece = quantity,
                    ProductID = productId,
                    WarehouseCarID = file.WarehouseCarId,
                    Settings = new WarehouseCarProductSettings() { isMissingProduct = isMissing, fileId = fileId }
                }, cancellationToken);
            }

            item.Pick(quantity, isMissing);
            await _warehouseProductTransferItemDal.Update(item, cancellationToken);
            string action = !isMissing ? "aldım" : "bulamadım";
            await _warehouseProductTransferMovementDal.Create(new WarehouseProductTransferMovementAggregate(file.Id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, $"{productId} Id'li ürünü {shelfId} Id'li raftan {quantity} adet {action}.", true), cancellationToken);
        }

        public async Task Check(Guid fileId, int productId, double quantity, bool isBulkProductCompleted, Guid? fileProductId, CancellationToken cancellationToken)
        {
            var file = await GetById(fileId, cancellationToken);

            if (file.Status != WarehouseProductTransferStatus.Checking)
            {
                file.ChangeStatus(WarehouseProductTransferStatus.Checking);
                await _warehouseProductTransferFileDal.Update(file, cancellationToken);
            }

            var item = (await _warehouseProductTransferItemDal.Get(file.Id, null, productId, null, null, null, true, int.MaxValue, 0, cancellationToken)).FirstOrDefault(x => x.Quantity > x.ControlQuantity);
            if (isBulkProductCompleted && fileProductId.HasValue)
                item = await _warehouseProductTransferItemDal.GetById(fileProductId.Value, cancellationToken);
            

            if (item == null)
                throw new NotFoundException("WAREHOUSE_PRODUCT_TRANSFER_ITEM_NOT_FOUND",
                    new KeyValuePair<string, string>("fileId", fileId.ToString()),
                    new KeyValuePair<string, string>("productId", productId.ToString()));

            var shelfs = await _shelfService.GetList(new ShelfGetListDto() { IsEmptyShelf = true }, cancellationToken: cancellationToken);
            if (!shelfs.Model.Any())
                throw new NotFoundException("EMPTY_SHELF_IS_NOT_FOUND");

            await _shelfProductService.Add(
                new ShelfProductAddDto()
                {
                    Type = ProductMovementStockControlTypeEnum.WarehouseTransferStockAdd.ToString(),
                    ObjectId = file.FileNo,
                    AddStockWebSite = false,
                    ShelfItems = new List<ShelfProductAddItemDto>()
                    {
                        new ShelfProductAddItemDto(item.ProductId, shelfs.Model.FirstOrDefault()!.ID, quantity, true)
                    }
                },
                cancellationToken);

            item.Check(quantity);
            await _warehouseProductTransferItemDal.Update(item, cancellationToken);
            await _warehouseProductTransferMovementDal.Create(new WarehouseProductTransferMovementAggregate(file.Id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, $"{productId} Id'li ürünü {quantity} adet kontrol ettim.", true), cancellationToken);
        }

        public async Task PickCompleted(Guid id, CancellationToken cancellationToken)
        {
            var file = await GetById(id, cancellationToken);
            file.ChangeStatus(WarehouseProductTransferStatus.Transfer);
            var transferItems = await _warehouseProductTransferItemDal.Get(id, null, null, null, null, null, null, int.MaxValue, 1, cancellationToken);
            //bool deleteCarAllProduct = transferItems.Sum(x => x.MissingQuantity) == 0;
            if (WebSiteInfo.User.Value.Settings.ArabaModulAktif && file.WarehouseCarId > 0)// && deleteCarAllProduct)
                await _warehouseCarProductService.DeleteCarAllProduct(new WarehouseCarProductDeleteCarAllProductDto() { WarehouseCarID = file.WarehouseCarId }, cancellationToken);

            await _warehouseProductTransferFileDal.Update(file, cancellationToken);
            await _warehouseProductTransferMovementDal.Create(new WarehouseProductTransferMovementAggregate(file.Id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, "Toplamayı tamamladım ve transfere gönderdim.", true), cancellationToken);

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(WarehouseProductTransferFileEvents.PickCompleted, new WarehouseProductTransferFileEventPayload(file)));
        }

        public async Task CheckCompleted(Guid id, CancellationToken cancellationToken)
        {
            var file = await GetById(id, cancellationToken);
            file.ChangeStatus(WarehouseProductTransferStatus.Completed);
            await _warehouseProductTransferFileDal.Update(file, cancellationToken);
            await _warehouseProductTransferMovementDal.Create(new WarehouseProductTransferMovementAggregate(file.Id, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, "Kontrol etmeyi tamamladım ve dosyayı kapattım.", true), cancellationToken);

            if (WebSiteInfo.User.Value.Settings.ArabaModulAktif)
            {
                await _warehouseCarProductService.DeleteCarAllProduct(new WarehouseCarProductDeleteCarAllProductDto
                {
                    WarehouseCarID = file.WarehouseCarId
                }, cancellationToken);
            }
        }

        public async Task<PickShelfsWarehouseProductTransferResponse> GetPickShelfs(Guid id, CancellationToken cancellationToken)
        {
            var response = new PickShelfsWarehouseProductTransferResponse();
            var items = await _warehouseProductTransferItemDal.Get(id, null, null, true, null, null, null, Int32.MaxValue, 0, cancellationToken);
            if (items.Count > 0)
            {
                var shelfIds = items.Select(x => x.ShelfId).Distinct().ToList();
                var shelfs = (await _shelfService.GetList(new ShelfGetListDto() { IDList = shelfIds }, null, cancellationToken)).Model;

                response.Shelfs = shelfs.Select(x => new PickShelfsWarehouseProductTransferItemResponse(x.ID, x.Definition, x.Barcode, x.Rank)).OrderBy(x => x.Rank).ToList();
            }

            return response;
        }

        public async Task<PickProductsWarehouseProductTransferResponse> GetPickProducts(Guid id, int shelfId, CancellationToken cancellationToken)
        {
            var response = new PickProductsWarehouseProductTransferResponse();

            var items = await _warehouseProductTransferItemDal.Get(id, shelfId, null, true, null, null, null, Int32.MaxValue, 0, cancellationToken);
            if (items.Count > 0)
            {
                var productIds = items.Select(x => x.ProductId).Distinct().ToList();
                var products = (await _productService.GetList(new ProductGetListDto() { IDList = productIds, AddSubQueries = false }, null, cancellationToken)).Model;

                var productGroupped = items.GroupBy(x => x.ProductId).Select(gp => new WarehouseProductTransferItemAggregate
                {
                    FileId = gp.First().FileId,
                    ProductId = gp.Key,
                    Quantity = gp.Sum(x => x.Quantity),
                    ShelfId = gp.First().ShelfId
                }).ToList();

                foreach (var item in productGroupped)
                {
                    var product = products.First(x => x.ID == item.ProductId);
                    response.Products.Add(new PickProductsWarehouseProductTransferItemResponse(item, product.ProductName, product.Image, product.Barcode, product.StockCode));
                }
            }
            return response;
        }

        public async Task<CheckProductsWarehouseProductTransferResponse> GetCheckProducts(Guid id, CancellationToken cancellationToken)
        {
            var response = new CheckProductsWarehouseProductTransferResponse();

            var items = await _warehouseProductTransferItemDal.Get(id, null, null, false, true, null, null, Int32.MaxValue, 0, cancellationToken);
            if (items.Count > 0)
            {
                var productIds = items.Select(x => x.ProductId).Distinct().ToList();
                var products = (await _productService.GetList(new ProductGetListDto() { IDList = productIds }, null, cancellationToken)).Model;

                foreach (var item in items)
                {
                    var product = products.First(x => x.ID == item.ProductId);
                    response.Products.Add(new CheckProductsWarehouseProductTransferItemResponse(item, product.ProductName, product.Image, product.Barcode));
                }
            }
            response.Products = response.Products.Where(x => x.PickQuantity >= x.Quantity).ToList();
            return response;
        }

        public async Task<List<NotPickProductsWarehouseProductTransferResponse>> GetNonPickProducts(CancellationToken cancellationToken)
        {
            var notPickingItems = await _warehouseProductTransferItemDal.Get(null, null, null, true, null, null, null, pageSize: int.MaxValue, pageIndex: 0, cancellationToken: cancellationToken);

            return notPickingItems.Select(x => new NotPickProductsWarehouseProductTransferResponse { ProductId = x.ProductId, ShelfId = x.ShelfId, Quantity = x.Quantity - x.PickQuantity }).ToList();
        }
        public async Task FileCompleted(WarehouseTransferBulkCompletedRequest request, CancellationToken cancellationToken)
        {
            if(!WebSiteInfo.User.Value.Settings.UrunToplamaAyar.WarehouseTransferBulkProductControl)
                throw new BusinessException("CHECK_THE_SETTINGS");

            var file = (await _warehouseProductTransferFileDal.Get(null, request.FileNo, null,
                null, null, null, null, 1, 1, cancellationToken)).FirstOrDefault();

            if (file == null)
                throw new NotFoundException("FILE_NOT_FOUND");

            if (file.Status != WarehouseProductTransferStatus.Transfer)
                throw new BusinessException("FILE_STATUS_NOT_TRANSFER");

            if (file.TransferWarehouseId != WebSiteInfo.User.Value.WarehouseID)
                throw new BusinessException("INVALID_WAREHOUSE");

            var fileProducts = await GetItems(file.Id, null, null, int.MaxValue, 1, cancellationToken);

            if (fileProducts.Contents.Count > 0)
            {
                foreach (var product in fileProducts.Contents)
                {
                    if(product.PickQuantity > 0)
                        await Check(file.Id, product.ProductId, product.PickQuantity, true, product.Id, cancellationToken);
                }
                await CheckCompleted(file.Id, cancellationToken);
            }

        }
    }
}