using System;
using System.Collections.Generic;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Item;

namespace Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Models.Responses
{
    public class CheckProductsWarehouseProductTransferResponse
    {
        public List<CheckProductsWarehouseProductTransferItemResponse> Products { get; set; } = new List<CheckProductsWarehouseProductTransferItemResponse>();
    }

    public class CheckProductsWarehouseProductTransferItemResponse
    {
        public CheckProductsWarehouseProductTransferItemResponse(WarehouseProductTransferItemAggregate aggregate, string name, string image, string barcode)
        {
            Id = aggregate.Id;
            ProductId = aggregate.ProductId;
            ShelfId = aggregate.ShelfId;
            Quantity = aggregate.Quantity;
            PickQuantity = aggregate.PickQuantity;
            Name = name;
            Image = image;
            Barcode = barcode;
        }

        public Guid Id { get; set; }

        public int ProductId { get; set; }

        public int ShelfId { get; set; }

        public double Quantity { get; set; }

        public double PickQuantity { get; set; }

        public string Name { get; set; }

        public string Image { get; set; }

        public string Barcode { get; set; }
    }
}