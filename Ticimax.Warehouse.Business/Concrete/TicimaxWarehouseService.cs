using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Ticimax.BL.KargoApi;
using Ticimax.Core.CrossCuttingConcerns.Caching;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.KargoHelper.DTO.Models;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Static;
using static Google.Apis.Requests.BatchRequest;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class TicimaxWarehouseService : ITicimaxWarehouseService
    {
        public string Token { get; set; }

        private readonly ILogger<TicimaxWarehouseService> _logger;
        private readonly IStoreAgentService _storeAgentService;
        public static string Adres = "http://kargowms.ticimax.com/";
        private readonly ICacheManager _cacheManager;

        public TicimaxWarehouseService(ILogger<TicimaxWarehouseService> logger, IStoreAgentService storeAgentService, ICacheManager cacheManager)
        {
            _logger = logger;
            _storeAgentService = storeAgentService;
            _cacheManager = cacheManager;
        }

        public async Task<CreateCargoPdfResponse> CreateCargoPdf(CreateCargoPdfRequest request, CancellationToken cancellationToken)
        {
            string responseStr = await CreateWebRequest("/api/Depo/CreateCargoPdf", request.ToJsonSerialize(), "POST", "application/json", Token);
            if (!responseStr.ToJsonDeserialize<CreateCargoPdfResponse>().IsError)
                _logger.LogError($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/CreateCargoPdf Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");

            return responseStr.ToJsonDeserialize<CreateCargoPdfResponse>();
        }

        public async Task<CreateOrderPdfResponse> CreateOrderPdf(CreateOrderPdfRequest request, CancellationToken cancellationToken)
        {
            string responseStr = await CreateWebRequest("/api/Depo/CreateOrderPdf", request.ToJsonSerialize(), "POST", "application/json", Token);
            if (responseStr.ToJsonDeserialize<CreateOrderPdfResponse>().IsError)
                _logger.LogError($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/CreateOrderPdf Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");

            return responseStr.ToJsonDeserialize<CreateOrderPdfResponse>();
        }

        public async Task<CreateEInvoiceResponse> CreateEInvoice(CreateEInvoiceRequest request, CancellationToken cancellationToken)
        {
            string responseStr = await CreateWebRequest("/api/Depo/CreateEInvoice", request.ToJsonSerialize(), "POST", "application/json", Token);
            if (!responseStr.ToJsonDeserialize<CreateEInvoiceResponse>().IsError)
                _logger.LogError($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/CreateEInvoice Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");

            return responseStr.ToJsonDeserialize<CreateEInvoiceResponse>();
        }

        public async Task<CreateGiftVoucherResponse> CreateGiftVoucher(CreateGiftVoucherRequest request, CancellationToken cancellationToken)
        {
            string responseStr = await CreateWebRequest("/api/Depo/CreateGiftVoucher", request.ToJsonSerialize(), "POST", "application/json", Token);
            _logger.LogInformation($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/CreateGiftVoucher Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
            return responseStr.ToJsonDeserialize<CreateGiftVoucherResponse>();
        }

        public async Task<SetOrderProductStatusResponse> SetOrderProductStatus(SetOrderProductStatusRequest request, CancellationToken cancellationToken)
        {
            request.AccessToken = AccessTokenExtension.Generate(WebSiteInfo.User.Value.DomainName);
            string responseStr = await CreateWebRequest("/api/Depo/SetOrderProductStatus", request.ToJsonSerialize(), "POST", "application/json", Token);
            _logger.LogInformation($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/SetOrderProductStatus Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
            if (string.IsNullOrWhiteSpace(responseStr))
                return new SetOrderProductStatusResponse
                { IsError = true };

            return responseStr.ToJsonDeserialize<SetOrderProductStatusResponse>();
        }

        public async Task<CreateCustomOrderCargoPdfResponse> CreateCustomOrderCargoPdf(CreateCustomOrderCargoPdfRequest request, CancellationToken cancellationToken)
        {
            string responseStr = await CreateWebRequest("/api/Depo/CreateCustomOrderCargoPdf", request.ToJsonSerialize(), "POST", "application/json", Token);
            if (responseStr.ToJsonDeserialize<CreateCustomOrderCargoPdfResponse>().IsError)
                _logger.LogError($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/CreateCustomOrderCargoPdf Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");

            return responseStr.ToJsonDeserialize<CreateCustomOrderCargoPdfResponse>();
        }

        public async Task<AddProductToOrderResponse> AddProductToOrder(AddProductToOrderRequest request, CancellationToken cancellationToken)
        {
            string responseStr = await CreateWebRequest("/api/Depo/AddProductToOrder", request.ToJsonSerialize(), "POST", "application/json", Token);
            _logger.LogInformation($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/AddProductToOrder Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
            return responseStr.ToJsonDeserialize<AddProductToOrderResponse>();
        }

        public async Task SendEMail(SendEMailRequest request, CancellationToken cancellationToken)
        {
            var responseStr = CreateWebRequest("/api/Depo/SendEMail", request.ToJsonSerialize(), "POST", "application/json", Token);
            _logger.LogInformation($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/SendEMail Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
        }

        public async Task PaymentPostAuth(PaymentPostAuthRequest request, CancellationToken cancellationToken)
        {
            var responseStr = await CreateWebRequest("/api/Depo/PaymentPostAuth", request.ToJsonSerialize(), "POST", "application/json", Token);
            _logger.LogInformation($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/PaymentPostAuth Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
        }

        public async Task OrderCargoControl(OrderCargoControlRequest request, CancellationToken cancellationToken)
        {
            var responseStr = await CreateWebRequest("/api/Cargo/OrderCargoStatus", request.ToJsonSerialize(), "POST", "application/json");
            _logger.LogInformation($"{WebSiteInfo.User.Value.DomainName} -- Url: /api/Depo/OrderCargoStatus Request Body: {request.ToJsonSerialize()} Response Body: {responseStr} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
        }

        public async Task TokenInitialize()
        {
            if (!string.IsNullOrWhiteSpace(WebSiteInfo.User.Value.WarehouseToken))
                return;

            string userName = "";
            if(WebSiteInfo.User.Value != null)
            {
                var agent = (await _storeAgentService.GetList(new Entities.Dtos.StoreAgentGetListDto() { ID = WebSiteInfo.User.Value.ID })).Model.FirstOrDefault();
                if (agent != null)
                    userName = agent.Username;
                else
                    userName = WebSiteInfo.User.Value.Username;
            }

            string tokenResponse = await CreateWebRequest("/api/token", "grant_type=password&username=" + userName + "&password=" + WebSiteInfo.User.Value.Password + "&type=1", "POST", "application/x-www-form-urlencoded");
            TokenResponse tokenObj = tokenResponse.ToJsonDeserialize<TokenResponse>();
            WebSiteInfo.User.Value.WarehouseToken = tokenObj.access_token;
            WebSiteInfo.User.Value.WarehouseTokenExpire = DateTime.Now.AddHours(6);
        }

        public async Task<Token> CargoApiLogin(string userName, string password, int cargoIntegrationId)
        {
            string cacheKey = WebSiteInfo.User.Value.DomainName + ":cargoaccesstoken:" + cargoIntegrationId;
            var cacheResponse = await _cacheManager.Get<Token>(cacheKey);
            if (cacheResponse != null)
                return cacheResponse;

            Result result = new Result();
            string parametreler = "grant_type=password&username=" + userName + "&password=" + password;
            StringContent content = new StringContent(parametreler, Encoding.UTF8,
              "application/x-www-form-urlencoded");

            using (var httpClient = new HttpClient())
            {
                try
                {
                    var response = httpClient.PostAsync($"{Adres}token", content).Result;
                    var requestContent = response.Content.ReadAsStringAsync().Result;
                    Token token = JsonConvert.DeserializeObject<Token>(requestContent);
                    if (token != null && token.access_token != null)
                    {
                        result = new Result(false, 0, "Ticimax.BL.KargoApi.Login", "Api girişi sağlandı", "", token);
                    }
                    else
                    {
                        result = new Result(true, 1, "Ticimax.BL.KargoApi.Login", "Api girişi sağlanamadı", "", requestContent);
                    }

                }
                catch (Exception ex)
                {
                    result = new Result(true, 1, "Ticimax.BL.KargoApi.Login", ex.Message, ex.StackTrace, null);
                }
            }
            ResponseControl(ref result);
            Ticimax.BL.KargoApi.Token cargoApiToken = null;
            if (!result.IsError)
            {
                cargoApiToken = (Ticimax.BL.KargoApi.Token)result.Model;
                await _cacheManager.Add(cacheKey, cargoApiToken, 60 * 15);
            }
            return cargoApiToken;
        }

        private void ResponseControl(ref Result result)
        {
            if (result != null && result.IsError)
            {
                Regex tagRegex = new Regex(@"<[^>]+>");
                if (!string.IsNullOrEmpty(result.Message) && tagRegex.IsMatch(result.Message))
                    result.Message = "";
                if (!string.IsNullOrEmpty(result.Detail) && tagRegex.IsMatch(result.Detail))
                    result.Detail = "";
                if (result.Model != null)
                {
                    try
                    {
                        string jsonModel = Newtonsoft.Json.JsonConvert.SerializeObject(result.Model);
                        if (tagRegex.IsMatch(jsonModel))
                            result.Model = string.Empty;
                    }
                    catch
                    { }
                }
            }
        }


        private async Task<string> CreateWebRequest(string link, string postData, string method, string contentType, string token = "")
        {
            var host = ((WebSiteInfo.User.Value.DomainName == "localhost" ? "http://localhost:53810" : "https://" + ((WebSiteInfo.User.Value.SiteSettings.SeoAyar?.WwwAktif ?? true) ? "www." + WebSiteInfo.User.Value.DomainName : WebSiteInfo.User.Value.DomainName))).Trim();
            link = host + link;

            if (WebSiteInfo.User.Value != null && (WebSiteInfo.User.Value.WarehouseTokenExpire <= DateTime.Now || string.IsNullOrWhiteSpace(WebSiteInfo.User.Value.WarehouseToken)) && !link.Contains("api/token"))
            {
                //var hashedPassword = MD5Hash(WebSiteInfo.User.Value.Password);
                string tokenResponse = await CreateWebRequest("/api/token", "grant_type=password&username=" + WebSiteInfo.User.Value.Username + "&password=" + WebSiteInfo.User.Value.Password + "&type=1", "POST", "application/x-www-form-urlencoded");
                TokenResponse tokenObj = tokenResponse.ToJsonDeserialize<TokenResponse>();
                WebSiteInfo.User.Value.WarehouseToken = tokenObj.access_token;
                WebSiteInfo.User.Value.WarehouseTokenExpire = DateTime.Now.AddHours(6);
                _logger.LogInformation($"{WebSiteInfo.User?.Value?.DomainName ?? "Domainless"} - Url: /api/token RequestQuery: grant_type=password&username={WebSiteInfo.User.Value.Username}&password={WebSiteInfo.User.Value.Password}&type=1 Response Body: {tokenResponse} ----- {DateTime.Now:dd.MM.yyyy HH.mm.ss}");
            }

            token = WebSiteInfo.User.Value.WarehouseToken;
            if (method == "GET" && !string.IsNullOrEmpty(postData))
            {
                link += "?" + postData;
            }

            var request = (HttpWebRequest)WebRequest.Create(link);
            request.CookieContainer = new CookieContainer();
            if (!string.IsNullOrEmpty(WebSiteInfo.User.Value.WarehouseTokenServer))
                request.CookieContainer.Add(new Uri(link), new Cookie("SERVERID", WebSiteInfo.User.Value.WarehouseTokenServer));

            if (!string.IsNullOrEmpty(token))
            {
                request.Headers.Add("Authorization", "Bearer " + token);
            }

            request.Method = method;
            request.ContentType = contentType;
            request.UserAgent = "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)";
            request.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
            if (method == "POST")
            {
                var bytes = Encoding.UTF8.GetBytes(postData);
                request.ContentLength = bytes.Length;
                using (var writeStream = request.GetRequestStream())
                {
                    writeStream.Write(bytes, 0, bytes.Length);
                }
            }

            var responseValue = string.Empty;

            try
            {
                using (var response = (HttpWebResponse) await request.GetResponseAsync())
                {
                    if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created && response.StatusCode != HttpStatusCode.NoContent)
                    {
                        var message = String.Format("Request failed. Received HTTP {0}", response.StatusCode);
                        _logger.LogError($"{WebSiteInfo.User.Value.DomainName} -- Url: {link} Request Body: {postData} StatusCode: {response.StatusCode} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
                        return message;
                    }

                    // grab the response
                    using (var responseStream = response.GetResponseStream())
                    {
                        if (responseStream != null)
                            using (var reader = new StreamReader(responseStream))
                            {
                                responseValue = reader.ReadToEnd();
                                if (link.Contains("token"))
                                {
                                    WebSiteInfo.User.Value.WarehouseTokenServer = response.Cookies["SERVERID"]?.Value ?? null;
                                }
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"{WebSiteInfo.User.Value.DomainName} -- Url: {link} Request Body: {postData} Response Exception: {ex.Message} Method: POST ----- {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
            }

            return responseValue;
        }
    }

    #region Request, Response class

    public class TokenResponse
    {
        public string access_token { get; set; }

        public string token_type { get; set; }

        public int expires_in { get; set; }
    }

    public class CreateGiftVoucherResponse : ErrorResponse
    {
        public string GiftVoucherCode { get; set; }
    }

    public class CreateGiftVoucherRequest
    {
        public string OrderNo { get; set; }

        public double Amounth { get; set; }

        public int CreatedID { get; set; }

        public string CreatedName { get; set; }

        public string MemberName { get; set; }

        public string MemberLastName { get; set; }

        public string MemberMailAddress { get; set; }

        public string Phone { get; set; }

        public string GSM { get; set; }
    }

    public class CreateEInvoiceResponse
    {
        public bool IsError { get; set; }

        public string ErrorMessage { get; set; }

        public byte[] InvoicePdf { get; set; }
    }

    public class CreateEInvoiceRequest
    {
        public int OrderId { get; set; }
    }

    public class OrderCargoControlRequest
    {
        public List<int> OrderIDs { get; set; }

        public string AccessToken { get; set; }
    }

    public class CreateOrderPdfRequest
    {
        public int OrderId { get; set; }

        public bool isTrendyolBarcode { get; set; } = true;
    }

    public class CreateOrderPdfResponse : ErrorResponse
    {
        public byte[] Pdf { get; set; }
    }

    public class SetOrderStatusRequest
    {
        public int OrderId { get; set; }

        public int StatusId { get; set; } = -1;

        public bool SendMail { get; set; }

        public bool SendSms { get; set; }
    }

    public class SetOrderStatusResponse : ErrorResponse
    {
    }

    public class SetOrderProductStatusRequest
    {
        public int OrderId { get; set; }

        public List<OrderProductStatus> Products { get; set; }
        public string AccessToken { get; set; }

    }

    public class SetOrderProductStatusResponse : ErrorResponse
    {
    }

    public class OrderProductStatus
    {
        public int OrderLineId { get; set; }

        public int StatusId { get; set; }

        public int Process { get; set; }

        public int ReturnCauseId { get; set; }

        public double Quantity { get; set; }
    }

    public class AddProductToOrderRequest
    {
        public int OrderId { get; set; }

        public int ProductId { get; set; }

        public double Quantity { get; set; }
    }

    public class AddProductToOrderResponse : ErrorResponse
    {
        public int OrderLineId { get; set; }
    }

    public class SendEMailRequest
    {
        public string Subject { get; set; }

        public string Message { get; set; }

        public string ToName { get; set; }

        public string ToMail { get; set; }

        public string FromName { get; set; }

        public string FromMail { get; set; }
    }

    public class PaymentPostAuthRequest
    {
        public int OrderId { get; set; }
    }

    public class CreateCargoPdfRequest
    {
        public List<Packet> Packets { get; set; }

        public List<SiparisKargoPaket> SiparisKargoPakets { get; set; }
    }

    public class CreateCargoPdfResponse : ErrorResponse
    {
        public byte[] Pdf { get; set; }
    }

    public class CreateCustomOrderCargoPdfRequest
    {
        public string Ids { get; set; }

        public string AccessToken { get; set; }
    }
    public class CreateCustomOrderCargoPdfResponse
    {
        public bool IsError { get; set; }

        public string ErrorMessage { get; set; }

        public byte[] CustomCargoPdf { get; set; }
    }

    #endregion Request, Response class
}