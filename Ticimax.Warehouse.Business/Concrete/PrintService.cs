using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HtmlAgilityPack;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.InvoiceThemeConcrete.Enums;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.PrintService;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class PrintService : IPrintService
    {
        private readonly IOrderService _orderService;
        private readonly IOrderProductService _orderProductService;
        private readonly IOrderPaymentService _orderPaymentService;
        private readonly ICargoCompanyService _cargoCompanyService;
        private readonly ITicimaxWarehouseService _ticimaxWarehouseService;
        private readonly IImageService _imageService;
        private readonly IInvoiceThemeService _invoiceThemeService;
        private readonly IOrderCargoPacketService _orderCargoPacketService;
        private readonly ICargoIntegrationService _cargoIntegrationService;

        public PrintService(IOrderService orderService, IOrderProductService orderProductService, IOrderPaymentService orderPaymentService, ICargoCompanyService cargoCompanyService, ITicimaxWarehouseService ticimaxWarehouseService, IImageService imageService, IInvoiceThemeService invoiceThemeService, IOrderCargoPacketService orderCargoPacketService, ICargoIntegrationService cargoIntegrationService)
        {
            _orderService = orderService;
            _orderProductService = orderProductService;
            _orderPaymentService = orderPaymentService;
            _cargoCompanyService = cargoCompanyService;
            _ticimaxWarehouseService = ticimaxWarehouseService;
            _imageService = imageService;
            _invoiceThemeService = invoiceThemeService;
            _orderCargoPacketService = orderCargoPacketService;
            _cargoIntegrationService = cargoIntegrationService;
        }

        public async Task<PrintDto> PrintOrder(int orderId, CancellationToken cancellationToken)
        {
            var response = new PrintDto();

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = orderId }, null, cancellationToken)).Model.FirstOrDefault();

            var invoiceTemplate = (await _invoiceThemeService.GetList(new InvoiceThemeGetListDto { IsDefault = true, Type = InvoiceThemeType.OrderPdf }, null, cancellationToken)).Model.FirstOrDefault();
            if (invoiceTemplate != null)
            {
                order.Products = (await _orderProductService.GetProductList(new OrderProductFilter
                { OrderID = orderId }, cancellationToken)).Model;

                order.Payments = (await _orderPaymentService.GetList(new OrderPaymentGetListDto
                { OrderID = orderId, isApproved = true }, cancellationToken)).Model;

                var cargoCompanies = (await _cargoCompanyService.GetList(new CargoCompanyGetListDto(), null, cancellationToken)).Model;

                var cargoCompany = cargoCompanies.FirstOrDefault(x => x.ID == order.CargoCompanyID);
                order.CargoCompany = cargoCompany != null ? cargoCompany.Description : "";
                var template = HandlebarsDotNet.Handlebars.Compile(invoiceTemplate.Html);

                if (!string.IsNullOrWhiteSpace(order.CargoCode))
                {
                    var image = await _imageService.CreateBarcode(order.CargoCode);
                    order.CargoCodeBarcode = image.Image;
                }

                if (!string.IsNullOrWhiteSpace(order.AdditionalInfo.MarketplaceCampaignCode))
                {
                    var image = await _imageService.CreateBarcode(order.AdditionalInfo.MarketplaceCampaignCode);
                    order.MarketplaceCampaignCodeBarcode = image.Image;
                }

                if (!string.IsNullOrWhiteSpace(order.OrderNo))
                {
                    var image = await _imageService.CreateBarcode(order.OrderNo);
                    order.OrderNoBarcode = image.Image;
                }

                order.OrderIdBarcode = (await _imageService.CreateBarcode(order.ID.ToString())).Image;

                string js = "<script type='text/javascript'> window.onload = function () { window.print() } </script>";
                response.OrderHtml = template(order) + js;
                return response;
            }

            var pdfResponse = await _ticimaxWarehouseService.CreateOrderPdf(new CreateOrderPdfRequest
            { OrderId = orderId }, cancellationToken);

            if (!pdfResponse.IsError)
                response.OrderPdf = pdfResponse.Pdf;

            return response;
        }

        public async Task<string> PrintGiftPackage(OrderGiftPackagePrint content, CancellationToken cancellationToken)
        {
            var invoiceTemplate = (await _invoiceThemeService.GetList(new InvoiceThemeGetListDto { IsDefault = true, Type = InvoiceThemeType.GiftPackage }, null, cancellationToken)).Model.FirstOrDefault();
            if (invoiceTemplate == null)
                return null;

            var template = HandlebarsDotNet.Handlebars.Compile(invoiceTemplate.Html);
            string js = "<script type='text/javascript'> window.onload = function () { window.print() } </script>";
            return template(content) + js;

        }

        public async Task<string> PrintCargo(int orderId, int? packetId, int? integrationId, List<int>? orderProductIds, bool isCargoControlScreen, CancellationToken cancellationToken)
        {
            OrderCargoPacket packet = null;
            if (packetId.HasValue)
            {
                packet = await _orderCargoPacketService.GetById(packetId.Value, cancellationToken);
                if (packet != null)
                    orderId = packet.OrderId;
            }

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = orderId }, null, cancellationToken)).Model.FirstOrDefault();
            var invoiceTemplate = (await _invoiceThemeService.GetList(new InvoiceThemeGetListDto { IsDefault = true, Type = InvoiceThemeType.Cargo }, null, cancellationToken)).Model.FirstOrDefault();
            if (invoiceTemplate != null)
            {
                var cargoCompanies = (await _cargoCompanyService.GetList(new CargoCompanyGetListDto(), null, cancellationToken)).Model;

                order.Products = (await _orderProductService.GetProductList(new OrderProductFilter
                { OrderID = orderId, OrderProductsIds = orderProductIds }, cancellationToken)).Model;

                order.Payments = (await _orderPaymentService.GetList(new OrderPaymentGetListDto
                { OrderID = orderId, isApproved = true }, cancellationToken)).Model;

                var cargoCompany = cargoCompanies.FirstOrDefault(x => x.ID == order.CargoCompanyID);
                string cargoIntegrationName = "";
                if (WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.CargoSendUseIntegrationId)
                {
                    var cargoIntegrationId = (integrationId ?? 0) <= 0 ? (order.CargoIntegrationId <= 0 ? WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.DefaultCargoIntegrationId : order.CargoIntegrationId) : integrationId.Value;
                    var cargoIntegration = (await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto { ID = cargoIntegrationId }, cancellationToken: cancellationToken)).Model.FirstOrDefault();
                    if (cargoIntegration.AutoIntegrationCargoCompanyId > 0)
                        cargoCompany = cargoCompanies.FirstOrDefault(x => x.ID == cargoIntegration.AutoIntegrationCargoCompanyId);
                    cargoIntegrationName = cargoIntegration.Description;
                }
                else
                {
                    if (order.CargoIntegrationId > 0)
                    {
                        var orderIntegration = (await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto { ID = order.CargoIntegrationId }, cancellationToken: cancellationToken)).Model.FirstOrDefault();
                        if (orderIntegration != null)
                            cargoIntegrationName = orderIntegration.Description;
                    }
                }
                order.CargoCompany = cargoCompany != null ? cargoCompany.Description : "";
                order.CargoIntegrationName = cargoIntegrationName;
                if (packet != null)
                {
                    if (packet.MarketPlaceInformation != null && !string.IsNullOrWhiteSpace(packet.MarketPlaceInformation.MarketplaceCampainCode))
                    {
                        order.CargoCode = packet.MarketPlaceInformation.MarketplaceCampainCode;
                        order.CargoCode2 = packet.MarketPlaceInformation.MarketplaceCampainCode;
                        var storeCount = await _orderProductService.GetAllStoreCountAsync(order.ID, cancellationToken);
                        if (storeCount > 1 && !isCargoControlScreen)
                            order.CargoCode2 = order.CargoPacketID2.ToString();
                    }
                    else
                    {
                        order.CargoCode = packet.Barcode;
                        order.CargoCode2 = packet.Barcode;
                    }

                }
                else if (order.AdditionalInfo != null && !string.IsNullOrEmpty(order.AdditionalInfo.MarketplaceCampaignCode))
                {
                    order.CargoCode = order.AdditionalInfo.MarketplaceCampaignCode;
                    order.CargoCode2 = order.AdditionalInfo.MarketplaceCampaignCode;
                    var storeCount = await _orderProductService.GetAllStoreCountAsync(order.ID, cancellationToken);
                    if (storeCount > 1)
                        order.CargoCode2 = order.CargoPacketID2.ToString();

                }


                if (!string.IsNullOrWhiteSpace(order.CargoCode))
                {
                    var image = await _imageService.CreateBarcode(order.CargoCode);
                    order.CargoCodeBarcode = image.Image;
                }

                if (!string.IsNullOrWhiteSpace(order.CargoCode2))
                {
                    var image2 = await _imageService.CreateBarcode(order.CargoCode2);
                    order.CargoCodeBarcode2 = image2.Image;
                }



                if (!string.IsNullOrEmpty(order.CargoTrackingNumber))
                {
                    order.CargoTrackingNumberBarcode = _imageService.CreateBarcode(order.CargoTrackingNumber).GetAwaiter().GetResult().Image;
                }

                if (!string.IsNullOrWhiteSpace(order.AdditionalInfo.MarketplaceCampaignCode))
                {
                    var image = await _imageService.CreateBarcode(order.AdditionalInfo.MarketplaceCampaignCode);
                    order.MarketplaceCampaignCodeBarcode = image.Image;
                }

                if (!string.IsNullOrWhiteSpace(order.OrderNo))
                {
                    var image = await _imageService.CreateBarcode(order.OrderNo);
                    order.OrderNoBarcode = image.Image;
                }

                var html = HandleIterators(invoiceTemplate.Html);
                var template = HandlebarsDotNet.Handlebars.Compile(html);
                string js = "<script type='text/javascript'> window.onload = function () { window.print() } </script>";
                return template(order) + js;
            }

            return null;
        }

        private static string HandleIterators(string html)
        {
            var doc = new HtmlDocument();
            doc.LoadHtml(html);
            var repeats = doc.DocumentNode.SelectNodes("//*[@tici-repeat]");

            if (repeats != null)
            {
                foreach (var repeat in repeats)
                {
                    var attribute = repeat.Attributes["tici-repeat"];
                    var value = new CultureInfo("en-US").TextInfo.ToTitleCase(attribute.Value);
                    var startText = HtmlNode.CreateNode("{{#each " + value + "}}");
                    var endText = HtmlNode.CreateNode("{{/each}}");
                    repeat.ParentNode.InsertBefore(startText, repeat);
                    repeat.ParentNode.InsertAfter(endText, repeat);
                }
            }

            return doc.DocumentNode.OuterHtml;
        }

        public async Task<OrderGiftPackageResponseDto> OrderGiftPackage(int orderId, CancellationToken cancellationToken)
        {
            OrderGiftPackageResponseDto response = new OrderGiftPackageResponseDto();

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = orderId }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
                throw new NotFoundException("ORDER_NOT_FOUND", new KeyValuePair<string, string>("orderId", orderId.ToString()));

            if (!string.IsNullOrWhiteSpace(order.GiftPackageNote))
                response.GiftPackageHtml = await PrintGiftPackage(new OrderGiftPackagePrint(order.GiftPackageNote), cancellationToken);

            return response;
        }

        public async Task<OrderPrintCargoResponseDto> OrderPrintCargo(int orderId, CancellationToken cancellationToken)
        {
            OrderPrintCargoResponseDto response = new OrderPrintCargoResponseDto();

            var order = (await _orderService.GetList(new OrderGetListDto { OrderID = orderId }, null, cancellationToken)).Model.FirstOrDefault();
            if (order == null)
                throw new NotFoundException("ORDER_NOT_FOUND", new KeyValuePair<string, string>("orderId", orderId.ToString()));

            if (WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.ShowCustomCargoPdf)
            {
                if (WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.CustomCargoPdfIntegrations != null)
                {
                    if (WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.CustomCargoPdfIntegrations.Count > 0)
                    {
                        List<int> customCargoIntegrationIds = new List<int>();
                        foreach (var integrationId in WebSiteInfo.User.Value.Settings.KaliteKontrolAyar.CustomCargoPdfIntegrations)
                        {
                            if (int.TryParse(integrationId, out int id))
                                customCargoIntegrationIds.Add(id);
                        }
                        var packet = (await _orderCargoPacketService.GetByOrderId(orderId, cancellationToken)).FirstOrDefault();
                        if (packet != null && customCargoIntegrationIds.Contains(packet.CargoIntegrationId))
                        {
                            var cargoPdfResponse = await _ticimaxWarehouseService.CreateCustomOrderCargoPdf(new CreateCustomOrderCargoPdfRequest()
                            {
                                Ids = packet.Id.ToString(),
                                AccessToken = AccessTokenExtension.Generate(WebSiteInfo.User.Value.DomainName)
                            }, cancellationToken);
                            if (!cargoPdfResponse.IsError)
                                response.CustomCargoPDF = cargoPdfResponse.CustomCargoPdf;
                        }
                    }
                }
            }
            response.CargoHtml = await PrintCargo(order.ID, null, null, null, false, cancellationToken);
            return response;
        }
    }
}