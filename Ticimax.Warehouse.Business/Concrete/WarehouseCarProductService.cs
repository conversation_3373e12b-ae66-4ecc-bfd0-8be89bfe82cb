using Autofac.Core;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Enums;
using Ticimax.Warehouse.Business.Concrete.PickingProductConcrete.Events;
using Ticimax.Warehouse.Business.Concrete.Report.MissingProducts.Events;
using Ticimax.Warehouse.Business.Concrete.Review.Enums;
using Ticimax.Warehouse.Business.Concrete.Review.Events;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.File;
using Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.Item;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class WarehouseCarProductService : BaseService, IWarehouseCarProductService
    {
        private readonly IWarehouseCarProductDal _warehouseCarProductDal;
        private readonly IProductMovementService _productMovementService;
        private readonly IWarehouseCarDal _warehouseCarDal;
        private readonly IOrderCollectionSetDal _orderCollectionSetDal;
        private readonly IShelfProductService _shelfProductService;
        private readonly IWarehouseProductTransferFileDal _warehouseProductTransferFileDal;
        private readonly IServiceProvider _services;
        private readonly IOrderMovementService _orderMovementService;

        public WarehouseCarProductService(IWarehouseCarProductDal warehouseCarProductDal, IProductMovementService productMovementService, IWarehouseCarDal warehouseCarDa, IOrderCollectionSetDal orderCollectionSetDal, IShelfProductService shelfProductService, IWarehouseProductTransferFileDal warehouseProductTransferFileDal, IServiceProvider services, IOrderMovementService orderMovementService)
        {
            _warehouseCarProductDal = warehouseCarProductDal;
            _productMovementService = productMovementService;
            _warehouseCarDal = warehouseCarDa;
            _orderCollectionSetDal = orderCollectionSetDal;
            _shelfProductService = shelfProductService;
            _warehouseProductTransferFileDal = warehouseProductTransferFileDal;
            _services = services;
            _orderMovementService = orderMovementService;
        }

        public async Task Add(WarehouseCarProductAddDto request, CancellationToken cancellationToken)
        {
            var carProducts = await GetList(new WarehouseCarProductGetListDto { WarehouseCarID = request.WarehouseCarID, ProductID = request.ProductID, OrderID = request.OrderID }, null, cancellationToken);
            if (!carProducts.IsError && carProducts.Model != null)
            {
                var carProduct = carProducts.Model.FirstOrDefault(x => x.Settings.isMissingProduct == request.Settings.isMissingProduct);

                if (carProduct != null)
                {
                    carProduct.Piece += request.Piece;
                    await Update(new WarehouseCarProductUpdateDto(carProduct), cancellationToken);
                }
                else
                {
                    if (request.OrderCombinesProductFound)
                    {
                        var carProductOrderCombines = carProducts.Model.FirstOrDefault(x => x.Settings.isMissingProduct == !request.Settings.isMissingProduct);
                        if(carProductOrderCombines != null)
                        {
                            carProductOrderCombines.Settings.isMissingProduct = true;
                            await Update(new WarehouseCarProductUpdateDto(carProductOrderCombines), cancellationToken);
                        }
                    }
                    else
                    {
                        await _warehouseCarProductDal.AddAsync(request.ToEntity(), cancellationToken);
                    }
                }

                await _productMovementService.CreateMovement(
                    request.ProductID,
                    new CreateProductMovementRequest(ProductMovementProcessType.ProductPlacementToWarehouseCar, request.Piece, ProductMovementMessage.ProductPlacementToWarehouseCar(WebSiteInfo.User.Value.WarehouseCar?.Definition ?? ""), null),
                    cancellationToken);
            }
        }

        public async Task Reduce(WarehouseCarProductReduceDto request, CancellationToken cancellationToken)
        {
            var carProducts = await GetList(new WarehouseCarProductGetListDto { WarehouseCarID = request.WarehouseCarID, ProductID = request.ProductID }, null, cancellationToken);
            if (!carProducts.IsError && carProducts.Model != null)
            {
                WarehouseCarProduct carProduct = null;

                if (request.WarehouseCarProductID == 0)
                    carProduct = carProducts.Model.FirstOrDefault(x => x.OrderID == request.OrderID && x.Settings.isMissingProduct == request.isMissingProduct);
                else
                    carProduct = carProducts.Model.FirstOrDefault(x => x.ID == request.WarehouseCarProductID);

                if (carProduct != null)
                {
                    carProduct.Piece -= request.Piece;
                    if (carProduct.Piece > 0)
                        await Update(new WarehouseCarProductUpdateDto(carProduct), cancellationToken);
                    else
                        await Delete(new WarehouseCarProductDeleteDto { ID = carProduct.ID }, cancellationToken);
                }

                await _productMovementService.CreateMovement(
                    request.ProductID,
                    new CreateProductMovementRequest(ProductMovementProcessType.RemoveProductFromTheWarehouseCar, request.Piece, ProductMovementMessage.RemoveProductFromTheWarehouseCar(WebSiteInfo.User.Value.WarehouseCar?.Definition ?? ""), null),
                    cancellationToken);
            }
        }

        public async Task<ErrorResponse> Delete(WarehouseCarProductDeleteDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            await _warehouseCarProductDal.DeleteAsync(new WarehouseCarProduct { ID = request.ID }, cancellationToken);

            return response;
        }

        public async Task<ErrorResponse> DeleteCarAllProduct(WarehouseCarProductDeleteCarAllProductDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            await _warehouseCarProductDal.DeleteCarAllProductAsync(request.ToEntity(), cancellationToken);

            return response;
        }

        public async Task<DataResult<List<WarehouseCarProduct>>> GetList(WarehouseCarProductGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            var response = new DataResult<List<WarehouseCarProduct>>();

            response.Model = (await _warehouseCarProductDal.GetListAsync(request.ToFilter(), paging != null ? new WarehouseCarProductPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();

            if (request.isGetCount)
                response.Count = (await GetCount(request, cancellationToken)).Model;

            return response;
        }

        public async Task<ErrorResponse> Update(WarehouseCarProductUpdateDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            await _warehouseCarProductDal.UpdateAsync(request.ToEntity(), cancellationToken);

            return response;
        }

        public async Task<ErrorResponse> ApprovalUpdate(int id, WarehouseCarProductSettingsUpdateDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();


            var products = await _warehouseCarProductDal.GetListAsync(new WarehouseCarProductFilter() { Id = id }, cancellationToken: cancellationToken);
            if (!products.Any())
                throw new NotFoundException();

            var product = products.FirstOrDefault();

            bool settingsUpdate = false;
            double productOriginalPiece = product.Piece;
            if (request.ApprovalStatus == 1)
            {
                if (product.Piece > 1)
                {
                    product.Piece -= 1;
                    WarehouseCarProduct carProduct = new WarehouseCarProduct()
                    {
                        WarehouseCarID = product.WarehouseCarID,
                        OrderID = product.OrderID,
                        ProductID = product.ProductID,
                        Piece = 1,
                        Settings = new WarehouseCarProductSettings() { approvalStatus = 1, isDecimalPiece = product.Settings.isDecimalPiece, isMissingProduct = product.Settings.isMissingProduct, fileId = product.Settings.fileId }
                    };
                    await _warehouseCarProductDal.AddAsync(carProduct, cancellationToken);
                    await _warehouseCarProductDal.UpdateAsync(product, cancellationToken);
                }
                else
                {
                    settingsUpdate = true;
                    product.Settings.approvalStatus = request.ApprovalStatus;
                }
            }
            else
            {
                if (product.Piece > 1)
                {
                    product.Piece -= 1;
                    WarehouseCarProduct carProduct = new WarehouseCarProduct()
                    {
                        WarehouseCarID = product.WarehouseCarID,
                        OrderID = product.OrderID,
                        ProductID = product.ProductID,
                        Piece = 1,
                        Settings = new WarehouseCarProductSettings() {
                            approvalStatus = request.ApprovalStatus,
                            isDecimalPiece = product.Settings.isDecimalPiece, 
                            isMissingProduct = product.Settings.isMissingProduct, 
                            fileId = product.Settings.fileId 
                        }
                    };
                    await _warehouseCarProductDal.AddAsync(carProduct, cancellationToken);
                    await _warehouseCarProductDal.UpdateAsync(product, cancellationToken);
                }
                else
                {
                    settingsUpdate = true;
                    product.Settings.approvalStatus = request.ApprovalStatus;
                    product.Settings.shelfName = request.ShelfName;
                }
            }
            if (settingsUpdate)
                await _warehouseCarProductDal.SettingsUpdateAsync(product, cancellationToken);


            if (request.IsWarehouseTransfer && product.Settings.fileId.HasValue)
            {
                var fileCarProducts = await _warehouseCarProductDal.GetListAsync(new WarehouseCarProductFilter() { FileId = product.Settings.fileId.ToString()}, cancellationToken: cancellationToken);

                var fileItems = await _services.GetRequiredService<IWarehouseProductTransferService>().GetItems(product.Settings.fileId.Value,
                        null, product.ProductID, int.MaxValue, 1, cancellationToken);

                fileCarProducts = fileCarProducts.Where(x => x.ID != id 
                                                    && x.Settings.isMissingProduct == true).ToList();

                bool isCompletedFile = fileCarProducts.All(x => x.Settings.approvalStatus > 0);



                if (request.ApprovalStatus == 1 && isCompletedFile)
                    isCompletedFile = productOriginalPiece == 1;

                if (request.ApprovalStatus == 2)
                {
                    var fileItem = fileItems.Contents.FirstOrDefault(x => x.MissingQuantity > 0);
                    if (fileItem != null)
                    {
                        await _shelfProductService.Add(new ShelfProductAddDto()
                        {
                            AddStockWebSite = false,
                            ShelfItems = new List<ShelfProductAddItemDto>()
                            {
                                new ShelfProductAddItemDto(){

                                    WarehouseID = WebSiteInfo.User.Value.WarehouseID,
                                    ProductID = product.ProductID,
                                    ShelfStock = product.Piece,
                                    ShelfID = fileItem.ShelfId
                                }
                            }
                        }, cancellationToken);
                    }
                }                

                if (isCompletedFile)
                    await  _services.GetRequiredService<IWarehouseProductTransferService>().
                        PickCompleted(product.Settings.fileId.Value, cancellationToken);
            }
            string orderMovementMessage = "Eksik işaretlenen ürünün, eksik olduğu onaylanmıştır.";
            if(request.ApprovalStatus == 2)
                orderMovementMessage = $"Eksik işaretlenen ürün talebi, eksik olduğu reddedilmiştir ve {request.ShelfName} isimli raftan toplanması için tavsiye yapılmıştır.";

            await _orderMovementService.AddAsync(new OrderMovementAddDto
            {
                OrderID = product.OrderID,
                AgentID = WebSiteInfo.User.Value.ID,
                isSystem = true,
                Name = WebSiteInfo.User.Value.Name,
                Message = orderMovementMessage,
            }, cancellationToken);

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(PickingProductEvent.ApprovalStatus, new MissingProductApprovalStatusEventPayload(request.OrderProductId, request.ApprovalStatus, DateTime.Now.ToTimestamp())));


            var collection = (await _orderCollectionSetDal.
                GetListAsync(new OrderCollectionSetFilter() { OrderID = product.OrderID, CarID = product.WarehouseCarID })).FirstOrDefault();

            int userId = collection != null ? collection.PreparedID : 0;

            WebSiteInfo.User.Value.Events.Add(new DomainEvent(NotificationEvent.Created,
                    new CreateNotificationEventPayload(userId, "Eksik Ürün Yönetici Kontrolü",
                    $"{WebSiteInfo.User.Value.Name} kullanıcısı tarafından, {product.WarehouseCarDefination} arabasında bulunan, {product.ProductName} ürününün eksik ürün yönetici kontrolü tamamlandı. Onay Durumu : {(request.ApprovalStatus == 1 ? "Onaylandı" : "Onaylanmadı")}",
                    "<i class='fi fi-sr-basket-shopping-plus'></i>", false, false, false, NotificationTypes.MissingProductApprovalStatus)));
            return response;
        }

        public async Task<DataResult<int>> GetCount(WarehouseCarProductGetListDto request, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _warehouseCarProductDal.GetCountAsync(request.ToFilter(), cancellationToken);

            return response;
        }

        public async Task<List<WarehouseCarMissingProductsDto>> GetListMissingProducts(WarehouseCarProductGetListDto request, PagingDto paging = null, CancellationToken cancellationToken = default)
        {
            List<WarehouseCarMissingProductsDto> response = new List<WarehouseCarMissingProductsDto>();

            var carProductResponse = new DataResult<List<WarehouseCarProduct>>();
            carProductResponse.Model = (await _warehouseCarProductDal.GetListAsync(request.ToFilter(), paging != null ? new WarehouseCarProductPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();
            var carIdList = carProductResponse.Model.Select(x => x.WarehouseCarID).Distinct().ToList();
            var cars = await _warehouseCarDal.GetListAsync(new WarehouseCarFilter()
            {
                CarIds = carIdList,
                WarehouseID = WebSiteInfo.User.Value.WarehouseID
            }, null, cancellationToken);

            var collectionProducts = await _orderCollectionSetDal.GetListAsync(
                new OrderCollectionSetFilter()
                {
                    ProductIds = carProductResponse.Model.Select(x => x.ProductID).ToList(),
                    CarIDList = carIdList,
                    MissingProduct = true
                });


            foreach (var carProduct in carProductResponse.Model)
            {
                var car = cars.FirstOrDefault(x => x.ID == carProduct.WarehouseCarID);
                string shelfBarcode = "";
                string shelfName = "";
                int orderProductId = 0;
                string fileId = "";
                string fileNo = "";

                if(request.IsWarehouseTransfer && carProduct.Settings.fileId.HasValue)
                {
                    var file = _warehouseProductTransferFileDal.GetById(carProduct.Settings.fileId.Value, cancellationToken);
                    fileId = file.Result.Id.ToString();
                    fileNo = file.Result.FileNo;
                }

                if (car != null)
                {
                    var collectionProduct = collectionProducts.FirstOrDefault(x => x.CarID == car.ID && x.OrderID == carProduct.OrderID && x.ProductID == carProduct.ProductID);
                    var collectionOrderProduct = collectionProducts.FirstOrDefault(x => x.OrderID == carProduct.OrderID && x.ProductID == carProduct.ProductID);
                    if (collectionOrderProduct != null)
                        orderProductId = collectionOrderProduct.OrderProductID;


                    if (collectionProduct != null)
                    {
                        shelfBarcode = collectionProduct.ShelfBarcode;
                        shelfName = collectionProduct.ShelfName;
                    }

                }
                var responseItem = new WarehouseCarMissingProductsDto()
                {
                    Id = carProduct.ID,
                    OrderId = carProduct.OrderID,
                    OrderProductId = orderProductId,
                    ProductBarcode = carProduct.Barcode,
                    ProductImage = carProduct.Image,
                    ProductName = carProduct.ProductName,
                    ProductStockCode = carProduct.StockCode,
                    SetNo = car != null ? car.SetNo : "",
                    UserName = car != null ? car.PersonName : "",
                    WarehouseCarDefinition = car != null ? car.Definition : "",
                    Amount = carProduct.Piece,
                    ApprovelStatus = carProduct.ApprovelStatus,
                    ShelfBarcode = shelfBarcode,
                    ShelfName = shelfName,
                    RecommendedShelf = carProduct.Settings.shelfName,
                    FileId = fileId,
                    FileNo = fileNo
                };
                response.Add(responseItem);
            }

            return response;
        }

    }
}