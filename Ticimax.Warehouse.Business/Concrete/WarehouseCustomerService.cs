using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class WarehouseCustomerService : BaseService, IWarehouseCustomerService
    {
        private readonly IWarehouseCustomerDal _warehouseCustomerDal;

        public WarehouseCustomerService(IWarehouseCustomerDal warehouseCustomerDal)
        {
            _warehouseCustomerDal = warehouseCustomerDal;
        }

        public async Task<ErrorResponse> Add(WarehouseCustomerAddDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var customer = request.ToEntity();
            customer.CreateUserID = WebSiteInfo.User.Value.ID;
            await _warehouseCustomerDal.AddAsync(customer,cancellationToken);

            return response;
        }

        public async Task<ErrorResponse> MultipleAdd(WarehouseCustomerMultipleAddDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var customers = request.List.Select(x => x.ToEntity()).ToList();
            await _warehouseCustomerDal.MultipleAdd(customers, cancellationToken);

            return response;
        }

        public async Task<ErrorResponse> Delete(WarehouseCustomerDeleteDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var count = await _warehouseCustomerDal.GetCountAsync(new WarehouseCustomerFilter { ID = request.ID }, cancellationToken);
            if (count > 0)
            {
                await _warehouseCustomerDal.DeleteAsync(new WarehouseCustomer { ID = request.ID }, cancellationToken);
            }
            else
            {
                throw new NotFoundException("MusteriBulunamadi");
            }

            return response;
        }

        public async Task<DataResult<int>> GetCount(WarehouseCustomerFilter filter, CancellationToken cancellationToken)
        {
            DataResult<int> response = new DataResult<int>();

            response.Model = await _warehouseCustomerDal.GetCountAsync(filter, cancellationToken);

            return response;
        }

        public async Task<DataResult<List<WarehouseCustomer>>> GetList(WarehouseCustomerFilter filter, PagingDto paging, CancellationToken cancellationToken)
        {
            DataResult<List<WarehouseCustomer>> response = new DataResult<List<WarehouseCustomer>>();

            response.Model = (await _warehouseCustomerDal.GetListAsync(filter, paging != null ? new WarehouseCustomerPaging(paging.PageNo, paging.RecordNumber) : null)).ToList();
            if (filter.isGetCount)
                response.Count = await _warehouseCustomerDal.GetCountAsync(filter, cancellationToken);

            return response;
        }

        public async Task<ErrorResponse> Update(WarehouseCustomerUpdateDto request, CancellationToken cancellationToken)
        {
            ErrorResponse response = new ErrorResponse();

            var count = await _warehouseCustomerDal.GetCountAsync(new WarehouseCustomerFilter { ID = request.ID }, cancellationToken);
            if (count > 0)
            {
                var customer = request.ToEntity();
                customer.ModifiedUserID = WebSiteInfo.User.Value.ID;
                await _warehouseCustomerDal.UpdateAsync(customer, cancellationToken);
            }
            else
            {
                throw new NotFoundException("MusteriBulunamadi");
            }

            return response;
        }
    }
}