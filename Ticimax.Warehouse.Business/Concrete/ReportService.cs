#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Entities.Dtos;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Inbound.Enums;
using Ticimax.Warehouse.Business.Concrete.Inbound.Models.Request;
using Ticimax.Warehouse.Business.Concrete.Inbound.Models.Response;
using Ticimax.Warehouse.Business.Concrete.Report.AnnualReport.Models.Request;
using Ticimax.Warehouse.Business.Concrete.Report.AnnualReport.Models.Response;
using Ticimax.Warehouse.Business.Concrete.Report.BilledOrder.Request;
using Ticimax.Warehouse.Business.Concrete.Report.BilledOrder.Response;
using Ticimax.Warehouse.Business.Concrete.Report.CargoChange.Request;
using Ticimax.Warehouse.Business.Concrete.Report.CargoChange.Response;
using Ticimax.Warehouse.Business.Concrete.Report.HistoricalStock.Events;
using Ticimax.Warehouse.Business.Concrete.Report.HistoricalStock.Response;
using Ticimax.Warehouse.Business.Concrete.Report.MissingProducts.Request;
using Ticimax.Warehouse.Business.Concrete.Report.MissingProducts.Response;
using Ticimax.Warehouse.Business.Concrete.Report.Models;
using Ticimax.Warehouse.Business.Concrete.Report.PerformanceReports.Request;
using Ticimax.Warehouse.Business.Concrete.Report.PerformanceReports.Response;
using Ticimax.Warehouse.Business.Concrete.Report.PickedProduct.Request;
using Ticimax.Warehouse.Business.Concrete.Report.PickedProduct.Response;
using Ticimax.Warehouse.Business.Concrete.Report.PickedProductStore.Request;
using Ticimax.Warehouse.Business.Concrete.Report.PickedProductStore.Response;
using Ticimax.Warehouse.Business.Concrete.Report.ProductExtraction.Request;
using Ticimax.Warehouse.Business.Concrete.Report.ProductExtraction.Response;
using Ticimax.Warehouse.Business.Concrete.Report.ProductMovementStockControl;
using Ticimax.Warehouse.Business.Concrete.Report.ProductPlacement.Request;
using Ticimax.Warehouse.Business.Concrete.Report.ProductPlacement.Response;
using Ticimax.Warehouse.Business.Concrete.Report.PurchaseOrder.Response;
using Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderProduct.Response;
using Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderReport.Request;
using Ticimax.Warehouse.Business.Concrete.Report.ReturnOrderReport.Response;
using Ticimax.Warehouse.Business.Concrete.Report.ShippedOrder.Models.Request;
using Ticimax.Warehouse.Business.Concrete.Report.ShippedOrder.Models.Response;
using Ticimax.Warehouse.Business.Concrete.Report.TableMovements.Request;
using Ticimax.Warehouse.Business.Concrete.Report.TableMovements.Response;
using Ticimax.Warehouse.Business.Concrete.Report.WarehouseCarUsage.Request;
using Ticimax.Warehouse.Business.Concrete.Report.WarehouseCarUsage.Response;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.Report;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Filters.GoodsAccept;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class ReportService : BaseService, IReportService
    {
        private readonly IReportDal _reportDal;
        private readonly IPickingProductDal _pickingProductDal;
        private readonly IShelfProductService _shelfProductService;
        private readonly IProductService _productService;
        private readonly IWarehouseCarProductService _warehouseCarProductService;
        private readonly IOrderCollectionService _orderCollectionService;
        private readonly IExcessProductService _excessProductService;
        private readonly IShelfService _shelfService;
        private readonly IOrderProductService _orderProductService;
        private readonly IOrderService _orderService;
        private readonly IPurchaseOrderService _purchaseOrderService;
        private readonly IOrderProductReturnCauseService _orderProductReturnCauseService;
        private readonly ICategoryService _categoryService;
        private readonly ICargoIntegrationService _cargoIntegrationService;
        private readonly ICargoCompanyService _cargoCompanyService;
        private readonly IConsignmentProductService _consigmentProductService;
        private readonly ILogger<ReportService> _logger;
        private readonly string _reportServiceUrl;
        private readonly string _wmsInternalApiUrl;
        public ReportService
        (
            IReportDal reportDal,
            IPickingProductDal pickingProductDal,
            IShelfProductService shelfProductService,
            IProductService productService,
            IWarehouseCarProductService warehouseCarProductService,
            IOrderCollectionService orderCollectionService,
            IExcessProductService excessProductService,
            IShelfService shelfService,
            IOrderProductService orderProductService,
            IOrderService orderService,
            IPurchaseOrderService purchaseOrderService,
            IOrderProductReturnCauseService orderProductReturnCauseService,
            IConfiguration configuration,
            ICategoryService categoryService,
            ICargoIntegrationService cargoIntegrationService,
            ICargoCompanyService cargoCompanyService,
            IConsignmentProductService consigmentProductService,
            ILogger<ReportService> logger
        )
        {
            _reportDal = reportDal;
            _pickingProductDal = pickingProductDal;
            _shelfProductService = shelfProductService;
            _productService = productService;
            _warehouseCarProductService = warehouseCarProductService;
            _orderCollectionService = orderCollectionService;
            _excessProductService = excessProductService;
            _shelfService = shelfService;
            _orderProductService = orderProductService;
            _orderService = orderService;
            _purchaseOrderService = purchaseOrderService;
            _orderProductReturnCauseService = orderProductReturnCauseService;
            _categoryService = categoryService;
            _cargoIntegrationService = cargoIntegrationService;
            _cargoCompanyService = cargoCompanyService;
            _consigmentProductService = consigmentProductService;
            _logger = logger;
            _reportServiceUrl = configuration.GetValue<string>(ConfigKeys.ReportUrl);
            _wmsInternalApiUrl = configuration.GetValue<string>(ConfigKeys.WmsInternalUrl);
        }

        public async Task<DataResult<List<ToBePickOrderDto>>> GetToBePickProductsAsync(GetToBePickOrderFilterDto filter, CancellationToken cancellationToken)
        {
            DataResult<List<ToBePickOrderDto>> response = new DataResult<List<ToBePickOrderDto>>();

            if (!string.IsNullOrEmpty(filter.ProductIdsStr))
            {
                if (filter.ProductIds == null)
                    filter.ProductIds = new List<int>();

                filter.ProductIds.AddRange(filter.ProductIdsStr.Split(",").Select(x => int.Parse(x)).ToList());
            }


            var pickingFilter = new PickingProductFilter
            {
                OrderStatus = OrderStatus.On,
                PackageStatus = PackageStatus.Beklemede,
                PriorityPackageStatus = WebSiteInfo.User.Value.Settings.SiparisSecimAyar.OncelikliSiparisPaketlemeDurumu,
                OrderSource = filter.OrderSource,
                CargoCompanyId = filter.CargoCompanyId,
                ProductIds = filter.ProductIds,
                IsConsignmentProduct = filter.IsConsignmentProduct,
                IsReportRequest = filter.IsReportRequest,
                OrderID = filter.OrderId,
                Barcode = filter.Barcode,
                StockCode = filter.StockCode,
                AllOrderFromAllStores = filter.AllOrderFromAllStores,
                PaymentType = filter.PaymentType
            };

            if (filter.isSingleOrder.HasValue)
            {
                if (filter.isSingleOrder.Value)
                    pickingFilter.ProductCount = 1;
                else
                    pickingFilter.ProductCountLarge = 1;
            }

            var toBePickOrder = await _pickingProductDal.GetListAsync(pickingFilter, null, cancellationToken);

            response.Model = toBePickOrder.Select(x =>
                new ToBePickOrderDto
                {
                    OrderID = x.OrderID,
                    OrderSource = x.OrderSource,
                    ProductID = x.ProductID,
                    Barcode = x.Barcode,
                    StockCode = x.StockCode,
                    Piece = x.Piece,
                    OrderProductPiece = x.OrderProductPiece,
                    CargoCompanyId = x.CarrierId,
                    CargoCompany = x.CarrierName,
                    ProductName = x.ProductName,
                    Variation = x.Variation,
                    IsConsignmentProduct = x.IsConsignmentProduct,
                    ConsignmentProductId = x.ConsignmentProductId,
                    OrderDate = x.OrderDate.ToTimestamp(),
                    OrderDateConvert = x.OrderDate,
                    MemberName = x.MemberName,
                    PaymentType = x.PaymentType,
                    PaymentTypeStr = x.PaymentTypeStr
                }).ToList();

            return response;
        }

        public async Task<DataResult<List<ToBePickOrderDto>>> OffTheShelfProductsAsync(GetToBePickOrderFilterDto filter, CancellationToken cancellationToken)
        {
            DataResult<List<ToBePickOrderDto>> response = new DataResult<List<ToBePickOrderDto>>();
            filter.IsReportRequest = true;

            var toBePickResult = await GetToBePickProductsAsync(filter, cancellationToken);
            var toBePick = toBePickResult.Model;
            if (toBePick.Count > 0)
            {
                List<ShelfProductInPiece> shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto { ProductIDList = toBePick.Select(x => x.ProductID).ToList(), IsStockAvailable = true }, null, cancellationToken)).Model.OrderBy(x => x.WarehouseRank).ToList();
                foreach (var product in toBePick)
                {
                    List<int> denenenRaflar = new List<int>();
                    var shelfProduct = shelfProducts.FirstOrDefault(x => x.ProductID == product.ProductID);
rafStokDene:
                    if (shelfProduct != null)
                    {
                        var assignedTotalStock = toBePick.Where(x => x.ShelfID == shelfProduct.ShelfID && x.ProductID == product.ProductID).Sum(x => x.Piece);
                        if (shelfProduct.ShelfStock > assignedTotalStock)
                            product.ShelfID = shelfProduct.ShelfID;
                        else
                        {
                            denenenRaflar.Add(shelfProduct.ShelfID);
                            shelfProduct = shelfProducts.FirstOrDefault(x => !denenenRaflar.Contains(x.ShelfID) && x.ProductID == product.ProductID);
                            goto rafStokDene;
                        }
                    }
                }
            }


            var tobePickProducts = toBePick.Where(x => x.ShelfID == 0).ToList();

            if (tobePickProducts.Count > 0)
            {
                List<int> consigmentProductIds = toBePick.Where(x => x.ConsignmentProductId != 0).Select(x => x.ConsignmentProductId).ToList();
                var consigmentProducts = await _consigmentProductService.GetList(new ConsignmentProductGetListDto() { IDs = consigmentProductIds });

                var products = (await _productService.GetList(new ProductGetListDto() { IDList = tobePickProducts.Select(x => x.ProductID).Distinct().ToList(), AddSubQueries = false }, null, cancellationToken)).Model;
                foreach (var tobePickProduct in tobePickProducts)
                {
                    var product = products.FirstOrDefault(x => x.ID == tobePickProduct.ProductID);
                    if (product == null)
                        continue;

                    tobePickProduct.Name = product.ProductName;
                    tobePickProduct.Image = product.Image;

                    if (tobePickProduct.ConsignmentProductId > 0 && consigmentProducts.Model.Count > 0)
                    {
                        var consigmentProduct = consigmentProducts.Model.FirstOrDefault(x => x.ID == tobePickProduct.ConsignmentProductId);
                        if (consigmentProduct != null)
                            tobePickProduct.ConsigmentProductSupplyDate = consigmentProduct.SupplyDate;
                    }
                }
            }


            response.Model = tobePickProducts;
            response.Count = response.Model.Count;

            return response;
        }

        public async Task<Nextable<StockReportDto>> StockMovementReport(StockReportFilter filter, Paging paging, CancellationToken cancellationToken)
        {
            Nextable<StockReportDto> response = new Nextable<StockReportDto>(false, new List<StockReportDto>());
            ProductGetListDto productFilter = new ProductGetListDto { ID = filter.ProductId.HasValue ? filter.ProductId.Value : 0 };
            if (!string.IsNullOrEmpty(filter.ProductIds))
            {
                List<int> productIdsInt = filter.ProductIds.Split(",").Select(x => int.Parse(x)).ToList();
                productFilter.IDList = productIdsInt;
            }

            if (paging.PageNo <= 0) paging.PageNo = 1;
            if (paging.RecordNumber <= 0) paging.RecordNumber = 20;

            paging.RecordNumber += 1;

            var products = (await _productService.GetList(productFilter, paging, cancellationToken)).Model.ToList();
            if (products == null || !products.Any())
                return response;

            response.Next = products.Count == paging.RecordNumber;

            var productIds = products.Select(x => x.ID).Take(paging.RecordNumber - 1).ToList();
            var shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto { ProductIDList = productIds, IsEmptyShelf = false }, null, cancellationToken)).Model;
            var warehouseCarProducts = (await _warehouseCarProductService.GetList(new WarehouseCarProductGetListDto { TargetIDs = productIds }, null, cancellationToken)).Model;
            var orderCollectionProducts = (await _orderCollectionService.GetCollectionSet(new GetOrderCollectionSetDto { Filter = new OrderCollectionSetFilter { isGrouping = true, ProductIds = productIds } }, cancellationToken)).Model;
            var excessProducts = (await _excessProductService.GetList(new ExcessProductGetListDto { ProductIDs = productIds, Status = 0 }, null, cancellationToken)).Model;
            var purchaseOrderProductReports = _purchaseOrderService.GetPurchaseOrderProductReport(productIds, cancellationToken).GetAwaiter().GetResult().Reports;
            var exitPendings = await _pickingProductDal.GetListAsync(new PickingProductFilter { ProductIds = productIds }, null, cancellationToken);

            foreach (var product in products)
            {
                response.Contents.Add(
                    new StockReportDto
                    {
                        ID = product.ID,
                        Barcode = product.Barcode,
                        Definition = product.ProductName,
                        QuantityOnTheShelf = shelfProducts != null && shelfProducts.Count > 0 ? shelfProducts.Where(y => y.ProductID == product.ID).Sum(y => y.ShelfStock) : 0,
                        QuantityOnTheCar = warehouseCarProducts != null && warehouseCarProducts.Count > 0 ? warehouseCarProducts.Where(y => y.ProductID == product.ID).Sum(y => y.Piece) : 0,
                        QuantityOnTheDesk = orderCollectionProducts != null && orderCollectionProducts.Products.Count > 0 ? orderCollectionProducts.Products.Where(y => y.ProductID == product.ID && y.TableID > 0 && y.BoxID > 0).Sum(y => y.Piece) : 0,
                        QuantityOfExcess = excessProducts.Count(y => y.ProductID == product.ID),
                        QuantityOfWaitingGoodsAccept = purchaseOrderProductReports != null && purchaseOrderProductReports.Any(a => a.ProductId == product.ID && a.PurchaseOrderStatus == PurchaseOrderStatus.WaitingForApproval) ? purchaseOrderProductReports.Where(a => a.ProductId == product.ID && a.PurchaseOrderStatus == PurchaseOrderStatus.WaitingForApproval).Sum(a => a.Quantity) : 0,
                        QuantityOfApprovedGoodsAccept = purchaseOrderProductReports != null && purchaseOrderProductReports.Any(a => a.ProductId == product.ID && a.PurchaseOrderStatus == PurchaseOrderStatus.Approved) ? purchaseOrderProductReports.Where(a => a.ProductId == product.ID && a.PurchaseOrderStatus == PurchaseOrderStatus.Approved).Sum(a => a.Quantity) : 0,
                        QuantityAwaitingExit = exitPendings.Where(y => y.ProductID == product.ID).Sum(x => x.OrderProductPiece)
                    });
            }

            return response;
        }

        public async Task<Pageable<WarehouseWaitingOrderDto>> WarehouseWaitingOrderAsync(WarehouseWaitingOrderFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _reportDal.WarehouseWaitingOrderAsync(filter, pageSize, pageIndex, cancellationToken);
            var counts = await _reportDal.WarehouseWaitingOrderCountAsync(filter, cancellationToken);

            //Task.WaitAll(new Task[] { contents, counts }, cancellationToken);

            return new Pageable<WarehouseWaitingOrderDto>(pageIndex, pageSize, counts, contents);
        }

        public async Task<Nextable<ReturnOrderProductResponse>> ReturnOrderProductAsync(int? orderId, int? productId, string? productIds, long? dateStart, long? dateEnd, int? returnReasonID, long? orderDateStart, long? orderDateEnd, int? categoryId, bool? isBeforeDelivery, int? supplierId, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            if (orderId.HasValue)
                query["orderId"] = orderId.Value.ToString();

            if (productId.HasValue)
                query["productId"] = productId.Value.ToString();
            if (dateStart.HasValue)
                query["dateStart"] = dateStart.Value.ToString();
            if (dateEnd.HasValue)
                query["dateEnd"] = dateEnd.Value.ToString();
            if (orderDateStart.HasValue)
                query["orderDateStart"] = orderDateStart.Value.ToString();
            if (orderDateEnd.HasValue)
                query["orderDateEnd"] = orderDateEnd.Value.ToString();
            if (returnReasonID.HasValue)
                query["returnReasonID"] = returnReasonID.Value.ToString();
            if (categoryId.HasValue)
                query["categoryId"] = categoryId.Value.ToString();
            if (supplierId.HasValue)
                query["supplierId"] = supplierId.Value.ToString();
            if (isBeforeDelivery.HasValue)
                query["isBeforeDelivery"] = isBeforeDelivery.Value.ToString();
            if (!string.IsNullOrEmpty(productIds))
                query["productIds"] = productIds;

            query["pageSize"] = pageSize.ToString();
            query["pageIndex"] = pageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("return-order-products?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();
                var returnCausies = (await _orderProductReturnCauseService.GetList(new OrderProductReturnCauseGetListDto { Active = 1 }, null, cancellationToken)).Model;
                var categories = (await _categoryService.GetList(new CategoryGetListDto() { Active = true }, null, cancellationToken)).Model;
                var returnOrderProductResponse = await JsonSerializerWrapper.Deserialize<Nextable<ReturnOrderProductResponse>>(response.Content, cancellationToken);

                foreach (var returnOrder in returnOrderProductResponse.Contents)
                {
                    returnOrder.CreatedDateConvert = DateTimeOffset.FromUnixTimeMilliseconds(returnOrder.CreatedDate).LocalDateTime;
                    returnOrder.OrderDateConvert = DateTimeOffset.FromUnixTimeMilliseconds(returnOrder.OrderDate).LocalDateTime;
                    var returnCausi = returnCausies.FirstOrDefault(x => x.ID == returnOrder.Product.ReturnReasonID);
                    var category = categories.FirstOrDefault(x => x.ID == returnOrder.Product.CategoryID);
                    if (returnCausi != null)
                    {
                        returnOrder.Product.ReturnReasonDefinition = returnCausi.Definition;
                    }
                    if (category != null)
                    {
                        returnOrder.Product.CategoryName = category.Definition;
                    }

                    if (returnOrder.Product.IsBeforeDelivery ?? false)
                        returnOrder.Product.IsBeforeDeliveryStr = "Teslim Edilmedi";
                    else
                        returnOrder.Product.IsBeforeDeliveryStr = "Teslim Edildi";
                }

                return returnOrderProductResponse;
            }
        }

        public async Task<Pageable<HistoricalStockResponse>> HistoricalStockAsync(int? productId, string productIds, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);

            if (productId.HasValue)
                query["productId"] = productId.Value.ToString();

            if (!string.IsNullOrEmpty(productIds))
                query["productIds"] = productIds.ToString();

            query["pageSize"] = pageSize.ToString();
            query["pageIndex"] = pageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("historical-stocks?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                return await JsonSerializerWrapper.Deserialize<Pageable<HistoricalStockResponse>>(response.Content, cancellationToken);
            }
        }


        public async Task<Pageable<ProductSearchResponse>> ProductSearchAsync(ProductSearchFilterDto filter, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();
            query["userId"] = WebSiteInfo.User.Value.ID.ToString();
            query["pageSize"] = filter.PageSize.ToString();
            query["pageIndex"] = filter.PageIndex.ToString();

            string queryString = query.ToString();


            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("product-search?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                return await JsonSerializerWrapper.Deserialize<Pageable<ProductSearchResponse>>(response.Content, cancellationToken);

            }
        }



        public async Task CreateInvalidateRequestForHistoryStocks(string domainName, CancellationToken cancellationToken)
        {
            try
            {
                List<HistoricalStockShelfInformationDto> products = new List<HistoricalStockShelfInformationDto>();
                int pageIndex = 0;
                int pageSize = 1000;
                do
                {
                    var items = await _reportDal.GetHistoricalStockShelfInformations(domainName, pageSize, pageIndex, cancellationToken);
                    products.AddRange(items);

                    pageIndex++;
                    if (items.Count < pageSize)
                        break;
                } while (true);

                foreach (var product in products)
                {
                    WebSiteInfo.User.Value.Events.Add(new DomainEvent("wms.historicalStock.created", new HistoricalStockCreateEvent(domainName, product.ProductId, product.ProductName, product.ProductUnit, product.Stock, product.ProductBarcode, product.StockCode, product.CreatedDate)));
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"{WebSiteInfo.User?.Value?.DomainName ?? "Domainless"} - History stocks trigger failed --- Message: {ex.Message} ----  {DateTime.Now:dd.MM.yyyy HH.mm.ss}");
            }

        }

        public async Task CreateInvalidateRequestForHistoryStocksSend(string domainName, CancellationToken cancellationToken)
        {
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_wmsInternalApiUrl}report-invalidates/history-stocks-internal"),
                Method = HttpMethod.Post
            };
            message.Headers.Add("domainName", domainName);

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (!response.IsSuccessStatusCode)
                {
                    await response.HandleKnownExceptions(cancellationToken);
                    response.EnsureSuccessStatusCode();
                }
            }

        }

        public async Task<Nextable<ShippedOrderResponse>> ShippedOrderAsync(FilterShippedOrderRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);

            if (request.OrderId.HasValue)
                query["orderId"] = request.OrderId.Value.ToString();

            if (request.PacketId.HasValue)
                query["packetId"] = request.PacketId.Value.ToString();

            if (!string.IsNullOrWhiteSpace(request.Code))
                query["code"] = request.Code;

            if (!string.IsNullOrWhiteSpace(request.CargoCompany))
                query["cargoCompany"] = request.CargoCompany;

            if (request.CargoIntegrationId.HasValue)
                query["cargoIntegrationId"] = request.CargoIntegrationId.Value.ToString();

            if (request.StartDate.HasValue)
                query["startDate"] = request.StartDate.Value.ToString();

            if (request.EndDate.HasValue)
                query["endDate"] = request.EndDate.Value.ToString();

            if (request.OrderStartDate.HasValue)
                query["orderStartDate"] = request.OrderStartDate.Value.ToString();

            if (request.OrderEndDate.HasValue)
                query["orderEndDate"] = request.OrderEndDate.Value.ToString();

            if (!string.IsNullOrWhiteSpace(request.OrderSource))
                query["orderSource"] = request.OrderSource;

            if (request.ProductCount.HasValue)
                query["productCount"] = request.ProductCount.Value.ToString();

            if (request.UseProductCountRange.HasValue)
                query["useProductCountRange"] = request.UseProductCountRange.Value.ToString();

            if (request.ProductCountStart.HasValue)
                query["productCountStart"] = request.ProductCountStart.Value.ToString();

            if (request.ProductCountEnd.HasValue)
                query["productCountEnd"] = request.ProductCountEnd.Value.ToString();

            if (request.QualityControlDesi.HasValue)
                query["qualityControlDesi"] = request.QualityControlDesi.Value.ToString();

            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("shipped-orders?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();
                var result = await JsonSerializerWrapper.Deserialize<Nextable<ShippedOrderResponse>>(response.Content, cancellationToken);
                result.Contents.ForEach(x => x.CreatedDateConvert = DateTimeOffset.FromUnixTimeMilliseconds(x.CreatedDate).LocalDateTime);
                return result;
            }
        }

        public async Task<DataResult<List<AnnualReportClientResponse>>> AnnualReportAsync(FilterAnnualReportRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);

            query["year"] = request.Year.ToString();
            if (request.Month.HasValue)
                query["month"] = request.Month.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("annual-report?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                var result = await JsonSerializerWrapper.Deserialize<List<AnnualReportResponse>>(response.Content, cancellationToken);

                foreach (var item in result)
                {
                    item.MonthStr = GetMonthName(item.Month);
                }
                DataResult<List<AnnualReportClientResponse>> cResponse = new DataResult<List<AnnualReportClientResponse>>();
                cResponse.Count = result.Count;
                cResponse.Model = result.Select(x => new AnnualReportClientResponse()
                {
                    Year = x.Year,
                    Month = x.Month,
                    MonthStr = x.MonthStr,
                    OrderCount = x.Information.OrderCount,
                    OrderAmount = x.Information.OrderAmount,
                    ProductCount = x.Information.ProductCount,
                    ProductAmount = x.Information.ProductAmount,
                    CancellationOrderCount = x.Information.CancellationOrderCount,
                    CancellationOrderAmount = x.Information.CancellationOrderAmount,
                    OrderReturnedToTheWarehouseBeforeDeliveryCount = x.Information.OrderReturnedToTheWarehouseBeforeDeliveryCount,
                    OrderReturnedToTheWarehouseBeforeDeliveryAmount = x.Information.OrderReturnedToTheWarehouseBeforeDeliveryAmount,
                    OrderReturnedToTheWarehouseDelivedCount = x.Information.OrderReturnedToTheWarehouseDelivedCount,
                    OrderReturnedToTheWarehouseDelivedAmount = x.Information.OrderReturnedToTheWarehouseDelivedAmount
                }).ToList();


                return cResponse;
            }
        }

        public async Task<Pageable<InstantStockDTO>> InstantStock(InstantStockFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _reportDal.InstantStock(filter, pageSize, pageIndex, cancellationToken);
            var count = await _reportDal.InstantStockCount(filter, pageSize, pageIndex, cancellationToken);

            return new Pageable<InstantStockDTO>(pageIndex, pageSize, count, contents);
        }

        public async Task<Pageable<BilledOrderResponse>> GetBilledOrdersAsync(FilterBilledOrderRequest filter, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            if (filter.OrderId.HasValue)
                query["orderId"] = filter.OrderId.Value.ToString();

            if (filter.PersonId.HasValue)
                query["personId"] = filter.PersonId.Value.ToString();

            if (filter.DateStart.HasValue)
                query["dateStart"] = filter.DateStart.Value.ToString();

            if (filter.DateEnd.HasValue)
                query["dateEnd"] = filter.DateEnd.Value.ToString();

            if (!string.IsNullOrEmpty(filter.CargoCompanyIds))
                query["cargoCompanyIds"] = filter.CargoCompanyIds;

            if (!string.IsNullOrEmpty(filter.OrderSource))
                query["orderSource"] = filter.OrderSource;

            if (!filter.GetAllWarehouse)
            {
                query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
                query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();
            }

            query["pageSize"] = filter.PageSize.ToString();
            query["pageIndex"] = filter.PageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("billed-orders?" + queryString, cancellationToken);

                var responseOrders = (await JsonSerializerWrapper.
                    Deserialize<Pageable<BilledOrderResponse>>
                    (response.Content, cancellationToken));

                var orderIds = responseOrders.Contents.Where(x => string.IsNullOrEmpty(x.InvoiceNo)).Select(x => x.OrderId).Distinct().ToList();

                responseOrders.Contents.ForEach(x => x.CreatedDateConvert =
                DateTimeOffset.FromUnixTimeMilliseconds(x.CreatedDate).LocalDateTime);

                if (orderIds != null && orderIds.Count > 0)
                {
                    var orders = await _orderService.GetList(new OrderGetListDto() { OrderIDList = orderIds });
                    foreach (var order in orders.Model)
                    {
                        var item = responseOrders.Contents.FirstOrDefault(x => x.OrderId == order.ID);
                        if (item != null)
                            item.InvoiceNo = order.InvoiceNo;
                    }
                }


                response.EnsureSuccessStatusCode();

                return responseOrders;
            }
        }

        public async Task<Pageable<WarehouseCarUsageResponse>> GetWarehouseCarUsageReport(FilterWarehouseCarUsageRequest filter, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            if (filter.WarehouseCarId.HasValue)
                query["WarehouseCarId"] = filter.WarehouseCarId.Value.ToString();

            if (!string.IsNullOrEmpty(filter.WarehouseCarBarcode))
                query["WarehouseCarBarcode"] = filter.WarehouseCarBarcode.ToString();

            if (filter.UserId.HasValue)
                query["UserId"] = filter.UserId.Value.ToString();

            if (filter.StartDate.HasValue)
                query["StartDate"] = filter.StartDate.Value.ToString();

            if (filter.EndDate.HasValue)
                query["EndDate"] = filter.EndDate.Value.ToString();

            query["WarehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["pageSize"] = filter.PageSize.ToString();
            query["pageIndex"] = filter.PageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("warehouse-car-usage?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();
                var result = await JsonSerializerWrapper.Deserialize<Pageable<WarehouseCarUsageResponse>>(response.Content, cancellationToken);

                foreach (var item in result.Contents)
                {
                    item.AssignTimeConvert = DateTimeOffset.FromUnixTimeMilliseconds(item.AssignTime).LocalDateTime;

                    if (item.DifferenceTime.HasValue)
                        item.DifferenceTimeConvert = DateTimeOffset.FromUnixTimeMilliseconds(item.DifferenceTime.Value).LocalDateTime;
                    if(item.LeaveTime.HasValue)
                        item.LeaveTimeConvert = DateTimeOffset.FromUnixTimeMilliseconds(item.LeaveTime.Value).LocalDateTime;

                }
                return result;
            }
        }

        public async Task<WarehouseCarUsageAverageResponse> GetWarehouseCarUsageAverage(FilterWarehouseCarUsageAverageRequest filter, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            if (filter.WarehouseCarId.HasValue)
                query["WarehouseCarId"] = filter.WarehouseCarId.Value.ToString();

            if (filter.UserId.HasValue)
                query["UserId"] = filter.UserId.Value.ToString();

            if (filter.StartDate.HasValue)
                query["StartDate"] = filter.StartDate.Value.ToString();

            if (filter.EndDate.HasValue)
                query["EndDate"] = filter.EndDate.Value.ToString();

            query["WarehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("warehouse-car-usage/average?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                var average = await JsonSerializerWrapper.Deserialize<double>(response.Content, cancellationToken);
                return new WarehouseCarUsageAverageResponse(average);
            }
        }

        public async Task<Pageable<ProductExtractionResponse>> GetProductExtractionReport(FilterProductExtractionRequest filter, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["WarehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["PageSize"] = filter.PageSize.ToString();
            query["PageIndex"] = filter.PageIndex.ToString();

            if (filter.ProductId.HasValue)
                query["ProductId"] = filter.ProductId.Value.ToString();

            if (!string.IsNullOrEmpty(filter.ProductBarcode))
                query["ProductBarcode"] = filter.ProductBarcode;

            if (!string.IsNullOrEmpty(filter.ProductStockCode))
                query["ProductStockCode"] = filter.ProductStockCode;

            if (filter.ShelfId.HasValue)
                query["ShelfId"] = filter.ShelfId.Value.ToString();

            if (!string.IsNullOrEmpty(filter.ShelfBarcode))
                query["ShelfBarcode"] = filter.ShelfBarcode;

            if (filter.UserId.HasValue)
                query["UserId"] = filter.UserId.Value.ToString();

            if (filter.StartDate.HasValue)
                query["StartDate"] = filter.StartDate.Value.ToString();

            if (filter.EndDate.HasValue)
                query["EndDate"] = filter.EndDate.Value.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("product-extraction?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();
                var result = await JsonSerializerWrapper.Deserialize<Pageable<ProductExtractionResponse>>(response.Content, cancellationToken);

                result.Contents.ForEach(x => x.CreatedDateConvert =
                DateTimeOffset.FromUnixTimeMilliseconds(x.CreatedDate).LocalDateTime);
                return result;
            }
        }

        public async Task<Pageable<ProductPlacementResponse>> GetProductPlacementReport(FilterProductPlacementRequest filter, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["WarehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["PageSize"] = filter.PageSize.ToString();
            query["PageIndex"] = filter.PageIndex.ToString();

            if (filter.ProductId.HasValue)
                query["ProductId"] = filter.ProductId.Value.ToString();

            if (!string.IsNullOrEmpty(filter.ProductBarcode))
                query["ProductBarcode"] = filter.ProductBarcode;

            if (!string.IsNullOrEmpty(filter.ProductStockCode))
                query["ProductStockCode"] = filter.ProductStockCode;

            if (filter.ShelfId.HasValue)
                query["ShelfId"] = filter.ShelfId.Value.ToString();

            if (!string.IsNullOrEmpty(filter.ShelfBarcode))
                query["ShelfBarcode"] = filter.ShelfBarcode;

            if (!string.IsNullOrEmpty(filter.ShelfIds))
                query["ShelfIds"] = filter.ShelfIds;

            if (filter.UserId.HasValue)
                query["UserId"] = filter.UserId.Value.ToString();

            if (filter.SupplierId.HasValue)
                query["SupplierId"] = filter.SupplierId.Value.ToString();

            if (filter.StartDate.HasValue)
                query["StartDate"] = filter.StartDate.Value.ToString();

            if (filter.EndDate.HasValue)
                query["EndDate"] = filter.EndDate.Value.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("product-placement?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();
                var result = await JsonSerializerWrapper.Deserialize<Pageable<ProductPlacementResponse>>(response.Content, cancellationToken);

                result.Contents.ForEach(x => x.CreatedDateConvert =
                DateTimeOffset.FromUnixTimeMilliseconds(x.CreatedDate).LocalDateTime);
                return result;
            }
        }

        public async Task<Pageable<GoodsAcceptFileReportDto>> GoodsAcceptFileReport(GoodsAcceptFileReportFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var contents = await _reportDal.GoodsAcceptFileReportAsync(filter, pageSize, pageIndex, cancellationToken);
            var count = await _reportDal.GoodsAcceptFileReportCountAsync(filter, pageSize, pageIndex, cancellationToken);

            return new Pageable<GoodsAcceptFileReportDto>(pageIndex, pageSize, count, contents);
        }

        public async Task<Pageable<StockReportForAllWarehousesDto>> StockReportForAllWarehouses(int? productId, double? minAllocatedStock, double? maxAllocatedStock, double? minWebStock, double? maxWebStock, string productIds, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            List<int> productIdsInt = new List<int>();
            if (!string.IsNullOrEmpty(productIds))
                productIdsInt = productIds.Split(",").Select(x => int.Parse(x)).ToList();
            var contents = await _reportDal.StockReportForAllWarehouses(productId, minAllocatedStock, maxAllocatedStock, minWebStock, maxWebStock, productIdsInt, pageSize, pageIndex, cancellationToken);
            var totalCount = await _reportDal.StockReportForAllWarehousesCountAsync(productId, minAllocatedStock, maxAllocatedStock, minWebStock, maxWebStock, productIdsInt, cancellationToken);

            var toBePickResult = await GetToBePickProductsAsync(new GetToBePickOrderFilterDto() { ProductIds = contents.Select(x => x.ProductId).ToList(), IsReportRequest = true }, cancellationToken);
            var toBePick = toBePickResult.Model;
            foreach (var content in contents)
            {
                content.SoldStock = toBePick.Where(x => x.ProductID == content.ProductId).Sum(x => x.Piece);
            }

            return new Pageable<StockReportForAllWarehousesDto>(pageIndex, pageSize, totalCount, contents);
        }

        public async Task<Pageable<StockLocationReportForAllWarehousesDto>> StockLocationReportForAllWarehouses(StockLocationReportFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            if (!string.IsNullOrEmpty(filter.ShelfDefinition))
            {
                var shelf = (await _shelfService.GetList(new ShelfGetListDto() { Definition = filter.ShelfDefinition })).Model.FirstOrDefault();
                if (shelf != null)
                    filter.ShelfID = shelf.ID;
            }
            var contents = await _reportDal.StockLocationReportForAllWarehouses(filter, pageSize, pageIndex, cancellationToken);
            var totalCount = await _reportDal.StockLocationReportForAllWarehousesCount(filter, cancellationToken);

            return new Pageable<StockLocationReportForAllWarehousesDto>(pageIndex, pageSize, totalCount, contents);
        }

        public async Task<Pageable<FilterPurchaseOrderHistoryReportResponse>> FilterPurchaseOrderHistoryReport(PurchaseOrderHistoryReportRequest filter, CancellationToken cancellationToken)
        {
            var result = await _purchaseOrderService.FilterPurchaseOrderHistoryReport(filter, cancellationToken);
            return result;
        }

        public async Task<Pageable<FilterPurchaseOrderProductReportResponse>> FilterPurchaseOrderProductReport(PurchaseOrderProductReportRequest filter, CancellationToken cancellationToken)
        {
            var result = await _purchaseOrderService.FilterPurchaseOrderProductReport(filter, cancellationToken);
            return result;
        }

        public async Task<DataResult<List<QualityControlPerformanceReportResponse>>> QualityControlPerformanceReport(QualityControlPerformanceReportRequest request, CancellationToken cancellationToken)
        {
            var result = new DataResult<List<QualityControlPerformanceReportResponse>>();

            var query = HttpUtility.ParseQueryString(string.Empty);
            query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();

            if (request.StartDate.HasValue)
                query["startDate"] = request.StartDate.ToString();

            if (request.EndDate.HasValue)
                query["endDate"] = request.EndDate.ToString();

            string queryString = query.ToString();
            if (!string.IsNullOrEmpty(request.UserId))
            {
                var userIds = request.UserId.Split(',').Where(x => !string.IsNullOrEmpty(x)).Select(x => $"userId={x}").ToList();
                queryString += "&" + string.Join("&", userIds);
            }

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("performance-report/quality-control?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                result.Model = await JsonSerializerWrapper.Deserialize<List<QualityControlPerformanceReportResponse>>(response.Content, cancellationToken);
            }

            return result;
        }

        public async Task<DataResult<List<ProductPickingPerformanceReportResponse>>> ProductPickingPerformanceReport(ProductPickingPerformanceReportRequest request, CancellationToken cancellationToken)
        {
            var result = new DataResult<List<ProductPickingPerformanceReportResponse>>();

            var query = HttpUtility.ParseQueryString(string.Empty);
            query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();

            if (request.StartDate.HasValue)
                query["startDate"] = request.StartDate.ToString();

            if (request.EndDate.HasValue)
                query["endDate"] = request.EndDate.ToString();

            string queryString = query.ToString();
            if (!string.IsNullOrEmpty(request.UserId))
            {
                var userIds = request.UserId.Split(',').Where(x => !string.IsNullOrEmpty(x)).Select(x => $"userId={x}").ToList();
                queryString += "&" + string.Join("&", userIds);
            }

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("performance-report/product-picking?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                result.Model = await JsonSerializerWrapper.Deserialize<List<ProductPickingPerformanceReportResponse>>(response.Content, cancellationToken);
            }

            return result;
        }

        public async Task<DataResult<List<ReturnOrderProductPerformanceReportResponse>>> ReturnOrderProductPerformanceReport(ReturnOrderProductPerformanceReportRequest request, CancellationToken cancellationToken)
        {
            var result = new DataResult<List<ReturnOrderProductPerformanceReportResponse>>();

            var query = HttpUtility.ParseQueryString(string.Empty);
            query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();

            if (request.StartDate.HasValue)
                query["startDate"] = request.StartDate.ToString();

            if (request.EndDate.HasValue)
                query["endDate"] = request.EndDate.ToString();

            string queryString = query.ToString();
            if (!string.IsNullOrEmpty(request.UserId))
            {
                var userIds = request.UserId.Split(',').Where(x => !string.IsNullOrEmpty(x)).Select(x => $"userId={x}").ToList();
                queryString += "&" + string.Join("&", userIds);
            }

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("performance-report/return-order-product?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                result.Model = await JsonSerializerWrapper.Deserialize<List<ReturnOrderProductPerformanceReportResponse>>(response.Content, cancellationToken);
            }

            return result;
        }

        public async Task<DataResult<List<ProductPlacementPerformanceReportResponse>>> ProductPlacementPerformanceReport(ProductPlacementPerformanceReportRequest request, CancellationToken cancellationToken)
        {
            var result = new DataResult<List<ProductPlacementPerformanceReportResponse>>();

            var query = HttpUtility.ParseQueryString(string.Empty);
            query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();

            if (request.StartDate.HasValue)
                query["startDate"] = request.StartDate.ToString();

            if (request.EndDate.HasValue)
                query["endDate"] = request.EndDate.ToString();

            string queryString = query.ToString();
            if (request.UserId != null && request.UserId.Any())
            {
                var userIds = request.UserId.Split(',').Where(x => !string.IsNullOrEmpty(x)).Select(x => $"userId={x}").ToList();
                queryString += "&" + string.Join("&", userIds);
            }

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("performance-report/product-placement?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                result.Model = await JsonSerializerWrapper.Deserialize<List<ProductPlacementPerformanceReportResponse>>(response.Content, cancellationToken);
            }

            return result;
        }

        public async Task<Pageable<PickedProductResponse>> FilterPickedProductReport(FilterPickedProductRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();

            if (request.UserId.HasValue)
                query["userId"] = request.UserId.ToString();

            if (request.DateStart.HasValue)
                query["dateStart"] = request.DateStart.ToString();

            if (request.DateEnd.HasValue)
                query["dateEnd"] = request.DateEnd.ToString();

            if (!string.IsNullOrEmpty(request.ProductIds))
                query["productIds"] = request.ProductIds.ToString();

            if (!request.GetAllWarehouse)
            {
                query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
                query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();
            }

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("picked-product?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();
                var result = await JsonSerializerWrapper.Deserialize<Pageable<PickedProductResponse>>(response.Content, cancellationToken);
                result.Contents.ForEach(x => x.CreatedDateConvert =
               DateTimeOffset.FromUnixTimeMilliseconds(x.CreatedDate).LocalDateTime);
                return result;
            }
        }

        public async Task<Pageable<PickedProductStoreResponse>> FilterPickedProductStoreReport(FilterPickedProductStoreRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();
            query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();
            query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();

            if (request.OrderDateStart.HasValue)
                query["orderDateStart"] = request.OrderDateStart.Value.ToString();
            if (request.OrderDateEnd.HasValue)
                query["OrderDateEnd"] = request.OrderDateEnd.Value.ToString();
            if (request.RedirectDateStart.HasValue)
                query["redirectDateStart"] = request.RedirectDateStart.Value.ToString();
            if (request.RedirectDateEnd.HasValue)
                query["redirectDateEnd"] = request.RedirectDateEnd.Value.ToString();
            if (request.ProductId.HasValue)
                query["productId"] = request.ProductId.Value.ToString();
            if (request.OrderId.HasValue)
                query["orderId"] = request.OrderId.Value.ToString();

            if (!string.IsNullOrEmpty(request.ProductIds))
                query["productIds"] = request.ProductIds.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("picked-product-store?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                return await JsonSerializerWrapper.Deserialize<Pageable<PickedProductStoreResponse>>(response.Content, cancellationToken);
            }
        }

        public async Task<Pageable<ProductWithStockErrorItem>> GetProductsWithStockErrorsReport(FilterProductsWithStockErrorRequest request, int pageIndex, int pageSize, CancellationToken cancellationToken)
        {
            List<int> productIdsInt = new List<int>();
            if (!string.IsNullOrEmpty(request.ProductIds))
                productIdsInt = request.ProductIds.Split(",").Select(x => int.Parse(x)).ToList();

            if (request.OnlyWrong.HasValue)
                pageSize = int.MaxValue;

            var haveStockProblemsProducts = await _reportDal.
                    ProductsWithStockErrorsReportAsync(request.ProductId, productIdsInt,
                    request.MinWebStock, request.MaxWebStock,
                    request.MinShelfStock, request.MaxShelfStock, request.MinConsigmentStock,
                    request.MaxConsigmentStock, pageIndex, pageSize, cancellationToken);
            var totalCount = await _reportDal.ProductsWithStockErrorsReportCountAsync(request.ProductId,
                             productIdsInt, request.MinWebStock, request.MaxWebStock,
                             request.MinShelfStock, request.MaxShelfStock, request.MinConsigmentStock,
                             request.MaxConsigmentStock, cancellationToken);

            var toBePickResult = await GetToBePickProductsAsync(new GetToBePickOrderFilterDto() { ProductIds = haveStockProblemsProducts.Select(x => x.ProductId).ToList(), IsReportRequest = true }, cancellationToken);
            var toBePick = toBePickResult.Model;

            List<ShelfProductInPiece> shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto { ProductIDList = toBePick.Select(x => x.ProductID).ToList(), IsStockAvailable = true }, null, cancellationToken)).Model.OrderBy(x => x.WarehouseRank).ToList();
            foreach (var product in toBePick)
            {
                List<int> denenenRaflar = new List<int>();
                var shelfProduct = shelfProducts.FirstOrDefault(x => x.ProductID == product.ProductID);
rafStokDene:
                if (shelfProduct != null)
                {
                    var assignedTotalStock = toBePick.Where(x => x.ShelfID == shelfProduct.ShelfID && x.ProductID == product.ProductID).Sum(x => x.Piece);
                    if (shelfProduct.ShelfStock > assignedTotalStock)
                        product.ShelfID = shelfProduct.ShelfID;
                    else
                    {
                        denenenRaflar.Add(shelfProduct.ShelfID);
                        shelfProduct = shelfProducts.FirstOrDefault(x => !denenenRaflar.Contains(x.ShelfID) && x.ProductID == product.ProductID);
                        goto rafStokDene;
                    }
                }
            }
            List<ProductWithStockErrorItem> removedItems = new List<ProductWithStockErrorItem>();
            List<ProductWithStockErrorItem> removedItems2 = new List<ProductWithStockErrorItem>();
            foreach (var item in haveStockProblemsProducts)
            {
                item.SoldStock = toBePick.Where(x => x.ProductID == item.ProductId).Sum(x => x.Piece);
                item.UpgradedAfterECommerceStock = item.ShelfStock - item.SoldStock;
                if ((item.ECommerceStock == item.UpgradedAfterECommerceStock)
                    || (item.UpgradedAfterECommerceStock + item.ConsignmentStock < 0))
                    removedItems.Add(item);
                else
                    removedItems2.Add(item);
            }
            if(request.OnlyWrong.HasValue && request.OnlyWrong.Value)
            {
                haveStockProblemsProducts.RemoveAll(x => removedItems.Contains(x));
                totalCount = haveStockProblemsProducts.Count;
            }
                
            else if(request.OnlyWrong.HasValue && !request.OnlyWrong.Value)
            {
                haveStockProblemsProducts.RemoveAll(x => removedItems2.Contains(x));
                totalCount = haveStockProblemsProducts.Count;
            }
                

            return new Pageable<ProductWithStockErrorItem>(pageIndex, pageSize, totalCount, haveStockProblemsProducts);
        }

        public async Task HaveAStockProblemProductsFix(HaveAStockProblemProductsFixRequest request, CancellationToken cancellationToken)
        {
            if (!request.ProductIds.Any())
                throw new BusinessException("REQUEST_PRODUCTS_IS_NOT_BE_NULL");

            var products = await _productService.GetListAsync(new ProductGetListDto() { IDList = request.ProductIds, AddSubQueries = false }, cancellationToken: cancellationToken);
            var toBePickResult = await GetToBePickProductsAsync(new GetToBePickOrderFilterDto() { ProductIds = products.Select(x => x.ID).ToList(), IsReportRequest = true }, cancellationToken);
            var toBePick = toBePickResult.Model;

            List<ShelfProductInPiece> shelfProducts = (await _shelfProductService.GetList(new ShelfProductGetListDto { ProductIDList = toBePick.Select(x => x.ProductID).ToList(), IsStockAvailable = true, IsSaleOpened = true }, null, cancellationToken)).Model.OrderBy(x => x.WarehouseRank).ToList();
            foreach (var product in toBePick)
            {
                List<int> denenenRaflar = new List<int>();
                var shelfProduct = shelfProducts.FirstOrDefault(x => x.ProductID == product.ProductID);
rafStokDene:
                if (shelfProduct != null)
                {
                    var assignedTotalStock = toBePick.Where(x => x.ShelfID == shelfProduct.ShelfID && x.ProductID == product.ProductID).Sum(x => x.Piece);
                    if (shelfProduct.ShelfStock > assignedTotalStock)
                        product.ShelfID = shelfProduct.ShelfID;
                    else
                    {
                        denenenRaflar.Add(shelfProduct.ShelfID);
                        shelfProduct = shelfProducts.FirstOrDefault(x => !denenenRaflar.Contains(x.ShelfID) && x.ProductID == product.ProductID);
                        goto rafStokDene;
                    }
                }
            }

            var shelfStocks = (await _shelfProductService.GetList(new ShelfProductGetListDto() { ProductIDList = request.ProductIds, IsStockAvailable = true, IsSaleOpened = true }, cancellationToken: cancellationToken)).Model;
            foreach (var product in products)
            {
                var soldStock = toBePick.Where(x => x.ProductID == product.ID).Sum(x => x.Piece);
                var shelfStock = shelfStocks.Where(x => x.ProductID == product.ID).Sum(x => x.ShelfStock);
                var upgradedAfterECommerceStock = shelfStock - soldStock;

                if (upgradedAfterECommerceStock < 0)
                    upgradedAfterECommerceStock = 0;

                if (upgradedAfterECommerceStock == product.StockPiece)
                    continue;

                await _productService.OptimizeStock(new ProductAddStockDto()
                {
                    ConsignmentModuleActive = WebSiteInfo.User.Value.Settings.PacketSettings.ConsignmentProductModulActive,
                    Piece = upgradedAfterECommerceStock,
                    ProductID = product.ID,
                    StoreID = WebSiteInfo.User.Value.StoreModuleSettings.MagazaStokSatis.Aktif ? WebSiteInfo.User.Value.StoreID : 0,
                }, cancellationToken);
            }

        }
        public async Task<DataResult<List<MissingProductReportResponse>>> MissingProductReport(MissingProductReportRequest filter, CancellationToken cancellationToken)
        {
            var result = new DataResult<List<MissingProductReportResponse>>();
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["WarehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["PageSize"] = filter.PageSize.ToString();
            query["PageIndex"] = filter.PageIndex.ToString();

            if (!string.IsNullOrEmpty(filter.ProductIds))
                query["ProductIds"] = filter.ProductIds.ToString();

            if (filter.IsFound.HasValue) { }
            query["IsFound"] = filter.IsFound.ToString();

            if (filter.UserId.HasValue)
                query["UserId"] = filter.UserId.Value.ToString();

            if (filter.OrderId.HasValue)
                query["OrderId"] = filter.OrderId.Value.ToString();

            if (filter.ApprovalStatus.HasValue)
                query["ApprovalStatus"] = filter.ApprovalStatus.Value.ToString();

            if (filter.FoundDate.HasValue)
                query["FoundDate"] = filter.FoundDate.Value.ToString();

            if (filter.StartDate.HasValue)
                query["StartDate"] = filter.StartDate.Value.ToString();

            if (filter.EndDate.HasValue)
                query["EndDate"] = filter.EndDate.Value.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("missing-product?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                result.Model = await JsonSerializerWrapper.Deserialize<List<MissingProductReportResponse>>(response.Content, cancellationToken);

                var shelfs = await _shelfService.GetList(new ShelfGetListDto() { }, null, cancellationToken);

                foreach (var missingProductResponse in result.Model)
                {
                    missingProductResponse.MissingDateConvert = DateTimeOffset.FromUnixTimeMilliseconds(missingProductResponse.MissingDate).LocalDateTime;
                    if(missingProductResponse.ApprovalDate.HasValue)
                        missingProductResponse.ApprovalDateConvert = DateTimeOffset.FromUnixTimeMilliseconds(missingProductResponse.ApprovalDate.Value).LocalDateTime;
                    if (missingProductResponse.CompletedDate.HasValue)
                        missingProductResponse.CompletedDateConvert = DateTimeOffset.FromUnixTimeMilliseconds(missingProductResponse.CompletedDate.Value).LocalDateTime;

                    if (missingProductResponse.FoundShelfId.HasValue)
                    {
                        var shelf = shelfs.Model.FirstOrDefault(x => x.ID == missingProductResponse.FoundShelfId);
                        if (shelf != null)
                            missingProductResponse.FoundShelfName = shelf.Definition;
                    }

                }

            }
            return result;
        }

        public async Task<Pageable<ProductMovementStockControlResponse>> ProductMovementStockControlAsync(int? productId, string? productIds, string? type, long? dateStart, long? dateEnd, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["WarehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();

            if (productId.HasValue)
                query["productId"] = productId.Value.ToString();
            if (!string.IsNullOrEmpty(productIds))
                query["productIds"] = productIds;
            if (!string.IsNullOrEmpty(type))
                query["type"] = type;
            if (dateStart.HasValue)
                query["dateStart"] = dateStart.Value.ToString();
            if (dateEnd.HasValue)
                query["dateEnd"] = dateEnd.Value.ToString();

            query["pageSize"] = pageSize.ToString();
            query["pageIndex"] = pageIndex.ToString();

            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("product-movement-stock-control?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();
                var result = await JsonSerializerWrapper.Deserialize<Pageable<ProductMovementStockControlResponse>>(response.Content, cancellationToken);
                return result;
            }
        }

        public async Task<Pageable<ReturnOrderPaymentsResponse>> FilterReturnOrderPaymentsReport(ReturnOrderPaymentsRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();

            if (request.OrderId.HasValue)
                query["orderId"] = request.OrderId.ToString();

            if (request.StartDate.HasValue)
                query["startDate"] = request.StartDate.ToString();

            if (request.EndDate.HasValue)
                query["endDate"] = request.EndDate.ToString();

            if (request.OrderStartDate.HasValue)
                query["orderStartDate"] = request.OrderStartDate.ToString();

            if (request.OrderEndDate.HasValue)
                query["orderEndDate"] = request.OrderEndDate.ToString();

            if (request.ReturnPaymentType.HasValue)
                query["returnPaymentType"] = request.ReturnPaymentType.ToString();

            if (request.MemberId.HasValue)
                query["memberId"] = request.MemberId.ToString();

            string queryString = query.ToString();



            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("return-order-payments?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                var result = await JsonSerializerWrapper.Deserialize<Pageable<ReturnOrderPaymentsResponse>>(response.Content, cancellationToken);
                var orderIdList = result.Contents.Select(x => x.OrderId).ToList();

                DataResult<List<Order>> orders = null;

                if (orderIdList.Count > 0)
                    orders = await _orderService.GetList(new OrderGetListDto() { OrderIDList = orderIdList });

                foreach (var orderResponse in result.Contents)
                {
                    orderResponse.CreatedDateConvert = DateTimeOffset.FromUnixTimeMilliseconds(orderResponse.CreatedDate).LocalDateTime;
                    if (orders != null)
                    {
                        var order = orders.Model.FirstOrDefault(x => x.ID == orderResponse.OrderId);
                        if (order != null)
                            orderResponse.Currency = order.CurrencyCode ?? "";
                    }
                    orderResponse.ReturnPaymentTypeStr = GetReturnPaymentType(orderResponse.ReturnPaymentType);
                }
                return result;
            }
        }

        public async Task<Pageable<CargoChangedReportResponse>> FiltreCargoChangedReport(CargoChangedReportRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();

            if (request.OrderId.HasValue)
                query["orderId"] = request.OrderId.ToString();

            if (request.UserId.HasValue)
                query["userId"] = request.UserId.ToString();

            if (request.DateStart.HasValue)
                query["dateStart"] = request.DateStart.ToString();

            if (request.DateEnd.HasValue)
                query["dateEnd"] = request.DateEnd.ToString();


            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("cargo-changed-report?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                var result = await JsonSerializerWrapper.Deserialize<Pageable<CargoChangedReportResponse>>(response.Content, cancellationToken);

                List<int> cargoIntegrationList = new List<int>();
                cargoIntegrationList.AddRange(result.Contents.Select(x => x.NewCargoIntegrationId).ToList());
                cargoIntegrationList.AddRange(result.Contents.Select(x => x.OldCargoIntegrationId).ToList());

                List<int> cargoCompanyList = new List<int>();
                cargoCompanyList.AddRange(result.Contents.Select(x => x.NewCargoCompanyId).ToList());
                cargoCompanyList.AddRange(result.Contents.Select(x => x.OldCargoCompanyId).ToList());

                var integrations = await _cargoIntegrationService.GetList(new CargoIntegrationGetListDto() { IDList = cargoIntegrationList.Distinct().ToList() }, null, cancellationToken);
                var cargoCompanies = await _cargoCompanyService.GetList(new CargoCompanyGetListDto() { Ids = cargoCompanyList.Distinct().ToList() }, null, cancellationToken);

                foreach (var item in result.Contents)
                {
                    item.CreatedDateConvert = DateTimeOffset.FromUnixTimeMilliseconds(item.CreatedDate).LocalDateTime;
                    var newIntegration = integrations.Model.FirstOrDefault(x => x.ID == item.NewCargoIntegrationId);
                    var oldIntegration = integrations.Model.FirstOrDefault(x => x.ID == item.OldCargoIntegrationId);

                    var newCargoCompany = cargoCompanies.Model.FirstOrDefault(x => x.ID == item.NewCargoCompanyId);
                    var oldCargoCompany = cargoCompanies.Model.FirstOrDefault(x => x.ID == item.OldCargoCompanyId);

                    if (newIntegration != null)
                        item.NewCargoIntegrationName = newIntegration.Description;

                    if (oldIntegration != null)
                        item.OldCargoIntegrationName = oldIntegration.Description;

                    if (newCargoCompany != null)
                        item.NewCargoCompanyName = newCargoCompany.Description;

                    if (oldCargoCompany != null)
                        item.OldCargoCompanyName = oldCargoCompany.Description;
                }

                return result;
            }
        }

        public async Task<Pageable<TableMovementsResponse>> FilterTableMovements(TableMovementsRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["warehouseId"] = WebSiteInfo.User.Value.WarehouseID.ToString();
            query["storeId"] = WebSiteInfo.User.Value.StoreID.ToString();
            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();
            query["tableId"] = request.TableId.ToString();

            if (request.PersonId.HasValue)
                query["personId"] = request.PersonId.ToString();

            if (request.DateStart.HasValue)
                query["dateStart"] = request.DateStart.ToString();

            if (request.DateEnd.HasValue)
                query["dateEnd"] = request.DateEnd.ToString();


            string queryString = query.ToString();

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                client.BaseAddress = new Uri(_reportServiceUrl);
                var response = await client.GetAsync("table-movements?" + queryString, cancellationToken);
                response.EnsureSuccessStatusCode();

                var result = await JsonSerializerWrapper.Deserialize<Pageable<TableMovementsResponse>>(response.Content, cancellationToken);
                return result;
            }
        }

        private string GetReturnPaymentType(int returnPaymentType)
        {
            switch (returnPaymentType)
            {
                case 0: return "Kredi Kartı";
                case -1: return "Müşteri Hizmetleri Aransın";
                case 1: return "Havale";
                case 13: return "Bakiye";
                case 16: return "PayCell";
                case 17: return "IyziPay";
                case 29: return "Bakiye";
                default: return "";
            }
        }

        private string GetMonthName(int month)
        {
            return month switch
            {
                1 => "Ocak",
                2 => "Şubat",
                3 => "Mart",
                4 => "Nisan",
                5 => "Mayıs",
                6 => "Haziran",
                7 => "Temmuz",
                8 => "Ağustos",
                9 => "Eylül",
                10 => "Ekim",
                11 => "Kasım",
                12 => "Aralık",
                _ => ""
            };
        }

    }
}