using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.BL.KargoApi;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Utilities.Service;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class WarehouseOperationRulesService : BaseService, IWarehouseOperationRulesService
    {
        private readonly IWarehoseOperationRulesDal _warehouseOperationRulesDal;
        private readonly ICategoryService _categoryService;        
        private readonly IOrderService _orderService;
        private readonly ICancelReturnReasonService _cancelReturnReasonService;
        public WarehouseOperationRulesService(IWarehoseOperationRulesDal warehouseOperationRulesDal, ICategoryService categoryService, IOrderService orderService, ICancelReturnReasonService cancelReturnReasonService)
        {
            _warehouseOperationRulesDal = warehouseOperationRulesDal;
            _categoryService = categoryService;
            _orderService = orderService;
            _cancelReturnReasonService = cancelReturnReasonService;
        }

        public async Task Add(WarehouseOperationRulesAddDto request, CancellationToken cancellationToken)
        {
            if (request.Process == WarehouseOperationRuleEnums.PackagingOperation)
            {
                var allCategories = await GetPackagingRuleForCategoryIds(request.WarehousePackagingOperationRules, cancellationToken);
                request.WarehousePackagingOperationRules.BreadCrumbsCategories = allCategories;
            }

            if (request.Process == WarehouseOperationRuleEnums.ReturnOrderOperation)
            {
                var returnOrderOperationCount = await GetCount(new WarehouseOperationRulesFilter(null, WarehouseOperationRuleEnums.ReturnOrderOperation, 1), cancellationToken);
                if (returnOrderOperationCount > 0)
                    throw new BusinessException("RETURN_ORDER_RULE_ALREADY_EXISTS");

                if(request.ReturnOrderOperationRules.WorkReasonForReturn)
                {
                    if(request.ReturnOrderOperationRules.ReturnOrderReasonIds == null || request.ReturnOrderOperationRules.ReturnOrderReasonIds.Count < 1)
                        throw new BusinessException("SELECT_REASON_FOR_RETURN");
                }
            }
            await _warehouseOperationRulesDal.AddAsync(request.ToEntity(), cancellationToken);
        }

        private async Task<List<int>> GetPackagingRuleForCategoryIds(WarehousePackagingOperationRules operationRules, CancellationToken cancellationToken)
        {
            List<int> allCategories = new List<int>();
            if (operationRules != null && operationRules.BreadCrumbsCategories != null
                    && operationRules.BreadCrumbsCategories.Count > 0)
            {
                foreach (var categoryId in operationRules.BreadCrumbsCategories)
                {
                    var categoryIds = (await _categoryService.GetAllChildAndParentCategories(categoryId, null, cancellationToken))
                                                                .Model.Select(x => x.ID).ToList();
                    allCategories.AddRange(categoryIds);
                }
                if (allCategories.Count > 0)
                    allCategories = allCategories.Distinct().ToList();
            }
            else
                throw new BusinessException("PACKAGING_OPERATION_RULE_REQUIRED");

            return allCategories;
        }

        public async Task Delete(int id, CancellationToken cancellationToken)
        {
            if (id <= 0)
                throw new NotFoundException("WAREHOUSE_OPERATION_RULE_NOT_FOUND");
            var entity = (await GetList(new WarehouseOperationRulesGetListDto() { ID = id })).Model.FirstOrDefault();
            if (entity == null)
                throw new NotFoundException("WAREHOUSE_OPERATION_RULE_NOT_FOUND");

            await _warehouseOperationRulesDal.DeleteAsync(entity, cancellationToken);
        }

        public async Task<int> GetCount(WarehouseOperationRulesFilter filter, CancellationToken cancellationToken)
        {
            return (await _warehouseOperationRulesDal.GetCountAsync(filter, cancellationToken));
        }

        public async Task<DataResult<List<WarehouseOperationRules>>> GetList(WarehouseOperationRulesGetListDto request, WarehouseOperationRulesPaging paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<WarehouseOperationRules>> response = new DataResult<List<WarehouseOperationRules>>();
            response.Model = (await _warehouseOperationRulesDal.GetListAsync(request.ToFilter(), paging != null ? new WarehouseOperationRulesPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();
            response.Count = await GetCount(request.ToFilter(), cancellationToken);
            return response;
        }

        public async Task Update(int id, WarehouseOperationRulesUpdateDto request, CancellationToken cancellationToken)
        {
            if (id <= 0)
                throw new NotFoundException("WAREHOUSE_OPERATION_RULE_NOT_FOUND");
            var entity = (await GetList(new WarehouseOperationRulesGetListDto() { ID = id })).Model.FirstOrDefault();
            if (entity == null)
                throw new NotFoundException("WAREHOUSE_OPERATION_RULE_NOT_FOUND");

            if(request.Process == WarehouseOperationRuleEnums.ReturnOrderOperation)
            {
                if (request.ReturnOrderOperationRules.WorkReasonForReturn)
                {
                    if (request.ReturnOrderOperationRules.ReturnOrderReasonIds == null || request.ReturnOrderOperationRules.ReturnOrderReasonIds.Count < 1)
                        throw new BusinessException("SELECT_REASON_FOR_RETURN");
                }
            }
            if (request.Process == WarehouseOperationRuleEnums.PackagingOperation)
            {
                var allCategories = await GetPackagingRuleForCategoryIds(request.WarehousePackagingOperationRules, cancellationToken);
                request.WarehousePackagingOperationRules.BreadCrumbsCategories = allCategories;
            }

            await _warehouseOperationRulesDal.UpdateAsync(request.ToEntity(id), cancellationToken);
        }

        public async Task<bool> SingleItemOrderPackagingRulesControl(OrderProduct readProduct, CancellationToken cancellationToken)
        {
            bool isParcelShipping = false;
            bool exitLoops = false;

            if (readProduct.BreadCrumbCategoriesDto == null)
                return isParcelShipping;

            var packagingRules = (await GetList(new WarehouseOperationRulesGetListDto()
            {
                Active = 1,
                Process = WarehouseOperationRuleEnums.PackagingOperation
            })).Model;


            if (packagingRules.Count > 0)
            {
                var order = (await _orderService.GetList(new Core.Order.Entities.Dtos.OrderGetListDto() { OrderID = readProduct.OrderID })).Model.First();
                foreach (var category in readProduct.BreadCrumbCategoriesDto)
                {
                    if (exitLoops)
                        break;

                    var rules = packagingRules
                        .Where(x => x.WarehousePackagingOperationRules.BreadCrumbsCategories.Contains(category.ID))
                        .ToList();

                    if (rules.Count > 0)
                    {
                        foreach (var rule in rules)
                        {
                            if (readProduct.KDVIncludeAmount >= rule.WarehousePackagingOperationRules.Amount
                                 &&
                                (order.PaymentTypeID == rule.WarehousePackagingOperationRules.PaymentType ||
                                rule.WarehousePackagingOperationRules.PaymentType == -1))
                            {
                                isParcelShipping = true;
                                exitLoops = true;
                                break;
                            }
                        }
                    }
                }
            }
            return isParcelShipping;
        }

        public async Task<bool> OrderPackagingRulesControl(Order order, CancellationToken cancellationToken)
        {
            bool isParcelShipping = false;
            bool exitLoops = false;


            var packagingRules = (await GetList(new WarehouseOperationRulesGetListDto()
            {
                Active = 1,
                Process = WarehouseOperationRuleEnums.PackagingOperation
            })).Model;


            if (packagingRules.Count > 0)
            {
                foreach (var orderProduct in order.Products)
                {
                    if (exitLoops)
                        break;
                    if (orderProduct.BreadCrumbCategoriesDto != null)
                    {
                        foreach (var category in orderProduct.BreadCrumbCategoriesDto)
                        {
                            if (exitLoops)
                                break;

                            var rules = packagingRules
                            .Where(x => x.WarehousePackagingOperationRules.BreadCrumbsCategories.Contains(category.ID))
                            .ToList();

                            if (rules.Count > 0)
                            {
                                foreach (var rule in rules)
                                {
                                    if (order.TotalAmount >= rule.WarehousePackagingOperationRules.Amount &&
                                        (order.PaymentTypeID == rule.WarehousePackagingOperationRules.PaymentType ||
                                             rule.WarehousePackagingOperationRules.PaymentType == -1))
                                    {
                                        isParcelShipping = true;
                                        //Eğer kutu ile gönderim onaylandıysa tüm döngülerden çıkmaya sağlayacak flag.
                                        exitLoops = true;
                                        break;
                                    }

                                }
                            }
                        }
                    }
                }
            }
            return isParcelShipping;
        }

        public async Task<DataResult<List<CancelReturnReason>>> GetCancelReturnReason(CancelReturnReasonGetListDto request, CancelReturnReasonPaging paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<CancelReturnReason>> response = new DataResult<List<CancelReturnReason>>();
            response.Model = (await _cancelReturnReasonService.GetList(request, paging != null ? new WarehouseOperationRulesPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).Model;
            return response;
        }
    }
}