using System;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.Entities.Dtos.ImageService;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class ImageService : IImageService
    {
        private readonly string _imageGeneratorUrl;

        public ImageService(IConfiguration configuration)
        {
            _imageGeneratorUrl = configuration.GetValue<string>(ConfigKeys.ImageGeneratorUrl);
        }

        public async Task<ImageCaptchaDto> CreateCaptcha(string text)
        {
            using (HttpClient client = new HttpClient())
            {
                client.BaseAddress = new Uri(_imageGeneratorUrl);
                var response = await client.PostAsync("captcha", new { Text = text }.ToJsonContent());

                return await JsonSerializerWrapper.Deserialize<ImageCaptchaDto>(response.Content);
            }
        }

        public async Task<ImageQrDto> CreateQr(string text)
        {
            using (HttpClient client = new HttpClient())
            {
                client.BaseAddress = new Uri(_imageGeneratorUrl);
                var response = await client.PostAsync("qr", new { Text = text }.ToJsonContent());

                return await JsonSerializerWrapper.Deserialize<ImageQrDto>(response.Content);
            }
        }

        public async Task<ImageBarcodeDto> CreateBarcode(string text)
        {
            using (HttpClient client = new HttpClient())
            {
                client.BaseAddress = new Uri(_imageGeneratorUrl);
                var response = await client.PostAsync("barcode", new { Text = text }.ToJsonContent());

                return await JsonSerializerWrapper.Deserialize<ImageBarcodeDto>(response.Content);
            }
        }
    }
}