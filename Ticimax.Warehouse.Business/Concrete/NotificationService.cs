using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Ticimax.Core.Entities;
using Ticimax.Core.Utilities.Service;
using Ticimax.KargoHelper.DTO.Models;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Review.Request;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Business.Extensions;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Business.Concrete
{
    public class NotificationService : BaseService, INotificationService
    {
        private readonly string _reviewApiUrl;
        private readonly ILogger<NotificationService> _logger;
        public NotificationService(IConfiguration configuration, ILogger<NotificationService> logger)
        {
            _reviewApiUrl = configuration.GetValue<string>(ConfigKeys.ReviewUrl);
            _logger = logger;
        }

        public async Task<Pageable<NotificationDto>> GetNotifications(FilterNotificationRequest request, CancellationToken cancellationToken)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["userId"] = WebSiteInfo.User.Value.ID.ToString();

            if (request.IsConfirmationRequied.HasValue)
                query["isConfirmationRequied"] = request.IsConfirmationRequied.Value.ToString();
            if (request.IsConfirmed.HasValue)
                query["isConfirmed"] = request.IsConfirmed.Value.ToString();
            if (request.IsRead.HasValue)
                query["isRead"] = request.IsRead.Value.ToString();

            query["pageSize"] = request.PageSize.ToString();
            query["pageIndex"] = request.PageIndex.ToString();

            string queryString = query.ToString();

            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_reviewApiUrl}notifications?{queryString}"),
                Method = HttpMethod.Get
            };

            var client = new HttpClient();
            client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
            var response = await client.SendAsync(message, cancellationToken);
            if (response.IsSuccessStatusCode)
                return await JsonSerializerWrapper.Deserialize<Pageable<NotificationDto>>(response.Content, cancellationToken);

            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogError($"[error] notificationService get notifications, statusCode: {response.StatusCode} content: {errorContent}");

            await response.HandleKnownExceptions(cancellationToken);
            response.EnsureSuccessStatusCode();
            return null;
        }

        public async Task CreateNotification(CreateNotificationRequest request, CancellationToken cancellationToken)
        {
            var payload = JsonSerializerWrapper.Serialize(request);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri($"{_reviewApiUrl}notifications"),
                Method = HttpMethod.Post,
                Content = new StringContent(payload, Encoding.UTF8, "application/json")
            };

            using (HttpClient client = new())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                var response = await client.SendAsync(message, cancellationToken);
                if (!response.IsSuccessStatusCode)
                {
                    await response.HandleKnownExceptions(cancellationToken);
                    response.EnsureSuccessStatusCode();
                }
            }
        }
        public async Task<bool> UpdateNotification(Guid id, UpdateNotificationRequest request, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewApiUrl}notifications/{id}";
            var payload = JsonSerializerWrapper.Serialize(request);
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Put,
                Content = new StringContent(payload, Encoding.UTF8, "application/json")
            };

            using (HttpClient client = new())
            {
                client.DefaultRequestHeaders.Add("domainName", WebSiteInfo.User.Value.DomainName);
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] NotificationService UpdateNotification, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }

        public async Task<bool> ConfirmNotification(Guid id, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewApiUrl}notifications/{id}/confirm";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Put
            };

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] NotificationService ConfirmNotification, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }
        public async Task<bool> ReadNotification(Guid id, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewApiUrl}notifications/{id}/read";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Put
            };

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] NotificationService ReadNotification, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }
        public async Task<bool> ReadAllNotification(AllReadNotificationRequest request, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewApiUrl}notifications/all/read";
            var payload = JsonSerializerWrapper.Serialize(request);

            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Put,
                Content = new StringContent(payload, Encoding.UTF8, "application/json")
            };

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] NotificationService ReadAllNotification, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }
        public async Task<bool> DeleteNotification(Guid id, CancellationToken cancellationToken)
        {
            var endpoint = $"{_reviewApiUrl}notifications/{id}";
            var message = new HttpRequestMessage
            {
                RequestUri = new Uri(endpoint),
                Method = HttpMethod.Delete
            };

            using (HttpClient client = new())
            {
                var response = await client.SendAsync(message, cancellationToken);
                if (response.IsSuccessStatusCode)
                    return true;

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError($"[error] NotificationService DeleteNotification, statusCode: {response.StatusCode} content: {content}");

                await response.HandleKnownExceptions(cancellationToken);
                response.EnsureSuccessStatusCode();
                return false;
            }
        }
    }
}
