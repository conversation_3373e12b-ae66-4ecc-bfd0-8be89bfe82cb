<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<LangVersion>9.0</LangVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Autofac" Version="6.4.0" />
		<PackageReference Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Autofac.Extras.DynamicProxy" Version="6.0.1" />
		<PackageReference Include="Google.Apis.Auth" Version="1.57.0" />
		<PackageReference Include="HtmlAgilityPack" Version="1.11.46" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="6.0.0" />
		<PackageReference Include="QRCoder" Version="1.4.3" />
		<PackageReference Include="RedLock.net" Version="2.3.2" />
		<PackageReference Include="RestSharp" Version="108.0.2" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
		<PackageReference Include="System.Text.Json" Version="8.0.4" />
		<PackageReference Include="Ticimax.Kampanya" Version="1.0.0" />
		<PackageReference Include="Ticimax.KargoHelper" Version="1.0.40" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Ticimax.Core.Business\Ticimax.Core.Business.csproj" />
		<ProjectReference Include="..\Ticimax.Core.Order.Business\Ticimax.Core.Order.Business.csproj" />
		<ProjectReference Include="..\Ticimax.Core.Product.Business\Ticimax.Core.Product.Business.csproj" />
		<ProjectReference Include="..\Ticimax.Warehouse.DataAccessLayer\Ticimax.Warehouse.DataAccessLayer.csproj" />
		<ProjectReference Include="..\Ticimax.Warehouse.Entities\Ticimax.Warehouse.Entities.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Concrete\Review\Response\" />
	  <Folder Include="Concrete\ShelfProductConcrete\Models\Response" />
	  <Folder Include="obj\Debug\netstandard2.1" />
	</ItemGroup>

</Project>
