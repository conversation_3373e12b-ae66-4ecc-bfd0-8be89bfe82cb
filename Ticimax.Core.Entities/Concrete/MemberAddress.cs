using System;

namespace Ticimax.Core.Entities.Concrete
{
    public class MemberAddress : IEntity
    {
        public int ID { get; set; }

        public int UyeID { get; set; }

        public string AliciAdi { get; set; }

        public string AliciTelefon { get; set; }

        public string Tanim { get; set; }

        public string Adres { get; set; }

        public string PostaKodu { get; set; }

        public int SemtID { get; set; }

        public int IlceID { get; set; }

        public int SehirID { get; set; }

        public int UlkeID { get; set; }

        public string FirmaAdi { get; set; }

        public string VergiDairesi { get; set; }

        public string VergiNo { get; set; }

        public int Kurumsal { get; set; }

        public Boolean Varsayilan { get; set; }

        public Boolean Aktif { get; set; }

        public Boolean Silindi { get; set; }

        public DateTime SilmeTarihi { get; set; }

        public int SilenKullanici { get; set; }

        public string AdresTarifi { get; set; }

        public string <PERSON>hir { get; set; }

        public string Sehir<PERSON>odu { get; set; }

        public string <PERSON>ce { get; set; }

        public string <PERSON><PERSON><PERSON>odu { get; set; }

        public string Semt { get; set; }

        public string SemtKodu { get; set; }

        public string Ulke { get; set; }

        public string UlkeKodu { get; set; }

        public string BuKoliPointCode { get; set; }

        public bool TCVatandasiDegilim { get; set; }
    }
}