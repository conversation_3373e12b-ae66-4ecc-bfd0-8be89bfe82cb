using System;
using Newtonsoft.Json;

namespace Ticimax.Core.Entities.Concrete
{
    public class LogModel<T> where T : class
    {
        public string Date
        {
            get { return DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss:fff"); }
        }

        public int EventID { get; set; }

        public string Event { get; set; }

        public string Logger
        {
            get { return typeof(T).FullName; }
        }

        public string IPAdress { get; set; }

        public string Detail { get; set; }

        public string URL { get; set; }

        public int UserID { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ExceptionLogModel ExceptionHandle { get; set; }

        public class ExceptionLogModel
        {
            public int HRResult { get; set; }

            public string Message { get; set; }
        }
    }
}