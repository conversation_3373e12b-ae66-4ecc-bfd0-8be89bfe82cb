using Newtonsoft.Json;

namespace Ticimax.Core.Entities.Concrete
{
    public class CustomizationItem : IEntity
    {
        [JsonProperty("Question")]
        public string Question { get; set; }

        [JsonProperty("Answer")]
        public string Answer { get; set; }

        [JsonProperty("Amount")]
        public double Amount { get; set; }

        [JsonProperty("AmountStr")]
        public string AmountStr { get; set; }

        [JsonProperty("Type")]
        public string Type { get; set; }
    }
}