using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IStoreDal
    {
        Task<List<Store>> GetListAsync(StoreFilter filter = null, StorePaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(Store entity, CancellationToken cancellationToken);

        Task UpdateAsync(Store entity, CancellationToken cancellationToken);

        Task DeleteAsync(Store entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(StoreFilter filter = null, CancellationToken cancellationToken = default);
    }
}