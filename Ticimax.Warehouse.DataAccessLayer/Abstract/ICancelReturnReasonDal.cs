using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface ICancelReturnReasonDal
    {
        Task<List<CancelReturnReason>> GetListAsync(CancelReturnReasonFilter filter = null, CancelReturnReasonPaging paging = null, CancellationToken cancellationToken = default);
    }
}