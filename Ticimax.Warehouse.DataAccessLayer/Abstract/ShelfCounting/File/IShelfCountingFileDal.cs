using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.File;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract.ShelfCounting.File
{
#nullable enable
    public interface IShelfCountingFileDal
    {
        Task<ShelfCountingFileAggregate> GetById(Guid id, CancellationToken cancellationToken);
        Task<List<ShelfCountingFileAggregate>> Get(string? name, string? fileNo, string? shelfId, string? status, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<int> Count(string? name, string? fileNo, string? shelfId, string? status, CancellationToken cancellationToken);
        Task Create(ShelfCountingFileAggregate aggregate, CancellationToken cancellationToken);
        Task Update(ShelfCountingFileAggregate aggregate, CancellationToken cancellationToken);
        Task Delete(Guid id, CancellationToken cancellationToken);
    }
}