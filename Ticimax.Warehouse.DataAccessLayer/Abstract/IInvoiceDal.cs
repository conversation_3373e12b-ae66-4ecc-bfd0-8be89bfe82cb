using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IInvoiceDal
    {
        Task Create(Entities.Concrete.Invoice entity, CancellationToken cancellationToken);

        Task UpdateCancel(Entities.Concrete.Invoice entity, CancellationToken cancellationToken);

        Task<List<Entities.Concrete.Invoice>> GetListAsync(InvoiceFilter filter = null, InvoicePaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(Entities.Concrete.Invoice entity, CancellationToken cancellationToken);

        Task UpdateAsync(Entities.Concrete.Invoice entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(InvoiceFilter filter = null, CancellationToken cancellationToken = default);

        Task CreateAsync(Entities.Concrete.Invoice entity, CancellationToken cancellationToken);

        Task UpdateCancelAsync(Entities.Concrete.Invoice entity, CancellationToken cancellationToken);
    }
}