using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface ICountryCityDistrictDal
    {
        Task<List<Country>> GetCountryList(CountryFilter filter, CancellationToken cancellationToken);

        Task<IList<City>> GetCityList(CityFilter filter = null, CancellationToken cancellationToken = default);

        Task<IList<District>> GetDistrictList(DistrictFilter filter = null, CancellationToken cancellationToken = default);

        Task<List<Country>> GetCountryListAsync(CancellationToken cancellationToken);

        Task<List<City>> GetCityListAsync(CityFilter filter = null, CancellationToken cancellationToken = default);

        Task<List<District>> GetDistrictListAsync(DistrictFilter filter = null, CancellationToken cancellationToken = default);
    }
}