using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface ICampaignTicimaxDal
    {
        Task<List<CampaignTicimax>> GetListAsync(CampaignTicimaxFilter filter = null, CampaignTicimaxPaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(CampaignTicimax entity, CancellationToken cancellationToken);

        Task UpdateAsync(CampaignTicimax entity, CancellationToken cancellationToken);

        Task DeleteAsync(CampaignTicimax entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(CampaignTicimaxFilter filter = null, CancellationToken cancellationToken = default);
    }
}