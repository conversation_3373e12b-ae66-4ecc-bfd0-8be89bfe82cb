using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IWarehoseOperationRulesDal
    {
        Task<List<WarehouseOperationRules>> GetListAsync(WarehouseOperationRulesFilter filter = null, WarehouseOperationRulesPaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(WarehouseOperationRules entity, CancellationToken cancellationToken);

        Task UpdateAsync(WarehouseOperationRules entity, CancellationToken cancellationToken);

        Task DeleteAsync(WarehouseOperationRules entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(WarehouseOperationRulesFilter filter = null, CancellationToken cancellationToken = default);
    }
}