using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IWarehouseAddressDal
    {
        Task SetDefault(WarehouseAddress entity, CancellationToken cancellationToken);

        Task<List<WarehouseAddress>> GetListAsync(WarehouseAddressFilter filter = null, WarehouseAddressPaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(WarehouseAddress entity, CancellationToken cancellationToken);

        Task UpdateAsync(WarehouseAddress entity, CancellationToken cancellationToken);

        Task DeleteAsync(WarehouseAddress entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(WarehouseAddressFilter filter = null, CancellationToken cancellationToken = default);

        Task SetDefaultAsync(WarehouseAddress entity, CancellationToken cancellationToken);
    }
}