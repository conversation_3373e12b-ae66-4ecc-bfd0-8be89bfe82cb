using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IWarehouseCarProductDal
    {
        Task DeleteCarAllProduct(WarehouseCarProduct entity, CancellationToken cancellationToken);

        Task<List<WarehouseCarProduct>> GetListAsync(WarehouseCarProductFilter filter = null, WarehouseCarProductPaging paging = null, CancellationToken cancellationToken = default);
        Task AddAsync(WarehouseCarProduct entity, CancellationToken cancellationToken);

        Task UpdateAsync(WarehouseCarProduct entity, CancellationToken cancellationToken);

        Task DeleteAsync(WarehouseCarProduct entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(WarehouseCarProductFilter filter = null, CancellationToken cancellationToken = default);
        Task DeleteCarAllProductAsync(WarehouseCarProduct entity, CancellationToken cancellationToken);

        Task SettingsUpdateAsync(WarehouseCarProduct entity, CancellationToken cancellationToken);

    }
}