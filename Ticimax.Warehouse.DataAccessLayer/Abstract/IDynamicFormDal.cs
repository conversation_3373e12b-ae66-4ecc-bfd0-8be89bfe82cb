using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IDynamicFormDal
    {
        Task<List<DynamicForm>> GetListAsync(DynamicFormFilter filter = null, DynamicFormPaging paging = null, CancellationToken cancellationToken = default);

        Task<int> GetCountAsync(DynamicFormFilter filter = null, CancellationToken cancellationToken = default);
    }
}