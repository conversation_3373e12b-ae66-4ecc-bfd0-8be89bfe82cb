using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.File
{
#nullable enable
    public interface IWarehouseProductTransferFileDal
    {
        Task<WarehouseProductTransferFileAggregate> GetById(Guid id, CancellationToken cancellationToken);
        Task<List<WarehouseProductTransferFileAggregate>> Get(string? name, string? fileNo, string? status, int? createdUserId, List<int>? productIds, List<string>? statusList, long? time, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<int> Count(string? name, string? fileNo, string? status, int? createdUserId, List<int>? productIds, List<string>? statusList, long? time, bool? isAdminDashboard, CancellationToken cancellationToken);
        Task Create(WarehouseProductTransferFileAggregate aggregate, CancellationToken cancellationToken);
        Task Update(WarehouseProductTransferFileAggregate aggregate, CancellationToken cancellationToken);
        Task Delete(Guid id, CancellationToken cancellationToken);
    }
}