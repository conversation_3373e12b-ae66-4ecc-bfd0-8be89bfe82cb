using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Item;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.Item
{
    public interface IWarehouseProductTransferItemDal
    {
        Task<WarehouseProductTransferItemAggregate> GetById(Guid id, CancellationToken cancellationToken);
        Task<List<WarehouseProductTransferItemAggregate>> Get(Guid? fileId, int? shelfId, int? productId, bool? notPickingItem, bool? notCheckingItem, long? time, bool? isControlItem, int pageSize, int pageIndex, CancellationToken cancellationToken);
        Task<List<WarehouseProductTransferItemAggregate>> GetGroupped(Guid? fileId, int? productId, int pageSize, int pageIndex, CancellationToken cancellationToken);
        
        Task<int> CountGroupped(Guid? fileId, int? productId, CancellationToken cancellationToken);
        
        Task Create(WarehouseProductTransferItemAggregate aggregate, CancellationToken cancellationToken);
        Task CreateBulk(List<WarehouseProductTransferItemAggregate> aggregates, CancellationToken cancellationToken);
        Task Delete(Guid id, CancellationToken cancellationToken);
        Task DeleteByFileId(Guid id, CancellationToken cancellationToken);
        Task<int> Count(Guid? fileId, int? shelfId, int? productId, bool? notPickingItem, bool? notCheckingItem, long? time, CancellationToken cancellationToken);
        Task Update(WarehouseProductTransferItemAggregate aggregate, CancellationToken cancellationToken);
        Task<WarehouseTransferContainingProductResponseDto> GetWarehouseItemsFileProductGrouped(int productId, string status, CancellationToken cancellationToken);

    }
}