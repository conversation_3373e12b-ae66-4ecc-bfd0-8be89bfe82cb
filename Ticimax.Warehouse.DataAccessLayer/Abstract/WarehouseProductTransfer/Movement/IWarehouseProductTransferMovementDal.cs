using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Movement;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.Movement
{
    public interface IWarehouseProductTransferMovementDal
    {
        Task Create(WarehouseProductTransferMovementAggregate aggregate, CancellationToken cancellationToken);
        Task<List<WarehouseProductTransferMovementAggregate>> Get(Guid id, CancellationToken cancellationToken);
    }
}