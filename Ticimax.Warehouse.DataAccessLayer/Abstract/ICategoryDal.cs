using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface ICategoryDal
    {
        Task<List<Category>> GetListAsync(CategoryFilter filter = null, CategoryPaging paging = null, CancellationToken cancellationToken = default);
        Task<List<Category>> GetAllChildAndParentCategoriesAsync(int categoryId, CategoryPaging paging = null, CancellationToken cancellationToken = default);
    }
}