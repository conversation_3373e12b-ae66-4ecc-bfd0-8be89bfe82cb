using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IShelfProductDal
    {
        Task UpdateStock(ShelfProduct entity, CancellationToken cancellationToken);

        Task DeleteByProductAndShelfID(ShelfProduct entity, CancellationToken cancellationToken);
        Task ResetAllStock(int warehouseId, int storeId, CancellationToken cancellationToken);

        Task DeleteByShelfID(ShelfProduct entity, CancellationToken cancellationToken);

        Task<List<ShelfProduct>> GetListAsync(ShelfProductFilter filter = null, ShelfProductPaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(ShelfProduct entity, CancellationToken cancellationToken);

        Task UpdateAsync(ShelfProduct entity, CancellationToken cancellationToken);

        Task DeleteAsync(ShelfProduct entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(ShelfProductFilter filter = null, CancellationToken cancellationToken = default);

        Task UpdateStockAsync(ShelfProduct entity, CancellationToken cancellationToken);

        Task DeleteByProductAndShelfIDAsync(ShelfProduct entity, CancellationToken cancellationToken);

        Task DeleteByShelfIDAsync(ShelfProduct entity, CancellationToken cancellationToken);

        Task<double> GetStockCount(ShelfProductFilter filter, CancellationToken cancellationToken);
        Task<double> GetShelfChildrenStock(int shelfId, CancellationToken cancellationToken);
        Task<double> GetShelfParentStock(int shelfId, CancellationToken cancellationToken);
        Task<List<CriticalStockShelfProductResponse>> GetCriticalStockProducts(CriticalStockShelfProductFilter filter, CriticalStockShelfProductPaging paging, CancellationToken cancellationToken = default);
        Task<int> GetCriticalStockProductsCount(CriticalStockShelfProductFilter filter, CancellationToken cancellationToken = default);
    }
}