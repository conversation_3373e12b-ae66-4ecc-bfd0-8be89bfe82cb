using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface ICustomerServiceDal
    {
        Task UpdateStatus(CustomerService entity, CancellationToken cancellationToken);
        Task UpdateNumberOfCall(CustomerService entity, CancellationToken cancellationToken);
        Task UpdateCallingMember(CustomerService entity, CancellationToken cancellationToken);
        Task UpdateDate(CustomerService entity, CancellationToken cancellationToken);
        Task<List<CustomerService>> GetListAsync(CustomerServiceFilter filter = null, CustomerServicePaging paging = null, CancellationToken cancellationToken = default);
        Task AddAsync(CustomerService entity, CancellationToken cancellationToken);
        Task UpdateAsync(CustomerService entity, CancellationToken cancellationToken);
        Task DeleteAsync(CustomerService entity, CancellationToken cancellationToken);
        Task<int> GetCountAsync(CustomerServiceFilter filter = null, CancellationToken cancellationToken = default);
        Task UpdateStatusAsync(CustomerService entity, CancellationToken cancellationToken);
        Task UpdateNumberOfCallAsync(CustomerService entity, CancellationToken cancellationToken);
        Task UpdateCallingMemberAsync(CustomerService entity, CancellationToken cancellationToken);
        Task UpdateDateAsync(CustomerService entity, CancellationToken cancellationToken);
        Task DeleteByOrderId(int orderId, CancellationToken cancellationToken);
        Task DeleteByOrderIdAndType(int orderId, CustomerServiceType type, CancellationToken cancellationToken);

    }
}