using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface ISupplierDal
    {
        Task MultipleAdd(List<Supplier> entityList, CancellationToken cancellationToken);

        Task<Supplier> GetAsync(int id, CancellationToken cancellationToken);

        Task<List<Supplier>> GetListAsync(SupplierDtoFilter filter = null, SupplierPaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(Supplier entity, CancellationToken cancellationToken);

        Task UpdateAsync(Supplier entity, CancellationToken cancellationToken);

        Task DeleteAsync(Supplier entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(SupplierDtoFilter filter = null, CancellationToken cancellationToken = default);

        Task MultipleAddAsync(List<Supplier> entityList, CancellationToken cancellationToken);

        Task<int> AddIdAsync(Supplier entity, CancellationToken cancellationToken);

    }
}