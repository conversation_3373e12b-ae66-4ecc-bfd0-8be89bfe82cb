using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IWarehouseBoxDal
    {
        Task<double> ProductCountInBoxes(string barcode, CancellationToken cancellationToken);

        Task<IList<WarehouseBox>> BoxesInProduct(string barcode, CancellationToken cancellationToken);

        Task DeleteOnBarkod(WarehouseBox barkod, CancellationToken cancellationToken);

        Task UpdateBoxShelf(int ID, int shelfID, CancellationToken cancellationToken);

        Task UpdateBoxProducts(int ID, List<WarehouseBoxProductDto> products, CancellationToken cancellationToken);

        Task<List<WarehouseBox>> GetListAsync(WarehouseBoxFilter filter = null, WarehouseBoxPaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(WarehouseBox entity, CancellationToken cancellationToken);

        Task UpdateAsync(WarehouseBox entity, CancellationToken cancellationToken);

        Task DeleteAsync(WarehouseBox entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(WarehouseBoxFilter filter = null, CancellationToken cancellationToken = default);

        Task<double> ProductCountInBoxesAsync(string barcode, CancellationToken cancellationToken);

        Task<List<WarehouseBox>> BoxesInProductAsync(string barcode, CancellationToken cancellationToken);

        Task DeleteOnBarkodAsync(WarehouseBox barkod, WarehouseBox entity, CancellationToken cancellationToken);

        Task UpdateBoxShelfAsync(int ID, int shelfID, CancellationToken cancellationToken);

        Task UpdateBoxProductsAsync(int ID, List<WarehouseBoxProductDto> products, CancellationToken cancellationToken);
    }
}