using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IWarehouseAdminDal
    {
        Task<List<DbColumns>> GetReferanceDbColumsAsync(bool isReferansDbConnection, string connection, string dbName, string tableName = "", string columnName = "", string dataType = "", int dataTypeMaxLength = 0, bool isCharsetNotNull = false, string columnComment = "", CancellationToken cancellationToken = default);
        Task TargetDbExecuteCommand(string domainName, string cmdCommandText, CancellationToken cancellationToken);

    }
}