using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Abstract
{
    public interface IWarehouseAuthGroupDal
    {
        Task<int> AddReturnId(WarehouseAuthGroup entity, CancellationToken cancellationToken);

        Task<List<WarehouseAuthGroup>> GetListAsync(WarehouseAuthGroupFilter filter = null, WarehouseAuthGroupPaging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(WarehouseAuthGroup entity, CancellationToken cancellationToken);

        Task UpdateAsync(WarehouseAuthGroup entity, CancellationToken cancellationToken);

        Task DeleteAsync(WarehouseAuthGroup entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(WarehouseAuthGroupFilter filter = null, CancellationToken cancellationToken = default);

        Task<int> AddReturnIdAsync(WarehouseAuthGroup entity, CancellationToken cancellationToken);
    }
}