using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.ShelfDtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class ShelfDal : IShelfDal
    {
        private MySqlConnection _cnn;

        public ShelfDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task MultipleAdd(List<Shelf> entityList, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            List<string> valueList = new List<string>();
            for (int i = 0; i < entityList.Count; i++)
            {
                valueList.Add($@"(
                                      @PARENT_ID{i}
                                    , @TANIM{i}
                                    , @KOD{i}
                                    , @BARKOD{i}
                                    , @DEPO_ID{i}
                                    , @MAGAZA_ID{i}
                                    , @SIRA{i}
                                    , NOW()
                                    , @EKSIK_URUN_RAFI{i}
                                    , @BOSRAF{i})");

                cmd.Parameters.Add($"@PARENT_ID{i}", MySqlDbType.Int32).Value = entityList[i].ParentId;
                cmd.Parameters.Add($"@TANIM{i}", MySqlDbType.VarChar).Value = entityList[i].Definition;
                cmd.Parameters.Add($"@KOD{i}", MySqlDbType.VarChar).Value = entityList[i].Code;
                cmd.Parameters.Add($"@BARKOD{i}", MySqlDbType.VarChar).Value = entityList[i].Barcode;
                cmd.Parameters.Add($"@DEPO_ID{i}", MySqlDbType.Int32).Value = entityList[i].WarehouseID > 0 ? entityList[i].WarehouseID : WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add($"@MAGAZA_ID{i}", MySqlDbType.Int32).Value = entityList[i].StoreID > 0 ? entityList[i].StoreID : WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add($"@SIRA{i}", MySqlDbType.Int32).Value = entityList[i].Rank;
                cmd.Parameters.Add($"@EKSIK_URUN_RAFI{i}", MySqlDbType.Bit).Value = entityList[i].IsMissingProductShelf;
                cmd.Parameters.Add($"@BOSRAF{i}", MySqlDbType.Bit).Value = entityList[i].IsEmptyShelf;
            }

            cmd.CommandText = $@"INSERT INTO raflar(
                                                  PARENT_ID
                                                , TANIM
                                                , KOD
                                                , BARKOD
                                                , DEPO_ID
                                                , MAGAZA_ID
                                                , SIRA
                                                , EKLENME_TARIHI
                                                , EKSIK_URUN_RAFI
                                                , BOSRAF)
                                    VALUES {string.Join(",", valueList)}";

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<bool> ShelfHaveProduct(Shelf entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                        COUNT(*)
                                    FROM
                                        urun_raf
                                    WHERE
                                        RAF_ID = @RAF_ID";

            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ID;
            int total = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            //await _cnn.CloseAsync();
            return total > 0;
        }

        public async Task<List<ShelfIncludesChildCountDto>> GetShelfIncludesChildCountAsync(ShelfFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<ShelfIncludesChildCountDto> list = new List<ShelfIncludesChildCountDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT      r.ID
                                          , r.PARENT_ID
                                          , r.TANIM
                                          , r.KOD
                                          , r.BARKOD
                                          , r.DEPO_ID
                                          , r.SIRA
                                          , r.EKLENME_TARIHI
                                          , r.EKSIK_URUN_RAFI
                                          , r.BOSRAF
                                          , r.SATISA_ACIK
                                          , (SELECT COUNT(*) FROM raflar AS rin WHERE rin.PARENT_ID = r.ID) AS CHILDCOUNT
                                    FROM raflar AS r
                                    WHERE 1 ";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, new Paging { PageNo = pageIndex, RecordNumber = pageSize });

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                ShelfIncludesChildCountDto raf = new ShelfIncludesChildCountDto();
                raf.Id = reader["ID"].ToInt32();
                raf.ParentId = reader["PARENT_ID"].ToInt32();
                raf.Definition = reader["TANIM"].ToString();
                raf.Code = reader["KOD"].ToString();
                raf.Barcode = reader["BARKOD"].ToString();
                raf.WarehouseId = reader["DEPO_ID"].ToInt32();
                raf.Rank = reader["SIRA"].ToInt32();
                raf.AddingDate = reader["EKLENME_TARIHI"].ToDateTime();
                raf.IsMissingProductShelf = reader["EKSIK_URUN_RAFI"].ToBoolean();
                raf.IsEmptyShelf = reader["BOSRAF"].ToBoolean();
                raf.IsOpenForSale = reader["SATISA_ACIK"].ToBoolean();
                raf.ChildShelfCount = reader["CHILDCOUNT"].ToInt32();
                list.Add(raf);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return list;
        }

        public async Task<int> CountAsync(ShelfFilter filter, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(ID)
                                FROM raflar AS r
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return count;
        }

        public async Task<List<Shelf>> GetListAsync(ShelfFilter filter = null, ShelfPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<Shelf> list = new List<Shelf>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT      r.ID
                                          , r.PARENT_ID
                                          , r.TANIM
                                          , r.KOD
                                          , r.BARKOD
                                          , r.DEPO_ID
                                          , r.SIRA
                                          , r.EKLENME_TARIHI
                                          , r.EKSIK_URUN_RAFI
                                          , r.BOSRAF
                                          , r.SATISA_ACIK
                                          , r.TOPLAMAYA_ACIK
                                    FROM raflar AS r
                                    WHERE 1 ";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                Shelf raf = new Shelf();
                raf.ID = reader["ID"].ToInt32();
                raf.ParentId = reader["PARENT_ID"].ToInt32();
                raf.Definition = reader["TANIM"].ToString();
                raf.Code = reader["KOD"].ToString();
                raf.Barcode = reader["BARKOD"].ToString();
                raf.WarehouseID = reader["DEPO_ID"].ToInt32();
                raf.Rank = reader["SIRA"].ToInt32();
                raf.AddingDate = reader["EKLENME_TARIHI"].ToDateTime();
                raf.IsMissingProductShelf = reader["EKSIK_URUN_RAFI"].ToBoolean();
                raf.IsOpenForSale = reader["SATISA_ACIK"].ToBoolean();
                raf.IsOpenPicking = reader["TOPLAMAYA_ACIK"].ToBoolean();
                raf.IsEmptyShelf = reader["BOSRAF"].ToBoolean();
                list.Add(raf);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return list;
        }

        public async Task<Shelf> GetById(int id, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            Shelf shelf = null;
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT      r.ID
                                          , r.PARENT_ID
                                          , r.TANIM
                                          , r.KOD
                                          , r.BARKOD
                                          , r.DEPO_ID
                                          , r.SIRA
                                          , r.EKLENME_TARIHI
                                          , r.EKSIK_URUN_RAFI
                                          , r.BOSRAF
                                          , r.SATISA_ACIK
                                    FROM raflar AS r
                                    WHERE r.ID = @Id ";

            cmd.Parameters.Add("@Id", MySqlDbType.Int32).Value = id;

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                shelf = new Shelf();
                shelf.ID = reader["ID"].ToInt32();
                shelf.ParentId = reader["PARENT_ID"].ToInt32();
                shelf.Definition = reader["TANIM"].ToString();
                shelf.Code = reader["KOD"].ToString();
                shelf.Barcode = reader["BARKOD"].ToString();
                shelf.WarehouseID = reader["DEPO_ID"].ToInt32();
                shelf.Rank = reader["SIRA"].ToInt32();
                shelf.AddingDate = reader["EKLENME_TARIHI"].ToDateTime();
                shelf.IsMissingProductShelf = reader["EKSIK_URUN_RAFI"].ToBoolean();
                shelf.IsOpenForSale = reader["SATISA_ACIK"].ToBoolean();
                shelf.IsEmptyShelf = reader["BOSRAF"].ToBoolean();
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return shelf;
        }

        public async Task AddAsync(Shelf entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO raflar
                                                (PARENT_ID
                                                ,TANIM
                                                , KOD
                                                , BARKOD
                                                , DEPO_ID
                                                , MAGAZA_ID
                                                , SIRA
                                                , EKSIK_URUN_RAFI
                                                , BOSRAF
                                                , SATISA_ACIK)
                                    VALUES (
                                            @PARENT_ID
                                          , @TANIM
                                          , @KOD
                                          , @BARKOD
                                          , @DEPO_ID
                                          , @MAGAZA_ID
                                          , @SIRA
                                          , @EKSIK_URUN_RAFI
                                          , @BOSRAF
                                          , @SATISA_ACIK)";

            cmd.Parameters.Add("@PARENT_ID", MySqlDbType.Int32).Value = entity.ParentId;
            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = entity.Code;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = entity.StoreID;
            cmd.Parameters.Add("@SIRA", MySqlDbType.Int32).Value = entity.Rank;
            cmd.Parameters.Add("@EKSIK_URUN_RAFI", MySqlDbType.Bit).Value = entity.IsMissingProductShelf;
            cmd.Parameters.Add("@BOSRAF", MySqlDbType.Bit).Value = entity.IsEmptyShelf;
            cmd.Parameters.Add("@SATISA_ACIK", MySqlDbType.Bit).Value = entity.IsOpenForSale;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateAsync(Shelf entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE raflar
                                    SET
                                        PARENT_ID = @PARENT_ID
                                      , TANIM = @TANIM
                                      , KOD = @KOD
                                      , BARKOD = @BARKOD
                                      , SIRA = @SIRA
                                      , EKSIK_URUN_RAFI = @EKSIK_URUN_RAFI
                                      , BOSRAF = @BOSRAF
                                      , SATISA_ACIK = @SATISA_ACIK
                                      , TOPLAMAYA_ACIK = @TOPLAMAYA_ACIK
                                    WHERE ID = @ID";

            cmd.Parameters.Add("@PARENT_ID", MySqlDbType.Int32).Value = entity.ParentId;
            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = entity.Code;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@SIRA", MySqlDbType.Int32).Value = entity.Rank;
            cmd.Parameters.Add("@EKSIK_URUN_RAFI", MySqlDbType.Bit).Value = entity.IsMissingProductShelf;
            cmd.Parameters.Add("@SATISA_ACIK", MySqlDbType.Bit).Value = entity.IsOpenForSale;
            cmd.Parameters.Add("@TOPLAMAYA_ACIK", MySqlDbType.Bit).Value = entity.IsOpenPicking;
            cmd.Parameters.Add("@BOSRAF", MySqlDbType.Bit).Value = entity.IsEmptyShelf;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(Shelf entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM raflar WHERE ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(ShelfFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(ID)
                                FROM raflar AS r
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<bool> ShelfHaveProductAsync(Shelf entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                        COUNT(*)
                                    FROM
                                        urun_raf
                                    WHERE
                                        RAF_ID = @RAF_ID";

            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ID;
            int total = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            //await _cnn.CloseAsync();
            return total > 0;
        }

        public async Task MultipleAddAsync(List<Shelf> entityList, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            List<string> valueList = new List<string>();
            for (int i = 0; i < entityList.Count; i++)
            {
                valueList.Add($@"(
                                      @PARENT_ID{i}
                                    , @TANIM{i}
                                    , @KOD{i}
                                    , @BARKOD{i}
                                    , @DEPO_ID{i}
                                    , @MAGAZA_ID{i}
                                    , @SIRA{i}
                                    , NOW()
                                    , @EKSIK_URUN_RAFI{i}
                                    , @BOSRAF{i})");

                cmd.Parameters.Add($"@PARENT_ID{i}", MySqlDbType.Int32).Value = entityList[i].ParentId;
                cmd.Parameters.Add($"@TANIM{i}", MySqlDbType.VarChar).Value = entityList[i].Definition;
                cmd.Parameters.Add($"@KOD{i}", MySqlDbType.VarChar).Value = entityList[i].Code;
                cmd.Parameters.Add($"@BARKOD{i}", MySqlDbType.VarChar).Value = entityList[i].Barcode;
                cmd.Parameters.Add($"@DEPO_ID{i}", MySqlDbType.Int32).Value = entityList[i].WarehouseID > 0 ? entityList[i].WarehouseID : WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add($"@MAGAZA_ID{i}", MySqlDbType.Int32).Value = entityList[i].StoreID > 0 ? entityList[i].StoreID : WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add($"@SIRA{i}", MySqlDbType.Int32).Value = entityList[i].Rank;
                cmd.Parameters.Add($"@EKSIK_URUN_RAFI{i}", MySqlDbType.Bit).Value = entityList[i].IsMissingProductShelf;
                cmd.Parameters.Add($"@BOSRAF{i}", MySqlDbType.Bit).Value = entityList[i].IsEmptyShelf;
            }

            cmd.CommandText = $@"INSERT INTO raflar(
                                                  PARENT_ID
                                                , TANIM
                                                , KOD
                                                , BARKOD
                                                , DEPO_ID
                                                , MAGAZA_ID
                                                , SIRA
                                                , EKLENME_TARIHI
                                                , EKSIK_URUN_RAFI
                                                , BOSRAF)
                                    VALUES {string.Join(",", valueList)}";

            await cmd.ExecuteTransactionCommandAsync();
        }



        private void AppendFilter(ref MySqlCommand cmd, ShelfFilter filter)
        {
            if (filter != null)
            {
                if (filter.StoreId.HasValue && filter.WarehouseId.HasValue)
                {
                    cmd.CommandText += " AND r.DEPO_ID = @DEPO_ID AND r.MAGAZA_ID = @MAGAZA_ID";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = filter.WarehouseId.Value;
                    cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = filter.StoreId.Value;
                }
                else if (!WebSiteInfo.User?.Value?.IsOneStore ?? false)
                {
                    cmd.CommandText += " AND r.DEPO_ID = @DEPO_ID AND r.MAGAZA_ID = @MAGAZA_ID";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                    cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                }

                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND r.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (filter.ParentId.HasValue)
                {
                    cmd.CommandText += " AND r.PARENT_ID = @PARENT_ID";
                    cmd.Parameters.Add("@PARENT_ID", MySqlDbType.Int32).Value = filter.ParentId.Value;
                }

                if (filter.IDList != null && filter.IDList.Count > 0)
                    cmd.CommandText += $" AND r.ID IN ({string.Join(',', filter.IDList)})";

                if (!string.IsNullOrEmpty(filter.Code))
                {
                    cmd.CommandText += " AND r.KOD = @KOD";
                    cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = filter.Code;
                }

                if (!string.IsNullOrEmpty(filter.Definition))
                {
                    cmd.CommandText += " AND r.TANIM LIKE @TANIM";
                    cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = "%" + filter.Definition + "%";
                }

                if (!string.IsNullOrEmpty(filter.Barcode))
                {
                    cmd.CommandText += " AND r.BARKOD = @BARKOD";
                    cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.Barcode;
                }

                if (filter.IsMissingProductShelf)
                {
                    cmd.CommandText += " AND r.EKSIK_URUN_RAFI=@EKSIK_URUN_RAFI";
                    cmd.Parameters.Add("@EKSIK_URUN_RAFI", MySqlDbType.Bit).Value = filter.IsMissingProductShelf;
                }

                if (filter.ProductID > 0)
                {
                    cmd.CommandText += " AND r.ID IN (SELECT RAF_ID FROM urun_raf WHERE URUN_ID = @URUN_ID)";
                    cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = filter.ProductID;
                }

                if (filter.ParentIds != null && filter.ParentIds.Count > 0)
                    cmd.CommandText += $" AND r.PARENT_ID IN ({string.Join(',', filter.ParentIds)})";

                if (filter.IsOpenForSale.HasValue)
                {
                    cmd.CommandText += " AND r.SATISA_ACIK = @SATISA_ACIK ";
                    cmd.Parameters.Add("SATISA_ACIK", MySqlDbType.Bit).Value = filter.IsOpenForSale.Value;
                }

                if (filter.Barcodes != null && filter.Barcodes.Count > 0)
                {
                    string barcodes = string.Join(",", filter.Barcodes.Select(x => $"'{x}'"));
                    cmd.CommandText += $" AND r.BARKOD IN ({barcodes})";
                }

                if (filter.IsEmptyShelf)
                    cmd.CommandText += $" AND r.BOSRAF = 1";
            }
        }

        public async Task<List<int>> ChildrenShelfIdsGetListAsync(int shelfId, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"WITH RECURSIVE RaflarCTE AS (
                                SELECT ID, PARENT_ID, TANIM
                                FROM raflar
                                WHERE ID = @RAF_ID
                                UNION ALL
                                SELECT r.ID, r.PARENT_ID, r.TANIM
                                FROM raflar r
                                INNER JOIN RaflarCTE rc ON r.PARENT_ID = rc.ID
                                )
                                SELECT ID
                                FROM RaflarCTE;";

            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = shelfId;

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            List<int> shelfIds = new List<int>();
            while (await Reader.ReadAsync(cancellationToken))
            {
                int id = Reader["ID"].ToInt32();
                shelfIds.Add(id);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return shelfIds;
        }
    }
}