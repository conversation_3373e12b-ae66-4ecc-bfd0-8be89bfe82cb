using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class CargoHourLimitDal : ICargoHourLimitDal
    {
        private MySqlConnection _cnn;

        public CargoHourLimitDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<IList<CargoHourLimitWeekDto>> GetCargoHour(CargoHourLimitFilter filter, CancellationToken cancellationToken)
        {
            string langCode = "tr";
            List<CargoHourLimitWeekDto> teslimatGunleri = new List<CargoHourLimitWeekDto>();
            var cultureInfo = CultureInfo.GetCultures(CultureTypes.AllCultures).FirstOrDefault(c => c.Name.StartsWith(langCode + "-"));
            for (int i = 0; i < filter.Day; i++)
            {
                CargoHourLimitWeekDto gun = new CargoHourLimitWeekDto();
                gun.Tarih = filter.Date.AddDays(i);
                filter.Date = gun.Tarih;
                gun.TeslimatSaatleri = (await GetCargoHour(filter, null, cancellationToken)).ToList();
                gun.TarihStr = gun.Tarih.ToString("dd-MM-yyyy");
                gun.GunStr = gun.Tarih.ToString("dd.MM.yyyy") + " " + cultureInfo.DateTimeFormat.GetDayName(gun.Tarih.DayOfWeek);
                teslimatGunleri.Add(gun);
            }

            return teslimatGunleri;
        }

        public async Task<List<CargoHourLimit>> GetListAsync(CargoHourLimitFilter filter = null, CargoHourLimitPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<CargoHourLimit> saatler = new List<CargoHourLimit>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  ksl.ID
                                      , ksl.KARGO_ID
                                      , ksl.ULKE_ID
                                      , ksl.IL_ID
                                      , ksl.ILCE_ID
                                      , ksl.HAFTANINGUNU
                                      , ksl.KARGOGONDERIMLIMITI
                                      , ksl.SAATARALIGI_ID
                                      , ksl.KARGOILAVEBEDEL
                                      , ksls.TANIM AS SAATARALIGI
                                      , ksls.SAATSINIRI
                                      , ulk.TANIM AS ULKE
                                      , il.TANIM AS IL
                                      , ilce.TANIM AS ILCE";

            cmd.CommandText += @" FROM kargosaatlimiti AS ksl, ulkeler AS ulk, iller AS il, ilceler AS ilce, kargosaatlimiti_saat AS ksls";
            cmd.CommandText += @" WHERE ksl.ULKE_ID = ulk.ID
                                        AND ksl.SAATARALIGI_ID = ksls.ID
	                                    AND ksl.IL_ID = il.ID
	                                    AND ksl.ILCE_ID = ilce.ID ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                CargoHourLimit sL = new CargoHourLimit();
                sL.ID = Convert.ToInt32(reader["ID"]);
                sL.CargoID = Convert.ToInt32(reader["KARGO_ID"]);
                sL.CountryID = Convert.ToInt32(reader["ULKE_ID"]);
                sL.ProvinceID = Convert.ToInt32(reader["IL_ID"]);
                sL.DistrictID = Convert.ToInt32(reader["ILCE_ID"]);
                sL.WeekDay = Convert.ToInt32(reader["HAFTANINGUNU"]);
                sL.CargoSendLimit = Convert.ToInt32(reader["KARGOGONDERIMLIMITI"]);
                sL.HourRangeID = Convert.ToInt32(reader["SAATARALIGI_ID"]);
                sL.CargoExtraPrice = Convert.ToDouble(reader["KARGOILAVEBEDEL"]);
                sL.Country = reader["ULKE"].ToString();
                sL.Province = reader["IL"].ToString();
                sL.District = reader["ILCE"].ToString();
                sL.HourRange = reader["SAATARALIGI"].ToString();
                sL.HourLimit = TimeSpan.Parse(reader["SAATSINIRI"].ToString());
                saatler.Add(sL);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return saatler;
        }

        public async Task AddAsync(CargoHourLimit entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public async Task UpdateAsync(CargoHourLimit entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public async Task DeleteAsync(CargoHourLimit entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public async Task<int> GetCountAsync(CargoHourLimitFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM kargosaatlimiti AS ksl
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<List<CargoHourLimitWeekDto>> GetCargoHourAsync(CargoHourLimitFilter filter, CancellationToken cancellationToken)
        {
            string langCode = "tr";
            List<CargoHourLimitWeekDto> teslimatGunleri = new List<CargoHourLimitWeekDto>();
            var cultureInfo = CultureInfo.GetCultures(CultureTypes.AllCultures).FirstOrDefault(c => c.Name.StartsWith(langCode + "-"));
            for (int i = 0; i < filter.Day; i++)
            {
                CargoHourLimitWeekDto gun = new CargoHourLimitWeekDto();
                gun.Tarih = filter.Date.AddDays(i);
                filter.Date = gun.Tarih;
                gun.TeslimatSaatleri = (await GetCargoHour(filter, null, cancellationToken)).ToList();
                gun.TarihStr = gun.Tarih.ToString("dd-MM-yyyy");
                gun.GunStr = gun.Tarih.ToString("dd.MM.yyyy") + " " + cultureInfo.DateTimeFormat.GetDayName(gun.Tarih.DayOfWeek);
                teslimatGunleri.Add(gun);
            }

            return teslimatGunleri;
        }


        private void AppendFilter(ref MySqlCommand cmd, CargoHourLimitFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND ksl.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (filter.CargoID > 0)
                {
                    cmd.CommandText += " AND ksl.KARGO_ID = @KARGO_ID";
                    cmd.Parameters.Add("@KARGO_ID", MySqlDbType.Int32).Value = filter.CargoID;
                }

                if (filter.CountryID > 0)
                {
                    cmd.CommandText += " AND ksl.ULKE_ID = @ULKE_ID";
                    cmd.Parameters.Add("@ULKE_ID", MySqlDbType.Int32).Value = filter.CountryID;
                }

                if (filter.ProvinceID > 0)
                {
                    cmd.CommandText += " AND ksl.IL_ID = @IL_ID";
                    cmd.Parameters.Add("@IL_ID", MySqlDbType.Int32).Value = filter.ProvinceID;
                }

                if (filter.DistrictID > 0)
                {
                    cmd.CommandText += " AND ksl.ILCE_ID = @ILCE_ID";
                    cmd.Parameters.Add("@ILCE_ID", MySqlDbType.Int32).Value = filter.DistrictID;
                }

                if (filter.SemtID > 0)
                {
                    cmd.CommandText += " AND ksl.SEMT_ID = @SEMT_ID";
                    cmd.Parameters.Add("@SEMT_ID", MySqlDbType.Int32).Value = filter.SemtID;
                }

                if (filter.Day > -1)
                {
                    cmd.CommandText += " AND ksl.HAFTANINGUNU = @HAFTANINGUNU";
                    cmd.Parameters.Add("@HAFTANINGUNU", MySqlDbType.Int32).Value = filter.Day;
                }
            }
        }

        private async Task<IList<CargoHourLimit>> GetCargoHour(CargoHourLimitFilter filter, CargoHourLimitPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            List<CargoHourLimit> saatler = new List<CargoHourLimit>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ks.ID
                                        , ksls.TANIM AS SAATARALIGI
                                        , ksls.SAATSINIRI
	                                    , ks.KARGOGONDERIMLIMITI
                                        , ks.KARGOILAVEBEDEL
	                                    , IFNULL((SELECT COUNT(ID) FROM siparis WHERE DURUM < 7 AND ODEMETIPI > -1 AND TESLIMATGUNU = @TARIH AND KARGOSAAT_ID = ks.ID GROUP BY KARGOSAAT_ID),0) AS MEVCUTSIPARISSAYISI
                                    FROM kargosaatlimiti AS ks, kargosaatlimiti_saat AS ksls
                                    WHERE ks.ILCE_ID = @ILCE_ID AND ks.KARGO_ID = @KARGO_ID AND ks.HAFTANINGUNU = @HAFTANINGUNU AND ks.SAATARALIGI_ID = ksls.ID";

            if (filter.SemtID > 0)
            {
                cmd.CommandText += " AND ks.SEMT_ID = @SEMT_ID";
                cmd.Parameters.Add("@SEMT_ID", MySqlDbType.Int32).Value = filter.SemtID;
            }

            cmd.Parameters.Add("@TARIH", MySqlDbType.Date).Value = filter.Date;
            cmd.Parameters.Add("@ILCE_ID", MySqlDbType.Int32).Value = filter.DistrictID;
            cmd.Parameters.Add("@KARGO_ID", MySqlDbType.Int32).Value = filter.CargoID;
            cmd.Parameters.Add("@HAFTANINGUNU", MySqlDbType.Int32).Value = Convert.ToInt32(filter.Date.DayOfWeek);

            cmd.CommandText += " ORDER BY ksls.SIRA ASC ";

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (Reader.Read())
            {
                CargoHourLimit s = new CargoHourLimit();
                s.ID = Convert.ToInt32(Reader["ID"]);
                s.HourRange = Reader["SAATARALIGI"].ToString();
                s.HourLimit = TimeSpan.Parse(Reader["SAATSINIRI"].ToString());
                s.CargoSendLimit = Convert.ToInt32(Reader["KARGOGONDERIMLIMITI"]);
                s.CurrentOrderCount = Convert.ToInt32(Reader["MEVCUTSIPARISSAYISI"]);
                DateTime sinirTarih = new DateTime(filter.Date.Year, filter.Date.Month, filter.Date.Day) + s.HourLimit;
                s.ShippingAvailable = (s.CargoSendLimit == -1 || s.CargoSendLimit > s.CurrentOrderCount) && (DateTime.Now < sinirTarih);
                if (s.ShippingAvailable)
                {
                    saatler.Add(s);
                }
            }

            Reader.Close();
            Reader.Dispose();
            //_cnn.Close();
            return saatler;
        }

    }
}