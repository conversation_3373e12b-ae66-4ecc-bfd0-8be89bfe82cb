using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Product.Entities.Enums;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Models.Responses.OrderCollection;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class OrderCollectionSetDal : IOrderCollectionSetDal
    {
        private readonly MySqlConnection _cnn;

        public OrderCollectionSetDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<IList<OrderCollectionPageDto>> OrderCollectionPage(OrderCollectionSetFilter filter = null, OrderCollectionSetPaging paging = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<OrderCollectionPageDto> list = new List<OrderCollectionPageDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.SET_NO
                                     , COUNT(DISTINCT su.SIPARIS_ID) AS SIPARISADEDI
                                     , SUM(su.ADET) AS URUNADEDI
                                     , COUNT(DISTINCT su.HAZIRLAYAN_ID) AS HAZIRLAYANSAYISI
                                     , SUM(su.BULUNAN_ADET) AS TOPLAMBULUNANADET
                                     , SUM(su.EKSIK_ADET) AS TOPLAMEKSIKADET
                                     , su.TARIH
                                     , HAZIRLAYAN_ID
                                     , su.ARABA_ID
                                     FROM depo_dagitilan_urun AS su
                                     LEFT JOIN raflar AS r ON su.RAF_ID = r.ID
                                     WHERE 1";

            AppendFilter(ref cmd, filter);
            cmd.CommandText += " GROUP BY SET_NO ORDER BY su.TARIH DESC";
            PagingExtension.AppendPaging(ref cmd, paging);
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderCollectionPageDto set = new OrderCollectionPageDto();
                set.SetNo = mdr["SET_NO"].ToString();
                set.OrderPiece = mdr["SIPARISADEDI"].ToInt32();
                set.ProductPiece = mdr["URUNADEDI"].ToDouble();
                set.PickerPiece = mdr["HAZIRLAYANSAYISI"].ToInt32();
                set.OccurrencesPiece = mdr["TOPLAMBULUNANADET"].ToDouble();
                set.MissingPiece = mdr["TOPLAMEKSIKADET"].ToDouble();
                set.CreateDate = mdr["TARIH"].ToDateTime();
                set.PreparedId = mdr["HAZIRLAYAN_ID"].ToInt32();
                set.CarId = mdr["ARABA_ID"] != DBNull.Value ? mdr["ARABA_ID"].ToInt32() : 0;
                if (set.MissingPiece == 0 && set.OccurrencesPiece == 0)
                {
                    set.Status = (int)SetStatus.Bekliyor;
                    set.StatusDefinition = SetStatus.Bekliyor.ToString();
                }
                else if(WebSiteInfo.User.Value.Settings.UrunToplamaAyar.UrunToplamaYontemi == 5 &&
                    !WebSiteInfo.User.Value.Settings.UrunToplamaAyar.MissingProductAdminApproval &&
                    (set.MissingPiece + set.OccurrencesPiece == set.ProductPiece))
                {
                    set.Status = (int)SetStatus.Tamamlandi;
                    set.StatusDefinition = SetStatus.Tamamlandi.ToString();
                }
                else if ((set.MissingPiece > 0 || set.OccurrencesPiece > 0) && set.OccurrencesPiece != set.ProductPiece)
                {
                    set.Status = (int)SetStatus.Islemde;
                    set.StatusDefinition = SetStatus.Islemde.ToString();
                }
                else if (set.OccurrencesPiece == set.ProductPiece)
                {
                    set.Status = (int)SetStatus.Tamamlandi;
                    set.StatusDefinition = SetStatus.Tamamlandi.ToString();
                }

                list.Add(set);
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();
            //_cnn.Close();


            return list;
        }

        public async Task<IList<OrderInTheSetDto>> OrdersInTheSet(OrderCollectionSetFilter filter = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<OrderInTheSetDto> list = new List<OrderInTheSetDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.SET_NO
                                     , su.SIPARIS_ID
                                     , su.ADET
                                     , su.URUN_TIPI
                                     FROM depo_dagitilan_urun AS su 
                                     LEFT JOIN raflar AS r ON su.RAF_ID = r.ID
                                     WHERE 1 ";

            AppendFilter(ref cmd, filter);
            if (!filter.isGrouping)
                cmd.CommandText += " GROUP BY su.SIPARIS_ID ";
            else
                cmd.CommandText += " ,su.SIPARIS_ID";

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                OrderInTheSetDto set = new OrderInTheSetDto();
                set.SetNo = mdr["SET_NO"].ToString();
                set.OrderID = mdr["SIPARIS_ID"].ToInt32();
                set.Piece = mdr["ADET"].ToInt32();
                set.ProductType = mdr["URUN_TIPI"].ToInt32();
                list.Add(set);
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();
            //_cnn.Close();


            return list;
        }

        public async Task<bool> GetStoreChangeConfirmation(StoreChangeConfirmationFilter filter, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<OrderInTheSetDto> list = new List<OrderInTheSetDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @$"SELECT COUNT(*)
                                     FROM depo_dagitilan_urun AS su 
                                     WHERE SIPARIS_ID = @SIPARIS_ID AND DEPO_ID IN (SELECT ID FROM depolar WHERE MAGAZA_ID = @MAGAZA_ID)";
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = filter.StoreId;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderId;

            var val = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            //_cnn.Close();
            return val == 0;
        }

        public async Task<IList<SetDetailDto>> SetDetail(OrderCollectionSetFilter filter = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<SetDetailDto> list = new List<SetDetailDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.ID AS ID
                                     , su.URUN_ID AS URUNID
                                     , uk.URUNADI AS URUNADI
                                     , su.RAF_ID AS RAFID
                                     , su.KUTU_ID AS KUTU_ID
                                     , su.KUTUBARKODU AS KUTUBARKODU
                                     , GROUP_CONCAT(DISTINCT su.SIPARIS_ID) AS SIPARISLER
                                     , GROUP_CONCAT(DISTINCT mt.ISIM ,' ',mt.SOYISIM) AS TOPLAYICILAR
                                     , su.BULUNAN_ADET AS BULUNANADET
                                     , su.EKSIK_ADET AS EKSIKADET
                                     , SUM(su.ADET) AS ADET
                                     FROM depo_dagitilan_urun su
                                     LEFT JOIN raflar AS r ON su.RAF_ID = r.ID
                                     LEFT JOIN magaza_temsilci mt ON su.HAZIRLAYAN_ID = mt.ID
                                     LEFT JOIN urunler u ON su.URUN_ID = u.ID
                                     LEFT JOIN urun_karti uk ON u.URUNKARTI_ID = uk.ID WHERE 1 ";

            AppendFilter(ref cmd, filter);
            cmd.CommandText += " GROUP BY su.URUN_ID, su.RAF_ID ";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                SetDetailDto set = new SetDetailDto();
                set.ID = mdr["ID"].ToInt32();
                set.ProductID = mdr["URUNID"].ToInt32();
                set.ProductName = mdr["URUNADI"].ToString();
                set.Piece = mdr["ADET"].ToDouble();
                set.ShelfID = mdr["RAFID"].ToInt32();
                set.OrderIDs = mdr["SIPARISLER"].ToString();
                set.Pickers = mdr["TOPLAYICILAR"].ToString();
                set.MissingPiece = mdr["EKSIKADET"].ToDouble();
                set.OccurrencesPiece = mdr["BULUNANADET"].ToDouble();
                set.WarehouseBoxID = mdr["KUTU_ID"] != DBNull.Value ? mdr["KUTU_ID"].ToInt32() : 0;
                set.WarehouseBoxBarcode = mdr["KUTUBARKODU"].ToString();
                if (set.MissingPiece == 0 && set.OccurrencesPiece == 0)
                {
                    set.Status = (int)SetStatus.Bekliyor;
                    set.StatusDefinition = SetStatus.Bekliyor.ToString();
                }
                else if ((set.MissingPiece > 0 || set.OccurrencesPiece > 0) && set.OccurrencesPiece != set.Piece)
                {
                    set.Status = (int)SetStatus.Islemde;
                    set.StatusDefinition = SetStatus.Islemde.ToString();
                }
                else if (set.OccurrencesPiece == set.Piece)
                {
                    set.Status = (int)SetStatus.Tamamlandi;
                    set.StatusDefinition = SetStatus.Tamamlandi.ToString();
                }

                list.Add(set);
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();
            //_cnn.Close();


            return list;
        }

        public async Task<IList<SetDetailEditDto>> SetDetailEdit(OrderCollectionSetFilter filter = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<SetDetailEditDto> list = new List<SetDetailEditDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.ID AS ID
                                     , su.URUN_ID AS URUNID
                                     , uk.URUNADI AS URUNADI
                                     , su.URUN_TIPI AS URUNTIPI
                                     , su.ADET AS ADET
                                     , su.RAF_ID AS RAFID
                                     , su.BULUNAN_ADET AS BULUNANADET
                                     , su.EKSIK_ADET AS EKSIKADET
                                     , su.SIPARIS_ID AS SIPARISID
                                     , CONCAT(mt.ISIM ,' ',mt.SOYISIM) AS TOPLAYICI 
                                     , su.KUTU_ID AS KUTU_ID
                                     , su.KUTUBARKODU AS KUTUBARKODU
                                     , su.HAZIRLAYAN_ID AS TOPLAYICIID FROM depo_dagitilan_urun su
                                     LEFT JOIN magaza_temsilci mt ON su.HAZIRLAYAN_ID = mt.ID 
                                     LEFT JOIN urunler u ON su.URUN_ID = u.ID 
                                     LEFT JOIN urun_karti uk ON u.URUNKARTI_ID = uk.ID WHERE 1 ";

            AppendFilter(ref cmd, filter);
            cmd.CommandText += " GROUP BY ID ";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                SetDetailEditDto set = new SetDetailEditDto();
                set.ID = mdr["ID"].ToInt32();
                set.ProductID = mdr["URUNID"].ToInt32();
                set.ProductName = mdr["URUNADI"].ToString();
                set.ProductType = mdr["URUNTIPI"].ToInt32();
                set.Piece = mdr["ADET"].ToDouble();
                set.ShelfID = mdr["RAFID"].ToInt32();
                set.Picker = mdr["TOPLAYICI"].ToString();
                set.PickerID = mdr["TOPLAYICIID"].ToInt32();
                set.OccurrencesPiece = mdr["BULUNANADET"].ToDouble();
                set.MissingPiece = mdr["EKSIKADET"].ToDouble();
                set.OrderID = mdr["SIPARISID"].ToInt32();
                set.WarehouseBoxID = mdr["KUTU_ID"] != DBNull.Value ? mdr["KUTU_ID"].ToInt32() : 0;
                set.WarehouseBoxBarcode = mdr["KUTUBARKODU"].ToString();
                list.Add(set);
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();
            //_cnn.Close();


            return list;
        }

        public async Task<IList<OrderCollectionSet>> GetPendingCollectSetWithPreparedID(int preparedID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<OrderCollectionSet> list = new List<OrderCollectionSet>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT SET_NO,TARIH FROM depo_dagitilan_urun as ddu WHERE 1 ";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND ddu.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.CommandText += @" AND ddu.HAZIRLAYAN_ID = @HAZIRLAYAN_ID AND ddu.HAZIRLAMA_DURUMU = 0 AND ddu.BULUNAN_ADET + ddu.EKSIK_ADET < ddu.ADET GROUP BY SET_NO ORDER BY TARIH;";
            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = preparedID;
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                OrderCollectionSet set = new OrderCollectionSet();
                set.SetNo = mdr["SET_NO"].ToString();
                set.Date = mdr["TARIH"].ToDateTime();
                list.Add(set);
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();
            //_cnn.Close();


            return list;
        }

        public async Task<IList<OrderCollectionSet>> GetPendingMergeSetWithTableID(int tableID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<OrderCollectionSet> list = new List<OrderCollectionSet>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT SET_NO,TARIH FROM depo_dagitilan_urun WHERE 1 ";
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = tableID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.CommandText += " AND MASA_ID = @MASA_ID AND HAZIRLAMA_DURUMU = 0 AND EKSIK_ADET = 0 GROUP BY SET_NO ORDER BY TARIH;";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                OrderCollectionSet set = new OrderCollectionSet();
                set.SetNo = mdr["SET_NO"].ToString();
                set.Date = mdr["TARIH"].ToDateTime();
                list.Add(set);
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();
            //_cnn.Close();


            return list;
        }

        public async Task<OrderCollectionSet> GetSetProducts(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            if (filter == null)
            {
                return null;
            }
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync();

            OrderCollectionSet set = new OrderCollectionSet();
            set.SetNo = filter.SetNo;
            set.Products = new List<OrderProduct>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT
                                    su.ID AS ID
                                    , su.SET_NO
                                    , su.SIPARIS_URUN_ID
                                    , su.SIPARIS_ID
                                    , s.DURUM
                                    , su.URUN_ID
                                    , su.URUNKARTI_ID
                                    , uk.{filter.ProductNameFields} AS URUNADI
                                    , uk.SATISBIRIMI
                                    , u.BARKOD
                                    , u.STOKKODU
                                    , uk.RESIM1
                                    , (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
                                            FROM urun_eksecenek AS ues
                                                WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA )  AS VARYASYON
                                    , su.RAFADI
                                    , su.RAFBARKODU
                                    , su.RAF_ID
                                    , su.DEPO_ID
                                    , su.DEPOADI
                                    , su.DEPOKODU
                                    , su.MASA_ID
                                    , su.MASAADI
                                    , su.KOLI_ID
                                    , su.KOLIADI
                                    , s.SIPARISKAYNAGI
                                    , s.KARGOFIRMA_ID
                                    , su.TARIH
                                    , u.TEDARIKCIKODU
                                    , u.TEDARIKCIKODU2
                                    , u.SATISFIYATI
                                    , uk.URUNADEDIONDALIKLI
                                    , uk.BREADCRUMBKATEGORI
                                    , su.HAZIRLAMA_DURUMU
                                    , su.HAZIRLAYAN_ID
                                    , su.URUN_TIPI
                                    , {(filter.isGrouping ? "SUM(su.ADET) AS ADET" : "su.ADET")}
                                    , {(filter.isGrouping ? "SUM(su.BULUNAN_ADET) AS BULUNAN_ADET" : "su.BULUNAN_ADET")}
                                    , {(filter.isGrouping ? "SUM(su.EKSIK_ADET) AS EKSIK_ADET" : "su.EKSIK_ADET")}
                                    , su.SIPARISURUNADEDI
                                    , su.ARABA_ID
                                    , su.KUTU_ID
                                    , su.KUTUBARKODU
                                    , su.RAF_TIPI
                                    , s.TARIH AS SIPARIS_TARIH
                                    , (SELECT sipu.DURUM FROM siparis_urun AS sipu WHERE sipu.ID = su.SIPARIS_URUN_ID) AS URUNDURUM
                                    , (SELECT sipu.TUTAR FROM siparis_urun AS sipu WHERE sipu.ID = su.SIPARIS_URUN_ID) AS TUTAR
                                    , (SELECT sipu.KDV FROM siparis_urun AS sipu WHERE sipu.ID = su.SIPARIS_URUN_ID) AS KDV
                                FROM depo_dagitilan_urun AS su
                                LEFT JOIN siparis AS s ON s.ID = su.SIPARIS_ID
                                LEFT JOIN urun_karti AS uk ON uk.ID = su.URUNKARTI_ID
                                LEFT JOIN urunler AS u ON u.ID = su.URUN_ID
                                LEFT JOIN raflar as r ON r.ID = su.RAF_ID
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            cmd.CommandText += " ORDER BY r.SIRA,su.URUN_ID";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderProduct p = new OrderProduct();
                p.ID = mdr["ID"] != DBNull.Value ? mdr["ID"].ToInt32() : 0;
                p.SetNo = set.SetNo;
                p.OrderID = mdr["SIPARIS_ID"].ToInt32();
                p.OrderStatus = mdr["DURUM"].ToInt32();
                p.OrderProductID = mdr["SIPARIS_URUN_ID"].ToInt32();
                p.ProductCardID = mdr["URUNKARTI_ID"].ToInt32();
                p.ProductID = mdr["URUN_ID"].ToInt32();
                p.ProductType = (ProductType)mdr["URUN_TIPI"].ToInt32();
                p.ProductName = mdr["URUNADI"] + " " + mdr["VARYASYON"];
                p.Barcode = mdr["BARKOD"].ToString();
                p.StockCode = mdr["STOKKODU"].ToString();
                p.AdditionalOptions = mdr["VARYASYON"].ToString();
                p.ShelfID = mdr["RAF_ID"] != DBNull.Value ? mdr["RAF_ID"].ToInt32() : 0;
                p.ShelfName = mdr["RAFADI"].ToString();
                p.ShelfBarcode = mdr["RAFBARKODU"].ToString();
                p.WarehouseID = mdr["DEPO_ID"] != DBNull.Value ? mdr["DEPO_ID"].ToInt32() : 0;
                p.WarehouseName = mdr["DEPOADI"].ToString();
                p.WarehouseCode = mdr["DEPOKODU"].ToString();
                p.Piece = mdr["ADET"] != DBNull.Value ? mdr["ADET"].ToDouble() : 0;
                p.SalePrice = mdr["SATISFIYATI"] != DBNull.Value ? mdr["SATISFIYATI"].ToDouble() : 0;
                p.OccurrencesPiece = mdr["BULUNAN_ADET"] != DBNull.Value ? mdr["BULUNAN_ADET"].ToDouble() : 0;
                p.Amount = mdr["TUTAR"] != DBNull.Value ? mdr["TUTAR"].ToDouble() : 0;
                p.KDVAmount = mdr["KDV"] != DBNull.Value ? mdr["KDV"].ToDouble() : 0;
                p.MissingPiece = mdr["EKSIK_ADET"] != DBNull.Value ? mdr["EKSIK_ADET"].ToDouble() : 0;
                p.Image = WebSiteInfo.User.Value.ImagePath + mdr["RESIM1"].ToString();
                p.SupplierCode = mdr["TEDARIKCIKODU"].ToString();
                p.SupplierCode2 = mdr["TEDARIKCIKODU2"].ToString();
                p.BreadCrumbCategoriesDto = mdr["BREADCRUMBKATEGORI"] != DBNull.Value ? mdr["BREADCRUMBKATEGORI"].ToJsonDeserialize<List<BreadCrumbCategoryDto>>() : new List<BreadCrumbCategoryDto>();
                p.SalesUnit = mdr["SATISBIRIMI"].ToString();
                p.isProductPieceDecimal = mdr["URUNADEDIONDALIKLI"] != DBNull.Value ? mdr["URUNADEDIONDALIKLI"].ToBoolean() : false;
                p.TableID = mdr["MASA_ID"].ToInt32();
                p.TableName = mdr["MASAADI"].ToString();
                p.BoxID = mdr["KOLI_ID"].ToInt32();
                p.BoxName = mdr["KOLIADI"].ToString();
                p.ShelfType = mdr["RAF_TIPI"].ToInt32();
                p.PreparationStatus = mdr["HAZIRLAMA_DURUMU"].ToInt32();
                var statusDefinition = (SetPreparedStatus)p.PreparationStatus;
                p.PreparationStatusDefinition = statusDefinition.ToString();
                p.OrderProductCount = mdr["SIPARISURUNADEDI"] != DBNull.Value ? mdr["SIPARISURUNADEDI"].ToDouble() : 0;
                p.CarID = mdr["ARABA_ID"] != DBNull.Value ? mdr["ARABA_ID"].ToInt32() : 0;
                p.PreparedID = mdr["HAZIRLAYAN_ID"].ToInt32();
                p.SetNo = mdr["SET_NO"].ToString();
                p.WarehouseBoxID = mdr["KUTU_ID"].ToInt32();
                p.WarehouseBoxBarcode = mdr["KUTUBARKODU"].ToString();
                p.Status = mdr["URUNDURUM"] != DBNull.Value ? mdr["URUNDURUM"].ToInt32() : 0;
                p.OrderSource = mdr["SIPARISKAYNAGI"].ToString();
                p.CargoCompanyId = mdr["KARGOFIRMA_ID"].ToInt32();
                p.OrderDate = mdr["SIPARIS_TARIH"].ToDateTime();
                set.Products.Add(p);

                set.SetNo = mdr["SET_NO"].ToString();
                set.Warehouse = mdr["DEPOADI"].ToString();
                set.Table = mdr["MASAADI"].ToString();
                set.Date = mdr["TARIH"].ToDateTime();
                if (p.MissingPiece == 0 && p.OccurrencesPiece == 0)
                {
                    set.Status = (int)SetStatus.Bekliyor;
                    set.StatusDefinition = SetStatus.Bekliyor.ToString();
                }
                else if ((p.MissingPiece > 0 || p.OccurrencesPiece > 0) && p.OccurrencesPiece != p.Piece)
                {
                    set.Status = (int)SetStatus.Islemde;
                    set.StatusDefinition = SetStatus.Islemde.ToString();
                }
                else if (p.OccurrencesPiece == p.Piece)
                {
                    set.Status = (int)SetStatus.Tamamlandi;
                    set.StatusDefinition = SetStatus.Tamamlandi.ToString();
                }
            }

            mdr.Close();
            await mdr.DisposeAsync();
            cmd.Dispose();
            //await _cnn.CloseAsync();

            var multipleBarcode = WebSiteInfo.User.Value?.Settings?.UrunToplamaAyar?.CokluBarkod ?? false;
            if (multipleBarcode && set.Products is { Count: > 0 })
            {
                var barcodes = await GetProductBarcodesByList(set.Products.Select(x => x.ProductID).ToList(), cancellationToken);
                foreach (var item in set.Products)
                {
                    item.Barcodes = barcodes.Where(x => x.ProductId == item.ProductID).Select(x => x.Barcode).ToList();
                }
            }

            return set;
        }

        public class ProductBarcodeItem
        {
            public int ProductId { get; set; }

            public string Barcode { get; set; }
        }

        public async Task<List<ProductBarcodeItem>> GetProductBarcodesByList(List<int> productIds, CancellationToken cancellationToken)
        {
            List<ProductBarcodeItem> barcodes = new List<ProductBarcodeItem>();
            //string connectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //using (MySqlConnection _cnn = new MySqlConnection(connectionString))
            //{
            //await _cnn.OpenAsync(cancellationToken);

            using (MySqlCommand cmd = new MySqlCommand($"SELECT URUN_ID,BARKOD FROM urun_barkod WHERE URUN_ID IN ({string.Join(',', productIds.Distinct())})", _cnn))
            {
                using (MySqlDataReader reader = await cmd.ExecuteReaderAsync(cancellationToken))
                {
                    while (await reader.ReadAsync(cancellationToken))
                    {
                        barcodes.Add(new ProductBarcodeItem() { ProductId = reader["URUN_ID"].ToInt32(), Barcode = reader["BARKOD"].ToString()?.Trim() });
                    }
                }
            }
            //}

            return barcodes;
        }

        public async Task<List<string>> GetProductBarcodes(int ProductID, CancellationToken cancellationToken)
        {
            List<string> Barcodes = new List<string>();
            //string connectionString = Connection.DevConnectionString(Info.DomainName.Value);

            //using (MySqlConnection _cnn = new MySqlConnection(connectionString))
            //{
            //await _cnn.OpenAsync(cancellationToken);

            using (MySqlCommand cmd = new MySqlCommand("SELECT * FROM urun_barkod WHERE URUN_ID=@URUN_ID", _cnn))
            {
                cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = ProductID;

                using (MySqlDataReader reader = await cmd.ExecuteReaderAsync(cancellationToken))
                {
                    while (await reader.ReadAsync(cancellationToken))
                    {
                        Barcodes.Add(reader["BARKOD"].ToString().Trim());
                    }
                }
            }
            //}

            return Barcodes;
        }

        public async Task<IList<OrderCollectionOrderTypeDto>> GetMySetOrder(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            if (filter == null)
            {
                return null;
            }

            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            var list = new List<OrderCollectionOrderTypeDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            cmd.CommandText = @"SELECT	
                                      ddu.SIPARIS_ID 	  AS OrderId,
                                      ddu.SIPARIS_URUN_ID AS ProductId,
                                      ddu.Set_No          AS SetNo
                                 FROM depo_dagitilan_urun AS ddu
                                 WHERE 1";

            AppendFilterOrderType(ref cmd, filter);
            cmd.CommandText += @" GROUP BY 
                                   ddu.Siparis_Id,
                                   ddu.Siparis_Urun_Id,
                                   ddu.Set_No";

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);

            while (mdr.Read())
            {
                var orderCollectionDto = new OrderCollectionOrderTypeDto
                {
                    OrderId = mdr["OrderId"].ToInt32(),
                    ProductId = mdr["ProductId"].ToInt32(),
                    SetNo = mdr["SetNo"].ToString()
                };

                list.Add(orderCollectionDto);
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();
            //_cnn.Close();


            return list;
        }

        public async Task<int> MissingProductCountInSet(string setNo, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                COUNT(*)
                            FROM
                                depo_dagitilan_urun
                            WHERE 
                                SET_NO = @SET_NO
                                AND EKSIK_ADET > 0 ";

            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var val = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            _cnn.Close();

            return val;
        }

        public async Task<int> ProductCountInSet(string setNo, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                COUNT(*)
                            FROM
                                depo_dagitilan_urun
                            WHERE
                                SET_NO = @SET_NO";

            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var val = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            //_cnn.Close();

            return val;
        }

        public async Task<int> CollectedProductCountInSet(string setNo, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                COUNT(*)
                            FROM
                                depo_dagitilan_urun
                            WHERE
                                SET_NO = @SET_NO
                                AND BULUNAN_ADET > 0";

            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var val = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            //_cnn.Close();

            return val;
        }

        public async Task<int> ParcelOrderCount(int parcelID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT COUNT(DISTINCT SIPARIS_ID) FROM depo_dagitilan_urun WHERE KOLI_ID = @KOLI_ID ";
            cmd.Parameters.Add("@KOLI_ID", MySqlDbType.Int32).Value = parcelID;

            int count = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();
            cmd.Dispose();
            //_cnn.Close();

            return count;
        }

        public async Task OrderProductParcelAssign(int parcelID, int orderID, string parcelName, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET KOLI_ID = @KOLI_ID, KOLIADI = @KOLIADI WHERE SIPARIS_ID = @SIPARIS_ID";
            cmd.Parameters.Add("@KOLI_ID", MySqlDbType.Int32).Value = parcelID;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderID;
            cmd.Parameters.Add("@KOLIADI", MySqlDbType.VarChar).Value = parcelName;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task OrderProductTableAssignAsync(int orderId, int tableId, string tableName, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET MASA_ID = @MASA_ID, MASAADI = @MASAADI WHERE SIPARIS_ID = @SIPARIS_ID";
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = tableId;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderId;
            cmd.Parameters.Add("@MASAADI", MySqlDbType.VarChar).Value = tableName;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task OrderParcelPreparation(List<int> IDs, DateTime? parcelAssignTime, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_dagitilan_urun SET HAZIRLAMA_DURUMU = 1 ";
            if (parcelAssignTime.HasValue)
            {
                cmd.CommandText += ", KOLIATAMATARIHI = @KOLIATAMATARIHI";
                cmd.Parameters.Add("@KOLIATAMATARIHI", MySqlDbType.DateTime).Value = parcelAssignTime.Value;
            }

            cmd.CommandText += $@" WHERE ID IN (" + string.Join(",", IDs) + ") ";
            //cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            //cmd.ExecuteNonQuery();
            //_cnn.Close();
            //
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetTableAssign(string setNo, int tableID, string tableName, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET MASA_ID = @MASA_ID, MASAADI = @MASAADI WHERE SET_NO = @SET_NO ";
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = tableID;
            cmd.Parameters.Add("@MASAADI", MySqlDbType.VarChar).Value = tableName;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            //
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateMissingProduct(int ID, int PreparingStatus, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET EKSIK_ADET = 1, BULUNAN_ADET = 0 ";
            if (PreparingStatus == 5)
            {
                cmd.CommandText += " , HAZIRLAMA_DURUMU = 0 ";
            }

            cmd.CommandText += " WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetQualityControlCompleated(int orderID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText += @"INSERT INTO depo_dagitilan_urun_rapor
                                                    (SET_NO
                                                    ,SIPARIS_URUN_ID
                                                    ,SIPARIS_ID
                                                    ,URUNKARTI_ID
                                                    ,URUN_ID
                                                    ,STOKKODU
                                                    ,BARKOD
                                                    ,ADET
                                                    ,BULUNAN_ADET
                                                    ,BULUNMA_TARIHI
                                                    ,EKSIK_ADET
                                                    ,TARIH
                                                    ,HAZIRLAYAN_ID
                                                    ,MASA_ID
                                                    ,MASAADI
                                                    ,KOLI_ID
                                                    ,KOLIADI
                                                    ,KOLIATAMATARIHI
                                                    ,HAZIRLAMA_DURUMU
                                                    ,DEPO_ID
                                                    ,DEPOADI
                                                    ,DEPOKODU
                                                    ,RAF_ID
                                                    ,RAFADI
                                                    ,RAFBARKODU
                                                    ,SIPARISURUNADEDI
                                                    ,ARABA_ID)
                                              SELECT SET_NO
                                                    ,SIPARIS_URUN_ID
                                                    ,SIPARIS_ID
                                                    ,URUNKARTI_ID
                                                    ,URUN_ID
                                                    ,STOKKODU
                                                    ,BARKOD
                                                    ,ADET
                                                    ,BULUNAN_ADET
                                                    ,BULUNMA_TARIHI
                                                    ,EKSIK_ADET
                                                    ,TARIH
                                                    ,HAZIRLAYAN_ID
                                                    ,MASA_ID
                                                    ,MASAADI
                                                    ,KOLI_ID
                                                    ,KOLIADI
                                                    ,KOLIATAMATARIHI
                                                    ,2
                                                    ,DEPO_ID
                                                    ,DEPOADI
                                                    ,DEPOKODU
                                                    ,RAF_ID
                                                    ,RAFADI
                                                    ,RAFBARKODU
                                                    ,SIPARISURUNADEDI
                                                    ,ARABA_ID
                                                    FROM depo_dagitilan_urun WHERE SIPARIS_ID = @SIPARIS_ID AND DEPO_ID = @DEPO_ID;";

            cmd.CommandText += @"DELETE FROM depo_dagitilan_urun WHERE SIPARIS_ID = @SIPARIS_ID AND DEPO_ID = @DEPO_ID;";
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderID;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetQualityControlCompletedByID(OrderCollectionSetQualityControlCompleatedByIDDto request, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            var IDParameter = string.Join(",", request.ID);
            if (!request.PreparationCondition.HasValue || request.PreparationCondition.Value)
            {
                cmd.CommandText = $"UPDATE depo_dagitilan_urun SET EKSIK_ADET = 0, BULUNAN_ADET = 1, HAZIRLAMA_DURUMU = 2, KOLI_ID = 0 WHERE ID IN ({IDParameter})";
                if (!WebSiteInfo.User.Value.IsOneStore)
                {
                    cmd.CommandText += " AND DEPO_ID = @DEPO_ID; ";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                }
                else
                    cmd.CommandText += ";";

                await cmd.ExecuteTransactionCommandAsync();
                cmd = new MySqlCommand(string.Empty, _cnn);
            }

            cmd.CommandText = $@"INSERT INTO depo_dagitilan_urun_rapor
                                                    (SET_NO
                                                    ,SIPARIS_URUN_ID
                                                    ,SIPARIS_ID
                                                    ,URUNKARTI_ID
                                                    ,URUN_ID
                                                    ,URUN_TIPI
                                                    ,STOKKODU
                                                    ,BARKOD
                                                    ,ADET
                                                    ,BULUNAN_ADET
                                                    ,BULUNMA_TARIHI
                                                    ,EKSIK_ADET
                                                    ,TARIH
                                                    ,HAZIRLAYAN_ID
                                                    ,MASA_ID
                                                    ,MASAADI
                                                    ,KOLI_ID
                                                    ,KOLIADI
                                                    ,KOLIATAMATARIHI
                                                    ,HAZIRLAMA_DURUMU
                                                    ,DEPO_ID
                                                    ,DEPOADI
                                                    ,DEPOKODU
                                                    ,RAF_ID
                                                    ,RAFADI
                                                    ,RAFBARKODU
                                                    ,SIPARISURUNADEDI
                                                    ,ARABA_ID)
                                              SELECT SET_NO
                                                    ,SIPARIS_URUN_ID
                                                    ,SIPARIS_ID
                                                    ,URUNKARTI_ID
                                                    ,URUN_ID
                                                    ,URUN_TIPI
                                                    ,STOKKODU
                                                    ,BARKOD
                                                    ,ADET
                                                    ,BULUNAN_ADET
                                                    ,BULUNMA_TARIHI
                                                    ,EKSIK_ADET
                                                    ,TARIH
                                                    ,HAZIRLAYAN_ID
                                                    ,MASA_ID
                                                    ,MASAADI
                                                    ,KOLI_ID
                                                    ,KOLIADI
                                                    ,KOLIATAMATARIHI
                                                    ,HAZIRLAMA_DURUMU
                                                    ,DEPO_ID
                                                    ,DEPOADI
                                                    ,DEPOKODU
                                                    ,RAF_ID
                                                    ,RAFADI
                                                    ,RAFBARKODU
                                                    ,SIPARISURUNADEDI
                                                    ,ARABA_ID
                                                    FROM depo_dagitilan_urun WHERE ID IN({IDParameter});";

            await cmd.ExecuteTransactionCommandAsync();
            cmd = new MySqlCommand(string.Empty, _cnn);

            cmd.CommandText = $@"DELETE FROM depo_dagitilan_urun WHERE ID IN({IDParameter}) ";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> DeleteByID(int ID, CancellationToken cancellationToken)
        {
            int result = 0;
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE ID = @ID ";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.Parameters.Add("@ID", MySqlDbType.VarChar).Value = ID;
            //result = cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            //

            await cmd.ExecuteTransactionCommandAsync();
            return result;
        }

        public async Task DeleteByOrderProductIdForOneRowAsync(int orderProductId, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE SIPARIS_URUN_ID = @SIPARIS_URUN_ID LIMIT 1;";
            cmd.Parameters.Add("@SIPARIS_URUN_ID", MySqlDbType.Int32).Value = orderProductId;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteByOrderProductIdMissingProductForOneRowAsync(int orderProductId, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE SIPARIS_URUN_ID = @SIPARIS_URUN_ID AND BULUNAN_ADET = 0 LIMIT 1;";
            cmd.Parameters.Add("@SIPARIS_URUN_ID", MySqlDbType.Int32).Value = orderProductId;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdatePreparingStatus(SetPreparingStatusCollectionProductDto request, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET HAZIRLAMA_DURUMU = @HAZIRLAMA_DURUMU WHERE 1 ";
            if (request.ID > 0)
            {
                cmd.CommandText += " AND ID = @ID";
                cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = request.ID;
            }

            if (request.IDs != null && request.IDs.Count > 0)
                cmd.CommandText += $" AND ID IN ({string.Join(",", request.IDs)})";

            cmd.Parameters.Add("@HAZIRLAMA_DURUMU", MySqlDbType.Int32).Value = request.PreparingStatus;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetPreparedAndShelfUpdate(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET RAFADI = @RAFADI, HAZIRLAYAN_ID = @HAZIRLAYAN_ID, RAF_ID = @RAF_ID WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = filter.PreparedID;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = filter.ShelfID;
            cmd.Parameters.Add("@RAFADI", MySqlDbType.VarChar).Value = filter.ShelfName;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteSet(string SetNo, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE SET_NO = @SET_NO ";
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetAssign(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET HAZIRLAYAN_ID = @HAZIRLAYAN_ID WHERE SET_NO = @SET_NO ";
            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = filter.PreparedID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetAssignByShelfID(List<int> IDs, List<int> ShelfIDs, int preparedID, CancellationToken cancellationToken)
        {
            if (ShelfIDs == null && ShelfIDs.Count == 0)
            {
                return;
            }

            //_cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = $@"UPDATE 
                                    depo_dagitilan_urun 
                                 SET HAZIRLAYAN_ID = @HAZIRLAYAN_ID
                                 WHERE 1 AND 
                                 {(
                                         IDs.Count > 0 ?
                                             $"ID IN ({string.Join(",", IDs)}) AND " :
                                             ""
                                     )}
                                 RAF_ID IN ({string.Join(",", ShelfIDs)})";

            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = preparedID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            //
        }

        public async Task SetTableIDUpdate(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET MASA_ID = @MASA_ID WHERE SET_NO = @SET_NO ";
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = filter.TableID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }
            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            //

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> DeleteByPreparedID(int userID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE HAZIRLAYAN_ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = userID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            int affectedRows = await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();
            //_cnn.Close();


            return affectedRows;
        }

        public async Task<IList<DistributionProductCheckerDto>> CheckDistributionProductAnyProblem(string setNo, CancellationToken cancellationToken)
        {
            List<DistributionProductCheckerDto> list = new List<DistributionProductCheckerDto>();
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                      ddu.SIPARIS_ID,
                                      SUM(ddu.ADET) AS TOPLAM_DAGITILAN_MIKTARI,
                                      (SELECT SUM(su.ADET) FROM siparis_urun AS su WHERE su.SIPARIS_ID = ddu.SIPARIS_ID GROUP BY su.SIPARIS_ID AND su.DURUM <> 2) AS TOPLAM_SIPARIS_URUN_MIKTARI
                                    FROM
                                      depo_dagitilan_urun AS ddu
                                    WHERE SET_NO = @SET_NO
                                    ";

            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND ddu.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.CommandText += "GROUP BY ddu.SIPARIS_ID;";
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (reader.Read())
            {
                list.Add(new DistributionProductCheckerDto
                {
                    OrderID = reader["SIPARIS_ID"].ToInt32(),
                    SumDistributionAmount = reader["TOPLAM_DAGITILAN_MIKTARI"].ToDouble(),
                    SumOrderProductsAmount = reader["TOPLAM_SIPARIS_URUN_MIKTARI"].ToDouble()
                });
            }

            cmd.Dispose();
            //_cnn.Close();

            return list;
        }

        public async Task DeleteByOrderID(int orderID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE SIPARIS_ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }
            //cmd.ExecuteNonQuery();

            //cmd.Dispose();
            //_cnn.Close();
            //

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task AddWithSetNo(AddWithSetNoDto request, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();

            List<string> valueList = new List<string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = request.SetNo;

            for (int i = 0; i < request.Products.Count; i++)
            {
                valueList.Add($@"(  @SET_NO
                                  , @SIPARIS_URUN_ID{i}
                                  , @SIPARIS_ID{i}
                                  , @URUNKARTI_ID{i}
                                  , @URUN_ID{i}
                                  , @URUN_TIPI{i}
                                  , @STOKKODU{i}
                                  , @BARKOD{i}
                                  , 1
                                  , 0
                                  , @EKSIK_ADET{i}
                                  , @TARIH{i}
                                  , @HAZIRLAYAN_ID{i}
                                  , @MASA_ID{i}
                                  , @MASAADI{i}
                                  , @KOLI_ID{i}
                                  , @KOLIADI{i}
                                  , 0
                                  , @DEPO_ID{i}
                                  , @DEPOADI{i}
                                  , @DEPOKODU{i}
                                  , @RAF_ID{i}
                                  , @RAFADI{i}
                                  , @RAFBARKODU{i}
                                  , @KUTU_ID{i}
                                  , @KUTUBARKODU{i}
                                  , @SIPARISURUNADEDI{i})");

                cmd.Parameters.Add($"@SIPARIS_URUN_ID{i}", MySqlDbType.Int32).Value = request.Products[i].OrderProductID;
                cmd.Parameters.Add($"@URUNKARTI_ID{i}", MySqlDbType.Int32).Value = request.Products[i].ProductCardID;
                cmd.Parameters.Add($"@URUN_ID{i}", MySqlDbType.Int32).Value = request.Products[i].ProductID;
                cmd.Parameters.Add($"@URUN_TIPI{i}", MySqlDbType.Int32).Value = request.Products[i].ProductType;
                cmd.Parameters.Add($"@SIPARIS_ID{i}", MySqlDbType.Int32).Value = request.Products[i].OrderID;
                cmd.Parameters.Add($"@STOKKODU{i}", MySqlDbType.VarChar).Value = request.Products[i].StockCode;
                cmd.Parameters.Add($"@BARKOD{i}", MySqlDbType.VarChar).Value = request.Products[i].Barcode;
                cmd.Parameters.Add($"@EKSIK_ADET{i}", MySqlDbType.Double).Value = request.Products[i].MissingPiece;
                cmd.Parameters.Add($"@TARIH{i}", MySqlDbType.DateTime).Value = DateTime.Now;
                cmd.Parameters.Add($"@HAZIRLAYAN_ID{i}", MySqlDbType.Int32).Value = request.Products[i].PickerID;
                cmd.Parameters.Add($"@MASA_ID{i}", MySqlDbType.Int32).Value = request.Products[i].TableID;
                cmd.Parameters.Add($"@MASAADI{i}", MySqlDbType.VarChar).Value = request.Products[i].TableName;
                cmd.Parameters.Add($"@KOLI_ID{i}", MySqlDbType.Int32).Value = request.Products[i].BoxID;
                cmd.Parameters.Add($"@KOLIADI{i}", MySqlDbType.VarChar).Value = request.Products[i].BoxName;
                cmd.Parameters.Add($"@DEPO_ID{i}", MySqlDbType.Int32).Value = request.Products[i].WarehouseID;
                cmd.Parameters.Add($"@DEPOADI{i}", MySqlDbType.VarChar).Value = request.Products[i].WarehouseName;
                cmd.Parameters.Add($"@DEPOKODU{i}", MySqlDbType.VarChar).Value = request.Products[i].WarehouseCode;
                cmd.Parameters.Add($"@RAF_ID{i}", MySqlDbType.Int32).Value = request.Products[i].ShelfID;
                cmd.Parameters.Add($"@RAFADI{i}", MySqlDbType.VarChar).Value = request.Products[i].ShelfName;
                cmd.Parameters.Add($"@RAFBARKODU{i}", MySqlDbType.VarChar).Value = request.Products[i].ShelfBarcode;
                cmd.Parameters.Add($"@KUTU_ID{i}", MySqlDbType.Int32).Value = request.Products[i].WarehouseBoxID;
                cmd.Parameters.Add($"@KUTUBARKODU{i}", MySqlDbType.VarChar).Value = request.Products[i].WarehouseBoxBarcode;
                cmd.Parameters.Add($"@SIPARISURUNADEDI{i}", MySqlDbType.Double).Value = request.Products[i].OrderProductPiece;

                cmd.CommandText = $@"INSERT INTO `depo_dagitilan_urun` (          `SET_NO`
                                                                                , `SIPARIS_URUN_ID`
                                                                                , `SIPARIS_ID`
                                                                                , `URUNKARTI_ID`
                                                                                , `URUN_ID`
                                                                                , `URUN_TIPI`
                                                                                , `STOKKODU`
                                                                                , `BARKOD`
                                                                                , `ADET`
                                                                                , `BULUNAN_ADET`
                                                                                , `EKSIK_ADET`
                                                                                , `TARIH`
                                                                                , `HAZIRLAYAN_ID`
                                                                                , `MASA_ID`
                                                                                , `MASAADI`
                                                                                , `KOLI_ID`
                                                                                , `KOLIADI`
                                                                                , `HAZIRLAMA_DURUMU`
                                                                                , `DEPO_ID`
                                                                                , `DEPOADI`
                                                                                , `DEPOKODU`
                                                                                , `RAF_ID`
                                                                                , `RAFADI`
                                                                                , `RAFBARKODU`
                                                                                , `KUTU_ID`
                                                                                , `KUTUBARKODU`
                                                                                , `SIPARISURUNADEDI`)
                                                                        VALUES {string.Join(",", valueList)};";
            }

            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            //

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetUpdateCarID(string setNo, int carID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET ARABA_ID = @ARABAID WHERE SET_NO = @SETNO";
            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            cmd.Parameters.Add("@ARABAID", MySqlDbType.Int32).Value = carID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }
            //cmd.ExecuteNonQuery();

            //cmd.Dispose();
            //_cnn.Close();
            //

            await cmd.ExecuteTransactionCommandAsync();
        }
        public async Task SetUpdatePreparedID(string setNo, int preparedId, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET HAZIRLAYAN_ID = @HAZIRLAYAN_ID WHERE SET_NO = @SETNO";
            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = preparedId;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetUndoInTheCar(int carID, string setNo, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET BULUNAN_ADET = 0, EKSIK_ADET = 0, HAZIRLAMA_DURUMU = 0, KOLI_ID = 0, KOLIADI = '', MASA_ID = 0, MASAADI = '', ARABA_ID = 0 WHERE ARABA_ID = @ARABAID AND SET_NO = @SETNO";
            cmd.Parameters.Add("@ARABAID", MySqlDbType.Int32).Value = carID;
            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task MissingPieceToOccunciesPiece(MissingPieceToOccunciesPieceDto request, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand();
            cmd.CommandText += @"UPDATE depo_dagitilan_urun SET 
                                    EKSIK_ADET = @ADET,
                                    BULUNAN_ADET = @ADET
                                 WHERE ID = @ID;";

            cmd.CommandText += "UPDATE siparis_urun SET BULUNMA_ADEDI = IFNULL(BULUNMA_ADEDI, 0) + @ADET, EKSIK_ADEDI = IFNULL(EKSIK_ADEDI, 0) - @ADET WHERE ID = @ORDERLINEID;";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = request.OrderCollectionID;
            cmd.Parameters.Add("@ORDERLINEID", MySqlDbType.Int32).Value = request.OrderLineId;
            cmd.Parameters.Add("@ADET", MySqlDbType.Double).Value = request.Piece;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<OrderCollectionMinifyOrder>> GetCollectionMinifyOrdersAsync(string setNo, OrderCollectionSetPaging paging = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            var orders = new List<OrderCollectionMinifyOrder>();
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ddu.SIPARIS_ID
                                     , s.SIPARISNO 
                                    FROM depo_dagitilan_urun AS ddu 
                                    INNER JOIN siparis AS s ON ddu.SIPARIS_ID = s.ID 
                                    WHERE ddu.SET_NO = @SETNO ";

            if (!WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                cmd.CommandText += " AND s.PAKETLEMETARIHI = '1970-01-01 00:00:00'";

            cmd.CommandText += " GROUP BY ddu.SIPARIS_ID";

            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            PagingExtension.AppendPaging(ref cmd, paging);
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                orders.Add(new OrderCollectionMinifyOrder
                {
                    OrderId = mdr["SIPARIS_ID"].ToInt32(),
                    OrderNo = mdr["SIPARISNO"].ToString(),
                });
            }

            // await _cnn.CloseAsync();
            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            return orders;
        }

        public async Task<int> GetCollectionMinifyOrdersCountAsync(string setNo, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(DISTINCT (ddu.SIPARIS_ID))
                                    FROM depo_dagitilan_urun AS ddu 
                                    INNER JOIN siparis AS s ON ddu.SIPARIS_ID = s.ID 
                                    WHERE ddu.SET_NO = @SETNO AND s.PAKETLEMETARIHI = '1970-01-01 00:00:00'";

            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            //await _cnn.CloseAsync();

            await cmd.DisposeAsync();
            return count.ToInt32();
        }

        public async Task<List<OrderCollectionMinifyShelf>> GetCollectionMinifyShelfsAsync(string setNo, OrderCollectionSetPaging paging = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            var shelfs = new List<OrderCollectionMinifyShelf>();
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ddu.RAF_ID
                                     , ddu.RAFADI
                                     , ddu.RAFBARKODU
                                     , IFNULL(r.SIRA,99999) AS SIRA
                                    FROM depo_dagitilan_urun AS ddu 
                                    LEFT JOIN raflar AS r ON r.ID = ddu.RAF_ID
                                    WHERE ddu.SET_NO = @SETNO AND (ddu.BULUNAN_ADET + ddu.EKSIK_ADET) < ddu.ADET
                                    GROUP BY ddu.RAF_ID ORDER BY r.SIRA";

            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            PagingExtension.AppendPaging(ref cmd, paging);
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                shelfs.Add(new OrderCollectionMinifyShelf
                {
                    ShelfBarcode = mdr["RAFBARKODU"].ToString(),
                    ShelfId = mdr["RAF_ID"].ToInt32(),
                    ShelfName = mdr["RAFADI"].ToString(),
                    ShelfRank = mdr["SIRA"].ToInt32(),
                });
            }

            //await _cnn.CloseAsync();
            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            return shelfs;
        }

        public async Task<int> GetCollectionMinifyShelfsCountsAsync(string setNo, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(DISTINCT (ddu.RAF_ID))
                                    FROM depo_dagitilan_urun AS ddu 
                                    INNER JOIN siparis AS s ON ddu.SIPARIS_ID = s.ID 
                                    WHERE ddu.SET_NO = @SETNO AND (ddu.BULUNAN_ADET + ddu.EKSIK_ADET) < ddu.ADET";

            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            //await _cnn.CloseAsync();

            await cmd.DisposeAsync();
            return count.ToInt32();
        }

        public async Task<List<OrderCollectionSet>> GetListAsync(OrderCollectionSetFilter filter = null, OrderCollectionSetPaging paging = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT * FROM depo_dagitilan_urun AS su LEFT JOIN raflar AS r ON su.RAF_ID = r.ID WHERE 1 ";
            AppendFilter(ref cmd, filter);
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);

            var list = new List<OrderCollectionSet>();

            while (await mdr.ReadAsync(cancellationToken))
            {
                int status = 0;
                string statusDef = string.Empty;
                if (mdr["BULUNAN_ADET"].ToDouble() == 0.0 && mdr["EKSIK_ADET"].ToDouble() == 0.0)
                {
                    status = (int)SetStatus.Bekliyor;
                    statusDef = SetStatus.Bekliyor.ToString();
                }
                else if ((mdr["BULUNAN_ADET"].ToDouble() > 0.0 && mdr["EKSIK_ADET"].ToDouble() > 0.0) && mdr["BULUNAN_ADET"].ToDouble() != mdr["ADET"].ToDouble())
                {
                    status = (int)SetStatus.Islemde;
                    statusDef = SetStatus.Islemde.ToString();
                }
                else if (mdr["BULUNAN_ADET"].ToDouble() == mdr["ADET"].ToDouble())
                {
                    status = (int)SetStatus.Tamamlandi;
                    statusDef = SetStatus.Tamamlandi.ToString();
                }

                list.Add(new OrderCollectionSet
                {
                    ID = mdr["ID"].ToInt32(),
                    SetNo = mdr["SET_NO"].ToString(),
                    OrderID = mdr["SIPARIS_ID"].ToInt32(),
                    OrderProductID = mdr["SIPARIS_URUN_ID"].ToInt32(),
                    ProductID = mdr["URUN_ID"].ToInt32(),
                    ProductCardID = mdr["URUNKARTI_ID"].ToInt32(),
                    ProductType = (ProductType)mdr["URUN_TIPI"],
                    Piece = mdr["ADET"].ToDouble(),
                    OccurenciesPieces = mdr["BULUNAN_ADET"].ToDouble(),
                    MissingPieces = mdr["EKSIK_ADET"].ToDouble(),
                    ShelfID = mdr["RAF_ID"].ToInt32(),
                    ShelfBarcode = mdr["RAFBARKODU"].ToString(),
                    ShelfName = mdr["RAFADI"].ToString(),
                    WarehouseID = mdr["DEPO_ID"].ToInt32(),
                    Warehouse = mdr["DEPOADI"].ToString(),
                    Status = status,
                    StatusDefinition = statusDef,
                    Date = mdr["TARIH"].ToDateTime(),
                    CarID = mdr["ARABA_ID"] != DBNull.Value ? mdr["ARABA_ID"].ToInt32() : 0,
                    PreparedID = mdr["HAZIRLAYAN_ID"].ToInt32(),
                    WarehouseBoxID = mdr["KUTU_ID"] != DBNull.Value ? mdr["KUTU_ID"].ToInt32() : 0,
                    WarehouseBoxBarcode = mdr["KUTUBARKODU"].ToString(),
                    ShelfType = mdr["RAF_TIPI"].ToInt32()
                });
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task AddAsync(OrderCollectionSet entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_dagitilan_urun
                                (
                                    SET_NO,
                                    SIPARIS_URUN_ID,
                                    SIPARIS_ID,
                                    URUNKARTI_ID,
                                    URUN_ID,
                                    URUN_TIPI,
                                    STOKKODU,
                                    BARKOD,
                                    ADET,
                                    BULUNAN_ADET,
                                    BULUNMA_TARIHI,
                                    EKSIK_ADET,
                                    TARIH,
                                    HAZIRLAYAN_ID,
                                    MASA_ID,
                                    MASAADI,
                                    KOLI_ID,
                                    KOLIADI,
                                    KOLIATAMATARIHI,
                                    HAZIRLAMA_DURUMU,
                                    DEPO_ID,
                                    DEPOADI,
                                    DEPOKODU,
                                    RAF_ID,
                                    RAFADI,
                                    RAFBARKODU,
                                    SIPARISURUNADEDI,
                                    ARABA_ID,
                                    KUTU_ID,
                                    KUTUBARKODU
                                ) VALUES
                                (
                                    @SET_NO,
                                    @SIPARIS_URUN_ID,
                                    @SIPARIS_ID,
                                    @URUNKARTI_ID,
                                    @URUN_ID,
                                    @URUN_TIPI,
                                    @STOKKODU,
                                    @BARKOD,
                                    @ADET,
                                    @BULUNAN_ADET,
                                    @BULUNMA_TARIHI,
                                    @EKSIK_ADET,
                                    @TARIH,
                                    @HAZIRLAYAN_ID,
                                    @MASA_ID,
                                    @MASAADI,
                                    @KOLI_ID,
                                    @KOLIADI,
                                    @KOLIATAMATARIHI,
                                    @HAZIRLAMA_DURUMU,
                                    @DEPO_ID,
                                    @DEPOADI,
                                    @DEPOKODU,
                                    @RAF_ID,
                                    @RAFADI,
                                    @RAFBARKODU,
                                    @SIPARISURUNADEDI,
                                    @ARABA_ID,
                                    @KUTU_ID,
                                    @KUTUBARKODU
                                )";

            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = entity.Products[0].SetNo;
            cmd.Parameters.Add("@SIPARIS_URUN_ID", MySqlDbType.Int32).Value = entity.Products[0].OrderProductID;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.Products[0].OrderID;
            cmd.Parameters.Add("@URUNKARTI_ID", MySqlDbType.Int32).Value = entity.Products[0].ProductCardID;
            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.Products[0].ProductID;
            cmd.Parameters.Add("@URUN_TIPI", MySqlDbType.Int32).Value = entity.Products[0].ProductType.ToInt32();
            cmd.Parameters.Add("@STOKKODU", MySqlDbType.VarChar).Value = entity.Products[0].StockCode;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Products[0].Barcode;
            cmd.Parameters.Add("@ADET", MySqlDbType.Double).Value = entity.Products[0].Piece;
            cmd.Parameters.Add("@BULUNAN_ADET", MySqlDbType.Double).Value = entity.Products[0].OccurrencesPiece;
            cmd.Parameters.Add("@BULUNMA_TARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@EKSIK_ADET", MySqlDbType.Double).Value = entity.Products[0].MissingPiece;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = entity.Products[0].PreparedID;
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = entity.Products[0].TableID;
            cmd.Parameters.Add("@MASAADI", MySqlDbType.VarChar).Value = entity.Products[0].TableName;
            cmd.Parameters.Add("@KOLI_ID", MySqlDbType.Int32).Value = entity.Products[0].BoxID;
            cmd.Parameters.Add("@KOLIADI", MySqlDbType.VarChar).Value = entity.Products[0].BoxName;
            cmd.Parameters.Add("@KOLIATAMATARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@HAZIRLAMA_DURUMU", MySqlDbType.Int32).Value = entity.Products[0].PreparationStatus;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            cmd.Parameters.Add("@DEPOADI", MySqlDbType.VarChar).Value = WebSiteInfo.User.Value.Warehouse;
            cmd.Parameters.Add("@DEPOKODU", MySqlDbType.VarChar).Value = entity.Products[0].WarehouseCode;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.Products[0].ShelfID;
            cmd.Parameters.Add("@RAFADI", MySqlDbType.VarChar).Value = entity.Products[0].ShelfName;
            cmd.Parameters.Add("@RAFBARKODU", MySqlDbType.VarChar).Value = entity.Products[0].ShelfBarcode;
            cmd.Parameters.Add("@SIPARISURUNADEDI", MySqlDbType.Double).Value = entity.Products[0].OrderProductCount;
            cmd.Parameters.Add("@ARABA_ID", MySqlDbType.Int32).Value = entity.Products[0].CarID;
            cmd.Parameters.Add("@KUTU_ID", MySqlDbType.Int32).Value = entity.Products[0].WarehouseBoxID;
            cmd.Parameters.Add("@KUTUBARKODU", MySqlDbType.VarChar).Value = entity.Products[0].WarehouseBoxBarcode;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(OrderCollectionSetFilter filter = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(su.ID)
                                FROM depo_dagitilan_urun AS su
                                LEFT JOIN raflar AS r ON su.RAF_ID = r.ID
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return Convert.ToInt32(count);
        }

        public async Task<List<OrderCollectionSet>> GetPendingMergeSetWithTableIDAsync(int tableID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<OrderCollectionSet> list = new List<OrderCollectionSet>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT SET_NO,TARIH FROM depo_dagitilan_urun WHERE 1 ";
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = tableID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.CommandText += " AND MASA_ID = @MASA_ID AND HAZIRLAMA_DURUMU = 0 AND EKSIK_ADET = 0 GROUP BY SET_NO ORDER BY TARIH;";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderCollectionSet set = new OrderCollectionSet();
                set.SetNo = mdr["SET_NO"].ToString();
                set.Date = mdr["TARIH"].ToDateTime();
                list.Add(set);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<List<OrderCollectionSet>> GetPendingCollectSetWithPreparedIDAsync(int preparedID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<OrderCollectionSet> list = new List<OrderCollectionSet>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT SET_NO,TARIH FROM depo_dagitilan_urun WHERE 1 ";
            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = preparedID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            if (preparedID > 0)
                cmd.CommandText += " AND HAZIRLAYAN_ID = @HAZIRLAYAN_ID ";

            cmd.CommandText += " AND HAZIRLAMA_DURUMU = 0 AND BULUNAN_ADET + EKSIK_ADET < ADET GROUP BY SET_NO ORDER BY TARIH;";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderCollectionSet set = new OrderCollectionSet();
                set.SetNo = mdr["SET_NO"].ToString();
                set.Date = mdr["TARIH"].ToDateTime();
                list.Add(set);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<int> ProductCountInSetAsync(int preparedID, string setNo, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                COUNT(*)
                            FROM
                                depo_dagitilan_urun
                            WHERE
                                HAZIRLAYAN_ID = @HAZIRLAYAN_ID
                                AND SET_NO = @SET_NO";

            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = preparedID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var val = await cmd.ExecuteScalarAsync(cancellationToken);
            //await _cnn.CloseAsync();

            return Convert.ToInt32(val);
        }

        public async Task<int> CollectedProductCountInSetAsync(int preparedID, string setNo, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                COUNT(*)
                            FROM
                                depo_dagitilan_urun
                            WHERE
                                HAZIRLAYAN_ID = @HAZIRLAYAN_ID
                                AND SET_NO = @SET_NO
                                AND BULUNAN_ADET > 0";

            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = preparedID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var val = await cmd.ExecuteScalarAsync(cancellationToken);
            //await _cnn.CloseAsync();

            return Convert.ToInt32(val);
        }

        public async Task<int> MissingProductCountInSetAsync(int preparedID, string setNo, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                COUNT(*)
                            FROM
                                depo_dagitilan_urun
                            WHERE
                                HAZIRLAYAN_ID = @HAZIRLAYAN_ID
                                AND SET_NO = @SET_NO
                                AND EKSIK_ADET > 0 ";

            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = preparedID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var val = await cmd.ExecuteScalarAsync(cancellationToken);
            //await _cnn.CloseAsync();

            return Convert.ToInt32(val);
        }

        public async Task<OrderCollectionSet> GetSetProductsAsync(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            if (filter == null)
            {
                return null;
            }

            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            OrderCollectionSet set = new OrderCollectionSet();
            set.SetNo = filter.SetNo;
            set.Products = new List<OrderProduct>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT
                                    su.ID AS ID
                                    , su.SET_NO
                                    , su.SIPARIS_URUN_ID
                                    , su.SIPARIS_ID
                                    , su.URUN_ID
                                    , su.URUNKARTI_ID
                                    , uk.{filter.ProductNameFields} AS URUNADI
                                    , uk.SATISBIRIMI
                                    , u.BARKOD
                                    , u.STOKKODU
                                    , uk.RESIM1
                                    , (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
                                            FROM urun_eksecenek AS ues
                                                WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA )  AS VARYASYON
                                    , su.RAFADI
                                    , su.RAFBARKODU
                                    , su.RAF_ID
                                    , su.DEPO_ID
                                    , su.DEPOADI
                                    , su.DEPOKODU
                                    , su.MASA_ID
                                    , su.MASAADI
                                    , su.KOLI_ID
                                    , su.KOLIADI
                                    , su.TARIH
                                    , u.TEDARIKCIKODU
                                    , u.TEDARIKCIKODU2
                                    , uk.URUNADEDIONDALIKLI
                                    , su.HAZIRLAMA_DURUMU
                                    , su.HAZIRLAYAN_ID
                                    , su.URUN_TIPI
                                    , {(filter.isGrouping ? "SUM(su.ADET) AS ADET" : "su.ADET")}
                                    , {(filter.isGrouping ? "SUM(su.BULUNAN_ADET) AS BULUNAN_ADET" : "su.BULUNAN_ADET")}
                                    , {(filter.isGrouping ? "SUM(su.EKSIK_ADET) AS EKSIK_ADET" : "su.EKSIK_ADET")}
                                    , su.SIPARISURUNADEDI
                                    , su.ARABA_ID
                                    , su.KUTU_ID
                                    , su.KUTUBARKODU
                                    , su.RAF_TIPI
                                FROM depo_dagitilan_urun AS su
                                LEFT JOIN urun_karti AS uk ON uk.ID = su.URUNKARTI_ID
                                LEFT JOIN urunler AS u ON u.ID = su.URUN_ID
                                LEFT JOIN raflar as r ON r.ID = su.RAF_ID
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            cmd.CommandText += " ORDER BY r.SIRA,su.URUN_ID";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderProduct p = new OrderProduct();
                p.ID = mdr["ID"] != DBNull.Value ? mdr["ID"].ToInt32() : 0;
                p.SetNo = set.SetNo;
                p.OrderID = mdr["SIPARIS_ID"].ToInt32();
                p.OrderProductID = mdr["SIPARIS_URUN_ID"].ToInt32();
                p.ProductCardID = mdr["URUNKARTI_ID"].ToInt32();
                p.ProductID = mdr["URUN_ID"].ToInt32();
                p.ProductType = (ProductType)mdr["URUN_TIPI"].ToInt32();
                p.ProductName = mdr["URUNADI"] + " " + mdr["VARYASYON"];
                p.Barcode = mdr["BARKOD"].ToString();
                p.StockCode = mdr["STOKKODU"].ToString();
                p.AdditionalOptions = mdr["VARYASYON"].ToString();
                p.ShelfID = mdr["RAF_ID"] != DBNull.Value ? mdr["RAF_ID"].ToInt32() : 0;
                p.ShelfName = mdr["RAFADI"].ToString();
                p.ShelfBarcode = mdr["RAFBARKODU"].ToString();
                p.WarehouseID = mdr["DEPO_ID"] != DBNull.Value ? mdr["DEPO_ID"].ToInt32() : 0;
                p.WarehouseName = mdr["DEPOADI"].ToString();
                p.WarehouseCode = mdr["DEPOKODU"].ToString();
                p.Piece = mdr["ADET"].ToDouble();
                p.OccurrencesPiece = mdr["BULUNAN_ADET"].ToDouble();
                p.MissingPiece = mdr["EKSIK_ADET"].ToDouble();
                p.Image = WebSiteInfo.User.Value.ImagePath + mdr["RESIM1"].ToString();
                p.SupplierCode = mdr["TEDARIKCIKODU"].ToString();
                p.SupplierCode2 = mdr["TEDARIKCIKODU2"].ToString();
                p.SalesUnit = mdr["SATISBIRIMI"].ToString();
                p.isProductPieceDecimal = mdr["URUNADEDIONDALIKLI"] != DBNull.Value ? mdr["URUNADEDIONDALIKLI"].ToBoolean() : false;
                p.TableID = mdr["MASA_ID"].ToInt32();
                p.TableName = mdr["MASAADI"].ToString();
                p.BoxID = mdr["KOLI_ID"].ToInt32();
                p.BoxName = mdr["KOLIADI"].ToString();
                p.ShelfType = mdr["RAF_TIPI"].ToInt32();
                p.PreparationStatus = mdr["HAZIRLAMA_DURUMU"].ToInt32();
                var statusDefinition = (SetPreparedStatus)p.PreparationStatus;
                p.PreparationStatusDefinition = statusDefinition.ToString();
                p.OrderProductCount = mdr["SIPARISURUNADEDI"].ToDouble();
                p.CarID = mdr["ARABA_ID"] != DBNull.Value ? mdr["ARABA_ID"].ToInt32() : 0;
                p.PreparedID = mdr["HAZIRLAYAN_ID"].ToInt32();
                p.SetNo = mdr["SET_NO"].ToString();
                p.WarehouseBoxID = mdr["KUTU_ID"].ToInt32();
                p.WarehouseBoxBarcode = mdr["KUTUBARKODU"].ToString();
                set.Products.Add(p);

                set.SetNo = mdr["SET_NO"].ToString();
                set.Warehouse = mdr["DEPOADI"].ToString();
                set.Table = mdr["MASAADI"].ToString();
                set.Date = mdr["TARIH"].ToDateTime();
                if (p.MissingPiece == 0 && p.OccurrencesPiece == 0)
                {
                    set.Status = (int)SetStatus.Bekliyor;
                    set.StatusDefinition = SetStatus.Bekliyor.ToString();
                }
                else if ((p.MissingPiece > 0 || p.OccurrencesPiece > 0) && p.OccurrencesPiece != p.Piece)
                {
                    set.Status = (int)SetStatus.Islemde;
                    set.StatusDefinition = SetStatus.Islemde.ToString();
                }
                else if (p.OccurrencesPiece == p.Piece)
                {
                    set.Status = (int)SetStatus.Tamamlandi;
                    set.StatusDefinition = SetStatus.Tamamlandi.ToString();
                }
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            if (set.Products != null && set.Products.Count > 0)
            {
                foreach (var item in set.Products)
                {
                    item.Barcodes = await GetProductBarcodes(item.ProductID, cancellationToken);
                }
            }

            return set;
        }

        public async Task<List<OrderCollectionOrderTypeDto>> GetMySetOrderAsync(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            if (filter == null)
            {
                return null;
            }

            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            var list = new List<OrderCollectionOrderTypeDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            cmd.CommandText = @"SELECT	
                                      ddu.SIPARIS_ID 	  AS OrderId,
                                      ddu.SIPARIS_URUN_ID AS ProductId,
                                      ddu.Set_No          AS SetNo
                                 FROM depo_dagitilan_urun AS ddu
                                 WHERE 1";

            AppendFilterOrderType(ref cmd, filter);
            cmd.CommandText += @" GROUP BY 
                                   ddu.Siparis_Id,
                                   ddu.Siparis_Urun_Id,
                                   ddu.Set_No";

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);

            while (await mdr.ReadAsync(cancellationToken))
            {
                var orderCollectionDto = new OrderCollectionOrderTypeDto
                {
                    OrderId = mdr["OrderId"].ToInt32(),
                    ProductId = mdr["ProductId"].ToInt32(),
                    SetNo = mdr["SetNo"].ToString()
                };

                list.Add(orderCollectionDto);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<int> ParcelOrderCountAsync(int parcelID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT COUNT(DISTINCT SIPARIS_ID) FROM depo_dagitilan_urun WHERE KOLI_ID = @KOLI_ID ";
            cmd.Parameters.Add("@KOLI_ID", MySqlDbType.Int32).Value = parcelID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return count.ToInt32();
        }

        public async Task OrderProductParcelAssignAsync(int parcelID, int orderID, string parcelName, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET KOLI_ID = @KOLI_ID, KOLIADI = @KOLIADI WHERE SIPARIS_ID = @SIPARIS_ID";
            cmd.Parameters.Add("@KOLI_ID", MySqlDbType.Int32).Value = parcelID;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderID;
            cmd.Parameters.Add("@KOLIADI", MySqlDbType.VarChar).Value = parcelName;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task OrderParcelPreparationAsync(int ID, DateTime? parcelAssignTime, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_dagitilan_urun SET HAZIRLAMA_DURUMU = 1 ";
            if (parcelAssignTime.HasValue)
            {
                cmd.CommandText += ", KOLIATAMATARIHI = @KOLIATAMATARIHI";
                cmd.Parameters.Add("@KOLIATAMATARIHI", MySqlDbType.DateTime).Value = parcelAssignTime.Value;
            }

            cmd.CommandText += @" WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetTableAssignAsync(string setNo, int tableID, string tableName, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET MASA_ID = @MASA_ID, MASAADI = @MASAADI WHERE SET_NO = @SET_NO ";
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = tableID;
            cmd.Parameters.Add("@MASAADI", MySqlDbType.VarChar).Value = tableName;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateMissingProductAsync(int ID, int PreparingStatus, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET EKSIK_ADET = 1, BULUNAN_ADET = 0 ";
            if (PreparingStatus == 5)
            {
                cmd.CommandText += " , HAZIRLAMA_DURUMU = 0 ";
            }

            cmd.CommandText += " WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetQualityControlCompleatedAsync(int orderID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET EKSIK_ADET = 0, BULUNAN_ADET = 1, HAZIRLAMA_DURUMU = 2 WHERE SIPARIS_ID = @SIPARIS_ID";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
            }

            cmd.CommandText += @";INSERT INTO depo_dagitilan_urun_rapor
                                                    (SET_NO
                                                    ,SIPARIS_URUN_ID
                                                    ,SIPARIS_ID
                                                    ,URUNKARTI_ID
                                                    ,URUN_ID
                                                    ,STOKKODU
                                                    ,BARKOD
                                                    ,ADET
                                                    ,BULUNAN_ADET
                                                    ,BULUNMA_TARIHI
                                                    ,EKSIK_ADET
                                                    ,TARIH
                                                    ,HAZIRLAYAN_ID
                                                    ,MASA_ID
                                                    ,MASAADI
                                                    ,KOLI_ID
                                                    ,KOLIADI
                                                    ,KOLIATAMATARIHI
                                                    ,HAZIRLAMA_DURUMU
                                                    ,DEPO_ID
                                                    ,DEPOADI
                                                    ,DEPOKODU
                                                    ,RAF_ID
                                                    ,RAFADI
                                                    ,RAFBARKODU
                                                    ,SIPARISURUNADEDI
                                                    ,ARABA_ID)
                                              SELECT SET_NO
                                                    ,SIPARIS_URUN_ID
                                                    ,SIPARIS_ID
                                                    ,URUNKARTI_ID
                                                    ,URUN_ID
                                                    ,STOKKODU
                                                    ,BARKOD
                                                    ,ADET
                                                    ,BULUNAN_ADET
                                                    ,BULUNMA_TARIHI
                                                    ,EKSIK_ADET
                                                    ,TARIH
                                                    ,HAZIRLAYAN_ID
                                                    ,MASA_ID
                                                    ,MASAADI
                                                    ,KOLI_ID
                                                    ,KOLIADI
                                                    ,KOLIATAMATARIHI
                                                    ,HAZIRLAMA_DURUMU
                                                    ,DEPO_ID
                                                    ,DEPOADI
                                                    ,DEPOKODU
                                                    ,RAF_ID
                                                    ,RAFADI
                                                    ,RAFBARKODU
                                                    ,SIPARISURUNADEDI
                                                    ,ARABA_ID
                                                    FROM depo_dagitilan_urun WHERE SIPARIS_ID = @SIPARIS_ID;";

            cmd.CommandText += @"DELETE FROM depo_dagitilan_urun WHERE SIPARIS_ID = @SIPARIS_ID";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
            }

            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderID;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetQualityControlCompletedByIDAsync(OrderCollectionSetQualityControlCompleatedByIDDto request, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            var IDParameter = string.Join(",", request.ID);
            if (!request.PreparationCondition.HasValue || request.PreparationCondition.Value)
            {
                cmd.CommandText = $"UPDATE depo_dagitilan_urun SET EKSIK_ADET = 0, BULUNAN_ADET = 1, HAZIRLAMA_DURUMU = 2, KOLI_ID = 0 WHERE ID IN ({IDParameter})";
                if (!WebSiteInfo.User.Value.IsOneStore)
                {
                    cmd.CommandText += " AND DEPO_ID = @DEPO_ID; ";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                }
                else
                    cmd.CommandText += ";";

                await cmd.ExecuteTransactionCommandAsync();
                cmd = new MySqlCommand(string.Empty, _cnn);
            }

            cmd.CommandText = $@"INSERT INTO depo_dagitilan_urun_rapor
                                                    (SET_NO
                                                    ,SIPARIS_URUN_ID
                                                    ,SIPARIS_ID
                                                    ,URUNKARTI_ID
                                                    ,URUN_ID
                                                    ,URUN_TIPI
                                                    ,STOKKODU
                                                    ,BARKOD
                                                    ,ADET
                                                    ,BULUNAN_ADET
                                                    ,BULUNMA_TARIHI
                                                    ,EKSIK_ADET
                                                    ,TARIH
                                                    ,HAZIRLAYAN_ID
                                                    ,MASA_ID
                                                    ,MASAADI
                                                    ,KOLI_ID
                                                    ,KOLIADI
                                                    ,KOLIATAMATARIHI
                                                    ,HAZIRLAMA_DURUMU
                                                    ,DEPO_ID
                                                    ,DEPOADI
                                                    ,DEPOKODU
                                                    ,RAF_ID
                                                    ,RAFADI
                                                    ,RAFBARKODU
                                                    ,SIPARISURUNADEDI
                                                    ,ARABA_ID)
                                              SELECT SET_NO
                                                    ,SIPARIS_URUN_ID
                                                    ,SIPARIS_ID
                                                    ,URUNKARTI_ID
                                                    ,URUN_ID
                                                    ,URUN_TIPI
                                                    ,STOKKODU
                                                    ,BARKOD
                                                    ,ADET
                                                    ,BULUNAN_ADET
                                                    ,BULUNMA_TARIHI
                                                    ,EKSIK_ADET
                                                    ,TARIH
                                                    ,HAZIRLAYAN_ID
                                                    ,MASA_ID
                                                    ,MASAADI
                                                    ,KOLI_ID
                                                    ,KOLIADI
                                                    ,KOLIATAMATARIHI
                                                    ,HAZIRLAMA_DURUMU
                                                    ,DEPO_ID
                                                    ,DEPOADI
                                                    ,DEPOKODU
                                                    ,RAF_ID
                                                    ,RAFADI
                                                    ,RAFBARKODU
                                                    ,SIPARISURUNADEDI
                                                    ,ARABA_ID
                                                    FROM depo_dagitilan_urun WHERE ID IN({IDParameter});";

            await cmd.ExecuteTransactionCommandAsync();
            cmd = new MySqlCommand(string.Empty, _cnn);

            cmd.CommandText = $@"DELETE FROM depo_dagitilan_urun WHERE ID IN({IDParameter}) ";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> DeleteByIDAsync(int ID, CancellationToken cancellationToken)
        {
            int result = 0;
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE ID = @ID ";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.Parameters.Add("@ID", MySqlDbType.VarChar).Value = ID;
            await cmd.ExecuteTransactionCommandAsync();
            return result;
        }

        public async Task UpdatePreparingStatusAsync(SetPreparingStatusCollectionProductDto request, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET HAZIRLAMA_DURUMU = @HAZIRLAMA_DURUMU WHERE 1 ";
            if (request.ID > 0)
            {
                cmd.CommandText += " AND ID = @ID";
                cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = request.ID;
            }

            if (request.IDs != null && request.IDs.Count > 0)
                cmd.CommandText += $" AND ID IN ({string.Join(",", request.IDs)})";

            cmd.Parameters.Add("@HAZIRLAMA_DURUMU", MySqlDbType.Int32).Value = request.PreparingStatus;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<OrderCollectionPageDto>> OrderCollectionPageAsync(OrderCollectionSetFilter filter = null, OrderCollectionSetPaging paging = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<OrderCollectionPageDto> list = new List<OrderCollectionPageDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.SET_NO
                                     , COUNT(DISTINCT su.SIPARIS_ID) AS SIPARISADEDI
                                     , SUM(su.ADET) AS URUNADEDI
                                     , COUNT(DISTINCT su.HAZIRLAYAN_ID) AS HAZIRLAYANSAYISI
                                     , SUM(su.BULUNAN_ADET) AS TOPLAMBULUNANADET
                                     , SUM(su.EKSIK_ADET) AS TOPLAMEKSIKADET
                                     , su.TARIH
                                     FROM depo_dagitilan_urun AS su
                                     LEFT JOIN raflar AS r ON su.RAF_ID = r.ID
                                     WHERE 1";

            AppendFilter(ref cmd, filter);
            cmd.CommandText += " GROUP BY SET_NO ORDER BY su.TARIH DESC";
            PagingExtension.AppendPaging(ref cmd, paging);
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderCollectionPageDto set = new OrderCollectionPageDto();
                set.SetNo = mdr["SET_NO"].ToString();
                set.OrderPiece = mdr["SIPARISADEDI"].ToInt32();
                set.ProductPiece = mdr["URUNADEDI"].ToDouble();
                set.PickerPiece = mdr["HAZIRLAYANSAYISI"].ToInt32();
                set.OccurrencesPiece = mdr["TOPLAMBULUNANADET"].ToDouble();
                set.MissingPiece = mdr["TOPLAMEKSIKADET"].ToDouble();
                set.CreateDate = mdr["TARIH"].ToDateTime();
                if (set.MissingPiece == 0 && set.OccurrencesPiece == 0)
                {
                    set.Status = (int)SetStatus.Bekliyor;
                    set.StatusDefinition = SetStatus.Bekliyor.ToString();
                }
                else if ((set.MissingPiece > 0 || set.OccurrencesPiece > 0) && set.OccurrencesPiece != set.ProductPiece)
                {
                    set.Status = (int)SetStatus.Islemde;
                    set.StatusDefinition = SetStatus.Islemde.ToString();
                }
                else if (set.OccurrencesPiece == set.ProductPiece)
                {
                    set.Status = (int)SetStatus.Tamamlandi;
                    set.StatusDefinition = SetStatus.Tamamlandi.ToString();
                }

                list.Add(set);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<List<SetDetailDto>> SetDetailAsync(OrderCollectionSetFilter filter = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<SetDetailDto> list = new List<SetDetailDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.ID AS ID
                                     , su.URUN_ID AS URUNID
                                     , uk.URUNADI AS URUNADI
                                     , su.RAF_ID AS RAFID
                                     , su.KUTU_ID AS KUTU_ID
                                     , su.KUTUBARKODU AS KUTUBARKODU
                                     , GROUP_CONCAT(DISTINCT su.SIPARIS_ID) AS SIPARISLER
                                     , GROUP_CONCAT(DISTINCT mt.ISIM ,' ',mt.SOYISIM) AS TOPLAYICILAR
                                     , su.BULUNAN_ADET AS BULUNANADET
                                     , su.EKSIK_ADET AS EKSIKADET
                                     , SUM(su.ADET) AS ADET
                                     FROM depo_dagitilan_urun su
                                     LEFT JOIN raflar AS r ON su.RAF_ID = r.ID
                                     LEFT JOIN magaza_temsilci mt ON su.HAZIRLAYAN_ID = mt.ID
                                     LEFT JOIN urunler u ON su.URUN_ID = u.ID
                                     LEFT JOIN urun_karti uk ON u.URUNKARTI_ID = uk.ID WHERE 1 ";

            AppendFilter(ref cmd, filter);
            cmd.CommandText += " GROUP BY su.URUN_ID, su.RAF_ID ";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                SetDetailDto set = new SetDetailDto();
                set.ID = mdr["ID"].ToInt32();
                set.ProductID = mdr["URUNID"].ToInt32();
                set.ProductName = mdr["URUNADI"].ToString();
                set.Piece = mdr["ADET"].ToDouble();
                set.ShelfID = mdr["RAFID"].ToInt32();
                set.OrderIDs = mdr["SIPARISLER"].ToString();
                set.Pickers = mdr["TOPLAYICILAR"].ToString();
                set.MissingPiece = mdr["EKSIKADET"].ToDouble();
                set.OccurrencesPiece = mdr["BULUNANADET"].ToDouble();
                set.WarehouseBoxID = mdr["KUTU_ID"] != DBNull.Value ? mdr["KUTU_ID"].ToInt32() : 0;
                set.WarehouseBoxBarcode = mdr["KUTUBARKODU"].ToString();
                if (set.MissingPiece == 0 && set.OccurrencesPiece == 0)
                {
                    set.Status = (int)SetStatus.Bekliyor;
                    set.StatusDefinition = SetStatus.Bekliyor.ToString();
                }
                else if ((set.MissingPiece > 0 || set.OccurrencesPiece > 0) && set.OccurrencesPiece != set.Piece)
                {
                    set.Status = (int)SetStatus.Islemde;
                    set.StatusDefinition = SetStatus.Islemde.ToString();
                }
                else if (set.OccurrencesPiece == set.Piece)
                {
                    set.Status = (int)SetStatus.Tamamlandi;
                    set.StatusDefinition = SetStatus.Tamamlandi.ToString();
                }

                list.Add(set);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<List<SetDetailEditDto>> SetDetailEditAsync(OrderCollectionSetFilter filter = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<SetDetailEditDto> list = new List<SetDetailEditDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.ID AS ID
                                     , su.URUN_ID AS URUNID
                                     , uk.URUNADI AS URUNADI
                                     , su.URUN_TIPI AS URUNTIPI
                                     , su.ADET AS ADET
                                     , su.RAF_ID AS RAFID
                                     , su.BULUNAN_ADET AS BULUNANADET
                                     , su.EKSIK_ADET AS EKSIKADET
                                     , su.SIPARIS_ID AS SIPARISID
                                     , CONCAT(mt.ISIM ,' ',mt.SOYISIM) AS TOPLAYICI 
                                     , su.KUTU_ID AS KUTU_ID
                                     , su.KUTUBARKODU AS KUTUBARKODU
                                     , su.HAZIRLAYAN_ID AS TOPLAYICIID FROM depo_dagitilan_urun su
                                     LEFT JOIN magaza_temsilci mt ON su.HAZIRLAYAN_ID = mt.ID 
                                     LEFT JOIN urunler u ON su.URUN_ID = u.ID 
                                     LEFT JOIN urun_karti uk ON u.URUNKARTI_ID = uk.ID WHERE 1 ";

            AppendFilter(ref cmd, filter);
            cmd.CommandText += " GROUP BY ID ";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                SetDetailEditDto set = new SetDetailEditDto();
                set.ID = mdr["ID"].ToInt32();
                set.ProductID = mdr["URUNID"].ToInt32();
                set.ProductName = mdr["URUNADI"].ToString();
                set.ProductType = mdr["URUNTIPI"].ToInt32();
                set.Piece = mdr["ADET"].ToDouble();
                set.ShelfID = mdr["RAFID"].ToInt32();
                set.Picker = mdr["TOPLAYICI"].ToString();
                set.PickerID = mdr["TOPLAYICIID"].ToInt32();
                set.OccurrencesPiece = mdr["BULUNANADET"].ToDouble();
                set.MissingPiece = mdr["EKSIKADET"].ToDouble();
                set.OrderID = mdr["SIPARISID"].ToInt32();
                set.WarehouseBoxID = mdr["KUTU_ID"] != DBNull.Value ? mdr["KUTU_ID"].ToInt32() : 0;
                set.WarehouseBoxBarcode = mdr["KUTUBARKODU"].ToString();
                list.Add(set);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task SetPreparedAndShelfUpdateAsync(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET RAFADI = @RAFADI, HAZIRLAYAN_ID = @HAZIRLAYAN_ID, RAF_ID = @RAF_ID WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = filter.PreparedID;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = filter.ShelfID;
            cmd.Parameters.Add("@RAFADI", MySqlDbType.VarChar).Value = filter.ShelfName;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteSetAsync(string SetNo, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE SET_NO = @SET_NO ";
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetAssignAsync(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET HAZIRLAYAN_ID = @HAZIRLAYAN_ID WHERE SET_NO = @SET_NO ";
            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = filter.PreparedID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<OrderInTheSetDto>> OrdersInTheSetAsync(OrderCollectionSetFilter filter = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<OrderInTheSetDto> list = new List<OrderInTheSetDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.SET_NO
                                     , su.SIPARIS_ID
                                     , su.ADET
                                     , su.URUN_TIPI
                                     , s.TARIH           
                                     , SUM(ADET) AS TOTAL_PRODUCT_PIECE
                                     FROM depo_dagitilan_urun AS su 
                                     LEFT JOIN raflar AS r ON su.RAF_ID = r.ID
                                     INNER JOIN siparis AS s ON su.SIPARIS_ID = s.ID
                                     WHERE 1 ";

            AppendFilter(ref cmd, filter);
            if (!filter.isGrouping)
                cmd.CommandText += " GROUP BY su.SIPARIS_ID ";
            else
                cmd.CommandText += " ,su.SIPARIS_ID";

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderInTheSetDto set = new OrderInTheSetDto();
                set.SetNo = mdr["SET_NO"].ToString();
                set.OrderID = mdr["SIPARIS_ID"].ToInt32();
                set.TotalProductPiece = mdr["TOTAL_PRODUCT_PIECE"].ToInt32();
                set.OrderDate = mdr["TARIH"].ToDateTime().ToTimestamp();
                set.Piece = mdr["ADET"].ToInt32();
                set.ProductType = mdr["URUN_TIPI"].ToInt32();
                list.Add(set);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task SetAssignByShelfIDAsync(List<int> IDs, List<int> ShelfIDs, int preparedID, CancellationToken cancellationToken)
        {
            if (ShelfIDs == null && ShelfIDs.Count == 0)
            {
                return;
            }

            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = $@"UPDATE 
                                    depo_dagitilan_urun 
                                 SET HAZIRLAYAN_ID = @HAZIRLAYAN_ID
                                 WHERE 1 AND 
                                 {(
                                     IDs.Count > 0 ?
                                         $"ID IN ({string.Join(",", IDs)}) AND " :
                                         ""
                                 )}
                                 RAF_ID IN ({string.Join(",", ShelfIDs)})";

            cmd.Parameters.Add("@HAZIRLAYAN_ID", MySqlDbType.Int32).Value = preparedID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetTableIDUpdateAsync(OrderCollectionSetFilter filter, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET MASA_ID = @MASA_ID WHERE SET_NO = @SET_NO ";
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = filter.TableID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<DistributionProductCheckerDto>> CheckDistributionProductAnyProblemAsync(string setNo, CancellationToken cancellationToken)
        {
            List<DistributionProductCheckerDto> list = new List<DistributionProductCheckerDto>();
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                      ddu.SIPARIS_ID,
                                      SUM(ddu.ADET) AS TOPLAM_DAGITILAN_MIKTARI,
                                      (SELECT SUM(su.ADET) FROM siparis_urun AS su WHERE su.SIPARIS_ID = ddu.SIPARIS_ID GROUP BY su.SIPARIS_ID AND su.DURUM <> 2) AS TOPLAM_SIPARIS_URUN_MIKTARI
                                    FROM
                                      depo_dagitilan_urun AS ddu
                                    WHERE SET_NO = @SET_NO
                                    ";

            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND ddu.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.CommandText += "GROUP BY ddu.SIPARIS_ID;";
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = setNo;
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                list.Add(new DistributionProductCheckerDto
                {
                    OrderID = reader["SIPARIS_ID"].ToInt32(),
                    SumDistributionAmount = reader["TOPLAM_DAGITILAN_MIKTARI"].ToDouble(),
                    SumOrderProductsAmount = reader["TOPLAM_SIPARIS_URUN_MIKTARI"].ToDouble()
                });
            }

            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return list;
        }

        public async Task DeleteByOrderIDAsync(int orderID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE SIPARIS_ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task AddWithSetNoAsync(AddWithSetNoDto request, CancellationToken cancellationToken)
        {
            List<string> valueList = new List<string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = request.SetNo;

            for (int i = 0; i < request.Products.Count; i++)
            {
                valueList.Add($@"(  @SET_NO
                                  , @SIPARIS_URUN_ID{i}
                                  , @SIPARIS_ID{i}
                                  , @URUNKARTI_ID{i}
                                  , @URUN_ID{i}
                                  , @URUN_TIPI{i}
                                  , @STOKKODU{i}
                                  , @BARKOD{i}
                                  , 1
                                  , 0
                                  , @EKSIK_ADET{i}
                                  , @TARIH{i}
                                  , @HAZIRLAYAN_ID{i}
                                  , @MASA_ID{i}
                                  , @MASAADI{i}
                                  , @KOLI_ID{i}
                                  , @KOLIADI{i}
                                  , 0
                                  , @DEPO_ID{i}
                                  , @DEPOADI{i}
                                  , @DEPOKODU{i}
                                  , @RAF_ID{i}
                                  , @RAFADI{i}
                                  , @RAFBARKODU{i}
                                  , @KUTU_ID{i}
                                  , @KUTUBARKODU{i}
                                  , @SIPARISURUNADEDI{i})");

                cmd.Parameters.Add($"@SIPARIS_URUN_ID{i}", MySqlDbType.Int32).Value = request.Products[i].OrderProductID;
                cmd.Parameters.Add($"@URUNKARTI_ID{i}", MySqlDbType.Int32).Value = request.Products[i].ProductCardID;
                cmd.Parameters.Add($"@URUN_ID{i}", MySqlDbType.Int32).Value = request.Products[i].ProductID;
                cmd.Parameters.Add($"@URUN_TIPI{i}", MySqlDbType.Int32).Value = request.Products[i].ProductType;
                cmd.Parameters.Add($"@SIPARIS_ID{i}", MySqlDbType.Int32).Value = request.Products[i].OrderID;
                cmd.Parameters.Add($"@STOKKODU{i}", MySqlDbType.VarChar).Value = request.Products[i].StockCode;
                cmd.Parameters.Add($"@BARKOD{i}", MySqlDbType.VarChar).Value = request.Products[i].Barcode;
                cmd.Parameters.Add($"@EKSIK_ADET{i}", MySqlDbType.Double).Value = request.Products[i].MissingPiece;
                cmd.Parameters.Add($"@TARIH{i}", MySqlDbType.DateTime).Value = DateTime.Now;
                cmd.Parameters.Add($"@HAZIRLAYAN_ID{i}", MySqlDbType.Int32).Value = request.Products[i].PickerID;
                cmd.Parameters.Add($"@MASA_ID{i}", MySqlDbType.Int32).Value = request.Products[i].TableID;
                cmd.Parameters.Add($"@MASAADI{i}", MySqlDbType.VarChar).Value = request.Products[i].TableName;
                cmd.Parameters.Add($"@KOLI_ID{i}", MySqlDbType.Int32).Value = request.Products[i].BoxID;
                cmd.Parameters.Add($"@KOLIADI{i}", MySqlDbType.VarChar).Value = request.Products[i].BoxName;
                cmd.Parameters.Add($"@DEPO_ID{i}", MySqlDbType.Int32).Value = request.Products[i].WarehouseID;
                cmd.Parameters.Add($"@DEPOADI{i}", MySqlDbType.VarChar).Value = request.Products[i].WarehouseName;
                cmd.Parameters.Add($"@DEPOKODU{i}", MySqlDbType.VarChar).Value = request.Products[i].WarehouseCode;
                cmd.Parameters.Add($"@RAF_ID{i}", MySqlDbType.Int32).Value = request.Products[i].ShelfID;
                cmd.Parameters.Add($"@RAFADI{i}", MySqlDbType.VarChar).Value = request.Products[i].ShelfName;
                cmd.Parameters.Add($"@RAFBARKODU{i}", MySqlDbType.VarChar).Value = request.Products[i].ShelfBarcode;
                cmd.Parameters.Add($"@KUTU_ID{i}", MySqlDbType.Int32).Value = request.Products[i].WarehouseBoxID;
                cmd.Parameters.Add($"@KUTUBARKODU{i}", MySqlDbType.VarChar).Value = request.Products[i].WarehouseBoxBarcode;
                cmd.Parameters.Add($"@SIPARISURUNADEDI{i}", MySqlDbType.Double).Value = request.Products[i].OrderProductPiece;

                cmd.CommandText = $@"INSERT INTO `depo_dagitilan_urun` (          `SET_NO`
                                                                                , `SIPARIS_URUN_ID`
                                                                                , `SIPARIS_ID`
                                                                                , `URUNKARTI_ID`
                                                                                , `URUN_ID`
                                                                                , `URUN_TIPI`
                                                                                , `STOKKODU`
                                                                                , `BARKOD`
                                                                                , `ADET`
                                                                                , `BULUNAN_ADET`
                                                                                , `EKSIK_ADET`
                                                                                , `TARIH`
                                                                                , `HAZIRLAYAN_ID`
                                                                                , `MASA_ID`
                                                                                , `MASAADI`
                                                                                , `KOLI_ID`
                                                                                , `KOLIADI`
                                                                                , `HAZIRLAMA_DURUMU`
                                                                                , `DEPO_ID`
                                                                                , `DEPOADI`
                                                                                , `DEPOKODU`
                                                                                , `RAF_ID`
                                                                                , `RAFADI`
                                                                                , `RAFBARKODU`
                                                                                , `KUTU_ID`
                                                                                , `KUTUBARKODU`
                                                                                , `SIPARISURUNADEDI`)
                                                                        VALUES {string.Join(",", valueList)};";
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> DeleteByPreparedIDAsync(int userID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "DELETE FROM depo_dagitilan_urun WHERE HAZIRLAYAN_ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = userID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            int affectedRows = await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return affectedRows;
        }

        public async Task SetUpdateCarIDAsync(string setNo, int carID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET ARABA_ID = @ARABAID WHERE SET_NO = @SETNO";
            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            cmd.Parameters.Add("@ARABAID", MySqlDbType.Int32).Value = carID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetUndoInTheCarAsync(int carID, string setNo, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_dagitilan_urun SET BULUNAN_ADET = 0, EKSIK_ADET = 0, HAZIRLAMA_DURUMU = 0, KOLI_ID = 0, KOLIADI = '', MASA_ID = 0, MASAADI = '', ARABA_ID = 0 WHERE ARABA_ID = @ARABAID AND SET_NO = @SETNO";
            cmd.Parameters.Add("@ARABAID", MySqlDbType.Int32).Value = carID;
            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = setNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task MissingPieceToOccunciesPieceAsync(MissingPieceToOccunciesPieceDto request, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand();
            cmd.CommandText += @"UPDATE depo_dagitilan_urun SET 
                                    EKSIK_ADET = 0,
                                    BULUNAN_ADET = 1
                                 WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.VarChar).Value = request.OrderCollectionID;
            cmd.Parameters.Add("@ADET", MySqlDbType.Double).Value = request.Piece;
            await cmd.ExecuteTransactionCommandAsync();
        }


        private void AppendFilter(ref MySqlCommand cmd, OrderCollectionSetFilter filter)
        {
            if (!WebSiteInfo.User.Value.IsOneStore && !filter.WarehouseID.HasValue)
            {
                cmd.CommandText += " AND su.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            if (filter.OrderProductID.HasValue)
            {
                cmd.CommandText += " AND su.SIPARIS_URUN_ID = @SIPARIS_URUN_ID";
                cmd.Parameters.Add("@SIPARIS_URUN_ID", MySqlDbType.Int32).Value = filter.OrderProductID.Value;
            }

            if (filter.IsEmptyShelf.HasValue)
            {
                cmd.CommandText += " AND r.BOSRAF = 0";
            }

            if (!string.IsNullOrEmpty(filter.SetNo))
            {
                cmd.CommandText += " AND su.SET_NO = @SET_NO";
                cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
            }

            if (!string.IsNullOrEmpty(filter.ProductBarcode))
            {
                cmd.CommandText += " AND su.BARKOD = @BARKOD";
                cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.ProductBarcode;
            }

            if (filter.ID.HasValue && filter.ID.Value > 0)
            {
                cmd.CommandText += $" AND su.ID = {filter.ID.Value}";
            }

            if (!filter.OrderCombinesProductFound)
            {
                if (filter.FindStatus.HasValue)
                {
                    if (filter.FindStatus.Value)
                    {
                        cmd.CommandText += " AND su.BULUNAN_ADET = su.ADET";
                    }
                    else
                    {
                        cmd.CommandText += " AND (su.BULUNAN_ADET + su.EKSIK_ADET) < su.ADET";
                    }
                }
            }

            else if (filter.MissingProduct.HasValue)
            {
                if (filter.MissingProduct.Value)
                {
                    cmd.CommandText += " AND su.EKSIK_ADET > 0";
                }
                else
                {
                    cmd.CommandText += " AND su.EKSIK_ADET < su.ADET";
                }
            }

            if (filter.PreparedID.HasValue)
            {
                cmd.CommandText += $" AND su.HAZIRLAYAN_ID = {filter.PreparedID.Value}";
            }

            if (filter.PreparationStatus.HasValue)
            {
                cmd.CommandText += $" AND su.HAZIRLAMA_DURUMU = {filter.PreparationStatus.Value}";
            }
            else if (filter.PreparedStatusList != null)
            {
                cmd.CommandText += $" AND su.HAZIRLAMA_DURUMU IN ({string.Join(",", filter.PreparedStatusList)})";
            }

            if (filter.OrderID.HasValue && filter.OrderID.Value > 0)
            {
                cmd.CommandText += $" AND su.SIPARIS_ID = {filter.OrderID.Value}";
            }

            if (filter.OrderIDList.Count > 0)
            {
                cmd.CommandText += $" AND su.SIPARIS_ID IN ({string.Join(",", filter.OrderIDList)})";
            }

            if (filter.BoxIDs.Count > 0)
            {
                cmd.CommandText += $" AND su.KOLI_ID IN ({string.Join(",", filter.BoxIDs)})";
            }

            if (filter.TableID.HasValue && filter.TableID.Value > 0)
            {
                cmd.CommandText += $" AND su.MASA_ID = {filter.TableID.Value}";
            }

            if (filter.WarehouseID.HasValue && filter.WarehouseID.Value > 0)
            {
                cmd.CommandText += $" AND su.DEPO_ID = {filter.WarehouseID.Value}";
            }

            if (filter.BoxID.HasValue && filter.BoxID.Value > 0)
            {
                cmd.CommandText += $" AND su.KOLI_ID = {filter.BoxID.Value}";
            }

            if (filter.CarID.HasValue && filter.CarID.Value > 0)
            {
                cmd.CommandText += $" AND su.ARABA_ID = {filter.CarID.Value}";
            }

            if (filter.CarIDList.Count > 0)
            {
                cmd.CommandText += $" AND su.ARABA_ID IN ({string.Join(",", filter.CarIDList)})";
            }

            if (filter.OrderProductPiece.HasValue && filter.OrderProductPiece.Value > 0)
            {
                cmd.CommandText += $" AND su.SIPARISURUNADEDI = {filter.OrderProductPiece.Value}";
            }

            if (filter.BoxAssign.HasValue)
            {
                if (filter.BoxAssign.Value)
                {
                    cmd.CommandText += " AND su.KOLI_ID > 0";
                }
                else
                {
                    cmd.CommandText += " AND su.KOLI_ID = 0";
                }
            }

            if (filter.DateStart.HasValue)
            {
                cmd.CommandText += " AND su.TARIH >= @sipbastarih ";
                cmd.Parameters.Add("@sipbastarih", MySqlDbType.DateTime).Value = filter.DateStart.Value;
            }

            if (filter.DateFinish.HasValue)
            {
                cmd.CommandText += " AND su.TARIH <= @sipsontarih ";
                cmd.Parameters.Add("@sipsontarih", MySqlDbType.DateTime).Value = filter.DateFinish.Value.AddDays(1);
            }

            if (filter.ProductID > 0)
            {
                cmd.CommandText += " AND su.URUN_ID = @URUN_ID";
                cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = filter.ProductID;
            }

            if (filter.ProductIds != null && filter.ProductIds.Any())
            {
                cmd.CommandText += $" AND su.URUN_ID IN({string.Join(',', filter.ProductIds)})";
            }

            if (filter.ShelfID.HasValue)
            {
                cmd.CommandText += " AND su.RAF_ID = @RAF_ID";
                cmd.Parameters.Add("@RAF_ID", MySqlDbType.VarChar).Value = filter.ShelfID.Value;
            }

            if (filter.Status.HasValue && filter.Status >= 0)
            {
                if (filter.Status == (int)SetStatus.Bekliyor)
                {
                    cmd.CommandText += " AND su.BULUNAN_ADET = 0 AND su.EKSIK_ADET = 0 ";
                }
                else if (filter.Status == (int)SetStatus.Islemde)
                {
                    cmd.CommandText += " AND (su.BULUNAN_ADET > 0 OR su.EKSIK_ADET > 0) AND su.BULUNAN_ADET != su.ADET ";
                }
                else if (filter.Status == (int)SetStatus.Tamamlandi)
                {
                    cmd.CommandText += " AND su.BULUNAN_ADET = su.ADET ";
                }
            }

            if (filter.WarehouseBoxID.HasValue)
            {
                cmd.CommandText += " AND su.KUTU_ID = @KUTU_ID";
                cmd.Parameters.Add("@KUTU_ID", MySqlDbType.Int32).Value = filter.WarehouseBoxID.Value;
            }

            if (!string.IsNullOrEmpty(filter.WarehouseBoxBarcode))
            {
                cmd.CommandText += " AND su.KUTUBARKODU = @KUTUBARKODU";
                cmd.Parameters.Add("@KUTUBARKODU", MySqlDbType.VarChar).Value = filter.WarehouseBoxBarcode;
            }

            if (filter.IsAvailableQualityControl && filter.BoxID.HasValue)
            {
                cmd.CommandText += " AND su.SIPARIS_ID IN (SELECT SIPARIS_ID FROM depo_dagitilan_urun WHERE KOLI_ID = @KOLI_ID GROUP BY SIPARIS_ID HAVING SUM(BULUNAN_ADET) >= SUM(ADET) AND SUM(EKSIK_ADET) = 0)";
                cmd.Parameters.Add("@KOLI_ID", MySqlDbType.Int32).Value = filter.BoxID.Value;
            }

            if (filter.isGrouping)
            {
                cmd.CommandText += " GROUP BY su.SIPARIS_ID, su.URUN_ID, su.RAF_ID";
            }
        }

        private void AppendFilterOrderType(ref MySqlCommand cmd, OrderCollectionSetFilter filter)
        {
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND ddu.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            if (!string.IsNullOrEmpty(filter.SetNo))
            {
                cmd.CommandText += " AND ddu.SET_NO = @SET_NO";
                cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
            }
        }

    }
}