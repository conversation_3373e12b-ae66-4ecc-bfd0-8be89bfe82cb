using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class CancelReturnReasonDal : ICancelReturnReasonDal
    {
        private MySqlConnection _cnn;

        public CancelReturnReasonDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }
        public async Task<List<CancelReturnReason>> GetListAsync(CancelReturnReasonFilter filter = null, CancelReturnReasonPaging paging = null, CancellationToken cancellationToken = default)
        {
            List<CancelReturnReason> cancelReturnReasons = new List<CancelReturnReason>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ID
                                    , Tanim
                            FROM iptaliadeneden WHERE 1";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                CancelReturnReason cancelReturnReason = new CancelReturnReason();
                cancelReturnReason.ID = Convert.ToInt32(reader["ID"]);
                cancelReturnReason.Definition = reader["Tanim"].ToString();
                cancelReturnReasons.Add(cancelReturnReason);
            }
            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            return cancelReturnReasons;
        }
        private void AppendFilter(ref MySqlCommand cmd, CancelReturnReasonFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID.HasValue)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID.Value;
                }

                if (filter.Active.HasValue)
                {
                    if(filter.Active.Value)
                        cmd.CommandText += " AND AKTIF = 1";
                    else
                        cmd.CommandText += " AND AKTIF = 0";
                }
                    
            }
        }
    }
}