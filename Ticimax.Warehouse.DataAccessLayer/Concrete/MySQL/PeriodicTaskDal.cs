using MySqlConnector;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class PeriodicTaskDal : IPeriodicTaskDal
    {
        private MySqlConnection _cnn;

        public PeriodicTaskDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task ClearEmptyStocks(CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"DELETE FROM urun_raf WHERE STOK <= 0";
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }
    }
}
