using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class CustomerServiceDal : ICustomerServiceDal
    {
        private readonly MySqlConnection _cnn;

        public CustomerServiceDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task DeleteByOrderId(int orderId, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM musteri_hizmetleri_aramalar
                                        WHERE SIPARIS_ID = @SIPARIS_ID";

            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderId;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task UpdateStatus(CustomerService entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE musteri_hizmetleri_aramalar
                                        SET
                                            DURUM_ID = @DURUM_ID
                                        WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@DURUM_ID", MySqlDbType.Int32).Value = entity.StatusID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();
            //_cnn.Close();
        }

        public async Task UpdateNumberOfCall(CustomerService entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE musteri_hizmetleri_aramalar
                                        SET
                                            ARAMASAYISI = (ARAMASAYISI + 1)
                                        WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();
            //_cnn.Close();
        }

        public async Task UpdateCallingMember(CustomerService entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE musteri_hizmetleri_aramalar
                                        SET
                                            CAGRIYAPAN_UYE_ID = @CAGRIYAPAN_UYE_ID
                                            ,CAGRIYAPAN_UYEADI = @CAGRIYAPAN_UYEADI";

            if (entity.FirstCallDate.HasValue)
            {
                cmd.CommandText += ",ILKARAMATARIHI = @ILKARAMATARIHI";
            }

            if (entity.LastCallDate.HasValue)
            {
                cmd.CommandText += ",SONARAMATARIHI = @SONARAMATARIHI";
            }

            cmd.CommandText += @" WHERE ID = @ID";
            cmd.Parameters.Add("@CAGRIYAPAN_UYE_ID", MySqlDbType.Int32).Value = entity.CallByMemberID;
            cmd.Parameters.Add("@CAGRIYAPAN_UYEADI", MySqlDbType.VarChar).Value = entity.CallByMemberName;
            cmd.Parameters.Add("@ILKARAMATARIHI", MySqlDbType.DateTime).Value = entity.FirstCallDate;
            cmd.Parameters.Add("@SONARAMATARIHI", MySqlDbType.DateTime).Value = entity.LastCallDate;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();
            //_cnn.Close();
        }

        public async Task UpdateDate(CustomerService entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE musteri_hizmetleri_aramalar
                                        SET ";

            if (entity.FirstCallDate.HasValue)
            {
                cmd.CommandText += "ILKARAMATARIHI = @ILKARAMATARIHI,";
            }

            if (entity.LastCallDate.HasValue)
            {
                cmd.CommandText += "SONARAMATARIHI = @SONARAMATARIHI";
            }

            cmd.CommandText = cmd.CommandText.TrimEnd(',');

            cmd.CommandText += @" WHERE
                                            ID = @ID";

            cmd.Parameters.Add("@ILKARAMATARIHI", MySqlDbType.DateTime).Value = entity.FirstCallDate;
            cmd.Parameters.Add("@SONARAMATARIHI", MySqlDbType.DateTime).Value = entity.LastCallDate;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();
            //_cnn.Close();
        }

        public async Task<List<CustomerService>> GetListAsync(CustomerServiceFilter filter = null, CustomerServicePaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<CustomerService> list = new List<CustomerService>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                       mha.ID,
                                       mha.TIP,
                                       mha.SIPARIS_ID,
                                       mha.SIPARIS_NO,
                                       mha.ODEME_ID,
                                       mha.DURUM_ID,
                                       mha.CAGRIYAPAN_UYE_ID,
                                       mha.ARANACAK_UYE_ID,
                                       mha.CAGRIYAPAN_UYEADI,
                                       mha.ARANACAK_UYEADI,
                                       mha.ARANACAK_UYE_TELEFON,
                                       mha.ARAMASAYISI,
                                       mha.EKLEMETARIHI,
                                       mha.ILKARAMATARIHI,
                                       mha.SONARAMATARIHI,
                                       mha.SIPARIS_TESLIMAT_TELEFON,
                                       siparis.TARIH AS SIPARIS_TARIHI,
                                       ROUND((
                                    siparis.TUTAR + siparis.TOPLAMKDV + siparis.KARGOTUTARI + siparis.HEDIYEPAKETTUTARI + siparis.EKVERGITUTARI + siparis.GUMRUKVERGISITUTARI
                                    + (SELECT IFNULL(SUM(IF(ONAYLANDI = 1, BANKAKOMISYONU - ODEMEINDIRIMI, 0) + KAPIDAODEMETUTARI),0) FROM siparis_odeme WHERE (ONAYLANDI = 1 OR ODEMETIPI = 1) AND SIPARIS_ID = siparis.ID)
                                    - siparis.HEDIYECEKITUTARI - IFNULL(JSON_EXTRACT(IF(siparis.HOPIKAMPANYA = '', NULL, siparis.HOPIKAMPANYA),'$.TicimaxInfo.discountValue'),0) - IFNULL(JSON_EXTRACT(PUANKULLANIM,'$.KullanilanPuanTLKarsiligi'),0)
                                ),2) AS TOPLAMTUTAR,
                                       siparis.PARABIRIMI
                                  FROM
                                       musteri_hizmetleri_aramalar AS mha INNER JOIN siparis ON mha.SIPARIS_ID = siparis.ID
                                  WHERE 1  ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync();
            while (await Reader.ReadAsync())
            {
                CustomerService p = new CustomerService();
                p.ID = Reader["ID"].ToInt32();
                p.Type = Reader["TIP"].ToInt32();
                p.OrderID = Reader["SIPARIS_ID"].ToInt32();
                p.OrderNo = Reader["SIPARIS_NO"].ToString();
                p.PaymentID = Reader["ODEME_ID"].ToInt32();
                p.StatusID = Reader["DURUM_ID"].ToInt32();
                p.StatusStr = ((CustomerServiceStatus)p.StatusID).GetStringValue();
                p.CallingMemberId = Reader["CAGRIYAPAN_UYE_ID"].ToInt32();
                p.CallByMemberName = Reader["CAGRIYAPAN_UYEADI"].ToString();
                p.CallByMemberID = Reader["ARANACAK_UYE_ID"].ToInt32();
                p.CallByMemberName = Reader["ARANACAK_UYEADI"].ToString();
                p.OrderDate = Reader["SIPARIS_TARIHI"].ToDateTime();
                p.CallByPhoneNumber = Reader["ARANACAK_UYE_TELEFON"].ToString();
                p.NumberOfCalls = Convert.ToInt32(Reader["ARAMASAYISI"]);
                p.AddingDate = Reader["EKLEMETARIHI"] != DBNull.Value ? Convert.ToDateTime(Reader["EKLEMETARIHI"]) : new DateTime(1900, 1, 1);
                if (Reader["ILKARAMATARIHI"] != DBNull.Value)
                {
                    p.FirstCallDate = Convert.ToDateTime(Reader["ILKARAMATARIHI"]);
                }

                if (Reader["SONARAMATARIHI"] != DBNull.Value)
                {
                    p.LastCallDate = Convert.ToDateTime(Reader["SONARAMATARIHI"]);
                }

                p.OrderDeliveryPhone = Reader["SIPARIS_TESLIMAT_TELEFON"].ToString();
                p.OrderTotalAmount = Reader["TOPLAMTUTAR"].ToDouble();
                p.OrderTotalAmountStr = p.OrderTotalAmount.ToString("0.00") + " " + Reader["ParaBirimi"].ToString().ToUpper();
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return list;
        }

        public async Task AddAsync(CustomerService entity, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO musteri_hizmetleri_aramalar (
                                              TIP,
                                              SIPARIS_ID,
                                              SIPARIS_NO,
                                              ODEME_ID,
                                              DURUM_ID,
                                              CAGRIYAPAN_UYE_ID,
                                              ARANACAK_UYE_ID,
                                              CAGRIYAPAN_UYEADI,
                                              ARANACAK_UYEADI,
                                              ARANACAK_UYE_TELEFON,
                                              ARAMASAYISI,
                                              EKLEMETARIHI,
                                              ILKARAMATARIHI,
                                              SONARAMATARIHI,
                                              SIPARIS_TESLIMAT_TELEFON,
                                              IADETUTAR,
                                              ODEMETIPI
                                            )
                                            VALUES
                                              (
                                                @TIP,
                                                @SIPARIS_ID,
                                                @SIPARIS_NO,
                                                @ODEME_ID,
                                                @DURUM_ID,
                                                0,
                                                @ARANACAK_UYE_ID,
                                                @CAGRIYAPAN_UYEADI,
                                                @ARANACAK_UYEADI,
                                                @ARANACAK_UYE_TELEFON,
                                                0,
                                                @EKLEMETARIHI,
                                                @ILKARAMATARIHI,
                                                @SONARAMATARIHI,
                                                @SIPARIS_TESLIMAT_TELEFON,
                                                @IADETUTAR,
                                                @ODEMETIPI
                                              );";

            cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = entity.Type;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@SIPARIS_NO", MySqlDbType.VarChar).Value = entity.OrderNo;
            cmd.Parameters.Add("@ODEME_ID", MySqlDbType.Int32).Value = entity.PaymentID;
            cmd.Parameters.Add("@DURUM_ID", MySqlDbType.Int32).Value = entity.StatusID;
            cmd.Parameters.Add("@ARANACAK_UYE_ID", MySqlDbType.Int32).Value = entity.CallByMemberID;
            cmd.Parameters.Add("@CAGRIYAPAN_UYEADI", MySqlDbType.VarChar).Value = entity.CallingMemberName;
            cmd.Parameters.Add("@ARANACAK_UYEADI", MySqlDbType.VarChar).Value = entity.CallByMemberName;
            cmd.Parameters.Add("@ARANACAK_UYE_TELEFON", MySqlDbType.VarChar).Value = entity.CallByPhoneNumber;
            cmd.Parameters.Add("@EKLEMETARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@SIPARIS_TESLIMAT_TELEFON", MySqlDbType.VarChar).Value = entity.OrderDeliveryPhone;
            cmd.Parameters.Add("@IADETUTAR", MySqlDbType.Double).Value = entity.ReturnAmount;
            cmd.Parameters.Add("@ODEMETIPI", MySqlDbType.Int32).Value = entity.PaymentType;
            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateAsync(CustomerService entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public async Task DeleteAsync(CustomerService entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public async Task<int> GetCountAsync(CustomerServiceFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                       COUNT(ID)
                                FROM
                                       musteri_hizmetleri_aramalar as mha WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var Count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return Convert.ToInt32(Count);
        }

        public async Task UpdateStatusAsync(CustomerService entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE musteri_hizmetleri_aramalar
                                        SET
                                            DURUM_ID = @DURUM_ID
                                        WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@DURUM_ID", MySqlDbType.Int32).Value = entity.StatusID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task UpdateNumberOfCallAsync(CustomerService entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE musteri_hizmetleri_aramalar
                                        SET
                                            ARAMASAYISI = (ARAMASAYISI + 1)
                                        WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task UpdateCallingMemberAsync(CustomerService entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE musteri_hizmetleri_aramalar
                                        SET
                                            CAGRIYAPAN_UYE_ID = @CAGRIYAPAN_UYE_ID
                                            ,CAGRIYAPAN_UYEADI = @CAGRIYAPAN_UYEADI";

            if (entity.FirstCallDate.HasValue)
            {
                cmd.CommandText += ",ILKARAMATARIHI = @ILKARAMATARIHI";
            }

            if (entity.LastCallDate.HasValue)
            {
                cmd.CommandText += ",SONARAMATARIHI = @SONARAMATARIHI";
            }

            cmd.CommandText += @" WHERE ID = @ID";
            cmd.Parameters.Add("@CAGRIYAPAN_UYE_ID", MySqlDbType.Int32).Value = entity.CallByMemberID;
            cmd.Parameters.Add("@CAGRIYAPAN_UYEADI", MySqlDbType.VarChar).Value = entity.CallByMemberName;
            cmd.Parameters.Add("@ILKARAMATARIHI", MySqlDbType.DateTime).Value = entity.FirstCallDate;
            cmd.Parameters.Add("@SONARAMATARIHI", MySqlDbType.DateTime).Value = entity.LastCallDate;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task UpdateDateAsync(CustomerService entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE musteri_hizmetleri_aramalar
                                        SET ";

            if (entity.FirstCallDate.HasValue)
            {
                cmd.CommandText += "ILKARAMATARIHI = @ILKARAMATARIHI,";
            }

            if (entity.LastCallDate.HasValue)
            {
                cmd.CommandText += "SONARAMATARIHI = @SONARAMATARIHI";
            }

            cmd.CommandText = cmd.CommandText.TrimEnd(',');

            cmd.CommandText += @" WHERE
                                            ID = @ID";

            cmd.Parameters.Add("@ILKARAMATARIHI", MySqlDbType.DateTime).Value = entity.FirstCallDate;
            cmd.Parameters.Add("@SONARAMATARIHI", MySqlDbType.DateTime).Value = entity.LastCallDate;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task DeleteByOrderIdAndType(int orderId, CustomerServiceType type, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM musteri_hizmetleri_aramalar
                                        WHERE SIPARIS_ID = @SIPARIS_ID AND TIP = @TIP";

            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderId;
            cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = (int)type;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }
        private void AppendFilter(ref MySqlCommand cmd, CustomerServiceFilter filtre = null)
        {
            if (filtre != null)
            {
                if (filtre.ID.HasValue)
                {
                    cmd.CommandText += " AND mha.ID = @ID ";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filtre.ID;
                }

                if (filtre.Type.HasValue)
                {
                    cmd.CommandText += " AND mha.TIP = @TIP ";
                    cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = (int)filtre.Type;
                }

                if (filtre.StatusID.HasValue)
                {
                    cmd.CommandText += " AND mha.DURUM_ID = @DURUM_ID ";
                    cmd.Parameters.Add("@DURUM_ID", MySqlDbType.Int32).Value = (int)filtre.StatusID;
                }
                else if (filtre.StatusIDs.Count > 0)
                {
                    cmd.CommandText += $@" AND mha.DURUM_ID IN ({string.Join(",", filtre.StatusIDs)}) ";
                }

                if (filtre.OrderID.HasValue)
                {
                    cmd.CommandText += " AND mha.SIPARIS_ID = @SIPARIS_ID ";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filtre.OrderID;
                }

                if (!string.IsNullOrEmpty(filtre.OrderNo))
                {
                    cmd.CommandText += " AND mha.SIPARIS_NO = @SIPARIS_NO ";
                    cmd.Parameters.Add("@SIPARIS_NO", MySqlDbType.Int32).Value = filtre.OrderNo;
                }

                if (filtre.PaymentID.HasValue)
                {
                    cmd.CommandText += " AND mha.ODEME_ID = @ODEME_ID ";
                    cmd.Parameters.Add("@ODEME_ID", MySqlDbType.Int32).Value = filtre.PaymentID;
                }

                if (filtre.CallingMemberId.HasValue)
                {
                    cmd.CommandText += " AND mha.CAGRIYAPAN_UYE_ID = @CAGRIYAPAN_UYE_ID ";
                    cmd.Parameters.Add("@CAGRIYAPAN_UYE_ID", MySqlDbType.Int32).Value = filtre.CallingMemberId;
                }

                if (!string.IsNullOrEmpty(filtre.CallByMemberName))
                {
                    cmd.CommandText += " AND mha.ARANACAK_UYEADI = @ARANACAK_UYEADI ";
                    cmd.Parameters.Add("@ARANACAK_UYEADI", MySqlDbType.Int32).Value = filtre.CallByMemberName;
                }

                if (!string.IsNullOrEmpty(filtre.CallByPhoneNumber))
                {
                    cmd.CommandText += " AND mha.ARANACAK_UYE_TELEFON = @ARANACAK_UYE_TELEFON ";
                    cmd.Parameters.Add("@ARANACAK_UYE_TELEFON", MySqlDbType.Int32).Value = filtre.CallByPhoneNumber;
                }

                if (filtre.AddingDateStart.HasValue)
                {
                    cmd.CommandText += " AND EKLEMETARIHI > @EKLENMETARIHI1";
                    cmd.Parameters.Add("@EKLENMETARIHI1", MySqlDbType.DateTime).Value = filtre.AddingDateStart.Value;
                }

                if (filtre.AddingDateFinish.HasValue)
                {
                    cmd.CommandText += " AND EKLEMETARIHI < @EKLENMETARIHI2";
                    cmd.Parameters.Add("@EKLENMETARIHI2", MySqlDbType.DateTime).Value = filtre.AddingDateFinish.Value;
                }

                if (filtre.FirstCallDateStart.HasValue)
                {
                    cmd.CommandText += " AND ILKARAMATARIHI > @ILKARAMATARIHI1";
                    cmd.Parameters.Add("@ILKARAMATARIHI1", MySqlDbType.DateTime).Value = filtre.FirstCallDateStart.Value;
                }

                if (filtre.FirstCallDateFinish.HasValue)
                {
                    cmd.CommandText += " AND ILKARAMATARIHI < @ILKARAMATARIHI2";
                    cmd.Parameters.Add("@ILKARAMATARIHI2", MySqlDbType.DateTime).Value = filtre.FirstCallDateFinish.Value;
                }

                if (filtre.LastCallDateStart.HasValue)
                {
                    cmd.CommandText += " AND SONARAMATARIHI > @SONARAMATARIHI1";
                    cmd.Parameters.Add("@SONARAMATARIHI1", MySqlDbType.DateTime).Value = filtre.LastCallDateStart.Value;
                }

                if (filtre.LastCallDateFinish.HasValue)
                {
                    cmd.CommandText += " AND SONARAMATARIHI < @SONARAMATARIHI2";
                    cmd.Parameters.Add("@SONARAMATARIHI2", MySqlDbType.DateTime).Value = filtre.LastCallDateFinish.Value;
                }

                if (!string.IsNullOrEmpty(filtre.OrderDeliveryPhone))
                {
                    cmd.CommandText += " AND mha.SIPARIS_TESLIMAT_TELEFON = @SIPARIS_TESLIMAT_TELEFON ";
                    cmd.Parameters.Add("@SIPARIS_TESLIMAT_TELEFON", MySqlDbType.Int32).Value = filtre.OrderDeliveryPhone;
                }

                if (filtre.PaymentType.HasValue)
                {
                    cmd.CommandText += " AND mha.ODEMETIPI = @ODEMETIPI ";
                    cmd.Parameters.Add("@ODEMETIPI", MySqlDbType.Int32).Value = filtre.PaymentType;
                }
            }
        }

    }
}