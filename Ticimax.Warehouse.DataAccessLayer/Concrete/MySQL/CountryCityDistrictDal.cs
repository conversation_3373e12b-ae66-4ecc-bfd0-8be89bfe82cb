using MySqlConnector;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class CountryCityDistrictDal : ICountryCityDistrictDal
    {
        private MySqlConnection _cnn;

        public CountryCityDistrictDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<Country>> GetCountryList(CountryFilter filter, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            List<Country> countrys = new List<Country>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT * FROM ulkeler WHERE 1";
            CountryAppendFilter(ref cmd, filter);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (reader.Read())
            {
                Country country = new Country();
                country.ID = reader["ID"].ToInt32();
                country.Definition = reader["TANIM"].ToString();
                country.Currency = reader["PARABIRIMI"].ToString();
                country.CountryCodeAlpha2 = reader["ULKEKODU_ALPHA2"].ToString();
                country.CountryCodeAlpha3 = reader["ULKEKODU_ALPHA3"].ToString();
                countrys.Add(country);
            }

            reader.Close();
            reader.Dispose();
            cmd.Dispose();
            //_cnn.Close();
            return countrys;
        }

        public async Task<IList<City>> GetCityList(CityFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            List<City> citys = new List<City>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT * FROM iller WHERE 1";
            CityAppendFilter(ref cmd, filter);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (reader.Read())
            {
                City city = new City();
                city.ID = reader["ID"].ToInt32();
                city.Definition = reader["TANIM"].ToString();
                citys.Add(city);
            }

            reader.Close();
            reader.Dispose();
            cmd.Dispose();
            //_cnn.Close();
            return citys;
        }

        public async Task<IList<District>> GetDistrictList(DistrictFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            List<District> districts = new List<District>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT * FROM ilceler WHERE 1";
            DistrictAppendFilter(ref cmd, filter);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (reader.Read())
            {
                District district = new District();
                district.ID = reader["ID"].ToInt32();
                district.Definition = reader["TANIM"].ToString();
                districts.Add(district);
            }

            reader.Close();
            reader.Dispose();
            cmd.Dispose();
            //_cnn.Close();
            return districts;
        }

        public async Task<List<Country>> GetCountryListAsync(CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<Country> countrys = new List<Country>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ID,TANIM FROM ulkeler WHERE 1";
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                Country country = new Country();
                country.ID = reader["ID"].ToInt32();
                country.Definition = reader["TANIM"].ToString();
                countrys.Add(country);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return countrys;
        }

        public async Task<List<City>> GetCityListAsync(CityFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<City> citys = new List<City>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ID,TANIM,ULKE_ID FROM iller WHERE 1";
            CityAppendFilter(ref cmd, filter);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                City city = new City();
                city.ID = reader["ID"].ToInt32();
                city.Definition = reader["TANIM"].ToString();
                city.CountryID = reader["ULKE_ID"].ToInt32();
                citys.Add(city);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return citys;
        }

        public async Task<List<District>> GetDistrictListAsync(DistrictFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<District> districts = new List<District>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ID,TANIM FROM ilceler WHERE 1";
            DistrictAppendFilter(ref cmd, filter);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                District district = new District();
                district.ID = reader["ID"].ToInt32();
                district.Definition = reader["TANIM"].ToString();
                districts.Add(district);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return districts;
        }


        private void CityAppendFilter(ref MySqlCommand cmd, CityFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID.HasValue)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID.Value;
                }
                if (filter.CountryID.HasValue)
                {
                    cmd.CommandText += " AND ULKE_ID = @CountryID";
                    cmd.Parameters.Add("@CountryID", MySqlDbType.Int32).Value = filter.CountryID.Value;
                }
            }
        }

        private void CountryAppendFilter(ref MySqlCommand cmd, CountryFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID.HasValue)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID.Value;
                }
                if (filter.Active.HasValue)
                {
                    cmd.CommandText += " AND AKTIF = @AKTIF";
                    cmd.Parameters.Add("@AKTIF", MySqlDbType.Int32).Value = filter.Active.Value;
                }
                if (!string.IsNullOrEmpty(filter.CountryCodeAlpha2))
                {
                    cmd.CommandText += " AND ULKEKODU_ALPHA2 = @ULKEKODU_ALPHA2";
                    cmd.Parameters.Add("@ULKEKODU_ALPHA2", MySqlDbType.VarChar).Value = filter.CountryCodeAlpha2;
                }
                if (!string.IsNullOrEmpty(filter.CountryCodeAlpha3))
                {
                    cmd.CommandText += " AND ULKEKODU_ALPHA3 = @ULKEKODU_ALPHA3";
                    cmd.Parameters.Add("@ULKEKODU_ALPHA3", MySqlDbType.VarChar).Value = filter.CountryCodeAlpha3;
                }
                if (!string.IsNullOrEmpty(filter.Currency))
                {
                    cmd.CommandText += " AND PARABIRIMI = @PARABIRIMI";
                    cmd.Parameters.Add("@PARABIRIMI", MySqlDbType.VarChar).Value = filter.Currency;
                }
            }
        }

        private void DistrictAppendFilter(ref MySqlCommand cmd, DistrictFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID.HasValue)
                {
                    cmd.CommandText += " AND IL_ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID.Value;
                }

                if (filter.DistrictID.HasValue)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.DistrictID.Value;
                }
            }
        }

    }
}