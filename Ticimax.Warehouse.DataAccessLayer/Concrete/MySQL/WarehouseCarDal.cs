using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseCarDal : IWarehouseCarDal
    {
        private MySqlConnection _cnn;

        public WarehouseCarDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        #region Public Methods

        public async Task<int> AddID(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_araba(
                                    TANIM
                                    , BARKOD
                                    , EKLENME_TARIHI
                                    , DEPO_ID
                                    , AKTIF)
                                VALUES (
                                    @TANIM
                                    , @BARKOD
                                    , @EKLENME_TARIHI
                                    , @DEPO_ID
                                    , @AKTIF)";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@EKLENME_TARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = entity.isActive;

            await cmd.ExecuteTransactionCommandAsync();
            entity.ID = Convert.ToInt32(cmd.LastInsertedId) + 1;
            return entity.ID;
        }

        public async Task CarLeave(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET KULLANICIID=0 WHERE KULLANICIID=@KULLANICIID AND BARKOD=@BARKOD";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task CarReceive(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET KULLANICIID=@KULLANICIID, DURUM=@DURUM WHERE KULLANICIID=0 AND BARKOD=@BARKOD";
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = 2;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task CarSetAssign(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET SET_NO=@SETNO,DURUM=@DURUM,MASA_ID=@MASAID WHERE ID=@ID";
            cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = (int)entity.Status;
            cmd.Parameters.Add("@MASAID", MySqlDbType.Int32).Value = entity.TableID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = entity.SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPOID";
                cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task CarTableAssign(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET MASA_ID=@MASA_ID WHERE KULLANICIID=@KULLANICIID AND ID=@ID";
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = entity.TableID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<bool> IsCarUsed(WarehouseCar entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                    COUNT(*)
                                FROM
                                    depo_araba
                                WHERE
                                    ID = @ID  AND KULLANICIID!=0";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var val = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken)) > 0;
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return val;
        }

        public async Task UpdateDynamicly(WarehouseCarUpdateDynamiclyDto request, CancellationToken cancellationToken)
        {
            List<string> setters = new List<string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_araba SET ";
            // Sets
            if (request.UserID.HasValue)
            {
                setters.Add(" KULLANICIID = @KULLANICIID");
                cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = request.UserID.Value;
            }

            if (request.SetNo != null)
            {
                setters.Add(" SET_NO = @SET_NO");
                cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = request.SetNo;
            }

            if (request.Status > 0)
            {
                setters.Add(" DURUM = @DURUM");
                cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = request.Status;
            }

            if (request.TableID.HasValue)
            {
                setters.Add(" MASA_ID = @MASA_ID");
                cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = request.TableID.Value;
            }

            cmd.CommandText += $"{string.Join(",", setters)}";

            // Conditions
            cmd.CommandText += " WHERE 1";
            if (request.IDs != null && request.IDs.Count > 0)
                cmd.CommandText += $" AND ID IN ({string.Join(",", request.IDs)})";

            if (request.UserIDs != null && request.UserIDs.Count > 0)
                cmd.CommandText += $" AND KULLANICIID IN ({string.Join(",", request.UserIDs)})";

            if (request.WarehouseID > 0)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = request.WarehouseID;
            }

            if (request.IsActive.HasValue)
            {
                cmd.CommandText += " AND AKTIF = @AKTIF";
                cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = request.IsActive.Value ? 1 : 0;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdatePersonID(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET KULLANICIID = @KULLANICIID WHERE ID = @ID ";
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@ID", MySqlDbType.Text).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateSetNoInPersonID(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET SET_NO = @SET_NO WHERE KULLANICIID = @KULLANICIID ";
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.Text).Value = entity.SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateTableID(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET MASA_ID = @MASAID WHERE ID = @ID ";
            cmd.Parameters.Add("@MASAID", MySqlDbType.Int32).Value = entity.TableID;
            cmd.Parameters.Add("@ID", MySqlDbType.Text).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPOID";
                cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<WarehouseCar>> GetListAsync(WarehouseCarFilter filter = null, WarehouseCarPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            var maneulCarControl = " 1 ";
            if (filter.isActive != null)
                maneulCarControl = filter.isActive == true ? " 1 " : " da.MANUEL_ARABA = 0 ";

            cmd.CommandText = @$"SELECT
                                          da.ID
                                        , da.TANIM
                                        , da.BARKOD
                                        , da.KULLANICIID
                                        , IFNULL((SELECT CONCAT(mt.ISIM,' ',mt.SOYISIM) FROM magaza_temsilci AS mt WHERE mt.ID = da.KULLANICIID),'') AS KULLANANKISI
                                        , da.SET_NO
                                        , da.DURUM
                                        , da.EKLENME_TARIHI
                                        , da.DEPO_ID
                                        , da.RAF_ID
                                        , da.MASA_ID
                                        , IFNULL((SELECT SUM(ADET) FROM depo_araba_urun as dau WHERE dau.ARABA_ID = da.ID),0) AS URUNSAYISI
                                        , da.AKTIF
                                        FROM
                                            depo_araba AS da
                                        WHERE {maneulCarControl} ";

            AppendFilter(ref cmd, filter);

            cmd.CommandText += " ORDER BY da.DURUM DESC";

            PagingExtension.AppendPaging(ref cmd, paging);

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            List<WarehouseCar> carList = new List<WarehouseCar>();
            while (await mdr.ReadAsync(cancellationToken))
            {
                WarehouseCar car = new WarehouseCar();
                car.ID = mdr["ID"].ToInt32();
                car.Barcode = mdr["BARKOD"].ToString();
                car.Definition = mdr["TANIM"].ToString();
                car.PersonID = mdr["KULLANICIID"] != DBNull.Value ? mdr["KULLANICIID"].ToInt32() : default;
                car.PersonName = mdr["KULLANANKISI"].ToString();
                car.SetNo = mdr["SET_NO"].ToString();
                car.Status = (WarehouseCarStatus)Enum.Parse(typeof(WarehouseCarStatus), mdr["DURUM"].ToString(), true);
                car.WarehouseID = mdr["DEPO_ID"] != DBNull.Value ? mdr["DEPO_ID"].ToInt32() : 0;
                car.AddingDate = mdr["EKLENME_TARIHI"].ToDateTime();
                car.ShelfID = mdr["RAF_ID"] != DBNull.Value ? mdr["RAF_ID"].ToInt32() : default;
                car.TableID = mdr["MASA_ID"].ToInt32();
                car.ProductCount = mdr["URUNSAYISI"].ToInt32();
                car.isActive = mdr["AKTIF"].ToBoolean();
                carList.Add(car);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return carList;
        }

        public async Task AddAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_araba(
                                    TANIM
                                    , BARKOD
                                    , EKLENME_TARIHI
                                    , DEPO_ID
                                    , AKTIF)
                                VALUES (
                                    @TANIM
                                    , @BARKOD
                                    , @EKLENME_TARIHI
                                    , @DEPO_ID
                                    , @AKTIF)";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@EKLENME_TARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = entity.isActive;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE
                                    depo_araba
                                SET
                                    TANIM = @TANIM
                                    , BARKOD = @BARKOD
                                    , DURUM = @DURUM
                                    , RAF_ID = @RAF_ID
                                    , AKTIF = @AKTIF
                                WHERE
                                    ID = @ID
                                    ";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = (int)entity.Status;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = entity.isActive;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM depo_araba WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(WarehouseCarFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM depo_araba da
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            

            return Convert.ToInt32(count);
        }

        public async Task<bool> IsCarUsedAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                    COUNT(*)
                                FROM
                                    depo_araba
                                WHERE
                                    ID = @ID  AND KULLANICIID!=0";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var val = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken)) > 0;
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return val;
        }

        public async Task CarLeaveAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET KULLANICIID=0 WHERE KULLANICIID=@KULLANICIID AND BARKOD=@BARKOD";
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task CarReceiveAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET KULLANICIID=@KULLANICIID, DURUM=@DURUM WHERE KULLANICIID=0 AND BARKOD=@BARKOD";
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = 2;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task CarSetAssignAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET SET_NO=@SETNO,DURUM=@DURUM,MASA_ID=@MASAID WHERE ID=@ID";
            cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = (int)entity.Status;
            cmd.Parameters.Add("@MASAID", MySqlDbType.Int32).Value = entity.TableID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = entity.SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPOID";
                cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task CarTableAssignAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET MASA_ID=@MASA_ID WHERE KULLANICIID=@KULLANICIID AND ID=@ID";
            cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = entity.TableID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateSetNoInPersonIDAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET SET_NO = @SET_NO WHERE KULLANICIID = @KULLANICIID ";
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.Text).Value = entity.SetNo;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateTableIDAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET MASA_ID = @MASAID WHERE ID = @ID ";
            cmd.Parameters.Add("@MASAID", MySqlDbType.Int32).Value = entity.TableID;
            cmd.Parameters.Add("@ID", MySqlDbType.Text).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPOID";
                cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdatePersonIDAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET KULLANICIID = @KULLANICIID WHERE ID = @ID ";
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@ID", MySqlDbType.Text).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateDynamiclyAsync(WarehouseCarUpdateDynamiclyDto request, CancellationToken cancellationToken)
        {
            List<string> setters = new List<string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_araba SET ";
            // Sets
            if (request.UserID.HasValue)
            {
                setters.Add(" KULLANICIID = @KULLANICIID");
                cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = request.UserID.Value;
            }

            if (request.SetNo != null)
            {
                setters.Add(" SET_NO = @SET_NO");
                cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = request.SetNo;
            }

            if (request.Status > 0)
            {
                setters.Add(" DURUM = @DURUM");
                cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = request.Status;
            }

            if (request.TableID.HasValue)
            {
                setters.Add(" MASA_ID = @MASA_ID");
                cmd.Parameters.Add("@MASA_ID", MySqlDbType.Int32).Value = request.TableID.Value;
            }

            cmd.CommandText += $"{string.Join(",", setters)}";

            // Conditions
            cmd.CommandText += " WHERE 1";
            if (request.IDs != null && request.IDs.Count > 0)
                cmd.CommandText += $" AND ID IN ({string.Join(",", request.IDs)})";

            if (request.UserIDs != null && request.UserIDs.Count > 0)
                cmd.CommandText += $" AND KULLANICIID IN ({string.Join(",", request.UserIDs)})";

            if (request.WarehouseID > 0)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = request.WarehouseID;
            }

            if (request.IsActive.HasValue)
            {
                cmd.CommandText += " AND AKTIF = @AKTIF";
                cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = request.IsActive.Value ? 1 : 0;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> AddIDAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_araba(
                                    TANIM
                                    , BARKOD
                                    , EKLENME_TARIHI
                                    , DEPO_ID
                                    , AKTIF)
                                VALUES (
                                    @TANIM
                                    , @BARKOD
                                    , @EKLENME_TARIHI
                                    , @DEPO_ID
                                    , @AKTIF)";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@EKLENME_TARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = entity.isActive;

            await cmd.ExecuteTransactionCommandAsync();
            entity.ID = Convert.ToInt32(cmd.LastInsertedId) + 1;
            return entity.ID;
        }

        public async Task MultipleAddAsync(List<WarehouseCar> entityList, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            List<string> valueList = new List<string>();
            for (int i = 0; i < entityList.Count; i++)
            {
                valueList.Add($@"(@TANIM{i}, @BARKOD{i}, @EKLENME_TARIHI{i}, @DEPO_ID{i}, @AKTIF{i})");

                cmd.Parameters.Add($"@TANIM{i}", MySqlDbType.VarChar).Value = entityList[i].Definition;
                cmd.Parameters.Add($"@BARKOD{i}", MySqlDbType.VarChar).Value = entityList[i].Barcode;
                cmd.Parameters.Add($"@EKLENME_TARIHI{i}", MySqlDbType.DateTime).Value = DateTime.Now;
                cmd.Parameters.Add($"@DEPO_ID{i}", MySqlDbType.Int32).Value = entityList[i].WarehouseID;
                cmd.Parameters.Add($"@AKTIF{i}", MySqlDbType.Bit).Value = entityList[i].isActive;
            }

            cmd.CommandText = $@"INSERT INTO depo_araba(TANIM, BARKOD, EKLENME_TARIHI, DEPO_ID, AKTIF) VALUES {string.Join(",", valueList)}";

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<Dictionary<int, string>> FindExistingBarcodes(List<WarehouseCar> entities, CancellationToken cancellationToken)
        {
            var barcodes = entities.Select(x => x.Barcode).ToList();
            Dictionary<int, string> ids = new Dictionary<int, string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $"SELECT dm.ID, dm.BARKOD FROM depo_araba dm WHERE dm.BARKOD IN (\"{string.Join("\",\"", barcodes)}\")";
            await _cnn.OpenAsync(cancellationToken);
            MySqlDataReader reader = (MySqlDataReader)await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync(cancellationToken))
            {
                var id = Convert.ToInt32(reader["ID"]);
                var barcode = reader["BARKOD"].ToString();
                ids.Add(id, barcode);
            }
            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return ids;
        }

        #endregion Public Methods

        private async Task<bool> TableControl(string domainName, string tableName, CancellationToken cancellationToken)
        {
            bool retValue = false;
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                       FROM INFORMATION_SCHEMA.TABLES
                                       WHERE TABLE_SCHEMA = @DomainName
                                       AND TABLE_NAME = @TableName ";

            cmd.Parameters.Add("@DomainName", MySqlDbType.VarChar).Value = domainName.Replace(".", "");
            cmd.Parameters.Add("@TableName", MySqlDbType.VarChar).Value = tableName;
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            if (count > 0)
            {
                retValue = true;
            }

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return retValue;
        }

        public async Task CarUserSyncAsync(WarehouseCar entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE depo_araba SET KULLANICIID = @KULLANICIID, SET_NO = @SET_NO WHERE ID = @ID ";
            cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = entity.SetNo;
            cmd.Parameters.Add("@ID", MySqlDbType.Text).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        private void AppendFilter(ref MySqlCommand cmd, WarehouseCarFilter filter)
        {
            if (filter != null)
            {
                if (!WebSiteInfo.User.Value?.IsOneStore ?? false)
                {
                    cmd.CommandText += " AND da.DEPO_ID = @DEPO_ID";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                }

                if (filter.PersonID > 0)
                {
                    cmd.CommandText += " AND da.KULLANICIID = @KULLANICIID";
                    cmd.Parameters.Add("@KULLANICIID", MySqlDbType.Int32).Value = filter.PersonID;
                }

                if (!string.IsNullOrEmpty(filter.CarBarcode))
                {
                    cmd.CommandText += " AND da.BARKOD = @BARKOD";
                    cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.CarBarcode;
                }

                if (filter.CarID > 0)
                {
                    cmd.CommandText += " AND da.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.VarChar).Value = filter.CarID;
                }

                if (filter.Status > 0)
                {
                    cmd.CommandText += " AND da.DURUM = @DURUM";
                    cmd.Parameters.Add("DURUM", MySqlDbType.Int32).Value = filter.Status;
                }

                if (!string.IsNullOrEmpty(filter.SetNo))
                {
                    cmd.CommandText += " AND da.SET_NO = @SET_NO";
                    cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
                }

                if (filter.isActive.HasValue)
                {
                    cmd.CommandText += " AND da.AKTIF = @AKTIF";
                    cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = filter.isActive;
                }
                if(filter.CarIds != null && filter.CarIds.Count > 0)
                {
                    cmd.CommandText += $" AND da.ID IN ({string.Join(",", filter.CarIds)})";
                }
            }
        }
    }
}