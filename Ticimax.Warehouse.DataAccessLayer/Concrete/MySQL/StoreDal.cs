using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Entities.Concrete;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class StoreDal : IStoreDal
    {
        private MySqlConnection _cnn;

        public StoreDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task AddAsync(Store entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO magazalar
                                (
                                    TANIM,
                                    ADRES,
                                    ULKE_ID,
                                    IL_ID,
                                    ILCE_ID,
                                    TELEFON,
                                    FAKS,
                                    GSM,
                                    MAGAZAKODU
                                ) 
                                VALUES
                                    (
                                        @TANIM,
                                        @ADRES,
                                        @ULKE_ID,
                                        @IL_ID,
                                        @ILCE_ID,
                                        @TELEFON,
                                        @FAKS,
                                        @GSM,
                                        @MAGAZAKODU
                                    )";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Description;
            cmd.Parameters.Add("@ADRES", MySqlDbType.Text).Value = entity.Address;
            cmd.Parameters.Add("@MAGAZAKODU", MySqlDbType.VarChar).Value = entity.StoreCode;
            cmd.Parameters.Add("@ULKE_ID", MySqlDbType.Int32).Value = entity.CountryID.ToInt32();
            cmd.Parameters.Add("@IL_ID", MySqlDbType.Int32).Value = entity.ProvinceID.ToInt32();
            cmd.Parameters.Add("@ILCE_ID", MySqlDbType.Int32).Value = entity.DistrictID.ToInt32();
            cmd.Parameters.Add("@TELEFON", MySqlDbType.VarChar).Value = !string.IsNullOrEmpty(entity.Telephone) ? entity.Telephone : string.Empty;
            cmd.Parameters.Add("@FAKS", MySqlDbType.VarChar).Value = !string.IsNullOrEmpty(entity.Faks) ? entity.Faks : string.Empty;
            cmd.Parameters.Add("@GSM", MySqlDbType.VarChar).Value = !string.IsNullOrEmpty(entity.Gsm) ? entity.Gsm : string.Empty;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(Store entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "DELETE FROM magazalar WHERE ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
        }

        public async Task<int> GetCountAsync(StoreFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM magazalar AS m
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<List<Store>> GetListAsync(StoreFilter filter = null, StorePaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<Store> magazalar = new List<Store>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  m.ID
                                      , m.TANIM
                                      , m.ADRES
                                      , m.TELEFON
                                      , m.FAKS
                                      , m.GSM
                                      , m.MAGAZAKODU
                                      , m.APISIFRE
                                      , m.ULKE_ID
                                      , m.IL_ID
                                      , m.ILCE_ID
                                      , ulke.TANIM AS ULKE
                                      , il.TANIM AS IL
                                      , ilc.TANIM AS ILCE
                                FROM  magazalar AS m
                                LEFT JOIN ulkeler AS ulke ON m.ULKE_ID = ulke.ID
                                LEFT JOIN iller AS il ON m.IL_ID = il.ID
                                LEFT JOIN ilceler AS ilc ON m.ILCE_ID = ilc.ID
                                WHERE 1";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                Store p = new Store();
                p.ID = Convert.ToInt32(reader["ID"]);
                p.Description = reader["TANIM"].ToString();
                p.Address = reader["ADRES"].ToString();
                p.Telephone = reader["TELEFON"].ToString();
                p.Faks = reader["FAKS"].ToString();
                p.Gsm = reader["GSM"].ToString();
                p.StoreCode = reader["MAGAZAKODU"].ToString();
                p.ApiPassword = reader["APISIFRE"].ToString();
                p.CountryID = reader["ULKE_ID"] != DBNull.Value ? reader["ULKE_ID"].ToInt32() : 0;
                p.Country = reader["ULKE"].ToString();
                p.ProvinceID = reader["IL_ID"] != DBNull.Value ? reader["IL_ID"].ToInt32() : 0;
                p.Province = reader["IL"].ToString();
                p.DistrictID = reader["ILCE_ID"] != DBNull.Value ? reader["ILCE_ID"].ToInt32() : 0;
                p.District = reader["ILCE"].ToString();
                magazalar.Add(p);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return magazalar;
        }

        public async Task UpdateAsync(Store entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE magazalar SET 
                                     TANIM = @TANIM,
                                     ADRES = @ADRES,
                                     TELEFON = @TELEFON,
                                     FAKS = @FAKS,
                                     GSM = @GSM,
                                     ULKE_ID = @ULKE_ID,
                                     IL_ID = @IL_ID,
                                     ILCE_ID = @ILCE_ID,
                                     MAGAZAKODU = @MAGAZAKODU
                                 WHERE ID = @ID";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Description;
            cmd.Parameters.Add("@ADRES", MySqlDbType.Text).Value = entity.Address;
            cmd.Parameters.Add("@MAGAZAKODU", MySqlDbType.VarChar).Value = entity.StoreCode;
            cmd.Parameters.Add("@ULKE_ID", MySqlDbType.Int32).Value = entity.CountryID.ToInt32();
            cmd.Parameters.Add("@IL_ID", MySqlDbType.Int32).Value = entity.ProvinceID.ToInt32();
            cmd.Parameters.Add("@ILCE_ID", MySqlDbType.Int32).Value = entity.DistrictID.ToInt32();
            cmd.Parameters.Add("@TELEFON", MySqlDbType.VarChar).Value = !string.IsNullOrEmpty(entity.Telephone) ? entity.Telephone : string.Empty;
            cmd.Parameters.Add("@FAKS", MySqlDbType.VarChar).Value = !string.IsNullOrEmpty(entity.Faks) ? entity.Faks : string.Empty;
            cmd.Parameters.Add("@GSM", MySqlDbType.VarChar).Value = !string.IsNullOrEmpty(entity.Gsm) ? entity.Gsm : string.Empty;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        private void AppendFilter(ref MySqlCommand cmd, StoreFilter filter)
        {
            if (filter != null)
            {
                if (filter.StoreID > 0)
                {
                    cmd.CommandText += " AND m.ID = @ID ";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.StoreID;
                }

                if (filter.StoreIds != null && filter.StoreIds.Count > 0)
                    cmd.CommandText += $" AND m.ID IN ({string.Join(',', filter.StoreIds)})";
            }
        }
    }
}