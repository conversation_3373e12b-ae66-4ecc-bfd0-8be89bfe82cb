using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class ExcessProductDal : IExcessProductDal
    {
        private MySqlConnection _cnn;

        public ExcessProductDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task AddAsync(ExcessProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO depo_fazla_urun (
                                  URUN_ID
                                  , URUNKARTI_ID
                                  , BARKOD
                                  , KULLANICI_ID
                                  , STOK_KODU
                                  , GIRIS_TARIHI
                                  , GIRIS_ACIKLAMA
                                  , DURUM
                                  , DEPO_ID
                                  , MAGAZA_ID
                                )
                                values
                                (
                                    @URUN_ID
                                    , @URUNKARTI_ID
                                    , @BARKOD
                                    , @KULLANICI_ID
                                    , @STOK_KODU
                                    , @GIRIS_TARIHI
                                    , @GIRIS_ACIKLAMA
                                    , @DURUM
                                    , @DEPO_ID
                                    , @MAGAZA_ID
                                  );";

            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.ProductID;
            cmd.Parameters.Add("@URUNKARTI_ID", MySqlDbType.Int32).Value = entity.ProductCardID;
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@STOK_KODU", MySqlDbType.VarChar).Value = entity.StockCode;
            cmd.Parameters.Add("@GIRIS_TARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@GIRIS_ACIKLAMA", MySqlDbType.VarChar).Value = entity.EntryDescription;
            cmd.Parameters.Add("@DURUM", MySqlDbType.Bit).Value = 0;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = entity.StoreID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(ExcessProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM depo_fazla_urun WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ExcessProductID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task ExitProduct(ExcessProduct entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            var cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE
                                    depo_fazla_urun
	                            SET
	                                  CIKIS_TARIHI = @CIKIS_TARIHI
	                                , CIKIS_ACIKLAMA = @CIKIS_ACIKLAMA
                                    , DURUM = 1
                                WHERE
	                                ID = @ID
                                    AND DURUM = 0 ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ExcessProductID;
            cmd.Parameters.Add("@CIKIS_TARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@CIKIS_ACIKLAMA", MySqlDbType.VarChar).Value = entity.ExitDescription;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task ExitProductAsync(ExcessProduct entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            var cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE
                                    depo_fazla_urun
	                            SET
	                                  CIKIS_TARIHI = @CIKIS_TARIHI
	                                , CIKIS_ACIKLAMA = @CIKIS_ACIKLAMA
                                    , DURUM = 1
                                WHERE
	                                ID = @ID
                                    AND DURUM = 0 ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ExcessProductID;
            cmd.Parameters.Add("@CIKIS_TARIHI", MySqlDbType.DateTime).Value = DateTime.Now;
            cmd.Parameters.Add("@CIKIS_ACIKLAMA", MySqlDbType.VarChar).Value = entity.ExitDescription;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task<int> GetCountAsync(ExcessProductFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM depo_fazla_urun AS dfu
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return Convert.ToInt32(count);
        }

        public async Task<List<ExcessProduct>> GetListAsync(ExcessProductFilter filter = null, ExcessProductPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            var list = new List<ExcessProduct>();
            var cmd = new MySqlCommand(string.Empty, _cnn);

            //TODO: sorgu kontrol edilecek
            cmd.CommandText = @"SELECT
                                      dfu.ID
                                      , dfu.URUN_ID
                                      , dfu.URUNKARTI_ID
                                      , dfu.BARKOD
                                      , dfu.KULLANICI_ID
                                      , dfu.STOK_KODU
                                      , dfu.GIRIS_TARIHI
                                      , dfu.GIRIS_ACIKLAMA
                                      , dfu.CIKIS_TARIHI
                                      , dfu.CIKIS_ACIKLAMA
                                      , dfu.DURUM
                                    FROM
                                        depo_fazla_urun AS dfu
                                    WHERE 1";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                list.Add(new ExcessProduct
                {
                    Barcode = reader["BARKOD"].ToString(),
                    ExitDescription = reader["CIKIS_ACIKLAMA"] != DBNull.Value ? reader["CIKIS_ACIKLAMA"].ToString() : default,
                    ExitDate = reader["CIKIS_TARIHI"] != DBNull.Value ? reader["CIKIS_TARIHI"].ToDateTime() : default,
                    EntryDescription = reader["GIRIS_ACIKLAMA"] != DBNull.Value ? reader["GIRIS_ACIKLAMA"].ToString() : default,
                    EntryDate = reader["GIRIS_TARIHI"].ToDateTime(),
                    ExcessProductID = reader["ID"].ToInt32(),
                    PersonID = reader["KULLANICI_ID"].ToInt32(),
                    StockCode = reader["STOK_KODU"] != DBNull.Value ? reader["STOK_KODU"].ToString() : default,
                    ProductID = reader["URUN_ID"].ToInt32(),
                    ProductCardID = reader["URUNKARTI_ID"].ToInt32(),
                    Status = reader["DURUM"].ToBoolean(),
                });
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return list;
        }

        public async Task<List<ExcessProductListItemDto>> GetListItemDtoAsync(ExcessProductFilter filter = null, ExcessProductPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            var list = new List<ExcessProductListItemDto>();
            var cmd = new MySqlCommand(string.Empty, _cnn);

            //TODO: sorgu kontrol edilecek
            cmd.CommandText = @"SELECT
                                      dfu.ID
                                      , dfu.URUN_ID
                                      , dfu.URUNKARTI_ID
                                      , dfu.BARKOD
                                      , dfu.KULLANICI_ID
                                      , dfu.STOK_KODU
                                      , dfu.GIRIS_TARIHI
                                      , dfu.GIRIS_ACIKLAMA
                                      , dfu.CIKIS_TARIHI
                                      , dfu.CIKIS_ACIKLAMA
                                      , dfu.DURUM
                                      , uk.URUNADI AS URUNADI
                                      , uk.RESIM1
                                      , CONCAT(mt.ISIM,' ', mt.SOYISIM) AS ADSOYAD
                                      , (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
	                                                                            FROM urun_eksecenek AS ues
	                                                                            WHERE ues.URUN_ID = dfu.URUN_ID ORDER BY ues.EKSECENEK_SIRA) AS VARYASYON
                                    FROM
                                        depo_fazla_urun AS dfu
                                    JOIN
	                                    urun_karti AS uk ON dfu.URUNKARTI_ID = uk.ID
                                    JOIN
	                                    magaza_temsilci AS mt ON dfu.KULLANICI_ID = mt.ID
                                    WHERE 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                list.Add(new ExcessProductListItemDto
                {
                    Barcode = reader["BARKOD"].ToString(),
                    ExitDescription = reader["CIKIS_ACIKLAMA"] != DBNull.Value ? reader["CIKIS_ACIKLAMA"].ToString() : default,
                    ExitDate = reader["CIKIS_TARIHI"] != DBNull.Value ? reader["CIKIS_TARIHI"].ToDateTime() : default,
                    EntryDescription = reader["GIRIS_ACIKLAMA"] != DBNull.Value ? reader["GIRIS_ACIKLAMA"].ToString() : default,
                    EntryDate = reader["GIRIS_TARIHI"].ToDateTime(),
                    ExcessProductID = reader["ID"].ToInt32(),
                    PersonID = reader["KULLANICI_ID"].ToInt32(),
                    PersonName = reader["ADSOYAD"].ToString(),
                    StockCode = reader["STOK_KODU"] != DBNull.Value ? reader["STOK_KODU"].ToString() : default,
                    ProductID = reader["URUN_ID"].ToInt32(),
                    ProductCardID = reader["URUNKARTI_ID"].ToInt32(),
                    Status = reader["DURUM"].ToBoolean(),
                    ProductName = reader["URUNADI"] + " " + reader["VARYASYON"],
                    Image = WebSiteInfo.User.Value.ImagePath + reader["RESIM1"].ToString()
                });
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await _cnn.CloseAsync();

            return list;
        }

        public async Task UpdateAsync(ExcessProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE
                                    depo_fazla_urun
	                            SET
	                                GIRIS_ACIKLAMA = @GIRIS_ACIKLAMA
	                                , CIKIS_TARIHI = @CIKIS_TARIHI
	                                , CIKIS_ACIKLAMA = @CIKIS_ACIKLAMA
                                    , DURUM = @DURUM
                                WHERE ID = @ID ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ExcessProductID;
            cmd.Parameters.Add("@GIRIS_ACIKLAMA", MySqlDbType.VarChar).Value = entity.EntryDescription;
            cmd.Parameters.Add("@CIKIS_TARIHI", MySqlDbType.DateTime).Value = entity.ExitDate;
            cmd.Parameters.Add("@CIKIS_ACIKLAMA", MySqlDbType.VarChar).Value = entity.ExitDescription;
            cmd.Parameters.Add("@DURUM", MySqlDbType.Bit).Value = entity.Status;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }



        private void AppendFilter(ref MySqlCommand cmd, ExcessProductFilter filter)
        {
            if (filter != null)
            {
                if (!WebSiteInfo.User.Value.IsOneStore)
                {
                    cmd.CommandText += " AND dfu.DEPO_ID = @DEPO_ID AND dfu.MAGAZA_ID = @MAGAZA_ID";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                    cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                }

                if (filter.ProductIDs != null && filter.ProductIDs.Count > 0)
                    cmd.CommandText += $" AND dfu.ID IN ({string.Join(",", filter.ProductIDs)})";

                if (filter.ExcessProductID > 0)
                {
                    cmd.CommandText += " AND dfu.ID = @ID ";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ExcessProductID;
                }

                if (filter.Status > -1)
                {
                    cmd.CommandText += " AND dfu.DURUM = @DURUM ";
                    cmd.Parameters.Add("@DURUM", MySqlDbType.Bit).Value = filter.Status;
                }

                if (filter.ProductID > 0)
                {
                    cmd.CommandText += " AND dfu.URUN_ID = @URUN_ID ";
                    cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = filter.ProductID;
                }

                if (filter.ProductCardID > 0)
                {
                    cmd.CommandText += " AND dfu.URUNKARTI_ID = @URUNKARTI_ID ";
                    cmd.Parameters.Add("@URUNKARTI_ID", MySqlDbType.Int32).Value = filter.ProductCardID;
                }

                if (!string.IsNullOrEmpty(filter.Barcode))
                {
                    if (filter.MultipleBarcode)
                    {
                        cmd.CommandText += " AND (dfu.BARKOD = @BARKOD OR dfu.URUN_ID IN (SELECT URUN_ID FROM urun_barkod WHERE BARKOD = @BARKOD))";
                    }
                    else
                    {
                        cmd.CommandText += " AND dfu.BARKOD = @BARKOD";
                    }

                    cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.Barcode;
                }

                if (!string.IsNullOrEmpty(filter.StockCode))
                {
                    cmd.CommandText += " AND dfu.STOK_KODU = @STOK_KODU ";
                    cmd.Parameters.Add("@STOK_KODU", MySqlDbType.VarChar).Value = filter.StockCode;
                }

                if (filter.EntryDate != null)
                {
                    cmd.CommandText += " AND dfu.GIRIS_TARIHI <= @GIRIS_TARIHI ";
                    cmd.Parameters.Add("@GIRIS_TARIHI", MySqlDbType.Date).Value = filter.EntryDate;
                }

                if (filter.ExitDate != null)
                {
                    cmd.CommandText += " AND dfu.CIKIS_TARIHI <= @CIKIS_TARIHI ";
                    cmd.Parameters.Add("@CIKIS_TARIHI", MySqlDbType.Date).Value = filter.ExitDate;
                }
            }
        }
    }
}