using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Product.Entities.Concrete;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseBoxDal : IWarehouseBoxDal
    {
        private MySqlConnection _cnn;

        public WarehouseBoxDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<IList<WarehouseBox>> BoxesInProduct(string barcode, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<WarehouseBox> Boxes = new List<WarehouseBox>();
            List<string> BoxesBarcode = new List<string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT Barkod FROM depo_kutu AS dk WHERE dk.`URUNLER` LIKE @Barkod AND dk.`DURUM` = 0;";
            cmd.Parameters.Add("Barkod", MySqlDbType.VarChar).Value = "%" + barcode + "%";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                BoxesBarcode.Add(mdr["Barkod"].ToString());
            }

            await mdr.CloseAsync();
            await _cnn.CloseAsync();

            foreach (var boxbarcode in BoxesBarcode)
            {
                Boxes.AddRange(await GetListAsync(new WarehouseBoxFilter { Barcode = boxbarcode }, null, cancellationToken));
            }

            return Boxes;
        }

        public async Task DeleteOnBarkod(WarehouseBox entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM depo_kutu WHERE BARKOD = @BARKOD";
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task<double> ProductCountInBoxes(string barcode, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            double Count = 0;
            List<Product> urunler = new List<Product>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT dk.URUNLER FROM depo_kutu AS dk WHERE dk.`URUNLER` LIKE @Barkod AND dk.`DURUM` = 0;";
            cmd.Parameters.Add("Barkod", MySqlDbType.VarChar).Value = "%" + barcode + "%";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                urunler.AddRange(mdr["Urunler"].ToJsonDeserialize<List<Product>>());
            }

            await mdr.CloseAsync();
            await _cnn.CloseAsync();
            var eslesenUrunler = urunler.Where(x => x.Barcode == barcode);
            foreach (var eslesenUrun in eslesenUrunler)
            {
                Count += eslesenUrun.Piece;
            }

            return Count;
        }

        public async Task UpdateBoxShelf(int ID, int shelfID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_kutu
                                    SET RAF_ID = @RAF_ID
                                    WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = shelfID;

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateBoxProducts(int ID, List<WarehouseBoxProductDto> products, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE depo_kutu
                                    SET URUNLER = @URUNLER
                                    WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            cmd.Parameters.Add("@URUNLER", MySqlDbType.Text).Value = products.ToJsonSerialize();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<WarehouseBox>> GetListAsync(WarehouseBoxFilter filter = null, WarehouseBoxPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<WarehouseBox> list = new List<WarehouseBox>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT      r.ID
                                          , r.BARKOD
                                          , r.URUNLER
                                          , r.RAF_ID
                                          , r.OLUSTURAN_ID
                                          , r.DURUM
                                          , r.EKLENME_TARIHI
                                    FROM depo_kutu AS r
                                    WHERE 1";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                WarehouseBox p = new WarehouseBox();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.Barcode = Reader["BARKOD"].ToString();
                p.Products = Reader["URUNLER"].ToJsonDeserialize<List<WarehouseBoxProductDto>>();
                p.ShelfID = Reader["RAF_ID"].ToInt32();
                p.CreatedID = Reader["OLUSTURAN_ID"].ToInt32();
                p.AddingDate = Reader["EKLENME_TARIHI"].ToDateTime();
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return list;
        }

        public async Task AddAsync(WarehouseBox entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO depo_kutu
                                                (BARKOD
                                                , URUNLER
                                                , LOTLAR
                                                , RAF_ID
                                                , OLUSTURAN_ID)
                                    VALUES (@BARKOD
                                          , @URUNLER
                                          , ''
                                          , @RAF_ID
                                          , @OLUSTURAN_ID)";

            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@OLUSTURAN_ID", MySqlDbType.Int32).Value = entity.CreatedID;
            cmd.Parameters.Add("@URUNLER", MySqlDbType.Text).Value = entity.Products != null && entity.Products.Count > 0 ? entity.Products.ToJsonSerialize() : null;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateAsync(WarehouseBox entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_kutu
                                    SET BARKOD = @BARKOD
                                      , URUNLER = @URUNLER
                                      , RAF_ID = @RAF_ID
                                    WHERE ID = @ID";

            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@URUNLER", MySqlDbType.Text).Value = entity.Products.ToJsonSerialize();

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(WarehouseBox entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"DELETE FROM depo_kutu WHERE ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(WarehouseBoxFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM depo_kutu AS r
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<double> ProductCountInBoxesAsync(string barcode, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            double Count = 0;
            List<Product> urunler = new List<Product>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT dk.URUNLER FROM depo_kutu AS dk WHERE dk.`URUNLER` LIKE @Barkod AND dk.`DURUM` = 0;";
            cmd.Parameters.Add("Barkod", MySqlDbType.VarChar).Value = "%" + barcode + "%";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                urunler.AddRange(mdr["Urunler"].ToJsonDeserialize<List<Product>>());
            }

            await mdr.CloseAsync();
            await _cnn.CloseAsync();
            var eslesenUrunler = urunler.Where(x => x.Barcode == barcode);
            foreach (var eslesenUrun in eslesenUrunler)
            {
                Count += eslesenUrun.Piece;
            }

            return Count;
        }

        public async Task<List<WarehouseBox>> BoxesInProductAsync(string barcode, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<WarehouseBox> Boxes = new List<WarehouseBox>();
            List<string> BoxesBarcode = new List<string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT Barkod FROM depo_kutu AS dk WHERE dk.`URUNLER` LIKE @Barkod AND dk.`DURUM` = 0;";
            cmd.Parameters.Add("Barkod", MySqlDbType.VarChar).Value = "%" + barcode + "%";
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                BoxesBarcode.Add(mdr["Barkod"].ToString());
            }

            await mdr.CloseAsync();
            await _cnn.CloseAsync();

            foreach (var boxbarcode in BoxesBarcode)
            {
                Boxes.AddRange(await GetListAsync(new WarehouseBoxFilter { Barcode = boxbarcode }, null, cancellationToken));
            }

            return Boxes;
        }

        public async Task DeleteOnBarkodAsync(WarehouseBox barkod, WarehouseBox entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM depo_kutu WHERE BARKOD = @BARKOD";
            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = entity.Barcode;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task UpdateBoxShelfAsync(int ID, int shelfID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_kutu
                                    SET RAF_ID = @RAF_ID
                                    WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = shelfID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateBoxProductsAsync(int ID, List<WarehouseBoxProductDto> products, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE depo_kutu
                                    SET URUNLER = @URUNLER
                                    WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            cmd.Parameters.Add("@URUNLER", MySqlDbType.Text).Value = products.ToJsonSerialize();
            await cmd.ExecuteTransactionCommandAsync();
        }

        private void AppendFilter(ref MySqlCommand cmd, WarehouseBoxFilter filter)
        {
            if (filter != null)
            {
                if (filter.WarehouseBoxID.HasValue)
                {
                    cmd.CommandText += " AND r.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.WarehouseBoxID.Value;
                }

                if (filter.ProductIDs != null && filter.ProductIDs.Count > 0)
                {
                    cmd.CommandText += $" AND r.URUNLER REGEXP '{string.Join("|", filter.ProductIDs.Select(x => $"\"ID\":{x}").ToList())}' ";
                }

                if (!string.IsNullOrEmpty(filter.Barcode))
                {
                    cmd.CommandText += " AND r.BARKOD = @BARKOD";
                    cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.Barcode;
                }

                if (filter.isShelf)
                {
                    cmd.CommandText += " AND r.RAF_ID = @RAF_ID";
                    cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = 0;
                }

                if (filter.ShelfID > 0)
                {
                    cmd.CommandText += " AND r.RAF_ID = @RAF_ID";
                    cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = filter.ShelfID;
                }
            }
        }

    }
}