using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete
{
    public class EInvoiceResultDal : IEInvoiceResultDal
    {
        private MySqlConnection _cnn;

        public EInvoiceResultDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<int> GetCountAsync(EInvoiceResultFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM efatura_sonuc AS m
                                INNER JOIN siparis s on s.ID=m.SIPARIS_ID
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<List<EInvoceResult>> GetListAsync(EInvoiceResultFilter filter = null, EInvoiceResultPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<EInvoceResult> eArsivSonuc = new List<EInvoceResult>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  m.ID
                                      , m.FATURATIPI
                                      , m.SIPARIS_ID
                                      , m.IPTALEDILDI
                                      , m.DETAIL
                                      , m.RESULT
                                      , m.FILENAME
                                      , m.INVOICENUMBER
                                      , m.SHA256HASH
                                      , m.CODE
                                      , m.DESCRIPTION
                                      , m.UUID
                                      , m.VKN
                                      , m.FILEPATH
                                      , m.OLUSTURMATARIHI
                                      , m.IPTALTARIHI
                                      , m.TOPLAMKDVHARICTUTAR
                                      , m.ENVUUID
                                      , m.CUSTINVID
                                      , m.EFATURAKABULRED
                                      , m.ENTEGRATOR
                                      , m.FATURATURU
                                      , s.TARIH AS SIPARIS_TARIHI
                                      , m.EFATURAUYGULAMAYANIT
                                      , (SELECT TANIM FROM kargofirma WHERE ID = (SELECT siparis.KARGOFIRMA_ID  FROM siparis WHERE  ID  = m.SIPARIS_ID LIMIT 1)) AS KARGOFIRMA
                                      , s.ODEMETIPI ";

            cmd.CommandText += @" FROM efatura_sonuc AS m
                              INNER JOIN siparis s on s.ID=m.SIPARIS_ID
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                EInvoceResult p = new EInvoceResult();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.OrderID = Convert.ToInt32(Reader["SIPARIS_ID"]);
                p.isCancel = (Reader["IPTALEDILDI"] == DBNull.Value ? false : Convert.ToBoolean(Reader["IPTALEDILDI"]));
                p.Detail = Reader["DETAIL"].ToString();
                p.Result = Reader["RESULT"].ToString();
                p.FileName = Reader["FILENAME"].ToString();
                p.InvoiceNumber = Reader["INVOICENUMBER"].ToString();
                p.Sha256Hash = Reader["SHA256HASH"].ToString();
                p.Code = Reader["CODE"].ToString();
                p.Description = Reader["DESCRIPTION"].ToString();
                p.UUID = Reader["UUID"].ToString();
                p.VKN = Reader["VKN"].ToString();
                p.FilePath = Reader["FILEPATH"].ToString();
                p.CreatedDate = (Reader["OLUSTURMATARIHI"] != DBNull.Value ? Convert.ToDateTime(Reader["OLUSTURMATARIHI"]) : new DateTime(1990, 1, 1));
                p.CancelDate = (Reader["IPTALTARIHI"] != DBNull.Value ? Convert.ToDateTime(Reader["IPTALTARIHI"]) : new DateTime(1990, 1, 1));
                //p.ToplamKDVHaricTutar = (Reader["TOPLAMKDVHARICTUTAR"] != DBNull.Value ? Convert.ToDouble(Reader["TOPLAMKDVHARICTUTAR"]).ToFormattedDouble(true) : 0);
                p.TotalVatExcludes = (Reader["TOPLAMKDVHARICTUTAR"] != DBNull.Value ? Convert.ToDouble(Reader["TOPLAMKDVHARICTUTAR"]) : 0); //TODO: Üstteki satır'daki extension metod alınacak.
                p.CustInvID = Reader["CUSTINVID"].ToString();
                p.EInvoiceType = ((EInvoiceType)Reader["FATURATIPI"]); //
                p.EnvUUID = Reader["ENVUUID"].ToString();
                p.EInvoiceAcceptedRed = DBNull.Value != Reader["EFATURAKABULRED"] ? Convert.ToInt16(Reader["EFATURAKABULRED"]) : -1;
                p.EInvoiceApplicationAnswer = Reader["EFATURAUYGULAMAYANIT"].ToString();
                p.OrderDate = (Reader["SIPARIS_TARIHI"] != DBNull.Value ? Convert.ToDateTime(Reader["SIPARIS_TARIHI"]) : new DateTime(1990, 1, 1));

                p.ShippingCompany = DBNull.Value != Reader["KARGOFIRMA"] ? Reader["KARGOFIRMA"].ToString() : "";
                p.PaymentType = Reader["ODEMETIPI"].ToInt32() < 0 ? "Ödeme Yok" : ((PaymentType)Reader["ODEMETIPI"].ToInt32()).GetStringValue();
                p.Integrator = DBNull.Value != Reader["ENTEGRATOR"] ? Convert.ToInt16(Reader["ENTEGRATOR"]) : 1;
                p.EInvoiceSort = ((EInvoiceSort)Reader["FATURATURU"]);
                if (p.EInvoiceType == EInvoiceType.EFatura && string.IsNullOrEmpty(p.Result) && !string.IsNullOrEmpty(p.InvoiceNumber))
                {
                    p.Result = "SUCCESS";
                }

                eArsivSonuc.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return eArsivSonuc;
        }



        private void AppendFilter(ref MySqlCommand cmd, EInvoiceResultFilter filter)
        {
            if (filter != null)
            {
                if (filter.ItemID > 0)
                {
                    cmd.CommandText += " AND m.ID = @ID ";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ItemID;
                }

                if (filter.OrderID > 0)
                {
                    cmd.CommandText += " AND m.SIPARIS_ID = @SIPARIS_ID ";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderID;
                }

                if (filter.OrderIDList != null && filter.OrderIDList.Count > 0)
                {
                    cmd.CommandText += $" AND m.SIPARIS_ID IN ({string.Join(",", filter.OrderIDList)})";
                }

                //EFatura Olusturma tarihi
                if (filter.CreatedStartDate.HasValue)
                {
                    cmd.CommandText += " AND m.OLUSTURMATARIHI >= @OlusturmaBaslangicTarihi";
                    cmd.Parameters.Add("@OlusturmaBaslangicTarihi", MySqlDbType.DateTime).Value = filter.CreatedStartDate.Value;
                }

                if (filter.CreatedFinishDate.HasValue)
                {
                    cmd.CommandText += " AND m.OLUSTURMATARIHI <= @OlusturmaBitisTarihi";
                    cmd.Parameters.Add("@OlusturmaBitisTarihi", MySqlDbType.DateTime).Value = filter.CreatedFinishDate.Value;
                }

                //Siparis tarihi
                if (filter.StartDate.HasValue)
                {
                    cmd.CommandText += " AND s.TARIH >= @BaslangicTarih ";
                    cmd.Parameters.Add("@BaslangicTarih", MySqlDbType.DateTime).Value = filter.StartDate.Value;
                }

                if (filter.FinishDate.HasValue)
                {
                    cmd.CommandText += "AND s.TARIH <= @BitisTarihi ";
                    cmd.Parameters.Add("@BitisTarihi", MySqlDbType.DateTime).Value = filter.FinishDate.Value;
                }

                if (filter.isCancel > -1)
                {
                    cmd.CommandText += " AND m.IPTALEDILDI = @IPTALEDILDI ";
                    cmd.Parameters.Add("@IPTALEDILDI", MySqlDbType.Int32).Value = filter.isCancel;
                }

                if (filter.isSuccess > -1)
                {
                    if (filter.isSuccess == 1)
                        cmd.CommandText += " AND (m.CODE IN (125,200) OR m.RESULT = 'SUCCESS')";
                    else
                        cmd.CommandText += " AND m.RESULT = 'FAIL' ";
                }

                if (filter.Integrator.HasValue && filter.Integrator.Value > -1)
                {
                    cmd.CommandText += " AND m.ENTEGRATOR=@ENTEGRATOR ";
                    cmd.Parameters.Add("@ENTEGRATOR", MySqlDbType.Int32).Value = filter.Integrator;
                }

                if (filter.EInvoiceSort.HasValue && filter.EInvoiceSort.Value > 0)
                {
                    cmd.CommandText += " AND m.FATURATURU=@FATURATURU ";
                    cmd.Parameters.Add("@FATURATURU", MySqlDbType.Int32).Value = filter.EInvoiceSort;
                }
            }
        }
    }
}