using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class CampaignTicimaxDal : ICampaignTicimaxDal
    {
        private MySqlConnection _cnn;

        public CampaignTicimaxDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public Task AddAsync(CampaignTicimax entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(CampaignTicimax entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public async Task<int> GetCountAsync(CampaignTicimaxFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT (ID) FROM kampanya_ticimax WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return Convert.ToInt32(result);
        }

        public async Task<List<CampaignTicimax>> GetListAsync(CampaignTicimaxFilter filter = null, CampaignTicimaxPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT 
                                         ID
                                        ,TANIM
                                        ,ACIKLAMA
                                        ,ONCELIK
                                        ,TIP
                                        ,MINTUTAR
                                        ,MINURUNADEDI
                                        ,BASLANGICTARIHI
                                        ,BITISTARIHI
                                        ,ISLEMTIPI
                                        ,ISLEMDEGERI
                                        ,TEKRARDEGERI
                                        ,UYGULAMAYONTEMLERI
                                        ,KOSULLAR
                                        ,HEDIYELER
                                        ,MAKSSIPARISSAYISI
                                        ,SIPARISSAYISI
                                        ,UYEMAKSKULLANIMSAYISI
                                        ,SEPETTEACIKLAMAGOSTER
                                        ,KAMPANYASAYFASINDAGOSTER
                                        ,EKLEMETARIHI
                                        ,EKLEYENKULLANICI
                                        ,DUZENLEMETARIHI
                                        ,DUZENLEYENKULLANICI
                                        ,PARABIRIMI
                                FROM kampanya_ticimax WHERE 1 ";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            var list = new List<CampaignTicimax>();
            while (await reader.ReadAsync(cancellationToken))
                list.Add(new CampaignTicimax
                {
                    ID = reader["ID"].ToInt32(),
                    Definition = reader["TANIM"].ToString(),
                    Description = reader["ACIKLAMA"].ToString(),
                    Priority = reader["ONCELIK"].ToInt32(),
                    Type = reader["TIP"].ToInt32(),
                    MinAmount = reader["MINTUTAR"].ToDouble(),
                    MinProductCount = reader["MINURUNADEDI"].ToInt32(),
                    StartDate = reader["BASLANGICTARIHI"].ToDateTime(),
                    ExpireDate = reader["BITISTARIHI"].ToDateTime(),
                    ProcessType = reader["ISLEMTIPI"].ToInt32(),
                    ProcessValue = reader["ISLEMDEGERI"].ToDouble(),
                    RepeatablePiece = reader["TEKRARDEGERI"].ToInt32(),
                    AppMethods = reader["UYGULAMAYONTEMLERI"].ToJsonDeserialize<CampaignTicimax.ApplicationMethods>(),
                    Condition = reader["KOSULLAR"].ToJsonDeserialize<CampaignTicimax.Conditions>(),
                    Gift = reader["HEDIYELER"].ToJsonDeserialize<CampaignTicimax.Gifts>(),
                    MaxOrderCount = reader["MAKSSIPARISSAYISI"].ToInt32(),
                    OrderCount = reader["SIPARISSAYISI"].ToInt32(),
                    MaxCustomerUseCount = reader["UYEMAKSKULLANIMSAYISI"].ToInt32(),
                    BasketDescriptionVisible = reader["SEPETTEACIKLAMAGOSTER"].ToInt32(),
                    VisibleInCampaignPage = reader["KAMPANYASAYFASINDAGOSTER"].ToInt32(),
                    CreateDateTime = reader["EKLEMETARIHI"].ToDateTime(),
                    AddedUserID = reader["EKLEYENKULLANICI"].ToInt32(),
                    EditDateTime = reader["DUZENLEMETARIHI"].ToDateTime(),
                    EditUserID = reader["DUZENLEYENKULLANICI"].ToInt32(),
                    Currency = reader["PARABIRIMI"].ToString()
                });

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return list;
        }

        public Task UpdateAsync(CampaignTicimax entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }



        private void AppendFilter(ref MySqlCommand cmd, CampaignTicimaxFilter filter)
        {
            if (filter != null)
            {
            }
        }
    }
}