using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class MissingProductReasonDal : IMissingProductReasonDal
    {
        private MySqlConnection _cnn;

        public MissingProductReasonDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task AddAsync(MissingProductReason entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO eksik_urun_neden(
                                      TANIM
                                    , AKTIF
                                    , ISLEM )
                                VALUES (
                                      @TANIM
                                    , @AKTIF
                                    , @ISLEM )";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definiton;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = entity.isActive;
            cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = entity.Operation;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task<int> AddID(MissingProductReason entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO eksik_urun_neden(
                                      TANIM
                                    , AKTIF
                                    , ISLEM )
                                VALUES (
                                      @TANIM
                                    , @AKTIF
                                    , @ISLEM )";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definiton;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = entity.isActive;
            cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = entity.Operation;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            entity.ID = Convert.ToInt32(cmd.LastInsertedId);
            cmd.Dispose();
            _cnn.Close();
            return entity.ID;
        }

        public async Task<int> AddIDAsync(MissingProductReason entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO eksik_urun_neden(
                                      TANIM
                                    , AKTIF
                                    , ISLEM )
                                VALUES (
                                      @TANIM
                                    , @AKTIF
                                    , @ISLEM )";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definiton;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = entity.isActive;
            cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = entity.Operation;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            entity.ID = Convert.ToInt32(cmd.LastInsertedId);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return entity.ID;
        }

        public async Task DeleteAsync(MissingProductReason entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE eksik_urun_neden eun SET eun.AKTIF = 0 WHERE eun.ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task<int> GetCountAsync(MissingProductReasonFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(eun.ID) FROM eksik_urun_neden eun WHERE 1 ";
            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<List<MissingProductReason>> GetListAsync(MissingProductReasonFilter filter = null, MissingProductReasonPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<MissingProductReason> mprList = new List<MissingProductReason>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                      eun.ID,
                                      eun.TANIM,
                                      eun.AKTIF,
                                      eun.ISLEM FROM eksik_urun_neden eun WHERE 1 ";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                MissingProductReason mpr = new MissingProductReason();
                mpr.ID = reader["ID"].ToInt32();
                mpr.Definiton = reader["TANIM"].ToString();
                mpr.isActive = reader["AKTIF"].ToBoolean();
                mpr.Operation = reader["ISLEM"].ToInt32();
                mprList.Add(mpr);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return mprList;
        }

        public async Task UpdateAsync(MissingProductReason entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE eksik_urun_neden eun SET 
                                                                eun.TANIM = @TANIM,
                                                                eun.AKTIF = @AKTIF WHERE ID = @ID ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definiton;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = entity.isActive;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task UpdateOpeartion(MissingProductReason entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE eksik_urun_neden eun SET 
                                                                eun.ISLEM = @ISLEM WHERE ID = @ID ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = entity.Operation;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();
            _cnn.Close();
        }

        public async Task UpdateOpeartionAsync(MissingProductReason entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE eksik_urun_neden eun SET 
                                                                eun.ISLEM = @ISLEM WHERE ID = @ID ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = entity.Operation;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }



        private void AppendFilter(ref MySqlCommand cmd, MissingProductReasonFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID.HasValue)
                {
                    cmd.CommandText += " AND eun.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (!string.IsNullOrEmpty(filter.Definiton))
                {
                    cmd.CommandText += " AND eun.TANIM = @TANIM";
                    cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = filter.Definiton;
                }

                if (filter.isActive.HasValue)
                {
                    cmd.CommandText += " AND eun.AKTIF = @AKTIF";
                    cmd.Parameters.Add("@AKTIF", MySqlDbType.Bit).Value = filter.isActive;
                }

                if (filter.Operation.HasValue)
                {
                    cmd.CommandText += " AND eun.ISLEM = @ISLEM";
                    cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = filter.Operation;
                }
            }
        }
    }
}