using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseAddressDal : IWarehouseAddressDal
    {
        private MySqlConnection _cnn;

        public WarehouseAddressDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task SetDefault(WarehouseAddress entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE depo_adres AS da SET da.VARSAYILAN = 0 WHERE da.HEDEF_ID = @HEDEF_ID AND da.TIP = @TIP AND da.FATURA = @FATURA;
                                UPDATE depo_adres AS da SET da.VARSAYILAN = 1 WHERE da.ID = @ID;";

            cmd.Parameters.Add("@HEDEF_ID", MySqlDbType.Int32).Value = entity.TargetID;
            cmd.Parameters.Add("@FATURA", MySqlDbType.Int16).Value = entity.isInvoiceAddress;
            cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = entity.Type;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task<List<WarehouseAddress>> GetListAsync(WarehouseAddressFilter filter = null, WarehouseAddressPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<WarehouseAddress> warehouseAddresses = new List<WarehouseAddress>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText += @"SELECT   da.ID
                                        , da.HEDEF_ID
                                        , da.TIP
                                        , da.TANIM
                                        , da.TELEFON
                                        , da.ALICIADI
                                        , da.MAIL
                                        , da.ADRES
                                        , da.ULKE_ID
                                        , u.TANIM AS ULKE
                                        , da.IL_ID
                                        , il.TANIM AS IL
                                        , da.ILCE_ID
                                        , ilce.TANIM AS ILCE
                                        , da.POSTAKODU
                                        , da.VERGINO
                                        , da.VERGIDAIRESI
                                        , da.FATURA
                                        , da.AKTIF
                                        , da.VARSAYILAN
                                        , da.EKLENMETARIHI
                                        , da.EKLEYENKULLANICI_ID
                                        , da.DUZENLENMETARIHI
                                        , da.DUZENLEYENKULLANICI_ID
                                        FROM depo_adres AS da
                                        LEFT JOIN ulkeler AS u ON u.ID = da.ULKE_ID
                                        LEFT JOIN iller AS il ON il.ID = da.IL_ID
                                        LEFT JOIN ilceler AS ilce ON ilce.ID = da.ILCE_ID
                                        WHERE 1 ";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                warehouseAddresses.Add(
                    new WarehouseAddress
                    {
                        ID = mdr["ID"].ToInt32(),
                        TargetID = mdr["HEDEF_ID"].ToInt32(),
                        Type = mdr["TIP"].ToInt32(),
                        Definition = mdr["TANIM"].ToString(),
                        RecieverName = mdr["ALICIADI"].ToString(),
                        Mail = mdr["MAIL"].ToString(),
                        Telephone = mdr["TELEFON"].ToString(),
                        Address = mdr["ADRES"].ToString(),
                        CountryID = mdr["ULKE_ID"].ToInt32(),
                        CountryName = mdr["ULKE"].ToString(),
                        ProvinceID = mdr["IL_ID"].ToInt32(),
                        ProvinceName = mdr["IL"].ToString(),
                        DistrictID = mdr["ILCE_ID"].ToInt32(),
                        DistrictName = mdr["ILCE"].ToString(),
                        PostalCode = mdr["POSTAKODU"].ToString(),
                        TaxNumber = mdr["VERGINO"].ToString(),
                        TaxAdministration = mdr["VERGIDAIRESI"].ToString(),
                        isInvoiceAddress = mdr["FATURA"].ToBoolean(),
                        isActive = mdr["AKTIF"].ToBoolean(),
                        isDefault = mdr["VARSAYILAN"].ToBoolean(),
                        CreateDate = mdr["EKLENMETARIHI"].ToDateTime(),
                        CreateUserID = mdr["EKLEYENKULLANICI_ID"].ToInt32(),
                        ModifiedDate = mdr["DUZENLENMETARIHI"].ToDateTime(),
                        ModifiedUserID = mdr["DUZENLEYENKULLANICI_ID"] != DBNull.Value ? mdr["DUZENLEYENKULLANICI_ID"].ToInt32() : 0
                    });
            }

            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return warehouseAddresses;
        }

        public async Task AddAsync(WarehouseAddress entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_adres(HEDEF_ID
                                                     , TIP
                                                     , TANIM
                                                     , ALICIADI
                                                     , MAIL
                                                     , TELEFON
                                                     , ADRES
                                                     , ULKE_ID
                                                     , IL_ID
                                                     , ILCE_ID
                                                     , POSTAKODU
                                                     , VERGINO
                                                     , VERGIDAIRESI
                                                     , FATURA
                                                     , AKTIF
                                                     , EKLEYENKULLANICI_ID
                                                     , EKLENMETARIHI)
                                                VALUES(@HEDEF_ID
                                                     , @TIP
                                                     , @TANIM
                                                     , @ALICIADI
                                                     , @MAIL
                                                     , @TELEFON
                                                     , @ADRES
                                                     , @ULKE_ID
                                                     , @IL_ID
                                                     , @ILCE_ID
                                                     , @POSTAKODU
                                                     , @VERGINO
                                                     , @VERGIDAIRESI
                                                     , @FATURA
                                                     , @AKTIF
                                                     , @EKLEYENKULLANICI_ID
                                                     , NOW())";

            cmd.Parameters.Add("@HEDEF_ID", MySqlDbType.Int32).Value = entity.TargetID;
            cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = entity.Type;
            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@ALICIADI", MySqlDbType.VarChar).Value = entity.RecieverName;
            cmd.Parameters.Add("@MAIL", MySqlDbType.VarChar).Value = entity.Mail;
            cmd.Parameters.Add("@TELEFON", MySqlDbType.VarChar).Value = entity.Telephone;
            cmd.Parameters.Add("@ADRES", MySqlDbType.VarChar).Value = entity.Address;
            cmd.Parameters.Add("@ULKE_ID", MySqlDbType.Int32).Value = entity.CountryID;
            cmd.Parameters.Add("@IL_ID", MySqlDbType.Int32).Value = entity.ProvinceID;
            cmd.Parameters.Add("@ILCE_ID", MySqlDbType.Int32).Value = entity.DistrictID;
            cmd.Parameters.Add("@POSTAKODU", MySqlDbType.VarChar).Value = entity.PostalCode;
            cmd.Parameters.Add("@VERGINO", MySqlDbType.VarChar).Value = entity.TaxNumber;
            cmd.Parameters.Add("@VERGIDAIRESI", MySqlDbType.VarChar).Value = entity.TaxAdministration;
            cmd.Parameters.Add("@FATURA", MySqlDbType.Int16).Value = entity.isInvoiceAddress;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Int16).Value = entity.isActive;
            cmd.Parameters.Add("@EKLEYENKULLANICI_ID", MySqlDbType.Int32).Value = entity.CreateUserID;

            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task UpdateAsync(WarehouseAddress entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE depo_adres SET  TANIM = @TANIM
                                                     , ALICIADI = @ALICIADI
                                                     , MAIL = @MAIL
                                                     , TELEFON = @TELEFON
                                                     , ADRES = @ADRES
                                                     , ULKE_ID = @ULKE_ID
                                                     , IL_ID = @IL_ID
                                                     , ILCE_ID = @ILCE_ID
                                                     , POSTAKODU = @POSTAKODU
                                                     , VERGINO = @VERGINO
                                                     , VERGIDAIRESI = @VERGIDAIRESI
                                                     , FATURA = @FATURA
                                                     , AKTIF = @AKTIF
                                                     , DUZENLENMETARIHI = NOW()
                                                     , DUZENLEYENKULLANICI_ID = @DUZENLEYENKULLANICI_ID
                                                     , VARSAYILAN = @VARSAYILAN
                                                     WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@ALICIADI", MySqlDbType.VarChar).Value = entity.RecieverName;
            cmd.Parameters.Add("@MAIL", MySqlDbType.VarChar).Value = entity.Mail;
            cmd.Parameters.Add("@TELEFON", MySqlDbType.VarChar).Value = entity.Telephone;
            cmd.Parameters.Add("@ADRES", MySqlDbType.VarChar).Value = entity.Address;
            cmd.Parameters.Add("@ULKE_ID", MySqlDbType.Int32).Value = entity.CountryID;
            cmd.Parameters.Add("@IL_ID", MySqlDbType.Int32).Value = entity.ProvinceID;
            cmd.Parameters.Add("@ILCE_ID", MySqlDbType.Int32).Value = entity.DistrictID;
            cmd.Parameters.Add("@POSTAKODU", MySqlDbType.VarChar).Value = entity.PostalCode;
            cmd.Parameters.Add("@VERGINO", MySqlDbType.VarChar).Value = entity.TaxNumber;
            cmd.Parameters.Add("@VERGIDAIRESI", MySqlDbType.VarChar).Value = entity.TaxAdministration;
            cmd.Parameters.Add("@FATURA", MySqlDbType.Int16).Value = entity.isInvoiceAddress;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Int16).Value = entity.isActive;
            cmd.Parameters.Add("@DUZENLEYENKULLANICI_ID", MySqlDbType.Int32).Value = entity.CreateUserID;
            cmd.Parameters.Add("@VARSAYILAN", MySqlDbType.Int16).Value = entity.isDefault;

            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task DeleteAsync(WarehouseAddress entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM depo_adres WHERE ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task<int> GetCountAsync(WarehouseAddressFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(ID)
                                FROM depo_adres AS da
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return count;
        }

        public async Task SetDefaultAsync(WarehouseAddress entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE depo_adres AS da SET da.VARSAYILAN = 0 WHERE da.HEDEF_ID = @HEDEF_ID AND da.TIP = @TIP AND da.FATURA = @FATURA;
                                UPDATE depo_adres AS da SET da.VARSAYILAN = 1 WHERE da.ID = @ID;";

            cmd.Parameters.Add("@HEDEF_ID", MySqlDbType.Int32).Value = entity.TargetID;
            cmd.Parameters.Add("@FATURA", MySqlDbType.Int16).Value = entity.isInvoiceAddress;
            cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = entity.Type;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        private void AppendFilter(ref MySqlCommand cmd, WarehouseAddressFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND da.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (filter.TargetID > 0)
                {
                    cmd.CommandText += " AND da.HEDEF_ID = @HEDEF_ID";
                    cmd.Parameters.Add("@HEDEF_ID", MySqlDbType.Int32).Value = filter.TargetID;
                }

                if (filter.Type > 0)
                {
                    cmd.CommandText += " AND da.TIP = @TIP";
                    cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = filter.Type;
                }

                if (filter.AddressType > 0)
                {
                    cmd.CommandText += " AND da.FATURA = @FATURA";
                    cmd.Parameters.Add("@FATURA", MySqlDbType.Bit).Value = filter.AddressType == 1;
                }
            }
        }
    }
}