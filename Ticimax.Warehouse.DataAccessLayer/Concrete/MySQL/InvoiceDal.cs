using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class InvoiceDal : IInvoiceDal
    {
        private readonly MySqlConnection _cnn;

        public InvoiceDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task AddAsync(Entities.Concrete.Invoice entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO ticimax_fatura (
                                                             SERI
                                                            ,SIRA
                                                            ,SIPARIS_ID
                                                            ,TARIH)
                                                    VALUES (
                                                            @SERI
                                                            ,IFNULL(
                                                                (SELECT * FROM (SELECT (SIRA+1) FROM ticimax_fatura WHERE SERI = @SERI ORDER BY SIRA DESC LIMIT 1) AS tmp)
                                                                ,1)
                                                            ,@SIPARIS_ID
                                                            ,@TARIH);";

            cmd.Parameters.Add("@SERI", MySqlDbType.VarChar).Value = entity.Series;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = entity.Date;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task Create(Entities.Concrete.Invoice entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO ticimax_fatura (
                                                             SERI
                                                            ,SIRA
                                                            ,SIPARIS_ID
                                                            ,TARIH
                                                            ,ICERIK
                                                            ,SABLON
                                                            ,SABLONAYAR)
                                                    VALUES (
                                                            @SERI
                                                            ,IFNULL(
                                                                (SELECT * FROM (SELECT (SIRA+1) FROM ticimax_fatura WHERE SERI = @SERI ORDER BY SIRA DESC LIMIT 1) AS tmp)
                                                                ,1)
                                                            ,@SIPARIS_ID
                                                            ,@TARIH
                                                            ,@ICERIK
                                                            ,@SABLON
                                                            ,@SABLONAYAR);";

            cmd.Parameters.Add("@SERI", MySqlDbType.VarChar).Value = entity.Series;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = entity.Date;
            cmd.Parameters.Add("@ICERIK", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.Content));
            cmd.Parameters.Add("@SABLON", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.Template));
            cmd.Parameters.Add("@SABLONAYAR", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.TemplateSettings.ToJsonSerialize()));
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task CreateAsync(Entities.Concrete.Invoice entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO ticimax_fatura (
                                                             SERI
                                                            ,SIRA
                                                            ,SIPARIS_ID
                                                            ,TARIH
                                                            ,ICERIK
                                                            ,SABLON
                                                            ,SABLONAYAR)
                                                    VALUES (
                                                            @SERI
                                                            ,IFNULL(
                                                                (SELECT * FROM (SELECT (SIRA+1) FROM ticimax_fatura WHERE SERI = @SERI ORDER BY SIRA DESC LIMIT 1) AS tmp)
                                                                ,1)
                                                            ,@SIPARIS_ID
                                                            ,@TARIH
                                                            ,@ICERIK
                                                            ,@SABLON
                                                            ,@SABLONAYAR);";

            cmd.Parameters.Add("@SERI", MySqlDbType.VarChar).Value = entity.Series;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = entity.Date;
            cmd.Parameters.Add("@ICERIK", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.Content));
            cmd.Parameters.Add("@SABLON", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.Template));
            cmd.Parameters.Add("@SABLONAYAR", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.TemplateSettings.ToJsonSerialize()));
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(InvoiceFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM ticimax_fatura
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return count;
        }

        public async Task<List<Entities.Concrete.Invoice>> GetListAsync(InvoiceFilter filter = null, InvoicePaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<Entities.Concrete.Invoice> liste = new List<Entities.Concrete.Invoice>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                  SERI,
                                  SIRA,
                                  SIPARIS_ID,
                                  ICERIK,
                                  SABLON,
                                  SABLONAYAR,
                                  TARIH,
                                  IPTAL
                                FROM
                                  ticimax_fatura WHERE 1 ";

            AppendFilter(ref cmd, filter);

            if (paging != null)
            {
                if (!string.IsNullOrEmpty(paging.SortingValue))
                    cmd.CommandText += $" ORDER BY {paging.SortingValue}";

                if (!string.IsNullOrEmpty(paging.SortingDirection))
                    cmd.CommandText += $"  {paging.SortingDirection}";
            }

            PagingExtension.AppendPaging(ref cmd, paging);
          

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                Entities.Concrete.Invoice p = new Entities.Concrete.Invoice();
                p.Series = Reader["SERI"].ToString();
                p.Rank = Reader["SIRA"].ToInt32();
                p.OrderID = Reader["SIPARIS_ID"].ToInt32();
                p.Content = ZipExtension.Unzip(Convert.FromBase64String(Reader["ICERIK"].ToString()));
                p.Template = ZipExtension.Unzip(Convert.FromBase64String(Reader["SABLON"].ToString()));
                p.TemplateSettings = ZipExtension.Unzip(Convert.FromBase64String(Reader["SABLONAYAR"].ToString())).ToJsonDeserialize<Invoice.DTO.Models.Invoice.Settings>();
                p.Date = Reader["TARIH"].ToDateTime();
                p.isCancel = Reader["IPTAL"].ToBoolean();
                liste.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return liste;
        }

        public async Task UpdateAsync(Entities.Concrete.Invoice entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE ticimax_fatura SET ICERIK = @ICERIK, SABLON = @SABLON, SABLONAYAR = @SABLONAYAR, TARIH = @TARIH WHERE SERI = @SERI AND SIRA = @SIRA";
            cmd.Parameters.Add("@ICERIK", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.Content));
            cmd.Parameters.Add("@SABLON", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.Template));
            cmd.Parameters.Add("@SABLONAYAR", MySqlDbType.Text).Value = Convert.ToBase64String(ZipExtension.Zip(entity.TemplateSettings.ToJsonSerialize()));
            cmd.Parameters.Add("@SIRA", MySqlDbType.Int32).Value = entity.Rank;
            cmd.Parameters.Add("@SERI", MySqlDbType.VarChar).Value = entity.Series;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = entity.Date;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateCancel(Entities.Concrete.Invoice entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE ticimax_fatura SET IPTAL = @IPTAL WHERE SIPARIS_ID = @SIPARIS_ID";
            cmd.Parameters.Add("@IPTAL", MySqlDbType.Bit).Value = entity.isCancel;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateCancelAsync(Entities.Concrete.Invoice entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE ticimax_fatura SET IPTAL = @IPTAL WHERE SIPARIS_ID = @SIPARIS_ID";
            cmd.Parameters.Add("@IPTAL", MySqlDbType.Bit).Value = entity.isCancel;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            await cmd.ExecuteTransactionCommandAsync();
        }



        private void AppendFilter(ref MySqlCommand cmd, InvoiceFilter filter)
        {
            if (filter != null)
            {
                if (!string.IsNullOrEmpty(filter.Series))
                {
                    cmd.CommandText += " AND SERI = @SERI";
                    cmd.Parameters.Add("@SERI", MySqlDbType.VarChar).Value = filter.Series;
                }

                if (filter.Rank.HasValue)
                {
                    cmd.CommandText += " AND SIRA = @SIRA";
                    cmd.Parameters.Add("@SIRA", MySqlDbType.Int32).Value = filter.Rank.Value;
                }

                if (filter.OrderID.HasValue)
                {
                    cmd.CommandText += " AND SIPARIS_ID = @SIPARIS_ID";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderID.Value;
                }
                else if (filter.OrderIDList != null && filter.OrderIDList.Count > 0)
                {
                    cmd.CommandText += $" AND SIPARIS_ID IN ({string.Join(",", filter.OrderIDList)})";
                }

                if (filter.DateStart.HasValue)
                {
                    cmd.CommandText += " AND TARIH >= @TARIHBAS";
                    cmd.Parameters.Add("@TARIHBAS", MySqlDbType.DateTime).Value = filter.DateStart.Value;
                }

                if (filter.DateFinish.HasValue)
                {
                    cmd.CommandText += " AND TARIH < @TARIHSON";
                    cmd.Parameters.Add("@TARIHSON", MySqlDbType.DateTime).Value = filter.DateFinish.Value;
                }

                if (filter.isCancel.HasValue)
                {
                    cmd.CommandText += " AND IPTAL = @IPTAL";
                    cmd.Parameters.Add("@IPTAL", MySqlDbType.Int16).Value = filter.isCancel.Value;
                }
            }
        }
    }
}