using Microsoft.AspNetCore.Http;
using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseCarProductDal : IWarehouseCarProductDal
    {
        private MySqlConnection _cnn;
        private readonly HttpContext _httpContext;

        public WarehouseCarProductDal(IDbConnection cnn, IHttpContextAccessor contextAccessor)
        {
            _cnn = (MySqlConnection)cnn;
            _httpContext = contextAccessor.HttpContext;
        }
        
        public async Task DeleteCarAllProduct(WarehouseCarProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            if (entity.WarehouseCarID > 0)
            {
                cmd.CommandText = "DELETE dau FROM depo_araba_urun AS dau WHERE dau.ARABA_ID = @ARABA_ID ";
                cmd.Parameters.Add("ARABA_ID", MySqlDbType.Int32).Value = entity.WarehouseCarID;
            }
            else if (entity.OrderID > 0)
            {
                cmd.CommandText = "DELETE dau FROM depo_araba_urun AS dau WHERE dau.SIPARIS_ID = @SIPARIS_ID ";
                cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            }
            else if (entity.OrderIDs != null && entity.OrderIDs.Count > 0)
            {
                cmd.CommandText = $"DELETE dau FROM depo_araba_urun AS dau WHERE dau.SIPARIS_ID IN ({string.Join(",", entity.OrderIDs)}) ";
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<WarehouseCarProduct>> GetListAsync(WarehouseCarProductFilter filter = null, WarehouseCarProductPaging paging = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<WarehouseCarProduct> warehouseCarProducts = new List<WarehouseCarProduct>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT dau.ID
                                      ,dau.ARABA_ID
                                      ,dau.SIPARIS_ID
                                      ,dau.URUN_ID
                                      ,dau.ADET
                                      ,dau.AYARLAR
                                      ,u.STOKKODU
                                      ,u.BARKOD
                                      ,u.URUNKARTI_ID
                                      ,uk.RESIM1
                                      ,uk.URUNADI
                                      ,da.TANIM AS ARABATANIM
                                      ,da.BARKOD AS ARABABARKOD
                                      , (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
	                                        FROM urun_eksecenek AS ues
	                                        WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA) AS VARYASYON
                                      FROM depo_araba_urun AS dau
                                      INNER JOIN urunler AS u ON
                                      u.ID = dau.URUN_ID
                                      LEFT JOIN urun_barkod AS ub ON
                                      ub.URUN_ID = dau.URUN_ID
                                      INNER JOIN urun_karti AS uk ON
                                      uk.ID = u.URUNKARTI_ID
                                      INNER JOIN depo_araba AS da ON
                                      da.ID = dau.ARABA_ID
                                      WHERE 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                var settings = mdr["AYARLAR"].ToJsonDeserialize<WarehouseCarProductSettings>();
                warehouseCarProducts.Add(new WarehouseCarProduct
                {
                    ID = mdr["ID"].ToInt32(),
                    WarehouseCarID = mdr["ARABA_ID"].ToInt32(),
                    WarehouseCarBarcode = mdr["ARABABARKOD"].ToString(),
                    WarehouseCarDefination = mdr["ARABATANIM"].ToString(),
                    OrderID = mdr["SIPARIS_ID"].ToInt32(),
                    ProductCardID = mdr["URUNKARTI_ID"].ToInt32(),
                    ProductID = mdr["URUN_ID"].ToInt32(),
                    Barcode = mdr["BARKOD"].ToString(),
                    Image = WebSiteInfo.User.Value.ImagePath + mdr["RESIM1"].ToString(),
                    Piece = mdr["ADET"].ToDouble(),
                    ProductName = mdr["URUNADI"] + " " + mdr["VARYASYON"],
                    StockCode = mdr["STOKKODU"].ToString(),
                    Settings = mdr["AYARLAR"].ToJsonDeserialize<WarehouseCarProductSettings>(),
                    ApprovelStatus = mdr["AYARLAR"].ToJsonDeserialize<WarehouseCarProductSettings>().approvalStatus
                });
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return warehouseCarProducts;
        }

        public async Task AddAsync(WarehouseCarProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO depo_araba_urun(
                                                            ARABA_ID
                                                           ,SIPARIS_ID
                                                           ,URUN_ID
                                                           ,ADET
                                                           ,AYARLAR)
                                                     VALUES(
                                                            @ARABA_ID
                                                           ,@SIPARIS_ID
                                                           ,@URUN_ID
                                                           ,@ADET
                                                           ,@AYARLAR)";

            cmd.Parameters.Add("ARABA_ID", MySqlDbType.Int32).Value = entity.WarehouseCarID;
            cmd.Parameters.Add("SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("URUN_ID", MySqlDbType.Int32).Value = entity.ProductID;
            cmd.Parameters.Add("ADET", MySqlDbType.Double).Value = entity.Piece;
            cmd.Parameters.Add("AYARLAR", MySqlDbType.Text).Value = entity.Settings.ToJsonSerialize();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateAsync(WarehouseCarProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_araba_urun AS dau
                                INNER JOIN depo_araba AS da ON dau.ARABA_ID = da.ID
                                                            SET dau.ARABA_ID = @ARABA_ID
                                                           ,dau.SIPARIS_ID = @SIPARIS_ID
                                                           ,dau.URUN_ID = @URUN_ID
                                                           ,dau.ADET = @ADET
                                                           ,dau.AYARLAR = @AYARLAR
                                                        WHERE dau.ID = @ID ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@ARABA_ID", MySqlDbType.Int32).Value = entity.WarehouseCarID;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.ProductID;
            cmd.Parameters.Add("@ADET", MySqlDbType.Double).Value = entity.Piece;
            cmd.Parameters.Add("@AYARLAR", MySqlDbType.Text).Value = entity.Settings.ToJsonSerialize();
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND da.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(WarehouseCarProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"DELETE dau FROM depo_araba_urun AS dau
                                INNER JOIN depo_araba AS da ON dau.ARABA_ID = da.ID
                                WHERE dau.ID = @ID ";

            cmd.Parameters.Add("ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND da.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(WarehouseCarProductFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM depo_araba_urun as dau
                                INNER JOIN depo_araba AS da ON da.ID = dau.ARABA_ID
                                LEFT JOIN urunler AS u ON u.ID = dau.URUN_ID
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task DeleteCarAllProductAsync(WarehouseCarProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            if (entity.WarehouseCarID > 0)
            {
                cmd.CommandText = "DELETE dau FROM depo_araba_urun AS dau INNER JOIN depo_araba AS da ON da.ID = dau.ARABA_ID WHERE dau.ARABA_ID = @ARABA_ID ";
                cmd.Parameters.Add("ARABA_ID", MySqlDbType.Int32).Value = entity.WarehouseCarID;
            }
            else if (entity.OrderID > 0)
            {
                cmd.CommandText = "DELETE dau FROM depo_araba_urun AS dau INNER JOIN depo_araba AS da ON da.ID = dau.ARABA_ID WHERE dau.SIPARIS_ID = @SIPARIS_ID ";

                cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            }
            else if (entity.OrderIDs != null && entity.OrderIDs.Count > 0)
            {
                cmd.CommandText = $"DELETE dau FROM depo_araba_urun AS dau INNER JOIN depo_araba AS da ON da.ID = dau.ARABA_ID WHERE dau.SIPARIS_ID IN ({string.Join(",", entity.OrderIDs)}) ";
            }

            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND da.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SettingsUpdateAsync(WarehouseCarProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_araba_urun AS dau
                                INNER JOIN depo_araba AS da ON dau.ARABA_ID = da.ID
                                                            SET dau.AYARLAR = @AYARLAR WHERE dau.ID = @ID ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@AYARLAR", MySqlDbType.Text).Value = entity.Settings.ToJsonSerialize();
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND da.DEPO_ID = @DEPO_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }


        private void AppendFilter(ref MySqlCommand cmd, WarehouseCarProductFilter filter)
        {
            if (filter != null)
            {
                if (!WebSiteInfo.User.Value.IsOneStore)
                {
                    cmd.CommandText += " AND da.DEPO_ID = @DEPO_ID ";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                }

                if (filter.Id > 0)
                {
                    cmd.CommandText += " AND dau.ID = @ID ";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.Id;
                }

                if (filter.OrderID > 0)
                {
                    cmd.CommandText += " AND dau.SIPARIS_ID = @SIPARIS_ID ";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderID;
                }

                if (filter.WarehouseCarID > 0)
                {
                    cmd.CommandText += " AND dau.ARABA_ID = @ARABAID";
                    cmd.Parameters.Add("@ARABAID", MySqlDbType.Int32).Value = filter.WarehouseCarID;
                }

                if (filter.ProductID > 0)
                {
                    cmd.CommandText += " AND dau.URUN_ID = @URUN_ID";
                    cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = filter.ProductID;
                }

                if (!string.IsNullOrEmpty(filter.ProductBarcode) && cmd.CommandText.Contains("urunler"))
                {
                    cmd.CommandText += " AND u.BARKOD = @BARKOD";
                    cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.ProductBarcode;
                    cmd.CommandText += " OR ub.BARKOD = @BARCODE";
                    cmd.Parameters.Add("@BARCODE", MySqlDbType.VarChar).Value = filter.ProductBarcode;
                }

                if (filter.TargetIDs != null && filter.TargetIDs.Count > 0)
                    cmd.CommandText += $" AND dau.URUN_ID IN ({string.Join(",", filter.TargetIDs)})";

                if (filter.IsGetNotApproval.HasValue)
                {
                    cmd.CommandText += $" AND dau.AYARLAR LIKE '%\"approvalStatus\":{filter.IsGetNotApproval}%'";
                    cmd.CommandText += $" AND dau.AYARLAR LIKE '%\"isMissingProduct\":true%'";
                }

                if (!string.IsNullOrEmpty(filter.FileId))
                {
                    cmd.CommandText += $" AND JSON_EXTRACT(AYARLAR, '$.fileId') = '{filter.FileId}' ";
                }

                if (filter.IsWarehouseTransfer)
                {
                    cmd.CommandText += " AND dau.SIPARIS_ID = @SIPARIS_ID_WAREHOUSE ";
                    cmd.Parameters.Add("@SIPARIS_ID_WAREHOUSE", MySqlDbType.Int32).Value = 0;
                }
            }
        }
    }
}