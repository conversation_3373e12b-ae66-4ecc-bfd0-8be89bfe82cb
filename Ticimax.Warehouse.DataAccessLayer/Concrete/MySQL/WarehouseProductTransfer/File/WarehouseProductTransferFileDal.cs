using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL.WarehouseProductTransfer.File
{
#nullable enable
    public class WarehouseProductTransferFileDal : IWarehouseProductTransferFileDal
    {
        private MySqlConnection _cnn;

        public WarehouseProductTransferFileDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<WarehouseProductTransferFileAggregate> GetById(Guid id, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            WarehouseProductTransferFileAggregate aggregate = null;

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT ID, NAME, FILE_NO, STATUS, NOTE, WAREHOUSE_CAR_ID, TOTAL_PRODUCT_QUANTITY, USER_ID, STORE_ID, WAREHOUSE_ID, TRANSFER_STORE_ID, TRANSFER_WAREHOUSE_ID, CREATED_DATE, LAST_MODIFIED_DATE FROM depo_transfer_dosya WHERE ID = UUID_TO_BIN(@ID)";
            cmd.Parameters.Add("@ID", MySqlDbType.Guid).Value = id;

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                aggregate = new WarehouseProductTransferFileAggregate();
                aggregate.Id = mdr["ID"].ByteArrayToGuid();
                aggregate.Name = mdr["NAME"].ToString();
                aggregate.FileNo = mdr["FILE_NO"].ToString();
                aggregate.Status = mdr["STATUS"].ToString();
                aggregate.Note = mdr["NOTE"].ToString();
                aggregate.WarehouseCarId = mdr["WAREHOUSE_CAR_ID"].ToInt32();
                aggregate.UserId = mdr["USER_ID"].ToInt32();
                aggregate.TotalProductQuantity = mdr["TOTAL_PRODUCT_QUANTITY"].ToInt32();
                aggregate.StoreId = mdr["STORE_ID"].ToInt32();
                aggregate.WarehouseId = mdr["WAREHOUSE_ID"].ToInt32();
                aggregate.TransferStoreId = mdr["TRANSFER_STORE_ID"].ToInt32();
                aggregate.TransferWarehouseId = mdr["TRANSFER_WAREHOUSE_ID"].ToInt32();
                aggregate.CreatedDate = mdr["CREATED_DATE"].ToInt64();
                aggregate.LastModifiedDate = mdr["LAST_MODIFIED_DATE"].ToInt64();
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return aggregate;
        }

        public async Task<List<WarehouseProductTransferFileAggregate>> Get(string? name, string? fileNo, string? status, int? createdUserId, List<int>? productIds, List<string>? statusList, long? time, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<WarehouseProductTransferFileAggregate> aggregates = new List<WarehouseProductTransferFileAggregate>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            cmd.CommandText = "SELECT ID, NAME, FILE_NO, STATUS, NOTE, WAREHOUSE_CAR_ID, TOTAL_PRODUCT_QUANTITY, USER_ID, STORE_ID, WAREHOUSE_ID, TRANSFER_STORE_ID, TRANSFER_WAREHOUSE_ID, CREATED_DATE, LAST_MODIFIED_DATE, CREATED_USER_ID, CREATED_USER_NAME FROM depo_transfer_dosya WHERE 1 ";


            AppendFilter(ref cmd, name, fileNo, status, createdUserId, productIds, statusList, time, false);

            cmd.CommandText += " ORDER BY CREATED_DATE DESC ";

            PagingExtension.AppendPaging(ref cmd, new Paging { PageNo = pageIndex, RecordNumber = pageSize });

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                var aggregate = new WarehouseProductTransferFileAggregate();
                aggregate.Id = mdr["ID"].ByteArrayToGuid();
                aggregate.Name = mdr["NAME"].ToString();
                aggregate.FileNo = mdr["FILE_NO"].ToString();
                aggregate.Status = mdr["STATUS"].ToString();
                aggregate.Note = mdr["NOTE"].ToString();
                aggregate.WarehouseCarId = mdr["WAREHOUSE_CAR_ID"].ToInt32();
                aggregate.UserId = mdr["USER_ID"].ToInt32();
                aggregate.TotalProductQuantity = mdr["TOTAL_PRODUCT_QUANTITY"].ToInt32();
                aggregate.StoreId = mdr["STORE_ID"].ToInt32();
                aggregate.WarehouseId = mdr["WAREHOUSE_ID"].ToInt32();
                aggregate.TransferStoreId = mdr["TRANSFER_STORE_ID"].ToInt32();
                aggregate.TransferWarehouseId = mdr["TRANSFER_WAREHOUSE_ID"].ToInt32();
                aggregate.CreatedDate = mdr["CREATED_DATE"].ToInt64();
                aggregate.LastModifiedDate = mdr["LAST_MODIFIED_DATE"].ToInt64();
                aggregate.CreatedUserId = mdr["CREATED_USER_ID"] != DBNull.Value ? mdr["CREATED_USER_ID"].ToInt32() : 0;
                aggregate.CreatedUserName = mdr["CREATED_USER_NAME"].ToString();
                aggregates.Add(aggregate);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return aggregates;
        }

        public async Task<int> Count(string? name, string? fileNo, string? status, int? createdUserId, List<int>? productIds, List<string> statusList, long? time, bool? isAdminDashboard, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT COUNT(*) FROM depo_transfer_dosya WHERE 1 ";
            AppendFilter(ref cmd, name, fileNo, status, createdUserId, productIds, statusList, time, isAdminDashboard);

            var count = await cmd.ExecuteScalarAsync(cancellationToken);

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return count.ToInt32();
        }

        public async Task Create(WarehouseProductTransferFileAggregate aggregate, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText =
                "INSERT INTO depo_transfer_dosya(ID,NAME,FILE_NO,STATUS,NOTE,WAREHOUSE_CAR_ID,TOTAL_PRODUCT_QUANTITY,USER_ID,STORE_ID,WAREHOUSE_ID,TRANSFER_STORE_ID,TRANSFER_WAREHOUSE_ID,CREATED_DATE,LAST_MODIFIED_DATE,CREATED_USER_ID,CREATED_USER_NAME) VALUES(UUID_TO_BIN(@ID),@NAME,@FILE_NO,@STATUS,@NOTE,@WAREHOUSE_CAR_ID,@TOTAL_PRODUCT_QUANTITY,@USER_ID,@STORE_ID,@WAREHOUSE_ID,@TRANSFER_STORE_ID,@TRANSFER_WAREHOUSE_ID,@CREATED_DATE,@LAST_MODIFIED_DATE,@CREATED_USER_ID,@CREATED_USER_NAME)";

            cmd.Parameters.Add("@ID", MySqlDbType.Guid).Value = aggregate.Id;
            cmd.Parameters.Add("@NAME", MySqlDbType.VarChar).Value = aggregate.Name;
            cmd.Parameters.Add("@FILE_NO", MySqlDbType.VarChar).Value = aggregate.FileNo;
            cmd.Parameters.Add("@STATUS", MySqlDbType.VarChar).Value = aggregate.Status;
            cmd.Parameters.Add("@NOTE", MySqlDbType.VarChar).Value = aggregate.Note;
            cmd.Parameters.Add("@WAREHOUSE_CAR_ID", MySqlDbType.Int32).Value = aggregate.WarehouseCarId;
            cmd.Parameters.Add("@TOTAL_PRODUCT_QUANTITY", MySqlDbType.Double).Value = aggregate.TotalProductQuantity;
            cmd.Parameters.Add("@USER_ID", MySqlDbType.Int32).Value = aggregate.UserId;
            cmd.Parameters.Add("@STORE_ID", MySqlDbType.Int32).Value = aggregate.StoreId;
            cmd.Parameters.Add("@WAREHOUSE_ID", MySqlDbType.Int32).Value = aggregate.WarehouseId;
            cmd.Parameters.Add("@TRANSFER_STORE_ID", MySqlDbType.Int32).Value = aggregate.TransferStoreId;
            cmd.Parameters.Add("@TRANSFER_WAREHOUSE_ID", MySqlDbType.Int32).Value = aggregate.TransferWarehouseId;
            cmd.Parameters.Add("@CREATED_DATE", MySqlDbType.Int64).Value = aggregate.CreatedDate;
            cmd.Parameters.Add("@LAST_MODIFIED_DATE", MySqlDbType.Int64).Value = aggregate.LastModifiedDate;
            cmd.Parameters.Add("@CREATED_USER_ID", MySqlDbType.Int64).Value = aggregate.CreatedUserId;
            cmd.Parameters.Add("@CREATED_USER_NAME", MySqlDbType.VarChar).Value = aggregate.CreatedUserName;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task Update(WarehouseProductTransferFileAggregate aggregate, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE depo_transfer_dosya SET NAME = @NAME, STATUS = @STATUS, NOTE = @NOTE, WAREHOUSE_CAR_ID = @WAREHOUSE_CAR_ID, TOTAL_PRODUCT_QUANTITY = @TOTAL_PRODUCT_QUANTITY, USER_ID = @USER_ID, LAST_MODIFIED_DATE = @LAST_MODIFIED_DATE WHERE ID = UUID_TO_BIN(@ID)";
            cmd.Parameters.Add("@ID", MySqlDbType.Guid).Value = aggregate.Id;
            cmd.Parameters.Add("@NAME", MySqlDbType.VarChar).Value = aggregate.Name;
            cmd.Parameters.Add("@NOTE", MySqlDbType.VarChar).Value = aggregate.Note;
            cmd.Parameters.Add("@STATUS", MySqlDbType.VarChar).Value = aggregate.Status;
            cmd.Parameters.Add("@WAREHOUSE_CAR_ID", MySqlDbType.Int32).Value = aggregate.WarehouseCarId;
            cmd.Parameters.Add("@TOTAL_PRODUCT_QUANTITY", MySqlDbType.Double).Value = aggregate.TotalProductQuantity;
            cmd.Parameters.Add("@USER_ID", MySqlDbType.Int32).Value = aggregate.UserId;
            cmd.Parameters.Add("@LAST_MODIFIED_DATE", MySqlDbType.Int64).Value = aggregate.LastModifiedDate;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task Delete(Guid id, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "DELETE FROM depo_transfer_dosya WHERE ID = UUID_TO_BIN(@ID)";
            cmd.Parameters.Add("@ID", MySqlDbType.Guid).Value = id;
            await cmd.ExecuteTransactionCommandAsync();
        }

        private void AppendFilter(ref MySqlCommand cmd, string? name, string? fileNo, string? status, int? createdUserId, List<int>? productIds, List<string>? statusList, long? time, bool? isAdminDashboard)
        {
            if(isAdminDashboard.HasValue && !isAdminDashboard.Value)
            {
                cmd.CommandText += " AND (WAREHOUSE_ID = @WAREHOUSE_ID OR TRANSFER_WAREHOUSE_ID = @WAREHOUSE_ID)";
                cmd.Parameters.Add("@WAREHOUSE_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }
            if (name != null)
            {
                cmd.CommandText += " AND NAME LIKE @NAME";
                cmd.Parameters.Add("@NAME", MySqlDbType.VarChar).Value = "%" + name + "%";
            }

            if (fileNo != null)
            {
                cmd.CommandText += " AND FILE_NO = @FILE_NO ";
                cmd.Parameters.Add("@FILE_NO", MySqlDbType.VarChar).Value = fileNo;
            }

            if (status != null)
            {
                cmd.CommandText += " AND STATUS = @STATUS";
                cmd.Parameters.Add("@STATUS", MySqlDbType.VarChar).Value = status;
            }
            if (createdUserId.HasValue)
            {
                cmd.CommandText += " AND CREATED_USER_ID = @CREATED_USER_ID";
                cmd.Parameters.Add("@CREATED_USER_ID", MySqlDbType.Int32).Value = createdUserId.Value;
            }

            if (productIds != null && productIds.Count > 0)
            {
                cmd.CommandText += $" AND ID IN (SELECT FILE_ID FROM depo_transfer_urun AS dpu WHERE dpu.PRODUCT_ID IN ({string.Join(",", productIds)}))";
            }

            if (statusList != null && statusList.Count > 0)
            {
                List<string> queryStatusList = statusList.Select(s => $"'{s}'").ToList();
                cmd.CommandText += $" AND STATUS IN ({string.Join(",", queryStatusList)})";
            }

            if (time.HasValue)
            {
                cmd.CommandText += " AND CREATED_DATE >= @CREATED_DATE";
                cmd.Parameters.Add("@CREATED_DATE", MySqlDbType.Int64).Value = time.Value;
            }
        }
    }
}