using MySqlConnector;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete
{
    public class WarehouseAuthGroupDal : IWarehouseAuthGroupDal
    {
        private readonly MySqlConnection _cnn;

        public WarehouseAuthGroupDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task AddAsync(WarehouseAuthGroup entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO depo_yetki_gruplari(TANIM,VARSAYILAN)
                                                         VALUES(@TANIM,@VARSAYILAN);";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Defination;
            cmd.Parameters.Add("@VARSAYILAN", MySqlDbType.Int16).Value = entity.isDefault;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> AddReturnId(WarehouseAuthGroup entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO depo_yetki_gruplari(TANIM,VARSAYILAN)
                                                         VALUES(@TANIM,@VARSAYILAN);";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Defination;
            cmd.Parameters.Add("@VARSAYILAN", MySqlDbType.Int16).Value = entity.isDefault;
            await cmd.ExecuteTransactionCommandAsync();

            cmd = new MySqlCommand($@"SET @@SESSION.information_schema_stats_expiry = 0; 
                                        SELECT AUTO_INCREMENT
                                        FROM  INFORMATION_SCHEMA.TABLES
                                        WHERE TABLE_SCHEMA = '{WebSiteInfo.User.Value?.DomainName?.Replace(".", "") ?? Info.DomainName?.Value.Replace(".", "")}'
                                        AND TABLE_NAME = 'depo_yetki_gruplari';", _cnn);

            int addedID = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();

            await cmd.DisposeAsync();

            return addedID;
        }

        public async Task<int> AddReturnIdAsync(WarehouseAuthGroup entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO depo_yetki_gruplari(TANIM,VARSAYILAN)
                                                         VALUES(@TANIM,@VARSAYILAN);";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Defination;
            cmd.Parameters.Add("@VARSAYILAN", MySqlDbType.Int16).Value = entity.isDefault;
            await cmd.ExecuteTransactionCommandAsync();

            cmd = new MySqlCommand($@"SET @@SESSION.information_schema_stats_expiry = 0; 
                                        SELECT AUTO_INCREMENT
                                        FROM  INFORMATION_SCHEMA.TABLES
                                        WHERE TABLE_SCHEMA = '{WebSiteInfo.User.Value?.DomainName?.Replace(".", "") ?? Info.DomainName?.Value}'
                                        AND TABLE_NAME = 'depo_masa';", _cnn);

            int addedID = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();

            await cmd.DisposeAsync();
            

            return addedID;
        }

        public async Task DeleteAsync(WarehouseAuthGroup entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_yetki_gruplari SET SILINDI = 1 WHERE ID=@ID AND VARSAYILAN = 0;";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(WarehouseAuthGroupFilter filter = null, CancellationToken cancellationToken = default)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(ID)
                                      FROM depo_yetki_gruplari
                                      WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            return count.ToInt32();
        }

        public async Task<List<WarehouseAuthGroup>> GetListAsync(WarehouseAuthGroupFilter filter = null, WarehouseAuthGroupPaging paging = null, CancellationToken cancellationToken = default)
        {
            List<WarehouseAuthGroup> authGroups = new List<WarehouseAuthGroup>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ID
                                      ,TANIM
                                      ,EKLENME_TARIHI
                                      ,SILINDI
                                      ,VARSAYILAN
                                      FROM depo_yetki_gruplari
                                      WHERE 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                authGroups.Add(new WarehouseAuthGroup
                {
                    ID = mdr["ID"].ToInt32(),
                    Defination = mdr["TANIM"].ToString(),
                    AddedDateTime = mdr["EKLENME_TARIHI"].ToDateTime(),
                    isDelete = mdr["SILINDI"].ToBoolean(),
                    isDefault = mdr["VARSAYILAN"].ToBoolean()
                });
            }

            await cmd.DisposeAsync();
            await mdr.DisposeAsync();
            return authGroups;
        }

        public async Task UpdateAsync(WarehouseAuthGroup entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE depo_yetki_gruplari SET TANIM = @TANIM WHERE ID=@ID;";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Defination;
            await cmd.ExecuteTransactionCommandAsync();
        }

        private void AppendFilter(ref MySqlCommand cmd, WarehouseAuthGroupFilter filter)
        {
            if (filter != null)
            {
                if (WebSiteInfo.User.Value != null && !WebSiteInfo.User.Value.isTicimaxUser)
                    cmd.CommandText += " AND TANIM != 'Ticimax Admin Yetki'";

                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (!string.IsNullOrEmpty(filter.Defination))
                {
                    cmd.CommandText += " AND TANIM = @TANIM";
                    cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = filter.Defination;
                }

                if (filter.isDelete.HasValue)
                {
                    cmd.CommandText += " AND SILINDI = @SILINDI";
                    cmd.Parameters.Add("@SILINDI", MySqlDbType.Int16).Value = filter.isDelete.Value;
                }

                if (filter.isDefault.HasValue)
                {
                    cmd.CommandText += " AND VARSAYILAN = @VARSAYILAN";
                    cmd.Parameters.Add("@VARSAYILAN", MySqlDbType.Int16).Value = filter.isDefault.Value;
                }
            }
        }
    }
}