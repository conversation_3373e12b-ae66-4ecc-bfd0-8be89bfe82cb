using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class InvoiceSettingsDal : IInvoiceSettingsDal
    {
        private readonly MySqlConnection _cnn;

        public InvoiceSettingsDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task AddAsync(InvoiceSettings entity, CancellationToken cancellationToken)
        {
            var cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO ticimax_fatura_ayar (
                                  SERI
                                  , SIRA_BASLANGIC
                                  , FATURANO_UZUNLUK
                                  , SABLON
                                  , SABLONAYAR
                                )
                                VALUES
                                  (
                                    @SERI
                                    , @SIRA_BASLANGIC
                                    , @FATURANO_UZUNLUK
                                    , @SABLON
                                    , @SABLONAYAR
                                  );";

            cmd.Parameters.Add("@SERI", MySqlDbType.VarChar).Value = entity.Series;
            cmd.Parameters.Add("@SIRA_BASLANGIC", MySqlDbType.Int32).Value = entity.RankStart;
            cmd.Parameters.Add("@FATURANO_UZUNLUK", MySqlDbType.Int32).Value = entity.InvoiceNoLength;
            cmd.Parameters.Add("@SABLON", MySqlDbType.Text).Value = entity.Template;
            cmd.Parameters.Add("@SABLONAYAR", MySqlDbType.Text).Value = entity.TemplateSettings.ToJsonSerialize();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(InvoiceSettings entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "DELETE FROM ticimax_fatura_ayar WHERE ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(InvoiceSettingsFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM ticimax_fatura_ayar
                                WHERE 1 ";

            int count = Convert.ToInt32(cmd.ExecuteScalar());
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return count;
        }

        public async Task<List<InvoiceSettings>> GetListAsync(InvoiceSettingsFilter filter = null, InvoiceSettingsPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<InvoiceSettings> liste = new List<InvoiceSettings>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                  ID,
                                  SERI,
                                  SIRA_BASLANGIC,
                                  FATURANO_UZUNLUK,
                                  SABLON,
                                  SABLONAYAR
                                FROM
                                  ticimax_fatura_ayar WHERE 1 ";

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                InvoiceSettings p = new InvoiceSettings();
                p.ID = Reader["ID"].ToInt32();
                p.Series = Reader["SERI"].ToString();
                p.RankStart = Reader["SIRA_BASLANGIC"].ToInt32();
                p.InvoiceNoLength = Reader["FATURANO_UZUNLUK"].ToInt32();
                p.Template = Reader["SABLON"].ToString();
                p.TemplateSettings = Reader["SABLONAYAR"].ToJsonDeserialize<InvoiceTemplateSettings>();
                liste.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return liste;
        }

        public async Task UpdateAsync(InvoiceSettings entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE ticimax_fatura_ayar SET 
                                SERI = @SERI, SIRA_BASLANGIC = @SIRA_BASLANGIC, FATURANO_UZUNLUK = @FATURANO_UZUNLUK, SABLON = @SABLON, SABLONAYAR = @SABLONAYAR
                                WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@SERI", MySqlDbType.VarChar).Value = entity.Series;
            cmd.Parameters.Add("@SIRA_BASLANGIC", MySqlDbType.Int32).Value = entity.RankStart;
            cmd.Parameters.Add("@FATURANO_UZUNLUK", MySqlDbType.Int32).Value = entity.InvoiceNoLength;
            cmd.Parameters.Add("@SABLON", MySqlDbType.Text).Value = entity.Template.ToJsonSerialize();
            cmd.Parameters.Add("@SABLONAYAR", MySqlDbType.Text).Value = entity.TemplateSettings.ToJsonSerialize();
            await cmd.ExecuteTransactionCommandAsync();
        }
    }
}