using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseAuthGroupUserDal : IWarehouseAuthGroupUserDal
    {
        private readonly MySqlConnection _cnn;

        public WarehouseAuthGroupUserDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task AddAsync(WarehouseAuthGroupUser entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_yetki_kullanici(
                                                            UYE_ID
                                                           ,YETKIGRUP_ID)
                                                         VALUES(
                                                            @UYE_ID
                                                           ,@YETKIGRUP_ID);";

            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = entity.UserID;
            cmd.Parameters.Add("@YETKIGRUP_ID", MySqlDbType.Int32).Value = entity.AuthGroupID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
        }

        public async Task DeleteAsync(WarehouseAuthGroupUser entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"DELETE FROM depo_yetki_kullanici WHERE ID = @ID;";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteByUserID(WarehouseAuthGroupUser entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM depo_yetki_kullanici WHERE UYE_ID = @UYE_ID;";
            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = entity.UserID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
        }

        public async Task DeleteByUserIDAsync(WarehouseAuthGroupUser entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM depo_yetki_kullanici WHERE UYE_ID = @UYE_ID;";
            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = entity.UserID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
        }

        public async Task<int> GetCountAsync(WarehouseAuthGroupUserFilter filter = null, CancellationToken cancellationToken = default)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(dyk.ID)
                                       FROM depo_yetki_kullanici AS dyk
                                       INNER JOIN depo_yetki_gruplari AS dyg
                                       WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            return count.ToInt32();
        }

        public async Task<List<WarehouseAuthGroupUser>> GetListAsync(WarehouseAuthGroupUserFilter filter = null, WarehouseAuthGroupUserPaging paging = null, CancellationToken cancellationToken = default)
        {
            List<WarehouseAuthGroupUser> authGroupUsers = new List<WarehouseAuthGroupUser>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT dyk.ID
                                      ,dyk.UYE_ID
                                      ,dyk.YETKIGRUP_ID
                                      FROM depo_yetki_kullanici AS dyk
                                      INNER JOIN depo_yetki_gruplari AS dyg ON dyg.ID = dyk.YETKIGRUP_ID
                                      WHERE 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                authGroupUsers.Add(new WarehouseAuthGroupUser
                {
                    ID = mdr["ID"].ToInt32(),
                    UserID = mdr["UYE_ID"].ToInt32(),
                    AuthGroupID = mdr["YETKIGRUP_ID"].ToInt32(),
                });
            }

            await cmd.DisposeAsync();
            await mdr.DisposeAsync();
            return authGroupUsers;
        }

        private void AppendFilter(ref MySqlCommand cmd, WarehouseAuthGroupUserFilter filter)
        {
            if (filter != null)
            {
                if (WebSiteInfo.User.Value != null && !WebSiteInfo.User.Value.isTicimaxUser)
                    cmd.CommandText += " AND dyg.TANIM != 'Ticimax Admin Yetki'";

                if (filter.UserID > 0)
                {
                    cmd.CommandText += " AND dyk.UYE_ID = @UYE_ID";
                    cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = filter.UserID;
                }

                if (filter.AuthGroupID > 0)
                {
                    cmd.CommandText += " AND dyk.YETKIGRUP_ID = @YETKIGRUP_ID";
                    cmd.Parameters.Add("@YETKIGRUP_ID", MySqlDbType.Int32).Value = filter.AuthGroupID;
                }
            }
        }
    }
}