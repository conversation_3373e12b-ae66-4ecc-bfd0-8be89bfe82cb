using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseAdminDal : IWarehouseAdminDal
    {
        private MySqlConnection _cnn;

        public WarehouseAdminDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<DbColumns>> GetReferanceDbColumsAsync(bool isReferansDbConnection, string connectionString, string dbName, string tableName = "", string columnName = "", string dataType = "", int dataTypeMaxLength = 0, bool isCharsetNotNull = false, string columnComment = "", CancellationToken cancellationToken = default)
        {
            List<DbColumns> columns = new List<DbColumns>();
            _cnn = new MySqlConnection();
            if (!isReferansDbConnection)
            {
                _cnn.ConnectionString = Connection.DevConnectionString(connectionString);
            }
            else
            {
                _cnn.ConnectionString = connectionString;
            }
            await _cnn.OpenAsync(cancellationToken);

            if(!isReferansDbConnection && string.IsNullOrEmpty(dbName))
            {
                MySqlCommand cmdDbName = new MySqlCommand(string.Empty, _cnn);
                cmdDbName.CommandText = "SELECT DATABASE();";
                dbName = cmdDbName.ExecuteScalar().ToString();
                await cmdDbName.DisposeAsync();
            }

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            cmd.CommandText = @"SELECT * 
                                    FROM information_schema.COLUMNS 
                                    WHERE TABLE_SCHEMA = @TABLE_SCHEMA ";

            cmd.Parameters.Add("@TABLE_SCHEMA", MySqlDbType.VarChar).Value = dbName;

            if (!string.IsNullOrEmpty(tableName))
            {
                cmd.CommandText += @" AND TABLE_NAME = @TABLE_NAME ";
                cmd.Parameters.Add("@TABLE_NAME", MySqlDbType.VarChar).Value = tableName;
            }
            if (!string.IsNullOrEmpty(columnName))
            {
                cmd.CommandText += @" AND COLUMN_NAME = @COLUMN_NAME ";
                cmd.Parameters.Add("@COLUMN_NAME", MySqlDbType.VarChar).Value = columnName;
            }
            if (!string.IsNullOrEmpty(dataType))
            {
                cmd.CommandText += @" AND DATA_TYPE = @DATA_TYPE ";
                cmd.Parameters.Add("@DATA_TYPE", MySqlDbType.VarChar).Value = dataType;
            }
            if (dataTypeMaxLength > 0)
            {
                cmd.CommandText += @" AND CHARACTER_MAXIMUM_LENGTH > @CHARACTER_MAXIMUM_LENGTH ";
                cmd.Parameters.Add("@CHARACTER_MAXIMUM_LENGTH", MySqlDbType.VarChar).Value = dataTypeMaxLength;
            }
            if (isCharsetNotNull)
            {
                cmd.CommandText += @" AND CHARACTER_SET_NAME IS NOT NULL ";
            }
            if (!string.IsNullOrEmpty(columnComment))
            {
                cmd.CommandText += @" AND COLUMN_COMMENT = @COLUMN_COMMENT ";
                cmd.Parameters.Add("@COLUMN_COMMENT", MySqlDbType.VarChar).Value = columnComment;
            }

            cmd.CommandText += @" ORDER BY TABLE_NAME, ORDINAL_POSITION ASC";
            MySqlDataReader Reader = cmd.ExecuteReader();
            while (Reader.Read())
            {
                DbColumns p = new DbColumns();
                p.TABLE_SCHEMA = Reader["TABLE_SCHEMA"].ToString();
                p.TABLE_NAME = Reader["TABLE_NAME"].ToString();
                p.COLUMN_NAME = Reader["COLUMN_NAME"].ToString();
                p.ORDINAL_POSITION = Convert.ToInt32(Reader["ORDINAL_POSITION"]);
                p.COLUMN_DEFAULT = Reader["COLUMN_DEFAULT"].ToString();
                p.IS_NULLABLE = Reader["IS_NULLABLE"].ToString();
                p.DATA_TYPE = Reader["DATA_TYPE"].ToString();
                p.CHARACTER_MAXIMUM_LENGTH = Reader["CHARACTER_MAXIMUM_LENGTH"].ToString();
                p.CHARACTER_OCTET_LENGTH = Reader["CHARACTER_OCTET_LENGTH"].ToString();
                p.NUMERIC_PRECISION = Reader["NUMERIC_PRECISION"].ToString();
                p.NUMERIC_SCALE = Reader["NUMERIC_SCALE"].ToString();
                p.DATETIME_PRECISION = Reader["DATETIME_PRECISION"].ToString();
                p.CHARACTER_SET_NAME = Reader["CHARACTER_SET_NAME"].ToString();
                p.COLLATION_NAME = Reader["COLLATION_NAME"].ToString();
                p.COLUMN_TYPE = Reader["COLUMN_TYPE"].ToString();
                p.COLUMN_KEY = Reader["COLUMN_KEY"].ToString();
                p.EXTRA = Reader["EXTRA"].ToString();
                p.PRIVILEGES = Reader["PRIVILEGES"].ToString();
                p.COLUMN_COMMENT = Reader["COLUMN_COMMENT"].ToString();
                p.GENERATION_EXPRESSION = Reader["GENERATION_EXPRESSION"].ToString();
                columns.Add(p);
            }
            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return columns;
        }


        public async Task TargetDbExecuteCommand(string domainName, string cmdCommandText, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(domainName);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = cmdCommandText;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }
    }
}