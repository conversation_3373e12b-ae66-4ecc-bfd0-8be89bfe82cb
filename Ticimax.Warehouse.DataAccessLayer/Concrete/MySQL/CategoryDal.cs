using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class CategoryDal : ICategoryDal
    {
        private MySqlConnection _cnn;

        public CategoryDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }
        public async Task<List<Category>> GetListAsync(CategoryFilter filter = null, CategoryPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync();
            List<Category> categories = new List<Category>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ID
                                    , KOD
                                    , TANIM
                            FROM kategoriler WHERE 1";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                Category c = new Category();
                c.ID = Convert.ToInt32(reader["ID"]);
                c.Definition = reader["TANIM"].ToString();
                c.Code = reader["KOD"].ToString();
                categories.Add(c);
            }
            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return categories;
        }

        public async Task<List<Category>> GetAllChildAndParentCategoriesAsync(int categoryId, CategoryPaging paging = null, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync();
            List<Category> categories = new List<Category>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"WITH RECURSIVE alt_kategoriler AS (
                                SELECT ID, PID, KOD, TANIM
                                FROM kategoriler
                                WHERE ID = @KATEGORI_ID
                                UNION ALL
                                SELECT k.ID, k.PID, k.KOD, k.TANIM
                                FROM kategoriler k
                                INNER JOIN alt_kategoriler ak ON k.PID = ak.ID
                                ),
                                ust_kategoriler AS (
                                    SELECT ID, PID, KOD, TANIM
                                    FROM kategoriler
                                    WHERE ID = @KATEGORI_ID
                                    UNION ALL
                                    SELECT k.ID, k.PID, k.KOD, k.TANIM
                                    FROM kategoriler k
                                    INNER JOIN ust_kategoriler uk ON uk.PID = k.ID
                                )
                                SELECT ID, PID, KOD, TANIM
                                FROM kategoriler
                                WHERE ID = @KATEGORI_ID
                                UNION
                                SELECT ID, PID, KOD, TANIM
                                FROM alt_kategoriler
                                WHERE ID != @KATEGORI_ID
                                UNION
                                SELECT ID, PID, KOD, TANIM
                                FROM ust_kategoriler
                                WHERE ID != @KATEGORI_ID;
                                ";
            cmd.Parameters.Add("@KATEGORI_ID", MySqlDbType.Int32).Value = categoryId;
            PagingExtension.AppendPaging(ref cmd, paging);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                Category c = new Category();
                c.ID = Convert.ToInt32(reader["ID"]);
                c.Definition = reader["TANIM"].ToString();
                c.Code = reader["KOD"].ToString();
                categories.Add(c);
            }
            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return categories;
        }

        private void AppendFilter(ref MySqlCommand cmd, CategoryFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID.HasValue)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID.Value;
                }
                if (filter.PID.HasValue)
                {
                    cmd.CommandText += " AND PID = @PID";
                    cmd.Parameters.Add("@PID", MySqlDbType.Int32).Value = filter.PID.Value;
                }

                if (!string.IsNullOrEmpty(filter.Code))
                {
                    cmd.CommandText += " AND KOD = @KOD";
                    cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = filter.Code;
                }

                if (filter.Active.HasValue)
                    cmd.CommandText += " AND AKTIF = " + filter.Active.Value.ToInt32();

                if (filter.Ids != null && filter.Ids.Count > 0)
                    cmd.CommandText += $" AND ID IN ({string.Join(',', filter.Ids)})";

            }
        }
    }
}