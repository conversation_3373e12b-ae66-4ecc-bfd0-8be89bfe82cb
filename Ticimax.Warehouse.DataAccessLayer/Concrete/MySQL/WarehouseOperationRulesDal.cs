using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseOperationRulesDal : IWarehoseOperationRulesDal
    {
        private MySqlConnection _cnn;

        public WarehouseOperationRulesDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }
        public async Task<List<WarehouseOperationRules>> GetListAsync(WarehouseOperationRulesFilter filter = null, WarehouseOperationRulesPaging paging = null, CancellationToken cancellationToken = default)
        {
            List<WarehouseOperationRules> rules = new List<WarehouseOperationRules>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  dok.ID, dok.DEPO_ID, dok.DEFINITION, dok.PROCESS, dok.RULES, dok.ACTIVE
                                FROM depo_operasyon_kural AS dok WHERE 1 = 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                WarehouseOperationRules op = new WarehouseOperationRules();
                op.ID = Convert.ToInt32(reader["ID"]);
                op.WarehouseId = Convert.ToInt32(reader["DEPO_ID"]);
                op.Definition = reader["DEFINITION"].ToString();
                op.Process = reader["PROCESS"].ToString();
                if (op.Process == WarehouseOperationRuleEnums.PackagingOperation)
                    op.WarehousePackagingOperationRules = reader["RULES"].ToJsonDeserialize<WarehousePackagingOperationRules>();

                if (op.Process == WarehouseOperationRuleEnums.ReturnOrderOperation)
                    op.ReturnOrderOperationRules = reader["RULES"].ToJsonDeserialize<ReturnOrderOperationRules>();

                op.Active = Convert.ToInt32(reader["ACTIVE"]);
                rules.Add(op);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            return rules;
        }

        public async Task AddAsync(WarehouseOperationRules entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_operasyon_kural(DEFINITION
                                                       ,PROCESS
                                                       ,RULES
                                                       ,DEPO_ID
                                                       ,ACTIVE)
                                                  VALUES(@DEFINITION
                                                       ,@PROCESS
                                                       ,@RULES
                                                       ,@DEPO_ID
                                                       ,@ACTIVE)";

            cmd.Parameters.Add("@DEFINITION", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@PROCESS", MySqlDbType.VarChar).Value = entity.Process;
            if (entity.Process == WarehouseOperationRuleEnums.PackagingOperation)
                cmd.Parameters.Add("@RULES", MySqlDbType.Text).Value = entity.WarehousePackagingOperationRules.ToJsonSerialize();

            if (entity.Process == WarehouseOperationRuleEnums.ReturnOrderOperation)
                cmd.Parameters.Add("@RULES", MySqlDbType.Text).Value = entity.ReturnOrderOperationRules.ToJsonSerialize();

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = entity.WarehouseId;
            cmd.Parameters.Add("@ACTIVE", MySqlDbType.Int16).Value = entity.Active;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
        }

        public async Task UpdateAsync(WarehouseOperationRules entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"UPDATE depo_operasyon_kural SET 
                                DEFINITION = @DEFINITION,
                                PROCESS = @PROCESS,
                                RULES = @RULES,
                                DEPO_ID = @DEPO_ID,
                                ACTIVE = @ACTIVE
                                WHERE ID = @ID";
            cmd.Parameters.Add("@DEFINITION", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@PROCESS", MySqlDbType.VarChar).Value = entity.Process;
            if (entity.Process == WarehouseOperationRuleEnums.PackagingOperation)
                cmd.Parameters.Add("@RULES", MySqlDbType.Double).Value = entity.WarehousePackagingOperationRules.ToJsonSerialize();
            if (entity.Process == WarehouseOperationRuleEnums.ReturnOrderOperation)
                cmd.Parameters.Add("@RULES", MySqlDbType.Double).Value = entity.ReturnOrderOperationRules.ToJsonSerialize();

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = entity.WarehouseId;
            cmd.Parameters.Add("@ACTIVE", MySqlDbType.Int16).Value = entity.Active;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;

            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();


        }

        public async Task DeleteAsync(WarehouseOperationRules entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"DELETE FROM depo_operasyon_kural 
                                WHERE ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
        }

        public async Task<int> GetCountAsync(WarehouseOperationRulesFilter filter = null, CancellationToken cancellationToken = default)
        {
            List<WarehouseOperationRules> rules = new List<WarehouseOperationRules>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  COUNT(*)
                                FROM depo_operasyon_kural AS dok WHERE 1 = 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            return Convert.ToInt32(count);
        }
        private void AppendFilter(ref MySqlCommand cmd, WarehouseOperationRulesFilter filter)
        {
            if (filter != null)
            {
                if (WebSiteInfo.User.Value.WarehouseID > 0)
                {
                    cmd.CommandText += " AND dok.DEPO_ID = @DEPO_ID";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                }
                if (filter.ID.HasValue && filter.ID > 0)
                {
                    cmd.CommandText += " AND dok.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID.Value;
                }

                if (!string.IsNullOrEmpty(filter.Process))
                {
                    cmd.CommandText += " AND dok.PROCESS = @PROCESS";
                    cmd.Parameters.Add("@PROCESS", MySqlDbType.VarChar).Value = filter.Process;
                }
                if (filter.Active.HasValue)
                {
                    cmd.CommandText += " AND dok.ACTIVE = @ACTIVE";
                    cmd.Parameters.Add("@ACTIVE", MySqlDbType.Int16).Value = filter.Active.Value;
                }
            }
        }
    }
}