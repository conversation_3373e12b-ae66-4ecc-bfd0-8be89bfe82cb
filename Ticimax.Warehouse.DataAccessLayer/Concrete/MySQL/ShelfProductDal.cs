using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class ShelfProductDal : IShelfProductDal
    {
        private MySqlConnection _cnn;

        public ShelfProductDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task UpdateStock(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE urun_raf
                                    SET STOK = @STOK
                                    WHERE ID = @ID ";

            cmd.Parameters.Add("@STOK", MySqlDbType.Int32).Value = entity.ShelfStock;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteByProductID(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM urun_raf WHERE URUN_ID = @URUN_ID";
            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.ProductID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }


        public async Task ResetAllStock(int warehouseId, int storeId, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM urun_raf WHERE MAGAZA_ID = @MAGAZA_ID AND DEPO_ID = @DEPO_ID";
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = storeId;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = warehouseId;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteByProductAndShelfID(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"DELETE FROM urun_raf WHERE URUN_ID = @URUN_ID AND RAF_ID = @RAF_ID;";
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.ProductID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteByShelfID(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM urun_raf WHERE RAF_ID = @RAF_ID ";
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<IList<ShelfProductStatus>> ShelfProductStatusGetList(ShelfProductStatusFilter filter = null, ShelfProductPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);

            List<ShelfProductStatus> list = new List<ShelfProductStatus>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT rud.ID,
                                        rud.RAF_ID,
                                        rud.URUN_ID,
                                        rud.ADET,
                                        rud.DURUM
                                        FROM raf_urun_durum AS rud
                                 INNER JOIN raflar AS r ON r.ID = rud.RAF_ID
                                 WHERE 1";

            AppendFilterShelfProductStatus(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);
            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                ShelfProductStatus p = new ShelfProductStatus();
                p.ID = Reader["ID"].ToInt32();
                p.ShelfID = Reader["RAF_ID"].ToInt32();
                p.ProductID = Reader["URUN_ID"].ToInt32();
                p.Piece = Reader["ADET"].ToInt32();
                p.Status = Reader["DURUM"].ToInt32();
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return list;
        }

        public async Task ShelfProductStatusAdd(ShelfProductStatus entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO raf_urun_durum
                                                (RAF_ID
                                                , URUN_ID
                                                , ADET
                                                , DURUM)
                                    VALUES (@RAF_ID
                                            , @URUN_ID
                                            , @ADET
                                            , @DURUM);";

            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.ProductID;
            cmd.Parameters.Add("@ADET", MySqlDbType.Int32).Value = entity.Piece;
            cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = entity.Status;

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task ShelfProductStatusUpdate(ShelfProductStatus entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE raf_urun_durum AS rud INNER JOIN raflar AS r ON r.ID = rud.RAF_ID SET rud.ADET = @ADET WHERE rud.ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@ADET", MySqlDbType.Int32).Value = entity.Piece;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND r.DEPO_ID = @DEPO_ID AND r.MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task ShelfProdutControlPieceUpdate(ShelfProductControlPieceUpdateDto entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE urun_raf ur SET ur.KONTROL_EDILECEK_STOK = ur.KONTROL_EDILECEK_STOK + @ADET WHERE ur.RAF_ID = @RAFID AND ur.URUN_ID = @URUNID ";
            cmd.Parameters.Add("@RAFID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@URUNID", MySqlDbType.Int32).Value = entity.ProductID;
            cmd.Parameters.Add("@ADET", MySqlDbType.Int32).Value = entity.ControlPiece;

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<ShelfProduct>> GetListAsync(ShelfProductFilter filter = null, ShelfProductPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<ShelfProduct> list = new List<ShelfProduct>();
            string urunAdiAlani = filter != null ? filter.ProductNameField : "URUNADI";

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT     ur.ID
                                          , ur.URUN_ID
                                          , ur.RAF_ID
                                          , r.TANIM AS RAFADI
                                          , r.BARKOD AS RAFBARKOD
                                          , r.SIRA AS RAFSIRA
                                          , ur.DEPO_ID
                                          , d.TANIM AS DEPOADI
                                          , d.KOD AS DEPOKODU
                                          , d.SIRA AS DEPOSIRA
                                          , ur.STOK
                                          , ur.KONTROL_EDILECEK_STOK
                                          , uk.{urunAdiAlani} AS URUNADI
                                          , uk.RESIM1
                                          , u.BARKOD
                                          , u.STOKKODU
                                          , u.ALISFIYATI
                                          , r.BOSRAF
                                          , (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
	                                        FROM urun_eksecenek AS ues
	                                        WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA) AS VARYASYON
                                    FROM urun_raf AS ur
                                    INNER JOIN urunler AS u ON ur.URUN_ID = u.ID
                                    INNER JOIN urun_karti AS uk ON u.URUNKARTI_ID = uk.ID
                                    LEFT JOIN raflar AS r ON ur.RAF_ID = r.ID
                                    LEFT JOIN depolar AS d ON ur.DEPO_ID = d.ID
                                    WHERE 1 AND u.SILINDI = 0 ";

            if (filter.IsShelfProdutControlPiece)
            {
                cmd.CommandText += " AND ur.KONTROL_EDILECEK_STOK > 0 ";
            }

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);
            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                ShelfProduct p = new ShelfProduct();
                p.ID = Reader["ID"].ToInt32();
                p.ProductID = Reader["URUN_ID"].ToInt32();
                p.ShelfID = Reader["RAF_ID"].ToInt32();
                p.ShelfName = Reader["RAFADI"].ToString();
                p.ShelfBarcode = Reader["RAFBARKOD"].ToString();
                p.WarehouseID = Reader["DEPO_ID"].ToInt32();
                p.WarehouseName = Reader["DEPOADI"].ToString();
                p.WarehouseCode = Reader["DEPOKODU"].ToString();
                p.WarehouseRank = Reader["DEPOSIRA"] != DBNull.Value ? Reader["DEPOSIRA"].ToInt32() : 0;
                p.ShelfStock = Reader["STOK"].ToDouble();
                p.PurchasePrice = Reader["ALISFIYATI"] != DBNull.Value ? Reader["ALISFIYATI"].ToDouble() : 0;
                p.ShelfRank = Reader["RAFSIRA"] != DBNull.Value ? Reader["RAFSIRA"].ToInt32() : 0;
                p.ProductName = Reader["URUNADI"] + " " + Reader["VARYASYON"];
                p.Barcode = Reader["BARKOD"].ToString();
                p.Image = WebSiteInfo.User.Value.ImagePath + Reader["RESIM1"].ToString();
                p.ProductStockCode = Reader["STOKKODU"].ToString();
                p.ControlPiece = Reader["KONTROL_EDILECEK_STOK"].ToInt32();
                p.isEmptyShelf = Reader["BOSRAF"] != DBNull.Value ? Reader["BOSRAF"].ToBoolean() : false;
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return list;
        }

        public async Task AddAsync(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO urun_raf
                                                (URUN_ID
                                                , RAF_ID
                                                , DEPO_ID
                                                , MAGAZA_ID
                                                , STOK)
                                    VALUES (@URUNID
                                            , @RAFID
                                            , @DEPOID
                                            , @MAGAZAID
                                            , @STOK)
                                    ON DUPLICATE KEY UPDATE STOK = STOK + @STOK";

            cmd.Parameters.Add("@URUNID", MySqlDbType.Int32).Value = entity.ProductID;
            cmd.Parameters.Add("@RAFID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@MAGAZAID", MySqlDbType.Int32).Value = entity.StoreID;
            cmd.Parameters.Add("@STOK", MySqlDbType.Int32).Value = entity.ShelfStock;

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateAsync(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE urun_raf
                                    SET URUN_ID = @URUN_ID
                                      , RAF_ID = @RAF_ID
                                      , STOK = @STOK
                                    WHERE ID = @ID ";

            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.ProductID;
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@STOK", MySqlDbType.Int32).Value = entity.ShelfStock;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM urun_raf WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(ShelfProductFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM urun_raf AS ur
                                INNER JOIN urunler AS u ON ur.URUN_ID = u.ID
                                WHERE u.SILINDI = 0 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task UpdateStockAsync(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE urun_raf
                                    SET STOK = @STOK
                                    WHERE ID = @ID ";

            cmd.Parameters.Add("@STOK", MySqlDbType.Int32).Value = entity.ShelfStock;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteByProductAndShelfIDAsync(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"DELETE FROM urun_raf WHERE URUN_ID = @URUN_ID AND RAF_ID = @RAF_ID ";
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.ProductID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteByShelfIDAsync(ShelfProduct entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM urun_raf WHERE RAF_ID = @RAF_ID ";
            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<double> GetStockCount(ShelfProductFilter filter, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT SUM(ur.STOK)
                                FROM urun_raf AS ur
                                INNER JOIN urunler AS u ON ur.URUN_ID = u.ID
                                WHERE u.SILINDI = 0 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return Convert.ToDouble(count != DBNull.Value ? count : 0);
        }

        public async Task<List<ShelfProductStatus>> ShelfProductStatusGetListAsync(ShelfProductStatusFilter filter = null, ShelfProductPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);

            List<ShelfProductStatus> list = new List<ShelfProductStatus>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT rud.ID,
                                        rud.RAF_ID,
                                        rud.URUN_ID,
                                        rud.ADET,
                                        rud.DURUM
                                        FROM raf_urun_durum AS rud
                                 INNER JOIN raflar AS r ON r.ID = rud.RAF_ID
                                 WHERE 1";

            AppendFilterShelfProductStatus(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);
            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                ShelfProductStatus p = new ShelfProductStatus();
                p.ID = Reader["ID"].ToInt32();
                p.ShelfID = Reader["RAF_ID"].ToInt32();
                p.ProductID = Reader["URUN_ID"].ToInt32();
                p.Piece = Reader["ADET"].ToInt32();
                p.Status = Reader["DURUM"].ToInt32();
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return list;
        }



        private void AppendFilter(ref MySqlCommand cmd, ShelfProductFilter f)
        {
            if (f != null)
            {
                if (!WebSiteInfo.User.Value.IsOneStore && f.WarehouseID == 0)
                {
                    cmd.CommandText += " AND ur.DEPO_ID = @DEPO_ID AND ur.MAGAZA_ID = @MAGAZA_ID ";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                    cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                }

                if (f.NotIncludingGoodsReceivingShelf)
                {
                    if(WebSiteInfo.User.Value.Settings.UrunToplamaAyar.NotIncludingTheGoodsReceivingShelf)
                        cmd.CommandText += " AND r.BARKOD != 'MALKABULALANI' ";
                }

                if (f.WarehouseID > 0)
                {
                    cmd.CommandText += " AND ur.DEPO_ID = @DEPO_ID";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = f.WarehouseID;
                }

                if (f.IsEmptyShelf.HasValue)
                {
                    cmd.CommandText += " AND r.BOSRAF = @BOSRAF";
                    cmd.Parameters.Add("@BOSRAF", MySqlDbType.Int16).Value = f.IsEmptyShelf.Value;
                }

                if (f.ID > 0)
                {
                    cmd.CommandText += " AND ur.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = f.ID;
                }

                if (f.ShelfID > 0)
                {
                    cmd.CommandText += " AND ur.RAF_ID = @RAF_ID";
                    cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = f.ShelfID;
                }
                else if (f.ShelfIds != null && f.ShelfIds.Count > 0)
                    cmd.CommandText += $" AND ur.RAF_ID IN ({string.Join(",", f.ShelfIds)})";
                else if (f.WarehouseIDList != null && f.WarehouseIDList.Count > 0)
                    cmd.CommandText += $" AND ur.DEPO_ID IN ({string.Join(",", f.WarehouseIDList)})";

                if (f.ProductID > 0)
                {
                    cmd.CommandText += " AND ur.URUN_ID = @URUN_ID";
                    cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = f.ProductID;
                }
                else if (f.ProductIDList != null && f.ProductIDList.Count > 0)
                    cmd.CommandText += $" AND ur.URUN_ID IN ({string.Join(",", f.ProductIDList.Distinct())})";

                if (f.IsStockAvailable.HasValue)
                {
                    if (f.IsStockAvailable.Value)
                        cmd.CommandText += " AND ur.STOK > 0";
                    else
                        cmd.CommandText += " AND ur.STOK <= 0";
                }

                if (!string.IsNullOrEmpty(f.ProductBarcode))
                {
                    cmd.CommandText += " AND (u.BARKOD = @BARKOD OR u.ID IN (SELECT URUN_ID FROM urun_barkod WHERE BARKOD = @BARKOD))";
                    cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = f.ProductBarcode;
                }

                if (!string.IsNullOrEmpty(f.ProductStockCode))
                {
                    cmd.CommandText += " AND u.STOKKODU = @STOKKODU";
                    cmd.Parameters.Add("@STOKKODU", MySqlDbType.VarChar).Value = f.ProductStockCode;
                }

                if (f.IsSaleOpened.HasValue)
                {
                    if (f.IsSaleOpened.Value)
                        cmd.CommandText += " AND (select r.SATISA_ACIK from raflar as r where r.ID = ur.RAF_ID LIMIT 1) = 1";
                    else
                        cmd.CommandText += " AND (select r.SATISA_ACIK from raflar as r where r.ID = ur.RAF_ID LIMIT 1) = 0";
                }
                if (f.IsPickingOpened.HasValue)
                {
                    if (f.IsPickingOpened.Value)
                        cmd.CommandText += " AND (select r.TOPLAMAYA_ACIK from raflar as r where r.ID = ur.RAF_ID LIMIT 1) = 1";
                    else
                        cmd.CommandText += " AND (select r.TOPLAMAYA_ACIK from raflar as r where r.ID = ur.RAF_ID LIMIT 1) = 0";
                }
            }
        }

        private void AppendFilterShelfProductStatus(ref MySqlCommand cmd, ShelfProductStatusFilter f)
        {
            if (f != null)
            {
                if (!WebSiteInfo.User.Value.IsOneStore)
                {
                    cmd.CommandText += " AND r.DEPO_ID = @DEPO_ID AND r.MAGAZA_ID = @MAGAZA_ID ";
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                    cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                }

                if (f.ID > 0)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = f.ID;
                }

                if (f.ShelfID > 0)
                {
                    cmd.CommandText += " AND RAF_ID = @RAFID";
                    cmd.Parameters.Add("@RAFID", MySqlDbType.Int32).Value = f.ShelfID;
                }

                if (f.ProductID > 0)
                {
                    cmd.CommandText += " AND URUN_ID = @URUNID";
                    cmd.Parameters.Add("@URUNID", MySqlDbType.Int32).Value = f.ProductID;
                }

                if (f.Status > 0)
                {
                    cmd.CommandText += " AND DURUM = @DURUM";
                    cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = f.Status;
                }
            }
        }

        public async Task<double> GetShelfChildrenStock(int shelfId, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT SUM(STOK) from urun_raf as ur WHERE ur.RAF_ID IN (
                            WITH RECURSIVE ChildCTE AS (
                                SELECT ID, PARENT_ID, TANIM
                                FROM raflar
                                WHERE ID = @RAFID
                                UNION ALL
                                SELECT h.ID, h.PARENT_ID, h.TANIM
                                FROM raflar h
                                INNER JOIN ChildCTE c ON h.PARENT_ID = c.ID
                            )
                            SELECT ID
                            FROM ChildCTE) ";

            cmd.Parameters.Add("@RAFID", MySqlDbType.Int32).Value = shelfId;

            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return Convert.ToDouble(count != DBNull.Value ? count : 0);
        }

        public async Task<double> GetShelfParentStock(int shelfId, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT * from urun_raf as ur WHERE ur.RAF_ID IN(
                                    WITH RECURSIVE ParentCTE AS (
                                        SELECT ID, PARENT_ID
                                        FROM raflar
                                        WHERE ID = @RAFID
                                        UNION ALL
                                        SELECT h.ID, h.PARENT_ID
                                        FROM raflar h
                                        INNER JOIN ParentCTE p ON h.ID = p.PARENT_ID
                                    ) ";

            cmd.Parameters.Add("@RAFID", MySqlDbType.Int32).Value = shelfId;

            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return Convert.ToDouble(count != DBNull.Value ? count : 0);
        }

        public async Task<List<CriticalStockShelfProductResponse>> GetCriticalStockProducts(CriticalStockShelfProductFilter filter, CriticalStockShelfProductPaging paging, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<CriticalStockShelfProductResponse> list = new List<CriticalStockShelfProductResponse>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT 
                            ur.URUN_ID, 
                            ur.DEPO_ID, 
                            ur.MAGAZA_ID, 
                            SUM(ur.STOK) AS TOPLAM_STOK, 
                            u.BARKOD, 
                            u.STOKKODU, 
                            uk.RESIM1,
                            (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
	                                        FROM urun_eksecenek AS ues
	                                        WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA) AS VARYASYON,
                            uk.URUNADI,
                            d.TANIM AS DEPO_TANIM
                            FROM urun_raf AS ur
                            INNER JOIN raflar AS r ON r.ID = ur.RAF_ID
                            INNER JOIN urunler AS u ON ur.URUN_ID = u.ID
                            INNER JOIN urun_karti AS uk ON u.URUNKARTI_ID = uk.ID
                            INNER JOIN depolar AS d ON r.DEPO_ID = d.ID
                            WHERE r.SATISA_ACIK = 1 AND ur.DEPO_ID = @DEPO_ID 
                            GROUP BY ur.URUN_ID, ur.DEPO_ID, ur.MAGAZA_ID
                            HAVING SUM(ur.STOK) <= @CRITICAL_STOCK ";

            cmd.Parameters.Add("@CRITICAL_STOCK", MySqlDbType.Double).Value = filter.CriticStok;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = filter.WarehouseId;

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                CriticalStockShelfProductResponse sp = new CriticalStockShelfProductResponse();
                sp.ProductId = Reader["URUN_ID"].ToInt32();
                sp.Stock = Reader["TOPLAM_STOK"].ToDouble();
                sp.WarehouseDefinition = Reader["DEPO_TANIM"].ToString();
                sp.ProductBarcode = Reader["BARKOD"].ToString();
                sp.ProductStockCode = Reader["STOKKODU"].ToString();
                sp.Image = WebSiteInfo.User.Value.ImagePath + Reader["RESIM1"].ToString();
                sp.ProductName = Reader["URUNADI"] + " " + Reader["VARYASYON"];
                list.Add(sp);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return list;
        }


        public async Task<int> GetCriticalStockProductsCount(CriticalStockShelfProductFilter filter, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<CriticalStockShelfProductResponse> list = new List<CriticalStockShelfProductResponse>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT COUNT(*) AS CRITICAL_STOCK_COUNT
                            FROM (
                                SELECT 
                                    ur.URUN_ID, 
                                    ur.DEPO_ID, 
                                    ur.MAGAZA_ID
                                FROM urun_raf AS ur
                                INNER JOIN raflar AS r ON r.ID = ur.RAF_ID
                                INNER JOIN urunler AS u ON ur.URUN_ID = u.ID
                                INNER JOIN urun_karti AS uk ON u.URUNKARTI_ID = uk.ID
                                INNER JOIN depolar AS d ON r.DEPO_ID = d.ID
                                WHERE r.SATISA_ACIK = 1 AND ur.DEPO_ID = @DEPO_ID 
                                GROUP BY ur.URUN_ID, ur.DEPO_ID, ur.MAGAZA_ID
                                HAVING SUM(ur.STOK) <= @CRITICAL_STOCK
                            ) AS T;";
                                

            cmd.Parameters.Add("@CRITICAL_STOCK", MySqlDbType.Double).Value = filter.CriticStok;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = filter.WarehouseId;
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return count;
        }

    }
}