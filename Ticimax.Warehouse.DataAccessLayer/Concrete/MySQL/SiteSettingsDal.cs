using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.BusinessEntities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class SiteSettingsDal : ISiteSettingsDal
    {
        private readonly MySqlConnection _cnn;

        public SiteSettingsDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<BLSiteAyarlari>> Select(int itemID = 0, string domainName = "", CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<BLSiteAyarlari> ayarlar = new List<BLSiteAyarlari>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT 	ID
                                      , DUZENLEYENKULLANICI_ID
                                      , DUZENLEMETARIHI
                                      , ALANADI
                                      , TICIMAXPAKET_ID
                                      , TICIMAXEKMENU
                                      , PREMIUMTEMA
                                      , LISANSTARIHI
                                      , VIPMUSTERI
                                      , FIRMABILGILERI
                                      , FIRMAADI
                                      , SEKTOR_ID
                                      , YETKILIKISI
                                      , TELEFON
                                      , FAKS
                                      , VERGIDAIRESI
                                      , VERGINO
                                      , MAIL
                                      , SIPARISMAIL
                                      , ADRES
                                      , ULKE_ID
                                      , ULKEKODU
                                      , SEHIR_ID
                                      , ILCE_ID
                                      , HAVALEODEME
                                      , KREDIKARTIODEME
                                      , KREDIKARTIONPROVIZYON
                                      , KAPIDAODEME
                                      , PAYPALODEME
                                      , MOBILODEME
                                      , MAILORDERODEME
                                      , BKMEXPRESSODEME
                                      , BKMEXPRESSMERCHANTID
                                      , PAYUAYAR
                                      , IPARAODEME
                                      , CARIODEME
                                      , PAYPALMAIL
                                      , HAVALEINDIRIMYUZDE
                                      , TEKCEKIMINDIRIMYUZDE
                                      , KAPIDAODEMEEKUCRET
                                      , UCRETSIZKARGOLIMITI
                                      , MINIMUMALISVERISTUTARI
                                      , MINIMUMALISVERISTUTARIUYARI
                                      , MAILSERVISSAGLAYICI
                                      , SMTPSERVER
                                      , SMTPPORT
                                      , SMTPENABLESSL
                                      , SMTPGONDERENMAIL
                                      , SMTPGONDERENISIM
                                      , SMTPUSER
                                      , SMTPPASS
                                      , SEOGOOGLEDOGRULAMA
                                      , SEOYANDEXDOGRULAMA
                                      , SEOYAHOODOGRULAMA
                                      , SEOMSNDOGRULAMA
                                      , SEOALEXADOGRULAMA
                                      , PINTERESTDOGRULAMA
                                      , SEOANALYTICS
                                      , SEOANALYTICSDOMAIN
                                      , YANDEXMETRICAID
                                      , URUNFIYATGOSTER
                                      , URUNFIYATUYEGOSTER
                                      , URUNKDVDAHILGOSTER
                                      , URUNVITRINRASTGELEGOSTER
                                      , URUNSTOKKODUGOSTER
                                      , URUNMARKAGOSTER
                                      , URUNSTOKOLMAYANGOSTER
                                      , URUNZIYARETSAYIGOSTER
                                      , URUNFOTOGRAFBUYUT
                                      , URUNSEPETATPOPUP
                                      , SEPETEEKLESEPETEYONLENDIR
                                      , URUNLISTEURUNSAYISI
                                      , URUNDETAYADETTIPI
                                      , URUNDETAYBENZERURUNGOSTER
                                      , URUNDETAYTAKSITACIKLAMAGOSTER
                                      , URUNDETAYTEDARIKCIGOSTER
                                      , SITEBAKIMMODU
                                      , ANASAYFAANIMASYON
                                      , KATEGORIDEMARKAGOSTER
                                      , HEDIYEPAKETINOTUGOSTER
                                      , HEDIYEPAKETITUTARI
                                      , KARGOHESAPLAMAMODULU
                                      , DOVIZOTOMATIKGUNCELLE
                                      , SIPARISDURUMMAILBILGILENDIRME
                                      , TELEFONSIPARIS
                                      , TELEFONSIPARISMETNI
                                      , FACEBOOKAPPID
                                      , TWITTERKULLANICI
                                      , FACEBOOKSECRET
                                      , PARAPUANAKTIF
                                      , PARAPUANLIMIT
                                      , ANASAYFAPOPUPGOSTER
                                      , ANASAYFAPOPUPMETNI
                                      , OZELTASARIM
                                      , OZELTASARIMMOBIL
                                      , SLIDERTIPI
                                      , MENUTIPI
                                      , SLIDERBANNER
                                      , USTSOLMENU
                                      , SITEMASTERPAGE
                                      , MOBILSITE
                                      , MOBILSITETEMA
                                      , URUNLISTEEKSECENEKTIPI
                                      , SITETEMA
                                      , CAPTCHAAKTIF
                                      , UYELIKSIZALISVERISAKTIF
                                      , SITEUYELIKZORUNLU
                                      , URUNDETAYUYELIKZORUNLU
                                      , SMSAKTIF
                                      , SMSKULLANICI
                                      , SMSSIFRE
                                      , SMSORGINATOR
                                      , SMSSERVISSAGLAYICI
                                      , SSLAKTIF
                                      , SSLZORUNLU
                                      , WWWAKTIF
                                      , KARGOHESAPLAMATIPI
                                      , ADWORDSDONUSUMKODU
                                      , ADWORDSYENIDENPAZARLAMAKODU
                                      , ROBOTS
                                      , DOVIZFIYATGOSTER
                                      , TESLIMATTARIHIGOSTER
                                      , FIRSATMODULUAKTIF
                                      , ADRESPOSTAKODUZORUNLU
                                      , ADRESPOSTAKODUDOGRULAMA
                                      , ADRESTKNZORUNLU
                                      , ADRESTCKNDOGRULAMA
                                      , GOOGLEMERCHANTAKTIF
                                      , ADWORDSDONUSUM_CONVERSIONID
                                      , ADWORDSDONUSUM_CONVERSIONLABEL
                                      , FACEBOOKDONUSUM_ID
                                      , HEDIYECEKIOLUSTURMAMINIMUMALISVERIS
                                      , URUNDETAYTABAKTIF
                                      , FACEBOOKYORUMAKTIF
                                      , TWITTERAPIKEY
                                      , TWITTERAPISECRET
                                      , GOOGLECLIENTID
                                      , GOOGLECLIENTSECRET
                                      , URUNOZELNOTAKTIF
                                      , URUNDOSYAYUKLEMEAKTIF
                                      , URUNKAMPANYAAKTIF
                                      , TEDARIKCIGUNCELLEMEONAYGEREKLI
                                      , ODEMEDESEPETBOSALT
                                      , STOKOLMAYANISONDAGOSTER
                                      , SITEDILIAKTIF
                                      , VARSAYILANSITEDILI
                                      , KULLANILABILIRDILLER
                                      , PARABIRIMIAKTIF
                                      , VARSAYILANPARABIRIMI
                                      , VARSAYILANPARABIRIMIDILKODU
                                      , KULLANILABILIRPARABIRIMI
                                      , ULKEIPYONLENDIR
                                      , PARCALIODEMEAKTIF
                                      , SITEGIRISIZINAKTIF
                                      , RICHSNIPPETAKTIF
                                      , RICHSNIPPETURUNADI
                                      , RICHSNIPPETMARKA
                                      , RICHSNIPPETKATEGORI
                                      , RICHSNIPPETFIYAT
                                      , RICHSNIPPETSTOK
                                      , RICHSNIPPETRESIM
                                      , RICHSNIPPETURUNKODU
                                      , RICHSNIPPETACIKLAMA
                                      , KOMBINURUNAKTIF
                                      , CSSYETKI
                                      , CDNAKTIF
                                      , CDNADRES
                                      , CAYMAHAKKINIAYRIGOSTER
                                      , ODEMEYAPILMAMISSIPARISIPTALEDILEBILIR
                                      , UCBOYUTRESIMAKTIF
                                      , URUNLISTE2RESIMAKTIF
                                      , KENDINTASARLAAKTIF
                                      , URUNDETAYSTOKADEDIGOSTER
                                      , RESIMPNGKAYDET
                                      , SIPARISSEPETBILGILENDIRMEAKTIF
                                      , URUNADEDIONDALIKLI
                                      , KATEGORIDESLIDERGOSTER
                                      , ASORTIAKTIF
                                      , SAYFALAMASECENEK
                                      , KREDILISATISAKTIF
                                      , SIPARISADIMINDAMENUGOSTER
                                      , URUNDETAYMINALIMGOSTER
                                      , UYEGIRISPOPUPAKTIF
                                      , ARAMADAKATEGORIAKTIF
                                      , KATEGORIDEUSTFILTRAKTIF
                                      , GARANTIPAYAKTIF
                                      , JSDEGISKENAKTIF
                                      , URUNAKSESUARAKTIF
                                      , SEPETTEGUNCELLEMEBUTONAKTIF
                                      , ADRESTARIFIAKTIF
                                      , UYEALISVERISHEDEFIAKTIF
                                      , UYELIKMAILONAYIAKTIF
                                      , UYELIKYONETICIONAYIAKTIF
                                      , ILGILIURUNRESIMAKTIF
                                      , FACEBOOKPIXELID
                                      , CRITEOACCOUNTID
                                      , YOTPOAPPKEY
                                      , STOKOLMAYANURUNDEBENZERGOSTER
                                      , URUNLISTEHIZLIBAKISAKTIF
                                      , CACHEAKTIF
                                      , CACHEEXPIRESMINUTES
                                      , CACHEVARYBYCUSTOM
                                      , KAPIDAODEMESMSONAYAKTIF
                                      , SLACKPOSTURL
                                      , IYZIPAYAKTIF
                                      , IYZIPAYAPIKEY
                                      , IYZIPAYSECUREKEY
                                      , IYZIPAYBASEURL
                                      , URUNDETAYSOSYALMEDYAAKTIF
                                      , URUNFILTREAKTIF
                                      , URUNFILTREAYAR
                                      , KAMPANYATEKLIFAKTIF
                                      , GLOBALPOPUPAKTIF
                                      , KAPIDAODEMELIMITI
                                      , KAPIDAODEMEUSTLIMITI
                                      , GOOGLEAPIPROJENO
                                      , ODEMEADIMINDASOZLESMEGOSTER
                                      , PASIFSEKMEMESAJI
                                      , RESIMSIZURUNGOSTER
                                      , BUKOLIAKTIF
                                      , BUKOLISERVISSIFRESI
                                      , KAMPANYABANNERAKTIF
                                      , MOBILDESEPETEGITAKTIF
                                      , MOBILUYGULAMAAKTIF
                                      , MOBILUYGULAMABILGILERI
                                      , SIPARISODEMESAATSINIRI
                                      , SLIDERPARAMETRELERI
                                      , ULKEGOSTERME
                                      , TINYPNGAYAR
                                      , HOPIAUTHINFO
                                      , SIPARISADIMIURUNGOSTERIMI
                                      , UYGULAMAMOBILTEMA
                                      , ARAMASECENEKLERI
                                      , URUNLISTEONYAZIAKTIF
                                      , SEPETKDVDAHILGOSTER
                                      , UYGULAMASEOANALYTICS
                                      , DINAMIKFORMPARAMETRE
                                      , HTMLCACHEAKTIF
                                      , ZUBIZUAYAR
                                      , HEPSIPAYAYAR
                                      , SEPETTEMARKAGOSTER
                                      , EKVERGITIPI
                                      , URUNDETAYZOOM
                                      , SEPETTEFAVORIURUNLERIGOSTER
                                      , KAPIDAODEMEONAYAYAR
                                      , TEKCEKIMSECENEK
                                      , DETAYLIUYELIKPARAMETRE
                                      , ODEMEONAYPOPUPAKTIF
                                      , ILCEOZELDEGERAKTIF
                                      , NESTPAYAKTIF
                                      , SIPARISAYARLARI
                                      , SEPETAYARLARI
                                      , KREDILISATISAYAR
                                      , EARSIVEFATURAAYARLARI
                                      , SITEYONETIMAYAR
                                      , ADRESAYARLARI
                                      , INGTAGRAMAYAR
                                      , HTMLCACHEAYAR
                                      , CARDTEKAKTIF
                                      , CARDTEKAYAR
                                      , PANELDILAYAR
                                      , ELASTICSEARCH
                                      , WEBHOOKAKTIF
                                      , BLOGAYAR
                                      , PAYBYMEAKTIF
                                      , PAYBYMEAYAR
                                      , PAYGURUAKTIF
                                      , PAYGURUAYAR
                                      , URUNAYAR
                                      , IFNULL((SELECT AYARLAR FROM depo_ayarlar GROUP BY DUZENLEMETARIHI ORDER BY ID DESC LIMIT 1),DEPOAYAR) AS DEPOAYAR
                                      , IFNULL((SELECT AYARLAR FROM depo_ayarlar GROUP BY DUZENLEMETARIHI ORDER BY ID DESC LIMIT 1, 1),DEPOAYAR) AS DEPOESKIAYAR
                                      , PAYNETAKTIF
                                      , PAYNETAYAR
                                      , TELRAKTIF
                                      , TELRAYAR
                                      , CACHEAYAR
                                      , SIPARISIPTALSINIRI
                                      , SIPARISIPTALSINIRTIPI
                                      , COMPAYAKTIF
                                      , COMPAYAYAR
                                      , GOOGLEANKETAYAR
                                      , FATURAAYAR
                                      , UYEAYAR
                                      , ADWORDSDONUSUMENTEGRASYONTIPI
                                      , ODEMEAYARLARI
                                      , MAILAYAR
                                      , SMSAYAR
                                      , DIJITALPAZARLAMA
                                      , IADEKARGOAYAR
                                      , KARGOBILGILENDIRMEDURUMU
                                      , SOSYALMEDYA
                                      , SEOAYAR
                                      , CDNAYAR
                                      , MAGAZAMODULAYAR
                                      , MAGAZAMODULUAKTIF
                                      , PASIFSEKMEMESAJLARI
                                      , SIPARISIPTALURUNUYAZDIR
                                      , KARGOBILGILENDIRMEMAIL
                                      , MINIOAYARLARI
                                FROM siteayarlari WHERE 1 ";

            if (!string.IsNullOrEmpty(domainName))
            {
                cmd.CommandText += " AND ALANADI = @ALANADI ";
                cmd.Parameters.Add("@ALANADI", MySqlDbType.VarChar).Value = domainName.Replace("www.", "").ToLower();
            }

            cmd.CommandText += " ORDER BY ID DESC LIMIT 1";

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                BLSiteAyarlari p = new BLSiteAyarlari();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.DuzenleyenKullaniciID = Convert.ToInt32(Reader["DUZENLEYENKULLANICI_ID"]);
                p.DuzenlemeTarihi = Convert.ToDateTime(Reader["DUZENLEMETARIHI"]);
                p.AlanAdi = Reader["ALANADI"].ToString();
                p.TicimaxPaketID = Convert.ToInt32(Reader["TICIMAXPAKET_ID"]);
                p.TicimaxEkMenu = Reader["TICIMAXEKMENU"].ToString();
                p.PremiumTema = Convert.ToInt32(Reader["PREMIUMTEMA"]);
                p.LisansTarihi = Convert.ToDateTime(Reader["LISANSTARIHI"]);
                p.VipMusteri = Convert.ToBoolean(Reader["VIPMUSTERI"]);
                p.FirmaAdi = Reader["FIRMAADI"].ToString();
                p.SektorId = Convert.ToInt32(Reader["SEKTOR_ID"]);
                p.YetkiliKisi = Reader["YETKILIKISI"].ToString();
                p.Telefon = Reader["TELEFON"].ToString();
                p.Faks = Reader["FAKS"].ToString();
                p.VergiDairesi = Reader["VERGIDAIRESI"].ToString();
                p.VergiNo = Reader["VERGINO"].ToString();
                p.FirmaMail = Reader["MAIL"].ToString();
                p.SiparisMail = Reader["SIPARISMAIL"].ToString().Trim().Length < 6 ? p.FirmaMail : Reader["SIPARISMAIL"].ToString();
                p.Adres = Reader["ADRES"].ToString();
                p.UlkeID = Convert.ToInt32(Reader["ULKE_ID"]);
                p.UlkeKodu = Reader["ULKEKODU"].ToString();
                p.SehirID = Convert.ToInt32(Reader["SEHIR_ID"]);
                p.IlceID = Convert.ToInt32(Reader["ILCE_ID"]);
                p.KapidaOdemeEkUcret = Convert.ToDouble(Reader["KAPIDAODEMEEKUCRET"]);
                p.UcretsizKargoLimiti = Convert.ToDouble(Reader["UCRETSIZKARGOLIMITI"]);
                p.MinimumAlisverisTutari = Convert.ToDouble(Reader["MINIMUMALISVERISTUTARI"]);
                p.MinimumAlisverisTutariUyari = Reader["MINIMUMALISVERISTUTARIUYARI"].ToString();
                p.SiteBakimModu = Convert.ToBoolean(Reader["SITEBAKIMMODU"]);
                p.AnasayfaAnimasyon = Convert.ToBoolean(Reader["ANASAYFAANIMASYON"]);
                p.KategorideMarkaGoster = Convert.ToBoolean(Reader["KATEGORIDEMARKAGOSTER"]);
                p.HediyePaketiNotuGoster = Convert.ToBoolean(Reader["HEDIYEPAKETINOTUGOSTER"]);
                p.HediyePaketiTutari = Convert.ToDouble(Reader["HEDIYEPAKETITUTARI"]);
                p.KargoHesaplamaModulu = Convert.ToBoolean(Reader["KARGOHESAPLAMAMODULU"]);
                p.DovizOtomatikGuncelle = Convert.ToBoolean(Reader["DOVIZOTOMATIKGUNCELLE"]);
                p.SiparisDurumMailBilgilendirme = Convert.ToBoolean(Reader["SIPARISDURUMMAILBILGILENDIRME"]);
                p.TelefonSiparis = Convert.ToBoolean(Reader["TELEFONSIPARIS"]);
                p.TelefonSiparisMetni = Reader["TELEFONSIPARISMETNI"].ToString();
                p.FacebookAppID = Reader["FACEBOOKAPPID"].ToString();
                p.TwitterKullanici = Reader["TWITTERKULLANICI"].ToString();
                p.FacebookSecret = Reader["FACEBOOKSECRET"].ToString();
                p.ParaPuanAktif = Convert.ToBoolean(Reader["PARAPUANAKTIF"]);
                p.ParaPuanLimit = Convert.ToDouble(Reader["PARAPUANLIMIT"]);
                p.AnasayfaPopupGoster = Convert.ToBoolean(Reader["ANASAYFAPOPUPGOSTER"]);
                p.AnasayfaPopupMetni = Reader["ANASAYFAPOPUPMETNI"].ToString();
                p.OzelTasarim = Convert.ToBoolean(Reader["OZELTASARIM"]);
                p.OzelTasarimMobil = Convert.ToBoolean(Reader["OZELTASARIMMOBIL"]);
                p.SliderTipi = Convert.ToInt32(Reader["SLIDERTIPI"]);
                p.MenuTipi = Convert.ToInt32(Reader["MENUTIPI"]);
                p.SliderBanner = Convert.ToInt32(Reader["SLIDERBANNER"]);
                p.UstSolMenu = Convert.ToInt32(Reader["USTSOLMENU"]);
                p.SiteMasterPage = Convert.ToInt32(Reader["SITEMASTERPAGE"]);
                p.MobilSite = Convert.ToBoolean(Reader["MOBILSITE"]);
                p.MobilSiteTema = Convert.ToInt32(Reader["MOBILSITETEMA"]);
                p.SiteTema = Reader["SITETEMA"].ToString();
                p.CaptchaAktif = Convert.ToBoolean(Reader["CAPTCHAAKTIF"]);
                p.KargoHesaplamaTipi = Convert.ToInt32(Reader["KARGOHESAPLAMATIPI"]);
                p.Robots = Reader["ROBOTS"].ToString();
                p.DovizFiyatGoster = Convert.ToBoolean(Reader["DOVIZFIYATGOSTER"]);
                p.TeslimatTarihiGoster = Convert.ToBoolean(Reader["TESLIMATTARIHIGOSTER"]);
                p.FirsatModuluAktif = Convert.ToBoolean(Reader["FIRSATMODULUAKTIF"]);
                p.AdresPostaKoduZorunlu = Convert.ToBoolean(Reader["ADRESPOSTAKODUZORUNLU"]);
                p.AdresPostaKoduDogrulama = Convert.ToBoolean(Reader["ADRESPOSTAKODUDOGRULAMA"]);
                p.AdresTCKNZorunlu = Convert.ToBoolean(Reader["ADRESTKNZORUNLU"]);
                p.AdresTCKNDogrulama = Convert.ToBoolean(Reader["ADRESTCKNDOGRULAMA"]);
                p.FacebookDonusumID = Reader["FACEBOOKDONUSUM_ID"].ToString();
                p.HediyeCekiOlusturmaMinimumAlisveris = Convert.ToDouble(Reader["HEDIYECEKIOLUSTURMAMINIMUMALISVERIS"]);
                p.UrunDetayTabAktif = Convert.ToBoolean(Reader["URUNDETAYTABAKTIF"]);
                p.FacebookYorumAktif = Convert.ToBoolean(Reader["FACEBOOKYORUMAKTIF"]);
                p.TwitterApiKey = Reader["TWITTERAPIKEY"].ToString();
                p.TwitterApiSecret = Reader["TWITTERAPISECRET"].ToString();
                p.GoogleClientID = Reader["GOOGLECLIENTID"].ToString();
                p.GoogleClientSecret = Reader["GOOGLECLIENTSECRET"].ToString();
                p.UrunOzelNotAktif = Convert.ToBoolean(Reader["URUNOZELNOTAKTIF"]);
                p.UrunDosyaYuklemeAktif = Convert.ToBoolean(Reader["URUNDOSYAYUKLEMEAKTIF"]);
                p.UrunKampanyaAktif = Convert.ToBoolean(Reader["URUNKAMPANYAAKTIF"]);
                p.TedarikciGuncellemeOnayGerekli = Convert.ToBoolean(Reader["TEDARIKCIGUNCELLEMEONAYGEREKLI"]);
                p.StokOlmayaniSondaGoster = Convert.ToBoolean(Reader["STOKOLMAYANISONDAGOSTER"]);
                p.DilIcerikYoksaVarsayilanGoster = false;
                p.SiteDiliAktif = Convert.ToBoolean(Reader["SITEDILIAKTIF"]);
                p.VarsayilanSiteDili = Reader["VARSAYILANSITEDILI"].ToString();
                p.KullanilabilirDiller = Reader["KULLANILABILIRDILLER"].ToString();
                p.ParaBirimiAktif = Convert.ToBoolean(Reader["PARABIRIMIAKTIF"]);
                p.VarsayilanParaBirimi = Reader["VARSAYILANPARABIRIMI"].ToString();
                p.VarsayilanParaBirimiDilKodu = Reader["VARSAYILANPARABIRIMIDILKODU"].ToString();
                p.KullanilabilirParaBirimi = Reader["KULLANILABILIRPARABIRIMI"].ToString();
                p.UlkeIPYonlendir = Convert.ToBoolean(Reader["ULKEIPYONLENDIR"]);
                p.SiteGirisIzinAktif = Convert.ToBoolean(Reader["SITEGIRISIZINAKTIF"]);
                p.KombinUrunAktif = Convert.ToBoolean(Reader["KOMBINURUNAKTIF"]);
                p.CssYetki = Convert.ToBoolean(Reader["CSSYETKI"]);
                p.CdnAktif = Convert.ToBoolean(Reader["CDNAKTIF"]);
                p.CdnAdres = Reader["CDNADRES"].ToString();
                p.CaymaHakkiniAyriGoster = Convert.ToBoolean(Reader["CAYMAHAKKINIAYRIGOSTER"]);
                p.OdemeYapilmamisSiparisIptalEdilebilir = Convert.ToBoolean(Reader["ODEMEYAPILMAMISSIPARISIPTALEDILEBILIR"]);
                p.UcBoyutResimAktif = Convert.ToBoolean(Reader["UCBOYUTRESIMAKTIF"]);
                p.KendinTasarlaAktif = Convert.ToBoolean(Reader["KENDINTASARLAAKTIF"]);
                p.ResimPngKaydet = Convert.ToBoolean(Reader["RESIMPNGKAYDET"]);
                p.KategorideSliderGoster = Convert.ToBoolean(Reader["KATEGORIDESLIDERGOSTER"]);
                p.AsortiAktif = Convert.ToBoolean(Reader["ASORTIAKTIF"]);
                p.SayfalamaSecenek = Convert.ToInt32(Reader["SAYFALAMASECENEK"]);
                p.SiparisAdimindaMenuGoster = Convert.ToBoolean(Reader["SIPARISADIMINDAMENUGOSTER"]);
                p.AramadaKategoriAktif = Convert.ToBoolean(Reader["ARAMADAKATEGORIAKTIF"]);
                p.KategoriUstFiltreAktif = Convert.ToBoolean(Reader["KATEGORIDEUSTFILTRAKTIF"]);
                p.JSDegiskenAktif = Convert.ToBoolean(Reader["JSDEGISKENAKTIF"]);
                p.UrunAksesuarAktif = Convert.ToBoolean(Reader["URUNAKSESUARAKTIF"]);
                p.AdresTarifiAktif = Convert.ToBoolean(Reader["ADRESTARIFIAKTIF"]);
                p.IlgiliUrunResimAktif = Convert.ToBoolean(Reader["ILGILIURUNRESIMAKTIF"]);
                p.YotpoAppKey = Reader["YOTPOAPPKEY"].ToString();
                p.CacheAktif = Convert.ToBoolean(Reader["CACHEAKTIF"]);
                p.CacheExpiresMinutes = Convert.ToInt32(Reader["CACHEEXPIRESMINUTES"]);
                p.CacheVaryByCustom = Reader["CACHEVARYBYCUSTOM"].ToString();
                p.KapidaOdemeSmsOnayAktif = Convert.ToBoolean(Reader["KAPIDAODEMESMSONAYAKTIF"]);
                p.SlackPostUrl = Reader["SLACKPOSTURL"].ToString();
                p.UrunFiltreAktif = Convert.ToBoolean(Reader["URUNFILTREAKTIF"]);
                p.KampanyaBannerAktif = Convert.ToBoolean(Reader["KAMPANYABANNERAKTIF"]);
                p.KampanyaTeklifAktif = Convert.ToBoolean(Reader["KAMPANYATEKLIFAKTIF"]);
                p.GlobalPopupAktif = Convert.ToBoolean(Reader["GLOBALPOPUPAKTIF"]);
                p.KapidaOdemeLimiti = Convert.ToDouble(Reader["KAPIDAODEMELIMITI"]);
                p.KapidaOdemeUstLimiti = Convert.ToDouble(Reader["KAPIDAODEMEUSTLIMITI"]);
                p.GoogleApiProjeNo = Reader["GOOGLEAPIPROJENO"].ToString();
                p.OdemeAdimindaSozlesmeGoster = Convert.ToBoolean(Reader["ODEMEADIMINDASOZLESMEGOSTER"]);
                //p.PasifSekmeMesaji = Reader["PASIFSEKMEMESAJI"].ToString();

                p.ResimsizUrunGoster = Convert.ToBoolean(Reader["RESIMSIZURUNGOSTER"]);
                p.BuKoliAktif = Convert.ToBoolean(Reader["BUKOLIAKTIF"]);
                p.BuKoliServisSifresi = Reader["BUKOLISERVISSIFRESI"].ToString();
                p.MobilUygulamaAktif = Convert.ToBoolean(Reader["MOBILUYGULAMAAKTIF"]);

                p.SiparisOdemeSaatSiniri = Convert.ToInt32(Reader["SIPARISODEMESAATSINIRI"]);
                p.UlkeGosterme = Convert.ToBoolean(Reader["ULKEGOSTERME"]);
                p.SiparisAdimiUrunGosterimi = Convert.ToInt32(Reader["SIPARISADIMIURUNGOSTERIMI"]);
                p.UygulamaMobilTema = Convert.ToInt32(Reader["UYGULAMAMOBILTEMA"]);
                p.HtmlCacheAktif = Convert.ToBoolean(Reader["HTMLCACHEAKTIF"]);
                p.EkVergiTipi = Convert.ToInt32(Reader["EKVERGITIPI"]);
                p.TekCekimSecenek = Convert.ToInt32(Reader["TEKCEKIMSECENEK"]);
                p.OdemeOnayPopupAktif = Convert.ToBoolean(Reader["ODEMEONAYPOPUPAKTIF"]);
                p.IlceOzelDegerAktif = Convert.ToBoolean(Reader["ILCEOZELDEGERAKTIF"]);

                try
                {
                    p.SiparisAyarlari = !string.IsNullOrEmpty(Reader["SIPARISAYARLARI"].ToString()) ? Reader["SIPARISAYARLARI"].ToString().ToJsonDeserialize<BLSiparisAyarlari>() : new BLSiparisAyarlari();
                }
                catch (Exception)
                {
                    p.SiparisAyarlari = new BLSiparisAyarlari();
                }

                try
                {
                    p.EArsivEFaturaAyarlari = !string.IsNullOrEmpty(Reader["EARSIVEFATURAAYARLARI"].ToString()) ? Reader["EARSIVEFATURAAYARLARI"].ToString().ToJsonDeserialize<BLEArsivFaturaAyarlari>() : new BLEArsivFaturaAyarlari();
                }
                catch (Exception)
                {
                    p.EArsivEFaturaAyarlari = new BLEArsivFaturaAyarlari();
                }

                #region Firma Bilgileri

                try
                {
                    p.FirmaBilgileri = !string.IsNullOrEmpty(Reader["FIRMABILGILERI"].ToString()) ? Reader["FIRMABILGILERI"].ToString().ToJsonDeserialize<BLFirmaBilgileri>() : new BLFirmaBilgileri();
                    if (p.FirmaBilgileri.Equals(new BLFirmaBilgileri()))
                    {
                        p.FirmaBilgileri.Adres = p.Adres;
                        p.FirmaBilgileri.Faks = p.Faks;
                        p.FirmaBilgileri.FirmaAdi = p.FirmaAdi;
                        p.FirmaBilgileri.FirmaMail = p.FirmaMail;
                        p.FirmaBilgileri.IlceID = p.IlceID;
                        p.FirmaBilgileri.SehirID = p.SehirID;
                        p.FirmaBilgileri.SektorId = p.SektorId;
                        p.FirmaBilgileri.Telefon = p.Telefon;
                        p.FirmaBilgileri.UlkeID = p.UlkeID;
                        p.FirmaBilgileri.UlkeKodu = p.UlkeKodu;
                        p.FirmaBilgileri.VergiDairesi = p.VergiDairesi;
                        p.FirmaBilgileri.VergiNo = p.VergiNo;
                        p.FirmaBilgileri.YetkiliKisi = p.YetkiliKisi;
                        //p.FirmaBilgileri.TicariSicilNo = "";
                        //p.FirmaBilgileri.MersisNo = "";
                    }
                }
                catch (Exception)
                {
                    p.FirmaBilgileri = new BLFirmaBilgileri();
                    p.FirmaBilgileri.Adres = p.Adres;
                    p.FirmaBilgileri.Faks = p.Faks;
                    p.FirmaBilgileri.FirmaAdi = p.FirmaAdi;
                    p.FirmaBilgileri.FirmaMail = p.FirmaMail;
                    p.FirmaBilgileri.IlceID = p.IlceID;
                    p.FirmaBilgileri.SehirID = p.SehirID;
                    p.FirmaBilgileri.SektorId = p.SektorId;
                    p.FirmaBilgileri.Telefon = p.Telefon;
                    p.FirmaBilgileri.UlkeID = p.UlkeID;
                    p.FirmaBilgileri.UlkeKodu = p.UlkeKodu;
                    p.FirmaBilgileri.VergiDairesi = p.VergiDairesi;
                    p.FirmaBilgileri.VergiNo = p.VergiNo;
                    p.FirmaBilgileri.YetkiliKisi = p.YetkiliKisi;
                    p.FirmaBilgileri.TicariSicilNo = "";
                    p.FirmaBilgileri.MersisNo = "";
                }

                #endregion Firma Bilgileri


                try
                {
                    p.SiteYonetimAyar = !string.IsNullOrEmpty(Reader["SITEYONETIMAYAR"].ToString()) ? Reader["SITEYONETIMAYAR"].ToString().ToJsonDeserialize<BLSiteYonetimAyar>() : new BLSiteYonetimAyar();
                }
                catch (Exception)
                {
                    p.SiteYonetimAyar = new BLSiteYonetimAyar();
                }

                p.SiteYonetimAyar.SeoAyar = Reader["SEOAYAR"].ToJsonDeserialize<BLSeoAyar>();
                p.WebHookAktif = Reader["WEBHOOKAKTIF"] != DBNull.Value ? Convert.ToBoolean(Reader["WEBHOOKAKTIF"]) : false;

                p.SiparisIptalSiniri = Convert.ToInt32(Reader["SIPARISIPTALSINIRI"]);
                p.SiparisIptalSinirTipi = Convert.ToInt32(Reader["SIPARISIPTALSINIRTIPI"]);
                p.MagazaModulAyar = Reader["MAGAZAMODULAYAR"].ToJsonDeserialize<BLMagazaModulAyar>();
                p.MagazaModulAyar.Aktif = Convert.ToBoolean(Reader["MAGAZAMODULUAKTIF"]);

                p.SiparisIptalUrunuYazdir = Reader["SIPARISIPTALURUNUYAZDIR"] != DBNull.Value ? Reader["SIPARISIPTALURUNUYAZDIR"].ToBoolean() : false;

                p.KargoBilgilendirmeMail = Reader["KARGOBILGILENDIRMEMAIL"] != DBNull.Value ? Reader["KARGOBILGILENDIRMEMAIL"].ToBoolean() : false;
                p.UrunAyar = Reader["URUNAYAR"].ToJsonDeserialize<BLUrunAyar>();
                p.OdemeAyarlari = Reader["ODEMEAYARLARI"].ToJsonDeserialize<BLOdemeAyar>();
                p.CdnAyar = Reader["CDNAYAR"].ToJsonDeserialize<BLCdnAyar>();

                p.MinioSettings = Reader["MINIOAYARLARI"].ToJsonDeserialize<MinioSetting>();


                ayarlar.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return ayarlar;
        }

        public async Task<List<BLSiteAyarlari>> SelectAsync(int itemID = 0, string domainName = "", CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<BLSiteAyarlari> ayarlar = new List<BLSiteAyarlari>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT 	ID
                                      , DUZENLEYENKULLANICI_ID
                                      , DUZENLEMETARIHI
                                      , ALANADI
                                      , TICIMAXPAKET_ID
                                      , TICIMAXEKMENU
                                      , PREMIUMTEMA
                                      , LISANSTARIHI
                                      , VIPMUSTERI
                                      , FIRMABILGILERI
                                      , FIRMAADI
                                      , SEKTOR_ID
                                      , YETKILIKISI
                                      , TELEFON
                                      , FAKS
                                      , VERGIDAIRESI
                                      , VERGINO
                                      , MAIL
                                      , SIPARISMAIL
                                      , ADRES
                                      , ULKE_ID
                                      , ULKEKODU
                                      , SEHIR_ID
                                      , ILCE_ID
                                      , HAVALEODEME
                                      , KREDIKARTIODEME
                                      , KREDIKARTIONPROVIZYON
                                      , KAPIDAODEME
                                      , PAYPALODEME
                                      , MOBILODEME
                                      , MAILORDERODEME
                                      , BKMEXPRESSODEME
                                      , BKMEXPRESSMERCHANTID
                                      , PAYUAYAR
                                      , IPARAODEME
                                      , CARIODEME
                                      , PAYPALMAIL
                                      , HAVALEINDIRIMYUZDE
                                      , TEKCEKIMINDIRIMYUZDE
                                      , KAPIDAODEMEEKUCRET
                                      , UCRETSIZKARGOLIMITI
                                      , MINIMUMALISVERISTUTARI
                                      , MINIMUMALISVERISTUTARIUYARI
                                      , MAILSERVISSAGLAYICI
                                      , SMTPSERVER
                                      , SMTPPORT
                                      , SMTPENABLESSL
                                      , SMTPGONDERENMAIL
                                      , SMTPGONDERENISIM
                                      , SMTPUSER
                                      , SMTPPASS
                                      , SEOGOOGLEDOGRULAMA
                                      , SEOYANDEXDOGRULAMA
                                      , SEOYAHOODOGRULAMA
                                      , SEOMSNDOGRULAMA
                                      , SEOALEXADOGRULAMA
                                      , PINTERESTDOGRULAMA
                                      , SEOANALYTICS
                                      , SEOANALYTICSDOMAIN
                                      , YANDEXMETRICAID
                                      , URUNFIYATGOSTER
                                      , URUNFIYATUYEGOSTER
                                      , URUNKDVDAHILGOSTER
                                      , URUNVITRINRASTGELEGOSTER
                                      , URUNSTOKKODUGOSTER
                                      , URUNMARKAGOSTER
                                      , URUNSTOKOLMAYANGOSTER
                                      , URUNZIYARETSAYIGOSTER
                                      , URUNFOTOGRAFBUYUT
                                      , URUNSEPETATPOPUP
                                      , SEPETEEKLESEPETEYONLENDIR
                                      , URUNLISTEURUNSAYISI
                                      , URUNDETAYADETTIPI
                                      , URUNDETAYBENZERURUNGOSTER
                                      , URUNDETAYTAKSITACIKLAMAGOSTER
                                      , URUNDETAYTEDARIKCIGOSTER
                                      , SITEBAKIMMODU
                                      , ANASAYFAANIMASYON
                                      , KATEGORIDEMARKAGOSTER
                                      , HEDIYEPAKETINOTUGOSTER
                                      , HEDIYEPAKETITUTARI
                                      , KARGOHESAPLAMAMODULU
                                      , DOVIZOTOMATIKGUNCELLE
                                      , SIPARISDURUMMAILBILGILENDIRME
                                      , TELEFONSIPARIS
                                      , TELEFONSIPARISMETNI
                                      , FACEBOOKAPPID
                                      , TWITTERKULLANICI
                                      , FACEBOOKSECRET
                                      , PARAPUANAKTIF
                                      , PARAPUANLIMIT
                                      , ANASAYFAPOPUPGOSTER
                                      , ANASAYFAPOPUPMETNI
                                      , OZELTASARIM
                                      , OZELTASARIMMOBIL
                                      , SLIDERTIPI
                                      , MENUTIPI
                                      , SLIDERBANNER
                                      , USTSOLMENU
                                      , SITEMASTERPAGE
                                      , MOBILSITE
                                      , MOBILSITETEMA
                                      , URUNLISTEEKSECENEKTIPI
                                      , SITETEMA
                                      , CAPTCHAAKTIF
                                      , UYELIKSIZALISVERISAKTIF
                                      , SITEUYELIKZORUNLU
                                      , URUNDETAYUYELIKZORUNLU
                                      , SMSAKTIF
                                      , SMSKULLANICI
                                      , SMSSIFRE
                                      , SMSORGINATOR
                                      , SMSSERVISSAGLAYICI
                                      , SSLAKTIF
                                      , SSLZORUNLU
                                      , WWWAKTIF
                                      , KARGOHESAPLAMATIPI
                                      , ADWORDSDONUSUMKODU
                                      , ADWORDSYENIDENPAZARLAMAKODU
                                      , ROBOTS
                                      , DOVIZFIYATGOSTER
                                      , TESLIMATTARIHIGOSTER
                                      , FIRSATMODULUAKTIF
                                      , ADRESPOSTAKODUZORUNLU
                                      , ADRESPOSTAKODUDOGRULAMA
                                      , ADRESTKNZORUNLU
                                      , ADRESTCKNDOGRULAMA
                                      , GOOGLEMERCHANTAKTIF
                                      , ADWORDSDONUSUM_CONVERSIONID
                                      , ADWORDSDONUSUM_CONVERSIONLABEL
                                      , FACEBOOKDONUSUM_ID
                                      , HEDIYECEKIOLUSTURMAMINIMUMALISVERIS
                                      , URUNDETAYTABAKTIF
                                      , FACEBOOKYORUMAKTIF
                                      , TWITTERAPIKEY
                                      , TWITTERAPISECRET
                                      , GOOGLECLIENTID
                                      , GOOGLECLIENTSECRET
                                      , URUNOZELNOTAKTIF
                                      , URUNDOSYAYUKLEMEAKTIF
                                      , URUNKAMPANYAAKTIF
                                      , TEDARIKCIGUNCELLEMEONAYGEREKLI
                                      , ODEMEDESEPETBOSALT
                                      , STOKOLMAYANISONDAGOSTER
                                      , SITEDILIAKTIF
                                      , VARSAYILANSITEDILI
                                      , KULLANILABILIRDILLER
                                      , PARABIRIMIAKTIF
                                      , VARSAYILANPARABIRIMI
                                      , VARSAYILANPARABIRIMIDILKODU
                                      , KULLANILABILIRPARABIRIMI
                                      , ULKEIPYONLENDIR
                                      , PARCALIODEMEAKTIF
                                      , SITEGIRISIZINAKTIF
                                      , RICHSNIPPETAKTIF
                                      , RICHSNIPPETURUNADI
                                      , RICHSNIPPETMARKA
                                      , RICHSNIPPETKATEGORI
                                      , RICHSNIPPETFIYAT
                                      , RICHSNIPPETSTOK
                                      , RICHSNIPPETRESIM
                                      , RICHSNIPPETURUNKODU
                                      , RICHSNIPPETACIKLAMA
                                      , KOMBINURUNAKTIF
                                      , CSSYETKI
                                      , CDNAKTIF
                                      , CDNADRES
                                      , CAYMAHAKKINIAYRIGOSTER
                                      , ODEMEYAPILMAMISSIPARISIPTALEDILEBILIR
                                      , UCBOYUTRESIMAKTIF
                                      , URUNLISTE2RESIMAKTIF
                                      , KENDINTASARLAAKTIF
                                      , URUNDETAYSTOKADEDIGOSTER
                                      , RESIMPNGKAYDET
                                      , SIPARISSEPETBILGILENDIRMEAKTIF
                                      , URUNADEDIONDALIKLI
                                      , KATEGORIDESLIDERGOSTER
                                      , ASORTIAKTIF
                                      , SAYFALAMASECENEK
                                      , KREDILISATISAKTIF
                                      , SIPARISADIMINDAMENUGOSTER
                                      , URUNDETAYMINALIMGOSTER
                                      , UYEGIRISPOPUPAKTIF
                                      , ARAMADAKATEGORIAKTIF
                                      , KATEGORIDEUSTFILTRAKTIF
                                      , GARANTIPAYAKTIF
                                      , JSDEGISKENAKTIF
                                      , URUNAKSESUARAKTIF
                                      , SEPETTEGUNCELLEMEBUTONAKTIF
                                      , ADRESTARIFIAKTIF
                                      , UYEALISVERISHEDEFIAKTIF
                                      , UYELIKMAILONAYIAKTIF
                                      , UYELIKYONETICIONAYIAKTIF
                                      , ILGILIURUNRESIMAKTIF
                                      , FACEBOOKPIXELID
                                      , CRITEOACCOUNTID
                                      , YOTPOAPPKEY
                                      , STOKOLMAYANURUNDEBENZERGOSTER
                                      , URUNLISTEHIZLIBAKISAKTIF
                                      , CACHEAKTIF
                                      , CACHEEXPIRESMINUTES
                                      , CACHEVARYBYCUSTOM
                                      , KAPIDAODEMESMSONAYAKTIF
                                      , SLACKPOSTURL
                                      , IYZIPAYAKTIF
                                      , IYZIPAYAPIKEY
                                      , IYZIPAYSECUREKEY
                                      , IYZIPAYBASEURL
                                      , URUNDETAYSOSYALMEDYAAKTIF
                                      , URUNFILTREAKTIF
                                      , URUNFILTREAYAR
                                      , KAMPANYATEKLIFAKTIF
                                      , GLOBALPOPUPAKTIF
                                      , KAPIDAODEMELIMITI
                                      , KAPIDAODEMEUSTLIMITI
                                      , GOOGLEAPIPROJENO
                                      , ODEMEADIMINDASOZLESMEGOSTER
                                      , PASIFSEKMEMESAJI
                                      , RESIMSIZURUNGOSTER
                                      , BUKOLIAKTIF
                                      , BUKOLISERVISSIFRESI
                                      , KAMPANYABANNERAKTIF
                                      , MOBILDESEPETEGITAKTIF
                                      , MOBILUYGULAMAAKTIF
                                      , MOBILUYGULAMABILGILERI
                                      , SIPARISODEMESAATSINIRI
                                      , SLIDERPARAMETRELERI
                                      , ULKEGOSTERME
                                      , TINYPNGAYAR
                                      , HOPIAUTHINFO
                                      , SIPARISADIMIURUNGOSTERIMI
                                      , UYGULAMAMOBILTEMA
                                      , ARAMASECENEKLERI
                                      , URUNLISTEONYAZIAKTIF
                                      , SEPETKDVDAHILGOSTER
                                      , UYGULAMASEOANALYTICS
                                      , DINAMIKFORMPARAMETRE
                                      , HTMLCACHEAKTIF
                                      , ZUBIZUAYAR
                                      , HEPSIPAYAYAR
                                      , SEPETTEMARKAGOSTER
                                      , EKVERGITIPI
                                      , URUNDETAYZOOM
                                      , SEPETTEFAVORIURUNLERIGOSTER
                                      , KAPIDAODEMEONAYAYAR
                                      , TEKCEKIMSECENEK
                                      , DETAYLIUYELIKPARAMETRE
                                      , ODEMEONAYPOPUPAKTIF
                                      , ILCEOZELDEGERAKTIF
                                      , NESTPAYAKTIF
                                      , SIPARISAYARLARI
                                      , SEPETAYARLARI
                                      , KREDILISATISAYAR
                                      , EARSIVEFATURAAYARLARI
                                      , SITEYONETIMAYAR
                                      , ADRESAYARLARI
                                      , INGTAGRAMAYAR
                                      , HTMLCACHEAYAR
                                      , CARDTEKAKTIF
                                      , CARDTEKAYAR
                                      , PANELDILAYAR
                                      , ELASTICSEARCH
                                      , WEBHOOKAKTIF
                                      , BLOGAYAR
                                      , PAYBYMEAKTIF
                                      , PAYBYMEAYAR
                                      , PAYGURUAKTIF
                                      , PAYGURUAYAR
                                      , URUNAYAR
                                      , IFNULL((SELECT AYARLAR FROM depo_ayarlar GROUP BY DUZENLEMETARIHI ORDER BY ID DESC LIMIT 1),DEPOAYAR) AS DEPOAYAR
                                      , IFNULL((SELECT AYARLAR FROM depo_ayarlar GROUP BY DUZENLEMETARIHI ORDER BY ID DESC LIMIT 1, 1),DEPOAYAR) AS DEPOESKIAYAR
                                      , PAYNETAKTIF
                                      , PAYNETAYAR
                                      , TELRAKTIF
                                      , TELRAYAR
                                      , CACHEAYAR
                                      , SIPARISIPTALSINIRI
                                      , SIPARISIPTALSINIRTIPI
                                      , COMPAYAKTIF
                                      , COMPAYAYAR
                                      , GOOGLEANKETAYAR
                                      , FATURAAYAR
                                      , UYEAYAR
                                      , ADWORDSDONUSUMENTEGRASYONTIPI
                                      , ODEMEAYARLARI
                                      , MAILAYAR
                                      , SMSAYAR
                                      , DIJITALPAZARLAMA
                                      , IADEKARGOAYAR
                                      , KARGOBILGILENDIRMEDURUMU
                                      , SOSYALMEDYA
                                      , SEOAYAR
                                      , CDNAYAR
                                      , MAGAZAMODULAYAR
                                      , MAGAZAMODULUAKTIF
                                      , PASIFSEKMEMESAJLARI
                                      , SIPARISIPTALURUNUYAZDIR
                                      , KARGOBILGILENDIRMEMAIL
                                FROM siteayarlari WHERE 1 ";

            if (!string.IsNullOrEmpty(domainName))
            {
                cmd.CommandText += " AND ALANADI = @ALANADI ";
                cmd.Parameters.Add("@ALANADI", MySqlDbType.VarChar).Value = domainName.Replace("www.", "").ToLower();
            }

            cmd.CommandText += " ORDER BY ID DESC LIMIT 1";

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                BLSiteAyarlari p = new BLSiteAyarlari();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.DuzenleyenKullaniciID = Convert.ToInt32(Reader["DUZENLEYENKULLANICI_ID"]);
                p.DuzenlemeTarihi = Convert.ToDateTime(Reader["DUZENLEMETARIHI"]);
                p.AlanAdi = Reader["ALANADI"].ToString();
                p.TicimaxPaketID = Convert.ToInt32(Reader["TICIMAXPAKET_ID"]);
                p.TicimaxEkMenu = Reader["TICIMAXEKMENU"].ToString();
                p.PremiumTema = Convert.ToInt32(Reader["PREMIUMTEMA"]);
                p.LisansTarihi = Convert.ToDateTime(Reader["LISANSTARIHI"]);
                p.VipMusteri = Convert.ToBoolean(Reader["VIPMUSTERI"]);
                p.FirmaAdi = Reader["FIRMAADI"].ToString();
                p.SektorId = Convert.ToInt32(Reader["SEKTOR_ID"]);
                p.YetkiliKisi = Reader["YETKILIKISI"].ToString();
                p.Telefon = Reader["TELEFON"].ToString();
                p.Faks = Reader["FAKS"].ToString();
                p.VergiDairesi = Reader["VERGIDAIRESI"].ToString();
                p.VergiNo = Reader["VERGINO"].ToString();
                p.FirmaMail = Reader["MAIL"].ToString();
                p.SiparisMail = Reader["SIPARISMAIL"].ToString().Trim().Length < 6 ? p.FirmaMail : Reader["SIPARISMAIL"].ToString();
                p.Adres = Reader["ADRES"].ToString();
                p.UlkeID = Convert.ToInt32(Reader["ULKE_ID"]);
                p.UlkeKodu = Reader["ULKEKODU"].ToString();
                p.SehirID = Convert.ToInt32(Reader["SEHIR_ID"]);
                p.IlceID = Convert.ToInt32(Reader["ILCE_ID"]);
                p.KapidaOdemeEkUcret = Convert.ToDouble(Reader["KAPIDAODEMEEKUCRET"]);
                p.UcretsizKargoLimiti = Convert.ToDouble(Reader["UCRETSIZKARGOLIMITI"]);
                p.MinimumAlisverisTutari = Convert.ToDouble(Reader["MINIMUMALISVERISTUTARI"]);
                p.MinimumAlisverisTutariUyari = Reader["MINIMUMALISVERISTUTARIUYARI"].ToString();
                p.SiteBakimModu = Convert.ToBoolean(Reader["SITEBAKIMMODU"]);
                p.AnasayfaAnimasyon = Convert.ToBoolean(Reader["ANASAYFAANIMASYON"]);
                p.KategorideMarkaGoster = Convert.ToBoolean(Reader["KATEGORIDEMARKAGOSTER"]);
                p.HediyePaketiNotuGoster = Convert.ToBoolean(Reader["HEDIYEPAKETINOTUGOSTER"]);
                p.HediyePaketiTutari = Convert.ToDouble(Reader["HEDIYEPAKETITUTARI"]);
                p.KargoHesaplamaModulu = Convert.ToBoolean(Reader["KARGOHESAPLAMAMODULU"]);
                p.DovizOtomatikGuncelle = Convert.ToBoolean(Reader["DOVIZOTOMATIKGUNCELLE"]);
                p.SiparisDurumMailBilgilendirme = Convert.ToBoolean(Reader["SIPARISDURUMMAILBILGILENDIRME"]);
                p.TelefonSiparis = Convert.ToBoolean(Reader["TELEFONSIPARIS"]);
                p.TelefonSiparisMetni = Reader["TELEFONSIPARISMETNI"].ToString();
                p.FacebookAppID = Reader["FACEBOOKAPPID"].ToString();
                p.TwitterKullanici = Reader["TWITTERKULLANICI"].ToString();
                p.FacebookSecret = Reader["FACEBOOKSECRET"].ToString();
                p.ParaPuanAktif = Convert.ToBoolean(Reader["PARAPUANAKTIF"]);
                p.ParaPuanLimit = Convert.ToDouble(Reader["PARAPUANLIMIT"]);
                p.AnasayfaPopupGoster = Convert.ToBoolean(Reader["ANASAYFAPOPUPGOSTER"]);
                p.AnasayfaPopupMetni = Reader["ANASAYFAPOPUPMETNI"].ToString();
                p.OzelTasarim = Convert.ToBoolean(Reader["OZELTASARIM"]);
                p.OzelTasarimMobil = Convert.ToBoolean(Reader["OZELTASARIMMOBIL"]);
                p.SliderTipi = Convert.ToInt32(Reader["SLIDERTIPI"]);
                p.MenuTipi = Convert.ToInt32(Reader["MENUTIPI"]);
                p.SliderBanner = Convert.ToInt32(Reader["SLIDERBANNER"]);
                p.UstSolMenu = Convert.ToInt32(Reader["USTSOLMENU"]);
                p.SiteMasterPage = Convert.ToInt32(Reader["SITEMASTERPAGE"]);
                p.MobilSite = Convert.ToBoolean(Reader["MOBILSITE"]);
                p.MobilSiteTema = Convert.ToInt32(Reader["MOBILSITETEMA"]);
                p.SiteTema = Reader["SITETEMA"].ToString();
                p.CaptchaAktif = Convert.ToBoolean(Reader["CAPTCHAAKTIF"]);
                p.KargoHesaplamaTipi = Convert.ToInt32(Reader["KARGOHESAPLAMATIPI"]);
                p.Robots = Reader["ROBOTS"].ToString();
                p.DovizFiyatGoster = Convert.ToBoolean(Reader["DOVIZFIYATGOSTER"]);
                p.TeslimatTarihiGoster = Convert.ToBoolean(Reader["TESLIMATTARIHIGOSTER"]);
                p.FirsatModuluAktif = Convert.ToBoolean(Reader["FIRSATMODULUAKTIF"]);
                p.AdresPostaKoduZorunlu = Convert.ToBoolean(Reader["ADRESPOSTAKODUZORUNLU"]);
                p.AdresPostaKoduDogrulama = Convert.ToBoolean(Reader["ADRESPOSTAKODUDOGRULAMA"]);
                p.AdresTCKNZorunlu = Convert.ToBoolean(Reader["ADRESTKNZORUNLU"]);
                p.AdresTCKNDogrulama = Convert.ToBoolean(Reader["ADRESTCKNDOGRULAMA"]);
                p.FacebookDonusumID = Reader["FACEBOOKDONUSUM_ID"].ToString();
                p.HediyeCekiOlusturmaMinimumAlisveris = Convert.ToDouble(Reader["HEDIYECEKIOLUSTURMAMINIMUMALISVERIS"]);
                p.UrunDetayTabAktif = Convert.ToBoolean(Reader["URUNDETAYTABAKTIF"]);
                p.FacebookYorumAktif = Convert.ToBoolean(Reader["FACEBOOKYORUMAKTIF"]);
                p.TwitterApiKey = Reader["TWITTERAPIKEY"].ToString();
                p.TwitterApiSecret = Reader["TWITTERAPISECRET"].ToString();
                p.GoogleClientID = Reader["GOOGLECLIENTID"].ToString();
                p.GoogleClientSecret = Reader["GOOGLECLIENTSECRET"].ToString();
                p.UrunOzelNotAktif = Convert.ToBoolean(Reader["URUNOZELNOTAKTIF"]);
                p.UrunDosyaYuklemeAktif = Convert.ToBoolean(Reader["URUNDOSYAYUKLEMEAKTIF"]);
                p.UrunKampanyaAktif = Convert.ToBoolean(Reader["URUNKAMPANYAAKTIF"]);
                p.TedarikciGuncellemeOnayGerekli = Convert.ToBoolean(Reader["TEDARIKCIGUNCELLEMEONAYGEREKLI"]);
                p.StokOlmayaniSondaGoster = Convert.ToBoolean(Reader["STOKOLMAYANISONDAGOSTER"]);
                p.DilIcerikYoksaVarsayilanGoster = false;
                p.SiteDiliAktif = Convert.ToBoolean(Reader["SITEDILIAKTIF"]);
                p.VarsayilanSiteDili = Reader["VARSAYILANSITEDILI"].ToString();
                p.KullanilabilirDiller = Reader["KULLANILABILIRDILLER"].ToString();
                p.ParaBirimiAktif = Convert.ToBoolean(Reader["PARABIRIMIAKTIF"]);
                p.VarsayilanParaBirimi = Reader["VARSAYILANPARABIRIMI"].ToString();
                p.VarsayilanParaBirimiDilKodu = Reader["VARSAYILANPARABIRIMIDILKODU"].ToString();
                p.KullanilabilirParaBirimi = Reader["KULLANILABILIRPARABIRIMI"].ToString();
                p.UlkeIPYonlendir = Convert.ToBoolean(Reader["ULKEIPYONLENDIR"]);
                p.SiteGirisIzinAktif = Convert.ToBoolean(Reader["SITEGIRISIZINAKTIF"]);
                p.KombinUrunAktif = Convert.ToBoolean(Reader["KOMBINURUNAKTIF"]);
                p.CssYetki = Convert.ToBoolean(Reader["CSSYETKI"]);
                p.CdnAktif = Convert.ToBoolean(Reader["CDNAKTIF"]);
                p.CdnAdres = Reader["CDNADRES"].ToString();
                p.CaymaHakkiniAyriGoster = Convert.ToBoolean(Reader["CAYMAHAKKINIAYRIGOSTER"]);
                p.OdemeYapilmamisSiparisIptalEdilebilir = Convert.ToBoolean(Reader["ODEMEYAPILMAMISSIPARISIPTALEDILEBILIR"]);
                p.UcBoyutResimAktif = Convert.ToBoolean(Reader["UCBOYUTRESIMAKTIF"]);
                p.KendinTasarlaAktif = Convert.ToBoolean(Reader["KENDINTASARLAAKTIF"]);
                p.ResimPngKaydet = Convert.ToBoolean(Reader["RESIMPNGKAYDET"]);
                p.KategorideSliderGoster = Convert.ToBoolean(Reader["KATEGORIDESLIDERGOSTER"]);
                p.AsortiAktif = Convert.ToBoolean(Reader["ASORTIAKTIF"]);
                p.SayfalamaSecenek = Convert.ToInt32(Reader["SAYFALAMASECENEK"]);
                p.SiparisAdimindaMenuGoster = Convert.ToBoolean(Reader["SIPARISADIMINDAMENUGOSTER"]);
                p.AramadaKategoriAktif = Convert.ToBoolean(Reader["ARAMADAKATEGORIAKTIF"]);
                p.KategoriUstFiltreAktif = Convert.ToBoolean(Reader["KATEGORIDEUSTFILTRAKTIF"]);
                p.JSDegiskenAktif = Convert.ToBoolean(Reader["JSDEGISKENAKTIF"]);
                p.UrunAksesuarAktif = Convert.ToBoolean(Reader["URUNAKSESUARAKTIF"]);
                p.AdresTarifiAktif = Convert.ToBoolean(Reader["ADRESTARIFIAKTIF"]);
                p.IlgiliUrunResimAktif = Convert.ToBoolean(Reader["ILGILIURUNRESIMAKTIF"]);
                p.YotpoAppKey = Reader["YOTPOAPPKEY"].ToString();
                p.CacheAktif = Convert.ToBoolean(Reader["CACHEAKTIF"]);
                p.CacheExpiresMinutes = Convert.ToInt32(Reader["CACHEEXPIRESMINUTES"]);
                p.CacheVaryByCustom = Reader["CACHEVARYBYCUSTOM"].ToString();
                p.KapidaOdemeSmsOnayAktif = Convert.ToBoolean(Reader["KAPIDAODEMESMSONAYAKTIF"]);
                p.SlackPostUrl = Reader["SLACKPOSTURL"].ToString();
                p.UrunFiltreAktif = Convert.ToBoolean(Reader["URUNFILTREAKTIF"]);
                p.KampanyaBannerAktif = Convert.ToBoolean(Reader["KAMPANYABANNERAKTIF"]);
                p.KampanyaTeklifAktif = Convert.ToBoolean(Reader["KAMPANYATEKLIFAKTIF"]);
                p.GlobalPopupAktif = Convert.ToBoolean(Reader["GLOBALPOPUPAKTIF"]);
                p.KapidaOdemeLimiti = Convert.ToDouble(Reader["KAPIDAODEMELIMITI"]);
                p.KapidaOdemeUstLimiti = Convert.ToDouble(Reader["KAPIDAODEMEUSTLIMITI"]);
                p.GoogleApiProjeNo = Reader["GOOGLEAPIPROJENO"].ToString();
                p.OdemeAdimindaSozlesmeGoster = Convert.ToBoolean(Reader["ODEMEADIMINDASOZLESMEGOSTER"]);
                //p.PasifSekmeMesaji = Reader["PASIFSEKMEMESAJI"].ToString();

                p.ResimsizUrunGoster = Convert.ToBoolean(Reader["RESIMSIZURUNGOSTER"]);
                p.BuKoliAktif = Convert.ToBoolean(Reader["BUKOLIAKTIF"]);
                p.BuKoliServisSifresi = Reader["BUKOLISERVISSIFRESI"].ToString();
                p.MobilUygulamaAktif = Convert.ToBoolean(Reader["MOBILUYGULAMAAKTIF"]);

                p.SiparisOdemeSaatSiniri = Convert.ToInt32(Reader["SIPARISODEMESAATSINIRI"]);
                p.UlkeGosterme = Convert.ToBoolean(Reader["ULKEGOSTERME"]);
                p.SiparisAdimiUrunGosterimi = Convert.ToInt32(Reader["SIPARISADIMIURUNGOSTERIMI"]);
                p.UygulamaMobilTema = Convert.ToInt32(Reader["UYGULAMAMOBILTEMA"]);
                p.HtmlCacheAktif = Convert.ToBoolean(Reader["HTMLCACHEAKTIF"]);
                p.EkVergiTipi = Convert.ToInt32(Reader["EKVERGITIPI"]);
                p.TekCekimSecenek = Convert.ToInt32(Reader["TEKCEKIMSECENEK"]);
                p.OdemeOnayPopupAktif = Convert.ToBoolean(Reader["ODEMEONAYPOPUPAKTIF"]);
                p.IlceOzelDegerAktif = Convert.ToBoolean(Reader["ILCEOZELDEGERAKTIF"]);

                try
                {
                    p.SiparisAyarlari = !string.IsNullOrEmpty(Reader["SIPARISAYARLARI"].ToString()) ? Reader["SIPARISAYARLARI"].ToString().ToJsonDeserialize<BLSiparisAyarlari>() : new BLSiparisAyarlari();
                }
                catch (Exception)
                {
                    p.SiparisAyarlari = new BLSiparisAyarlari();
                }

                try
                {
                    p.EArsivEFaturaAyarlari = !string.IsNullOrEmpty(Reader["EARSIVEFATURAAYARLARI"].ToString()) ? Reader["EARSIVEFATURAAYARLARI"].ToString().ToJsonDeserialize<BLEArsivFaturaAyarlari>() : new BLEArsivFaturaAyarlari();
                }
                catch (Exception)
                {
                    p.EArsivEFaturaAyarlari = new BLEArsivFaturaAyarlari();
                }

                #region Firma Bilgileri

                try
                {
                    p.FirmaBilgileri = !string.IsNullOrEmpty(Reader["FIRMABILGILERI"].ToString()) ? Reader["FIRMABILGILERI"].ToString().ToJsonDeserialize<BLFirmaBilgileri>() : new BLFirmaBilgileri();
                    if (p.FirmaBilgileri.Equals(new BLFirmaBilgileri()))
                    {
                        p.FirmaBilgileri.Adres = p.Adres;
                        p.FirmaBilgileri.Faks = p.Faks;
                        p.FirmaBilgileri.FirmaAdi = p.FirmaAdi;
                        p.FirmaBilgileri.FirmaMail = p.FirmaMail;
                        p.FirmaBilgileri.IlceID = p.IlceID;
                        p.FirmaBilgileri.SehirID = p.SehirID;
                        p.FirmaBilgileri.SektorId = p.SektorId;
                        p.FirmaBilgileri.Telefon = p.Telefon;
                        p.FirmaBilgileri.UlkeID = p.UlkeID;
                        p.FirmaBilgileri.UlkeKodu = p.UlkeKodu;
                        p.FirmaBilgileri.VergiDairesi = p.VergiDairesi;
                        p.FirmaBilgileri.VergiNo = p.VergiNo;
                        p.FirmaBilgileri.YetkiliKisi = p.YetkiliKisi;
                        //p.FirmaBilgileri.TicariSicilNo = "";
                        //p.FirmaBilgileri.MersisNo = "";
                    }
                }
                catch (Exception)
                {
                    p.FirmaBilgileri = new BLFirmaBilgileri();
                    p.FirmaBilgileri.Adres = p.Adres;
                    p.FirmaBilgileri.Faks = p.Faks;
                    p.FirmaBilgileri.FirmaAdi = p.FirmaAdi;
                    p.FirmaBilgileri.FirmaMail = p.FirmaMail;
                    p.FirmaBilgileri.IlceID = p.IlceID;
                    p.FirmaBilgileri.SehirID = p.SehirID;
                    p.FirmaBilgileri.SektorId = p.SektorId;
                    p.FirmaBilgileri.Telefon = p.Telefon;
                    p.FirmaBilgileri.UlkeID = p.UlkeID;
                    p.FirmaBilgileri.UlkeKodu = p.UlkeKodu;
                    p.FirmaBilgileri.VergiDairesi = p.VergiDairesi;
                    p.FirmaBilgileri.VergiNo = p.VergiNo;
                    p.FirmaBilgileri.YetkiliKisi = p.YetkiliKisi;
                    p.FirmaBilgileri.TicariSicilNo = "";
                    p.FirmaBilgileri.MersisNo = "";
                }

                #endregion Firma Bilgileri

                try
                {
                    p.SiteYonetimAyar = !string.IsNullOrEmpty(Reader["SITEYONETIMAYAR"].ToString()) ? Reader["SITEYONETIMAYAR"].ToString().ToJsonDeserialize<BLSiteYonetimAyar>() : new BLSiteYonetimAyar();
                }
                catch (Exception)
                {
                    p.SiteYonetimAyar = new BLSiteYonetimAyar();
                }

                p.WebHookAktif = Reader["WEBHOOKAKTIF"] != DBNull.Value ? Convert.ToBoolean(Reader["WEBHOOKAKTIF"]) : false;

                p.SiparisIptalSiniri = Convert.ToInt32(Reader["SIPARISIPTALSINIRI"]);
                p.SiparisIptalSinirTipi = Convert.ToInt32(Reader["SIPARISIPTALSINIRTIPI"]);
                p.MagazaModulAyar = Reader["MAGAZAMODULAYAR"].ToJsonDeserialize<BLMagazaModulAyar>();
                p.MagazaModulAyar.Aktif = Convert.ToBoolean(Reader["MAGAZAMODULUAKTIF"]);

                p.SiparisIptalUrunuYazdir = Reader["SIPARISIPTALURUNUYAZDIR"] != DBNull.Value ? Reader["SIPARISIPTALURUNUYAZDIR"].ToBoolean() : false;

                p.KargoBilgilendirmeMail = Reader["KARGOBILGILENDIRMEMAIL"] != DBNull.Value ? Reader["KARGOBILGILENDIRMEMAIL"].ToBoolean() : false;
                p.UrunAyar = Reader["URUNAYAR"].ToJsonDeserialize<BLUrunAyar>();
                p.OdemeAyarlari = Reader["ODEMEAYARLARI"].ToJsonDeserialize<BLOdemeAyar>();
                p.CdnAyar = Reader["CDNAYAR"].ToJsonDeserialize<BLCdnAyar>();
                ayarlar.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return ayarlar;
        }

        public async Task<WarehouseSettings> SelectWarehouseSettingsAsync(int warehouseId, int storeId, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            WarehouseSettings p = new WarehouseSettings();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT 	ID
                                      , DUZENLEYENKULLANICI_ID
                                      , DUZENLEMETARIHI
                                      , DUZENLEYEN_IP
                                      , DEPO_ID
                                      , MAGAZA_ID
                                      , AYARLAR
                                FROM depo_ayarlar WHERE DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID ORDER BY ID DESC LIMIT 1";

            cmd.Parameters.Add(new MySqlParameter("@DEPO_ID", MySqlDbType.Int32)).Value = warehouseId;
            cmd.Parameters.Add(new MySqlParameter("@MAGAZA_ID", MySqlDbType.Int32)).Value = storeId;

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                p.Id = Convert.ToInt32(reader["ID"]);
                p.ChangeDate = reader["DUZENLEMETARIHI"].ToDateTime();
                p.ChangerUser = reader["DUZENLEYENKULLANICI_ID"].ToInt32();
                p.ChangerIpAddress = reader["DUZENLEYEN_IP"].ToString();
                p.WarehouseId = reader["DEPO_ID"].ToInt32();
                p.StoreId = reader["MAGAZA_ID"].ToInt32();
                p.Settings = reader["AYARLAR"].ToJsonDeserialize<BLDepoAyar>();
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return p;
        }

        public async Task UpdateWarehouseSettingsAsync(int userId, int warehouseId, int storeId, BLDepoAyar settings, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_ayarlar(DUZENLEYENKULLANICI_ID
                                                       ,DEPO_ID
                                                       ,MAGAZA_ID
                                                       ,AYARLAR)
                                                  VALUES(@DUZENLEYENKULLANICI_ID
                                                       ,@DEPO_ID
                                                       ,@MAGAZA_ID
                                                       ,@AYARLAR)";

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = warehouseId;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = storeId;
            cmd.Parameters.Add("@DUZENLEYENKULLANICI_ID", MySqlDbType.Int32).Value = userId;
            cmd.Parameters.Add("@AYARLAR", MySqlDbType.Text).Value = settings.ToJsonSerialize();
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }
    }
}