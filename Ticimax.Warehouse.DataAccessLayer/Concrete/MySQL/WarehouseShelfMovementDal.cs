using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseShelfMovementDal : IWarehouseShelfMovementDal
    {
        private MySqlConnection _cnn;

        public WarehouseShelfMovementDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<WarehouseShelfMovement>> GetListAsync(WarehouseShelfMovementFilter filter = null, WarehouseShelfMovementPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<WarehouseShelfMovement> list = new List<WarehouseShelfMovement>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT      drh.ID
                                          , drh.RAF_ID
                                          , drh.MEMBER_ID
                                          , drh.MEMBER_NAME
                                          , drh.PROCESS_TYPE
                                          , drh.CREATED_DATE
                                    FROM depo_raf_hareket AS drh
                                    WHERE 1 ";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                WarehouseShelfMovement drh = new WarehouseShelfMovement();
                drh.ID = reader["ID"].ToInt32();
                drh.ShelfId = reader["RAF_ID"].ToInt32();
                drh.MemberId = reader["MEMBER_ID"].ToInt32();
                drh.MemberName = reader["MEMBER_NAME"].ToString();
                drh.ProcessType = reader["PROCESS_TYPE"].ToString();
                drh.CreatedDate = reader["CREATED_DATE"].ToDateTime();
                list.Add(drh);
            }
            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return list;
        }


        public async Task AddAsync(WarehouseShelfMovement entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depo_raf_hareket
                                                (RAF_ID
                                                , MEMBER_ID
                                                , MEMBER_NAME
                                                , PROCESS_TYPE
                                                , CREATED_DATE)
                                    VALUES (
                                            @RAF_ID
                                          , @MEMBER_ID
                                          , @MEMBER_NAME
                                          , @PROCESS_TYPE
                                          , @CREATED_DATE)";

            cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = entity.ShelfId;
            cmd.Parameters.Add("@MEMBER_ID", MySqlDbType.Int32).Value = entity.MemberId;
            cmd.Parameters.Add("@MEMBER_NAME", MySqlDbType.VarChar).Value = entity.MemberName;
            cmd.Parameters.Add("@PROCESS_TYPE", MySqlDbType.VarChar).Value = entity.ProcessType;
            cmd.Parameters.Add("@CREATED_DATE", MySqlDbType.DateTime).Value = entity.CreatedDate;
            await cmd.ExecuteTransactionCommandAsync();
        }



        private void AppendFilter(ref MySqlCommand cmd, WarehouseShelfMovementFilter filter)
        {
            if (filter != null)
            {
                if (filter.ShelfId > 0)
                {
                    cmd.CommandText += " AND drh.RAF_ID = @RAF_ID";
                    cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = filter.ShelfId;
                }
                if (filter.DateStart.HasValue)
                {
                    cmd.CommandText += " AND drh.CREATED_DATE >= @DATE_START";
                    cmd.Parameters.Add("@DATE_START", MySqlDbType.DateTime).Value = filter.DateStart.Value;
                }
                if (filter.DateEnd.HasValue)
                {
                    cmd.CommandText += " AND drh.CREATED_DATE <= @DATE_END";
                    cmd.Parameters.Add("@DATE_END", MySqlDbType.DateTime).Value = filter.DateEnd.Value;
                }
                if (!string.IsNullOrEmpty(filter.ProducesType))
                {
                    cmd.CommandText += " AND drh.PROCESS_TYPE = @PROCESS_TYPE";
                    cmd.Parameters.Add("@PROCESS_TYPE", MySqlDbType.VarChar).Value = filter.ProducesType;
                }
            }
        }
    }
}