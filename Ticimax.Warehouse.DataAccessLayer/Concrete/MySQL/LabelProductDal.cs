using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class LabelProductDal : ILabelProductDal
    {
        private MySqlConnection _cnn;

        public LabelProductDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<LabelProductCheckResponseDto> LabelProductCheckAsync(LabelProductFilter filter, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            LabelProductCheckResponseDto response = new LabelProductCheckResponseDto();
            response.Products = new List<LabelProduct>();
            List<LabelProduct> list = new List<LabelProduct>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.ID
                                    , su.ADET
                                    , su.BULUNMA_ADEDI
                                    , su.SIPARIS_ID
                                    , su.URUN_ID
                                    , uk.URUNADI
                                    , u.BARKOD
                                    , r.TANIM AS RAFADI
                                    , r.ID AS RAFID
                                    , dm.TANIM AS MASAADI
                                    , dm.ID AS MASAID
                                    , dk.TANIM AS KOLIADI
                                    , dk.ID AS KOLIID
                                    , s.TARIH
                                FROM siparis_urun AS su
                                LEFT JOIN siparis AS s ON s.ID = su.SIPARIS_ID
                                LEFT JOIN urun_karti AS uk ON uk.ID = su.URUNKARTI_ID
                                LEFT JOIN urunler AS u ON u.ID = su.URUN_ID
				                LEFT JOIN urun_raf AS ur ON ur.URUN_ID = u.ID
                                LEFT JOIN raflar AS r ON r.ID = ur.RAF_ID
                                LEFT JOIN depolar AS d ON d.ID = r.DEPO_ID
                                LEFT JOIN depo_masa AS dm ON dm.ID = su.MASA_ID
                                LEFT JOIN depo_koli AS dk ON dk.ID = su.KOLI_ID
                                WHERE u.BARKOD = @BARKOD AND su.BULUNMA_ADEDI > 0 AND su.TOPLAMADURUMU = 1 AND su.MAGAZA_ID = @MAGAZA_ID ORDER BY su.ID";

            cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.Barcode;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                LabelProduct p = new LabelProduct();
                p.OrderProductID = mdr["ID"].ToInt32();
                p.OrderID = mdr["SIPARIS_ID"].ToInt32();
                p.ProductID = mdr["URUN_ID"].ToInt32();
                //p.UrunAdi = mdr["URUNADI"].ToString();
                p.Barcode = mdr["BARKOD"].ToString();
                p.Piece = mdr["BULUNMA_ADEDI"].ToDouble();
                p.ShelfID = mdr["RAFID"] != DBNull.Value ? mdr["RAFID"].ToInt32() : 0;
                p.ShelfName = mdr["RAFADI"].ToString();
                p.TableID = mdr["MASAID"] != DBNull.Value ? mdr["MASAID"].ToInt32() : 0;
                p.Table = mdr["MASAADI"].ToString();
                p.BoxID = mdr["KOLIID"] != DBNull.Value ? mdr["KOLIID"].ToInt32() : 0;
                p.Box = mdr["KOLIADI"].ToString();
                p.Date = mdr["TARIH"].ToDateTime();
                for (int i = 1; i < p.Piece; i++)
                {
                    var item = p.Clone();
                    item.Piece = item.Piece < 1 ? item.Piece : 1;
                    list.Add(item);
                }

                p.Piece = 1;
                list.Add(p);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            if (list.Count > 0)
            {
                foreach (var reqUrun in filter.Products)
                {
                    var cikarilacakUrun = list.FirstOrDefault(x => x.Barcode == reqUrun.Barcode && x.OrderID == reqUrun.OrderID && x.ProductID == reqUrun.ProductID);
                    list.Remove(cikarilacakUrun);
                }

                var urun = list.FirstOrDefault();
                if (urun != null)
                {
                    filter.Products.Add(urun);
                    response.Products = filter.Products;
                }
                else
                {
                    response.IsError = true;
                    response.ErrorMessage = "Ürün bulunamadı";
                }
            }
            else
            {
                response.IsError = true;
                response.ErrorMessage = "Ürün bulunamadı";
            }

            return response;
        }

        public async Task<List<LabelProduct>> GetLabelPrintedAsync(List<LabelProduct> products, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<LabelProduct> list = new List<LabelProduct>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT su.ID
                                    , su.ADET
                                    , su.BULUNMA_ADEDI
                                    , su.SIPARIS_ID
                                    , su.URUN_ID
                                    , u.BARKOD
                                FROM siparis_urun AS su
                                LEFT JOIN urunler AS u ON u.ID = su.URUN_ID
                                WHERE su.BULUNMA_ADEDI = su.ADET AND su.TOPLAMADURUMU = 1 AND MAGAZA_ID = @MAGAZA_ID ORDER BY su.ID";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                LabelProduct p = new LabelProduct();
                p.OrderProductID = mdr["ID"].ToInt32();
                p.OrderID = mdr["SIPARIS_ID"].ToInt32();
                p.ProductID = mdr["URUN_ID"].ToInt32();
                p.Barcode = mdr["BARKOD"].ToString();
                p.Piece = mdr["BULUNMA_ADEDI"].ToDouble();
                list.Add(p);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();

            cmd.CommandText = "UPDATE siparis_urun SET TOPLAMADURUMU = 2 WHERE ID = @ID AND MAGAZA_ID = @MAGAZA_ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32);
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            foreach (var urun in products)
            {
                var dbUrun = list.FirstOrDefault(x => x.OrderProductID == urun.OrderProductID);
                if (dbUrun != null && dbUrun.Piece == urun.Piece)
                {
                    cmd.Parameters["@ID"].Value = urun.OrderProductID;
                    await cmd.ExecuteNonQueryAsync();
                }
            }

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return products;
        }
    }
}