using Microsoft.AspNetCore.Http.Extensions;
using MySqlConnector;
using Org.BouncyCastle.Asn1;
using Org.BouncyCastle.Asn1.Ocsp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.Report;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Filters.GoodsAccept;
using Ticimax.Warehouse.Entities.Static;
using static iText.StyledXmlParser.Jsoup.Select.Evaluator;
using static Ticimax.Warehouse.Entities.Concrete.CampaignTicimax;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class ReportDal : IReportDal
    {
        private MySqlConnection _cnn;
        private string domainName = Info.DomainName.Value?.Replace("-", "");

        public ReportDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<WarehouseWaitingOrderDto>> WarehouseWaitingOrderAsync(WarehouseWaitingOrderFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<WarehouseWaitingOrderDto> list = new List<WarehouseWaitingOrderDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"Select
                                s.ID AS SIPARISID,
                                s.SIPARISNO,
                                s.SIPARISKAYNAGI,
                                su.URUN_ID,
                                u.BARKOD,
                                uk.URUNADI,
                                su.ADET,
                                s.TARIH AS SIPARISTARIHI,
                                s.TESLIMATGUNU,
                                s.TESLIMATSAATI,
                                m.ID AS MAGAZA_ID,
                                m.TANIM AS MAGAZA
                            from siparis_urun as su
                                    INNER JOIN siparis as s ON s.ID = su.SIPARIS_ID
                                    INNER JOIN magazalar as m ON m.ID = su.MAGAZA_ID
                                    INNER JOIN urunler as u ON u.ID = su.URUN_ID
                                    INNER JOIN urun_karti as uk ON uk.ID = u.URUNKARTI_ID
                                    WHERE s.DURUM = 0 
                                    AND s.PAKETLEMEDURUM_ID = 1  
                                    AND s.TARIH >= @TARIH 
                                    AND su.DURUM <> 2 
                                    AND su.ID NOT IN (SELECT SIPARIS_URUN_ID FROM depo_dagitilan_urun) 
                                    AND (SELECT IFNULL(SUM(TUTAR),0) FROM siparis_odeme WHERE ODEMETIPI <> 20 AND SIPARIS_ID = s.ID AND ONAYLANDI = 1) >= s.TOPLAMTUTAR ";

            WaitingOrderReportAppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, new Paging { PageNo = pageIndex, RecordNumber = pageSize });

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                list.Add(new WarehouseWaitingOrderDto
                {
                    ID = reader["SIPARISID"].ToInt32(),
                    Barcode = reader["BARKOD"].ToString(),
                    OrderNO = reader["SIPARISNO"].ToString(),
                    OrderSource = reader["SIPARISKAYNAGI"].ToString(),
                    ProductID = reader["URUN_ID"].ToInt32(),
                    ProductName = reader["URUNADI"].ToString(),
                    Piece = reader["ADET"].ToInt32(),
                    Date = reader["SIPARISTARIHI"].ToDateTime(),
                    DeliveryDay = reader["TESLIMATGUNU"].ToString(),
                    DeliveryTime = reader["TESLIMATSAATI"].ToString(),
                    StoreID = reader["MAGAZA_ID"].ToInt32(),
                    StoreDefinition = reader["MAGAZA"].ToString()
                });
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<int> WarehouseWaitingOrderCountAsync(WarehouseWaitingOrderFilter filter, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"Select
                               COUNT(*)
                            from siparis_urun as su
                                    INNER JOIN siparis as s ON s.ID = su.SIPARIS_ID
                                    INNER JOIN magazalar as m ON m.ID = su.MAGAZA_ID
                                    INNER JOIN urunler as u ON u.ID = su.URUN_ID
                                    INNER JOIN urun_karti as uk ON uk.ID = u.URUNKARTI_ID
                                    WHERE s.DURUM = 0 
                                    AND s.PAKETLEMEDURUM_ID = 1  
                                    AND s.TARIH >= @TARIH 
                                    AND su.DURUM <> 2 
                                    AND su.ID NOT IN (SELECT SIPARIS_URUN_ID FROM depo_dagitilan_urun) 
                                    AND (SELECT IFNULL(SUM(TUTAR),0) FROM siparis_odeme WHERE ODEMETIPI <> 20 AND SIPARIS_ID = s.ID AND ONAYLANDI = 1) >= s.TOPLAMTUTAR ";

            WaitingOrderReportAppendFilter(ref cmd, filter);

            var count = await cmd.ExecuteScalarAsync(cancellationToken);

            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return count.ToInt32();
        }

        public async Task<List<HistoricalStockShelfInformationDto>> GetHistoricalStockShelfInformations(string domain, int pageIndex, int pageSize, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(domain);


            //await _cnn.OpenAsync(cancellationToken);
            List<HistoricalStockShelfInformationDto> list = new List<HistoricalStockShelfInformationDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                        ur.URUN_ID,
                                        uk.URUNADI,
                                        IFNULL((SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
                                                                                FROM urun_eksecenek AS ues
                                                                                    WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA ),'')  AS VARYASYON,
                                        u.BARKOD,
                                        u.STOKKODU,
                                        u.EKLEMETARIHI,
                                        uk.SATISBIRIMI,
                                        SUM(ur.STOK) AS STOK from urun_raf as ur
                                        INNER JOIN urunler as u on u.ID = ur.URUN_ID
                                        INNER JOIN urun_karti as uk on uk.ID = u.URUNKARTI_ID
                                        GROUP BY ur.URUN_ID ";

            PagingExtension.AppendPaging(ref cmd, new Paging { PageNo = pageIndex, RecordNumber = pageSize });

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                list.Add(new HistoricalStockShelfInformationDto
                {
                    ProductId = reader["URUN_ID"].ToInt32(),
                    ProductBarcode = reader["BARKOD"].ToString(),
                    ProductName = reader["URUNADI"].ToString() + " " + reader["VARYASYON"],
                    ProductUnit = reader["SATISBIRIMI"].ToString(),
                    Stock = reader["STOK"].ToDouble(),
                    StockCode = reader["STOKKODU"].ToString(),
                    CreatedDate = reader["EKLEMETARIHI"].ToDateTime().ToTimestamp()
                });
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<List<InstantStockDTO>> InstantStock(InstantStockFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<InstantStockDTO> list = new List<InstantStockDTO>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"select
                                 u.ID
                                 ,uk.URUNADI
                                 , (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
	                                    FROM urun_eksecenek AS ues
	                                    WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA) AS VARYASYON
                                 ,u.BARKOD
                                 ,IFNULL((SELECT SUM(ur.STOK) FROM urun_raf as ur where ur.URUN_ID = u.ID AND DEPO_ID = @DEPO_ID),0) AS RAFSTOK
                                 , u.STOKADEDI
                                 , u.KONSINYESTOKADEDI
                                 , u.STOKKODU
                                 , u.ALISFIYATI
                                 , u.SATISFIYATI
                                 , u.PIYASAFIYATI
                                 , uk.EKLEMETARIHI
                                 , uk.YAYINTARIHI
                                 , uk.TEDARIKCITANIM
                                 from urunler as u
                                INNER JOIN urun_karti as uk ON uk.ID = u.URUNKARTI_ID
                                INNER JOIN urun_raf as ur ON ur.URUN_ID = u.ID
                                WHERE ur.DEPO_ID = @DEPO_ID ";


            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            InstantStockAppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, new Paging { PageNo = pageIndex, RecordNumber = pageSize });

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                list.Add(new InstantStockDTO
                {
                    ProductID = reader["ID"].ToInt32(),
                    ProductName = reader["URUNADI"].ToString(),
                    AdditionalOptions = reader["VARYASYON"].ToString(),
                    Barcode = reader["BARKOD"].ToString(),
                    StockCode = reader["STOKKODU"].ToString(),
                    ShelfStock = reader["RAFSTOK"].ToInt32(),
                    StockPiece = reader["STOKADEDI"].ToInt32(),
                    ConsignmentStockQuantity = reader["KONSINYESTOKADEDI"].ToInt32(),
                    PurchasePrice = reader["ALISFIYATI"].ToDouble(),
                    SalePrice = reader["SATISFIYATI"].ToDouble(),
                    MarketPrice = reader["PIYASAFIYATI"].ToDouble(),
                    ReleaseDate = reader["YAYINTARIHI"].ToDateTime(),
                    UploadDate = reader["EKLEMETARIHI"].ToDateTime(),
                    Supplier = reader["TEDARIKCITANIM"] != DBNull.Value ? reader["TEDARIKCITANIM"].ToString() : ""
                });
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<int> InstantStockCount(InstantStockFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"select
                                 Count(*)
                                 from urunler as u 
                                INNER JOIN urun_karti as uk ON uk.ID = u.URUNKARTI_ID
                                INNER JOIN urun_raf as ur ON ur.URUN_ID = u.ID
                                WHERE ur.DEPO_ID = @DEPO_ID ";

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            InstantStockAppendFilter(ref cmd, filter);

            var count = await cmd.ExecuteScalarAsync(cancellationToken);

            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return count.ToInt32();
        }

        public async Task<List<GoodsAcceptFileReportDto>> GoodsAcceptFileReportAsync(GoodsAcceptFileReportFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<GoodsAcceptFileReportDto> list = new List<GoodsAcceptFileReportDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = (@"select
                                dmu.MALKABUL_ID,
                                dt.TANIM,
                                mt.ISIM,
                                u.BARKOD,
                                uk.URUNADI,
                                IFNULL((SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
                                                                        FROM urun_eksecenek AS ues
                                                                            WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA ),'') AS VARYASYON,
                                dmu.FIYAT,
                                dmu.ADET,
                                dmu.BULUNMAADEDI,
                                (dmu.FIYAT * dmu.ADET) AS TOPLAMALISFIYATI
                                from depo_malkabul_urun as dmu
                                INNER JOIN depo_malkabul as dm ON dm.ID = dmu.MALKABUL_ID
                                INNER JOIN depo_tedarikci as dt ON dt.ID = dm.TEDARIKCI_ID
                                INNER JOIN magaza_temsilci as mt ON mt.ID = dmu.HAZIRLAYAN_ID
                                INNER JOIN urunler as u ON u.ID = dmu.HEDEF_ID
                                INNER JOIN urun_karti as uk ON uk.ID = u.URUNKARTI_ID");

            GoodsAcceptFileReportAppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, new Paging { PageNo = pageIndex, RecordNumber = pageSize });

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                list.Add(new GoodsAcceptFileReportDto()
                {
                    GoodsAcceptID = reader["MALKABUL_ID"].ToInt32(),
                    Definition = reader["TANIM"].ToString(),
                    Barcode = reader["BARKOD"].ToInt32(),
                    ProductName = reader["URUNADI"].ToString(),
                    Variation = reader["VARYASYON"].ToString(),
                    Price = reader["FIYAT"].ToDouble(),
                    Piece = reader["ADET"].ToInt32(),
                    QuantityFound = reader["BULUNMAADEDI"].ToInt32(),
                    TotalPurchasePrice = reader["TOPLAMALISFIYATI"].ToDouble()
                });
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<int> GoodsAcceptFileReportCountAsync(GoodsAcceptFileReportFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"select
                                Count(*)
                                from depo_malkabul_urun as dmu
                                INNER JOIN depo_malkabul as dm ON dm.ID = dmu.MALKABUL_ID
                                INNER JOIN depo_tedarikci as dt ON dt.ID = dm.TEDARIKCI_ID
                                INNER JOIN magaza_temsilci as mt ON mt.ID = dmu.HAZIRLAYAN_ID
                                INNER JOIN urunler as u ON u.ID = dmu.HEDEF_ID
                                INNER JOIN urun_karti as uk ON uk.ID = u.URUNKARTI_ID";

            GoodsAcceptFileReportAppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);

            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return count.ToInt32();
        }

        public async Task<List<StockReportForAllWarehousesDto>> StockReportForAllWarehouses(int? productId, double? minAllocatedStock, double? maxAllocatedStock, double? minWebStock, double? maxWebStock, List<int>? productIds, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<StockReportForAllWarehousesDto> list = new List<StockReportForAllWarehousesDto>();
            string commandTextInner = "";
            if (productIds != null && productIds.Count > 0)
                commandTextInner += $" AND ur.URUN_ID IN({string.Join(',', productIds)}) ";
            if (minAllocatedStock.HasValue)
                commandTextInner += $" AND IFNULL((SELECT SUM(ddu.ADET) FROM depo_dagitilan_urun as ddu where ddu.URUN_ID = ur.URUN_ID),0) >= {minAllocatedStock} ";
            if (maxAllocatedStock.HasValue)
                commandTextInner += $" AND IFNULL((SELECT SUM(ddu.ADET) FROM depo_dagitilan_urun as ddu where ddu.URUN_ID = ur.URUN_ID),0) <= {maxAllocatedStock} ";
            if (minWebStock.HasValue)
                commandTextInner += $" AND u.STOKADEDI >= {minWebStock} ";
            if (maxWebStock.HasValue)
                commandTextInner += $" AND u.STOKADEDI <= {maxWebStock} ";

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = (@$"SET @sql = '';
                                SELECT
                                GROUP_CONCAT(DISTINCT
                                  CONCAT(
                                    'SUM(CASE WHEN DEPO_ID = ''',
                                    ur.DEPO_ID,
                                    ''' THEN STOK ELSE 0 END) AS ''',
                                    d.TANIM, ''''
                                  )
                                ) INTO @sql
                              FROM urun_raf as ur
                              INNER JOIN depolar as d ON d.ID = ur.DEPO_ID;
                              
                              SET @sql = CONCAT('SELECT ur.URUN_ID
                                   ,uk.URUNADI
                                   ,IFNULL((SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR '' '') FROM urun_eksecenek AS ues WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA ),'''') AS VARYASYON
                                   ,u.BARKOD
                                   ,u.ALISFIYATI
                                   ,u.SATISFIYATI
                                   ,uk.RESIM1
                                   ,uk.STOKKODU
                                   ,u.STOKADEDI
                                   , IFNULL((SELECT SUM(ADET) FROM depo_dagitilan_urun as ddu where ddu.URUN_ID = ur.URUN_ID),0) AS ALLOCATED_STOCK
                                   ,', @sql, ' 
                                  FROM urun_raf as ur
                                  INNER JOIN urunler as u ON u.ID = ur.URUN_ID
                                  INNER JOIN urun_karti as uk ON uk.ID = u.URUNKARTI_ID
                              WHERE ur.STOK > 0 AND ur.DEPO_ID = {WebSiteInfo.User.Value.WarehouseID} {(productId.HasValue ? "AND ur.URUN_ID = " + productId.Value : "")} {commandTextInner} GROUP BY URUN_ID LIMIT {pageSize * (pageIndex - 1)}, {pageSize}');

                              PREPARE stmt FROM @sql;
                              EXECUTE stmt;
                              DEALLOCATE PREPARE stmt;");

            List<string> fieldList = new List<string>() { "URUN_ID", "URUNADI", "VARYASYON", "BARKOD", "STOKKODU", "RESIM1", "STOKADEDI" };

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                var stockInformation = new StockReportForAllWarehousesDto()
                {
                    ProductId = reader["URUN_ID"].ToInt32(),
                    Name = reader["URUNADI"].ToString(),
                    AdditionalOptions = reader["VARYASYON"].ToString(),
                    Barcode = reader["BARKOD"].ToString(),
                    StockCode = reader["STOKKODU"].ToString(),
                    Image = WebSiteInfo.User.Value.ImagePath + reader["RESIM1"].ToString(),
                    Stock = reader["STOKADEDI"].ToDouble(),
                    AllocatedStock = reader["ALLOCATED_STOCK"].ToDouble(),
                    PurchasePrice = reader["ALISFIYATI"] != DBNull.Value ? reader["ALISFIYATI"].ToDouble() : 0,
                    SalePrice = reader["SATISFIYATI"] != DBNull.Value ? reader["SATISFIYATI"].ToDouble() : 0
                };

                for (int i = 0; i < reader.FieldCount; i++)
                {
                    string warehouseName = reader.GetName(i);
                    if (!fieldList.Contains(warehouseName))
                    {
                        double columnValue = reader.IsDBNull(i) ? 0 : reader.GetDouble(i);
                        stockInformation.WarehouseStockInformations.Add(new WarehouseStockInformation(warehouseName, columnValue));
                    }
                }

                list.Add(stockInformation);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<int> StockReportForAllWarehousesCount(int? productId, double? minAllocatedStock, double? maxAllocatedStock, double? minWebStock, double? maxWebStock, List<int>? productIds, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            string commandTextInner = "";
            if (productIds != null && productIds.Count > 0)
                commandTextInner += $" AND ur.URUN_ID IN({string.Join(',', productIds)}) ";
            if (minAllocatedStock.HasValue)
                commandTextInner += $" AND IFNULL((SELECT SUM(ddu.ADET) FROM depo_dagitilan_urun as ddu where ddu.URUN_ID = ur.URUN_ID),0) >= {minAllocatedStock} ";
            if (maxAllocatedStock.HasValue)
                commandTextInner += $" AND IFNULL((SELECT SUM(ddu.ADET) FROM depo_dagitilan_urun as ddu where ddu.URUN_ID = ur.URUN_ID),0) <= {maxAllocatedStock} ";
            if (minWebStock.HasValue)
                commandTextInner += $" AND u.STOKADEDI >= {minWebStock} ";
            if (maxWebStock.HasValue)
                commandTextInner += $" AND u.STOKADEDI <= {maxWebStock} ";

            cmd.CommandText = @$"SELECT COUNT(*)
                                  FROM urun_raf as ur
                                  INNER JOIN urunler AS u ON u.ID = ur.URUN_ID
                              WHERE ur.STOK > 0 {(productId.HasValue ? "AND ur.URUN_ID = " + productId.Value : "")} {commandTextInner}";

            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return count.ToInt32();
        }

        public async Task<int> StockReportForAllWarehousesCountAsync(int? productId, double? minAllocatedStock, double? maxAllocatedStock, double? minWebStock, double? maxWebStock, List<int>? productIds, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            string commandTextInner = "";
            if (productIds != null && productIds.Count > 0)
                commandTextInner += $" AND ur.URUN_ID IN({string.Join(',', productIds)}) ";
            if (minAllocatedStock.HasValue)
                commandTextInner += $" AND IFNULL((SELECT SUM(ddu.ADET) FROM depo_dagitilan_urun as ddu where ddu.URUN_ID = ur.URUN_ID),0) >= {minAllocatedStock} ";
            if (maxAllocatedStock.HasValue)
                commandTextInner += $" AND IFNULL((SELECT SUM(ddu.ADET) FROM depo_dagitilan_urun as ddu where ddu.URUN_ID = ur.URUN_ID),0) <= {maxAllocatedStock} ";
            if (minWebStock.HasValue)
                commandTextInner += $" AND u.STOKADEDI >= {minWebStock} ";
            if (maxWebStock.HasValue)
                commandTextInner += $" AND u.STOKADEDI <= {maxWebStock} ";

            cmd.CommandText = @$"                              
                                    SET @sql = '';
                                    SELECT GROUP_CONCAT(DISTINCT
                                            CONCAT(
                                              'SUM(CASE WHEN DEPO_ID = ''',
                                              ur.DEPO_ID,
                                              ''' THEN STOK ELSE 0 END) AS ''',
                                              d.TANIM, ''''
                                            )
                                    ) INTO @sql
                                    FROM urun_raf AS ur
                                    INNER JOIN depolar AS d ON d.ID = ur.DEPO_ID;
                                    SET @sql = CONCAT('SELECT COUNT(DISTINCT ur.URUN_ID)
                                                       FROM urun_raf AS ur
                                                       INNER JOIN urunler AS u ON u.ID = ur.URUN_ID
                                                       INNER JOIN urun_karti AS uk ON uk.ID = u.URUNKARTI_ID
                                                       WHERE ur.STOK > 0 AND ur.DEPO_ID = {WebSiteInfo.User.Value.WarehouseID} {(productId.HasValue ? "AND ur.URUN_ID = " + productId.Value : "")} {commandTextInner}');
                                    PREPARE stmt FROM @sql;
                                    EXECUTE stmt;
                                    DEALLOCATE PREPARE stmt;";

            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return count.ToInt32();
        }

        public async Task<int> ProductsWithStockErrorsReportCountAsync(int productId, List<int> productIds, double? MinWebStock,
                        double? MaxWebStock, double? MinShelfStock, double? MaxShelfStock, double? MinConsigmentStock,
                        double? MaxConsigmentStock, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @$"SELECT COUNT(*)
                                  FROM urunler as u
                                       INNER JOIN  urun_karti as uk ON uk.ID = u.URUNKARTI_ID
                                       WHERE u.STOKADEDI >= 0 AND u.SILINDI = 0 AND u.AKTIF = 1 AND uk.AKTIF = 1 and uk.SILINDI = 0 ";

            if (productId > 0)
            {
                cmd.CommandText += " AND u.ID = @ID";
                cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = productId;
            }
            if (productIds != null && productIds.Count > 0)
            {
                cmd.CommandText += $" AND u.ID IN({string.Join(',', productIds)})";
            }
            if (MinWebStock.HasValue)
            {
                cmd.CommandText += " AND u.STOKADEDI >= @MIN_WEB_STOCK ";
                cmd.Parameters.Add("@MIN_WEB_STOCK", MySqlDbType.Double).Value = MinWebStock.Value;
            }
            if (MaxWebStock.HasValue)
            {
                cmd.CommandText += " AND u.STOKADEDI <= @MAX_WEB_STOCK ";
                cmd.Parameters.Add("@MAX_WEB_STOCK", MySqlDbType.Double).Value = MaxWebStock.Value;
            }
            if (MinShelfStock.HasValue)
            {
                cmd.CommandText += " AND IFNULL((SELECT SUM(STOK) from urun_raf as ur where ur.STOK > 0 AND ur.URUN_ID = u.ID AND (select r.SATISA_ACIK from raflar as r where r.ID = ur.RAF_ID) = 1 AND ur.MAGAZA_ID = @MAGAZA_ID AND ur.DEPO_ID = @DEPO_ID),0) >= @MIN_SHELF_STOCK ";
                cmd.Parameters.Add("@MIN_SHELF_STOCK", MySqlDbType.Double).Value = MinShelfStock.Value;
            }
            if (MaxShelfStock.HasValue)
            {
                cmd.CommandText += " AND IFNULL((SELECT SUM(STOK) from urun_raf as ur where ur.STOK > 0 AND ur.URUN_ID = u.ID AND (select r.SATISA_ACIK from raflar as r where r.ID = ur.RAF_ID) = 1 AND ur.MAGAZA_ID = @MAGAZA_ID AND ur.DEPO_ID = @DEPO_ID),0) <= @MAX_SHELF_STOCK ";
                cmd.Parameters.Add("@MAX_SHELF_STOCK", MySqlDbType.Double).Value = MaxShelfStock.Value;
            }
            if (MinConsigmentStock.HasValue)
            {
                cmd.CommandText += " AND IF(u.KONSINYESTOKADEDI < 0 , 0 , u.KONSINYESTOKADEDI) >= @MIN_CONSIGMENT_STOCK ";
                cmd.Parameters.Add("@MIN_CONSIGMENT_STOCK", MySqlDbType.Double).Value = MinConsigmentStock.Value;
            }
            if (MaxConsigmentStock.HasValue)
            {
                cmd.CommandText += " AND IF(u.KONSINYESTOKADEDI < 0 , 0 , u.KONSINYESTOKADEDI) <= @MAX_CONSIGMENT_STOCK ";
                cmd.Parameters.Add("@MAX_CONSIGMENT_STOCK", MySqlDbType.Double).Value = MaxConsigmentStock.Value;
            }

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;

            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return count.ToInt32();
        }
        public async Task<List<ProductWithStockErrorItem>> ProductsWithStockErrorsReportAsync(int productId, List<int> productIds, double? MinWebStock,
                        double? MaxWebStock, double? MinShelfStock, double? MaxShelfStock, double? MinConsigmentStock,
                        double? MaxConsigmentStock, int pageIndex, int pageSize, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<ProductWithStockErrorItem> list = new List<ProductWithStockErrorItem>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT u.ID,
                                       uk.URUNADI,
                                       uk.RESIM1,
                                       (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
	                                                                    FROM urun_eksecenek AS ues
	                                                                    WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA) AS VARYASYON,
                                       u.BARKOD,
                                       u.STOKKODU,
                                       u.STOKADEDI AS WEBSTOK,
                                       IF(u.KONSINYESTOKADEDI < 0 , 0 , u.KONSINYESTOKADEDI) AS KONSINYEADEDI,
                                       IFNULL((SELECT SUM(STOK) from urun_raf as ur where ur.STOK > 0 AND ur.URUN_ID = u.ID AND (select r.SATISA_ACIK from raflar as r where r.ID = ur.RAF_ID) = 1 AND ur.MAGAZA_ID = @MAGAZA_ID AND ur.DEPO_ID = @DEPO_ID),0) as RAFSTOK
                                       FROM urunler as u
                                       INNER JOIN  urun_karti as uk ON uk.ID = u.URUNKARTI_ID
                                       WHERE u.STOKADEDI >= 0 AND u.SILINDI = 0 AND u.AKTIF = 1 AND uk.AKTIF = 1 and uk.SILINDI = 0";

            if (productId > 0)
            {
                cmd.CommandText += " AND u.ID = @ID";
                cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = productId;
            }
            if (productIds != null && productIds.Count > 0)
            {
                cmd.CommandText += $" AND u.ID IN({string.Join(',', productIds)})";
            }
            if (MinWebStock.HasValue)
            {
                cmd.CommandText += " AND u.STOKADEDI >= @MIN_WEB_STOCK ";
                cmd.Parameters.Add("@MIN_WEB_STOCK", MySqlDbType.Double).Value = MinWebStock.Value;
            }
            if (MaxWebStock.HasValue)
            {
                cmd.CommandText += " AND u.STOKADEDI <= @MAX_WEB_STOCK ";
                cmd.Parameters.Add("@MAX_WEB_STOCK", MySqlDbType.Double).Value = MaxWebStock.Value;
            }
            if (MinShelfStock.HasValue)
            {
                cmd.CommandText += " AND IFNULL((SELECT SUM(STOK) from urun_raf as ur where ur.STOK > 0 AND ur.URUN_ID = u.ID AND (select r.SATISA_ACIK from raflar as r where r.ID = ur.RAF_ID) = 1 AND ur.MAGAZA_ID = @MAGAZA_ID AND ur.DEPO_ID = @DEPO_ID),0) >= @MIN_SHELF_STOCK ";
                cmd.Parameters.Add("@MIN_SHELF_STOCK", MySqlDbType.Double).Value = MinShelfStock.Value;
            }
            if (MaxShelfStock.HasValue)
            {
                cmd.CommandText += " AND IFNULL((SELECT SUM(STOK) from urun_raf as ur where ur.STOK > 0 AND ur.URUN_ID = u.ID AND (select r.SATISA_ACIK from raflar as r where r.ID = ur.RAF_ID) = 1 AND ur.MAGAZA_ID = @MAGAZA_ID AND ur.DEPO_ID = @DEPO_ID),0) <= @MAX_SHELF_STOCK ";
                cmd.Parameters.Add("@MAX_SHELF_STOCK", MySqlDbType.Double).Value = MaxShelfStock.Value;
            }
            if (MinConsigmentStock.HasValue)
            {
                cmd.CommandText += " AND IF(u.KONSINYESTOKADEDI < 0 , 0 , u.KONSINYESTOKADEDI) >= @MIN_CONSIGMENT_STOCK ";
                cmd.Parameters.Add("@MIN_CONSIGMENT_STOCK", MySqlDbType.Double).Value = MinConsigmentStock.Value;
            }
            if (MaxConsigmentStock.HasValue)
            {
                cmd.CommandText += " AND IF(u.KONSINYESTOKADEDI < 0 , 0 , u.KONSINYESTOKADEDI) <= @MAX_CONSIGMENT_STOCK ";
                cmd.Parameters.Add("@MAX_CONSIGMENT_STOCK", MySqlDbType.Double).Value = MaxConsigmentStock.Value;
            }

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;



            PagingExtension.AppendPaging(ref cmd, new Paging { PageNo = pageIndex, RecordNumber = pageSize });

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                var stockInformation = new ProductWithStockErrorItem()
                {
                    ProductId = reader["ID"].ToInt32(),
                    Name = reader["URUNADI"].ToString(),
                    AdditionalOptions = reader["VARYASYON"].ToString(),
                    Barcode = reader["BARKOD"].ToString(),
                    StockCode = reader["STOKKODU"].ToString(),
                    Image = WebSiteInfo.User.Value.ImagePath + reader["RESIM1"].ToString(),
                    ECommerceStock = reader["WEBSTOK"].ToDouble(),
                    ConsignmentStock = reader["KONSINYEADEDI"].ToDouble(),
                    ShelfStock = reader["RAFSTOK"] == DBNull.Value ? 0 : reader["RAFSTOK"].ToDouble()
                };

                list.Add(stockInformation);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return list;
        }


        private void AppendPickedProductsFilter(ref MySqlCommand cmd, PickedProductsDtoFilter filter)
        {
            if (filter != null)
            {
                if (filter.PickerID > 0)
                {
                    cmd.CommandText += " AND ldu.KULLANICI_ID = @KULLANICI_ID";
                    cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = filter.PickerID;
                }

                if (filter.ShelfID > 0)
                {
                    cmd.CommandText += " AND ldu.RAF_ID = @RAF_ID";
                    cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = filter.ShelfID;
                }

                if (filter.ProductID > 0)
                {
                    cmd.CommandText += " AND ldu.URUN_ID = @URUN_ID";
                    cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = filter.ProductID;
                }

                if (filter.DateStart.HasValue)
                {
                    cmd.CommandText += " AND ldu.TARIH >= @sipbastarih ";
                    cmd.Parameters.Add("@sipbastarih", MySqlDbType.DateTime).Value = filter.DateStart.Value;
                }

                if (filter.DateFinish.HasValue)
                {
                    cmd.CommandText += " AND ldu.TARIH <= @sipsontarih ";
                    cmd.Parameters.Add("@sipsontarih", MySqlDbType.DateTime).Value = filter.DateFinish.Value;
                }
            }
        }

        private void AppendQualityControlCompleatedOrderFilter(ref MySqlCommand cmd, QualityControlCompleatedOrderReportFilterDto filter)
        {
            if (filter != null)
            {
                if (filter.PersonID > 0)
                {
                    cmd.CommandText += " AND ldk.KULLANICI_ID = @KULLANICI_ID";
                    cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = filter.PersonID;
                }

                if (filter.OrderID > 0)
                {
                    cmd.CommandText += " AND ldk.SIPARIS_ID = @SIPARIS_ID";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderID;
                }

                if (filter.DateStart.HasValue)
                {
                    cmd.CommandText += " AND ldk.TARIH >= @sipbastarih ";
                    cmd.Parameters.Add("@sipbastarih", MySqlDbType.DateTime).Value = filter.DateStart.Value;
                }

                if (filter.DateFinish.HasValue)
                {
                    cmd.CommandText += " AND ldk.TARIH <= @sipsontarih ";
                    cmd.Parameters.Add("@sipsontarih", MySqlDbType.DateTime).Value = filter.DateFinish.Value;
                }
            }
        }

        public void WaitingOrderReportAppendFilter(ref MySqlCommand cmd, WarehouseWaitingOrderFilter filter)
        {
            cmd.Parameters.Add("@TARIH", MySqlDbType.Datetime).Value = DateTime.Now.AddMonths(-1);

            if (filter != null)
            {
                if (filter.OrderId > 0)
                {
                    cmd.CommandText += " AND s.ID = @OrderId ";
                    cmd.Parameters.Add("@OrderId", MySqlDbType.Int32).Value = filter.OrderId;
                }

                if (filter.StoreId > 0)
                {
                    cmd.CommandText += " AND m.ID = @StoreId ";
                    cmd.Parameters.Add("@StoreId", MySqlDbType.Int32).Value = filter.StoreId;
                }

                if (filter.ProductId > 0)
                {
                    cmd.CommandText += " AND su.URUN_ID = @ProductId ";
                    cmd.Parameters.Add("@ProductId", MySqlDbType.Int32).Value = filter.ProductId;
                }
            }
        }

        private void InstantStockAppendFilter(ref MySqlCommand cmd, InstantStockFilter filter)
        {
            if (filter != null)
            {
                if (filter.ProductID > 0)
                {
                    cmd.CommandText += " AND u.ID = @UrunId ";
                    cmd.Parameters.Add("@UrunId", MySqlDbType.Int32).Value = filter.ProductID;
                }

                if (!string.IsNullOrEmpty(filter.ProductIds))
                {
                    List<int> productIds = filter.ProductIds.Split(",").Select(x => int.Parse(x)).ToList();
                    cmd.CommandText += $" AND u.ID IN({string.Join(',', productIds)})";
                }

                if (filter.WebStockQuantityMin.HasValue)
                {
                    cmd.CommandText += " AND u.STOKADEDI >= @WEBSTOKADEDIMIN ";
                    cmd.Parameters.Add("@WEBSTOKADEDIMIN", MySqlDbType.Double).Value = filter.WebStockQuantityMin;
                }
                if (filter.WebStockQuantityMax.HasValue)
                {
                    cmd.CommandText += " AND u.STOKADEDI <= @WEBSTOKADEDIMAX ";
                    cmd.Parameters.Add("@WEBSTOKADEDIMAX", MySqlDbType.Double).Value = filter.WebStockQuantityMax;
                }

                if (filter.ConsigmentStockQuantityMin.HasValue)
                {
                    cmd.CommandText += " AND u.KONSINYESTOKADEDI >= @KONSINYESTOKADEDIMIN ";
                    cmd.Parameters.Add("@KONSINYESTOKADEDIMIN", MySqlDbType.Double).Value = filter.ConsigmentStockQuantityMin;
                }
                if (filter.ConsigmentStockQuantityMax.HasValue)
                {
                    cmd.CommandText += " AND u.KONSINYESTOKADEDI <= @KONSINYESTOKADEDIMAX ";
                    cmd.Parameters.Add("@KONSINYESTOKADEDIMAX", MySqlDbType.Double).Value = filter.ConsigmentStockQuantityMax;
                }

                if (filter.ShelfStockQuantityMin.HasValue)
                {
                    cmd.CommandText += " AND IFNULL((SELECT SUM(ur.STOK) FROM urun_raf as ur where ur.URUN_ID = u.ID),0) >= @RAFSTOKADEDIMIN ";
                    cmd.Parameters.Add("@RAFSTOKADEDIMIN", MySqlDbType.Double).Value = filter.ShelfStockQuantityMin;
                }
                if (filter.ShelfStockQuantityMax.HasValue)
                {
                    cmd.CommandText += " AND IFNULL((SELECT SUM(ur.STOK) FROM urun_raf as ur where ur.URUN_ID = u.ID),0) <= @RAFSTOKADEDIMAX ";
                    cmd.Parameters.Add("@RAFSTOKADEDIMAX", MySqlDbType.Double).Value = filter.ShelfStockQuantityMax;
                }
            }
        }

        private async Task<bool> TableControl(string domainName, string tableName)
        {
            bool retValue = false;
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                       FROM INFORMATION_SCHEMA.TABLES
                                       WHERE TABLE_SCHEMA = @DomainName
                                       AND TABLE_NAME = @TableName ";

            cmd.Parameters.Add("@DomainName", MySqlDbType.VarChar).Value = domainName.Replace(".", "");
            cmd.Parameters.Add("@TableName", MySqlDbType.VarChar).Value = tableName;
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
            if (count > 0)
                retValue = true;

            await cmd.DisposeAsync();
            //_cnn.Close();
            return retValue;
        }

        private void GoodsAcceptFileReportAppendFilter(ref MySqlCommand cmd, GoodsAcceptFileReportFilter filter)
        {
            if (filter != null)
            {
                if (filter.GoodsAcceptID > 0)
                {
                    cmd.CommandText += " AND dmu.MALKABUL_ID = @MalkabulId";
                    cmd.Parameters.Add("@MalkabulId", MySqlDbType.Int32).Value = filter.GoodsAcceptID;
                }

                if (filter.ProductID > 0)
                {
                    cmd.CommandText += " AND u.ID = @UrunId";
                    cmd.Parameters.Add("@UrunId", MySqlDbType.Int32).Value = filter.ProductID;
                }
            }
        }

        public async Task<List<StockLocationReportForAllWarehousesDto>> StockLocationReportForAllWarehouses(StockLocationReportFilter filter, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            List<StockLocationReportForAllWarehousesDto> list = new List<StockLocationReportForAllWarehousesDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"
                                    SELECT
                                    ur.URUN_ID
                                   ,uk.URUNADI
                                   ,(SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
                                        FROM urun_eksecenek AS ues
                                        WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA) AS VARYASYON
                                   ,u.BARKOD
                                   ,uk.RESIM1
                                   ,uk.STOKKODU
                                   ,u.STOKADEDI
                                   ,r.TANIM AS SHELFNAME
                                   ,r.BARKOD AS SHELFBARCODE
                                   ,u.ALISFIYATI 
                                   ,u.SATISFIYATI 
                                   ,u.KONSINYESTOKADEDI
                                   ,ur.RAF_ID
                                   ,IFNULL((SELECT SUM(ur.STOK) FROM urun_raf as ur where ur.URUN_ID = u.ID AND ur.RAF_ID = r.ID),0) AS RAFSTOK
                                  FROM urun_raf as ur
                                  INNER JOIN urunler as u ON u.ID = ur.URUN_ID
                                  INNER JOIN urun_karti as uk ON uk.ID = u.URUNKARTI_ID
                                  INNER JOIN raflar as r ON r.ID = ur.RAF_ID
                                WHERE ur.STOK > 0 ";


            cmd.CommandText += " AND ur.DEPO_ID = @DEPO_ID";
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;

            StockLocationReportAppendFilter(ref cmd, filter);
            cmd.CommandText += " GROUP BY ur.RAF_ID, ur.URUN_ID ";
            PagingExtension.AppendPaging(ref cmd, new Paging { PageNo = pageIndex, RecordNumber = pageSize });
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                var stockInformation = new StockLocationReportForAllWarehousesDto()
                {
                    ProductId = reader["URUN_ID"].ToInt32(),
                    Name = reader["URUNADI"].ToString(),
                    AdditionalOptions = reader["VARYASYON"] != DBNull.Value ? reader["VARYASYON"].ToString() : "",
                    Barcode = reader["BARKOD"].ToString(),
                    StockCode = reader["STOKKODU"].ToString(),
                    Image = WebSiteInfo.User.Value.ImagePath + (reader["RESIM1"] != DBNull.Value ? reader["RESIM1"].ToString() : ""),
                    Stock = reader["STOKADEDI"].ToDouble(),
                    ConsigmentStock = reader["KONSINYESTOKADEDI"].ToDouble(),
                    ShelfStock = reader["RAFSTOK"].ToDouble(),
                    PurchasePrice = reader["ALISFIYATI"].ToDouble(),
                    SalePrice = reader["SATISFIYATI"].ToDouble(),
                    ShelfName = reader["SHELFNAME"].ToString(),
                    ShelfBarcode = reader["SHELFBARCODE"].ToString(),
                    ShelfId = reader["RAF_ID"].ToInt32(),
                };

                list.Add(stockInformation);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return list;
        }

        public async Task<int> StockLocationReportForAllWarehousesCount(StockLocationReportFilter filter, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @$"SELECT COUNT(*) FROM urun_raf as ur
                                  INNER JOIN urunler AS u ON u.ID = ur.URUN_ID
                                  INNER JOIN urun_karti AS uk ON uk.ID = u.URUNKARTI_ID
                                  INNER JOIN raflar AS r ON r.ID = ur.RAF_ID
                              WHERE ur.STOK > 0";
            cmd.CommandText += " AND ur.DEPO_ID = @DEPO_ID";
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            StockLocationReportAppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();


            return count.ToInt32();
        }

        private void StockLocationReportAppendFilter(ref MySqlCommand cmd, StockLocationReportFilter filter)
        {
            if (filter != null)
            {
                if (filter.ProductID.HasValue)
                {
                    cmd.CommandText += " AND ur.URUN_ID = @URUN_ID";
                    cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = filter.ProductID.Value;
                }
                if (!string.IsNullOrEmpty(filter.ProductIds))
                {
                    List<int> productIds = filter.ProductIds.Split(",").Select(x => int.Parse(x)).ToList();
                    cmd.CommandText += $" AND ur.URUN_ID IN({string.Join(',', productIds)})";
                }
                if (filter.ShelfID.HasValue)
                {
                    cmd.CommandText += " AND ur.RAF_ID = @RAF_ID";
                    cmd.Parameters.Add("@RAF_ID", MySqlDbType.Int32).Value = filter.ShelfID.Value;
                }
                if (!string.IsNullOrEmpty(filter.ShelfIDs))
                {
                    List<int> shelfIds = filter.ShelfIDs.Split(",").Select(x => int.Parse(x)).ToList();
                    cmd.CommandText += $" AND ur.RAF_ID IN ({string.Join(',', shelfIds)}) ";
                }
                if (filter.MinShelfStock.HasValue)
                {
                    cmd.CommandText += " AND IFNULL((SELECT SUM(ur.STOK) FROM urun_raf as ur where ur.URUN_ID = u.ID AND ur.RAF_ID = r.ID),0) >= @MinShelfStock ";
                    cmd.Parameters.Add("@MinShelfStock", MySqlDbType.Double).Value = filter.MinShelfStock.Value;
                }
                if (filter.MaxShelfStock.HasValue)
                {
                    cmd.CommandText += " AND IFNULL((SELECT SUM(ur.STOK) FROM urun_raf as ur where ur.URUN_ID = u.ID AND ur.RAF_ID = r.ID),0) <= @MaxShelfStock ";
                    cmd.Parameters.Add("@MaxShelfStock", MySqlDbType.Double).Value = filter.MaxShelfStock.Value;
                }
                if (filter.MinConsigmentStock.HasValue)
                {
                    cmd.CommandText += " AND u.KONSINYESTOKADEDI >= @MinConsigmentStock ";
                    cmd.Parameters.Add("@MinConsigmentStock", MySqlDbType.Double).Value = filter.MinConsigmentStock.Value;
                }
                if (filter.MaxConsigmentStock.HasValue)
                {
                    cmd.CommandText += " AND  u.KONSINYESTOKADEDI <= @MaxConsigmentStock ";
                    cmd.Parameters.Add("@MaxConsigmentStock", MySqlDbType.Double).Value = filter.MaxConsigmentStock.Value;
                }
                if (filter.MinStock.HasValue)
                {
                    cmd.CommandText += " AND u.STOKADEDI >= @MinStock ";
                    cmd.Parameters.Add("@MinStock", MySqlDbType.Double).Value = filter.MinStock.Value;
                }
                if (filter.MaxStock.HasValue)
                {
                    cmd.CommandText += " AND  u.STOKADEDI <= @MaxStock ";
                    cmd.Parameters.Add("@MaxStock", MySqlDbType.Double).Value = filter.MaxStock.Value;
                }
            }
        }

    }
}