using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class StoreAgentDal : IStoreAgentDal
    {
        private readonly MySqlConnection _cnn;

        public StoreAgentDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task SetDefaultTableAndSetNoAsync(int personId, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE magaza_temsilci SET MASA_ID =0, SET_NO = '' WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = personId;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<StoreAgent>> GetListAsync(StoreAgentFilter filter = null, StoreAgentPaging paging = null, CancellationToken cancellationToken = default)
        {
//            if (_cnn.State == ConnectionState.Closed)
//            {
//                _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//                await _cnn.OpenAsync(cancellationToken);
//            }

            List<StoreAgent> liste = new List<StoreAgent>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  mt.ID
                                        , mt.MAGAZA_ID
                                        , m.TANIM AS MAGAZA
                                        , mt.AKTIF
                                        , mt.TIP_ID
                                        , mt.TELEFON
                                        , mt.DAHILI
                                        , mt.ISIM
                                        , mt.SOYISIM
                                        , mt.PUAN
                                        , mt.PUANVERENSAYISI
                                        , mt.RESIM
                                        , mt.KULLANICIADI
                                        , mt.KARGOTIPI
                                        , mt.DEPO_ID
                                        , mt.MASA_ID
                                        , mt.SET_NO
                                        , d.TANIM AS DEPO
                                        , dm.TANIM AS MASA
                                        , mt.HIZLIMENU
                                        , mt.ROLLER
                                        , mt.GOLGE_KULLANICI_ID
                                        , mt.SIPARIS_IPTAL_LIMITI
                                        , dyk.YETKIGRUP_ID
                                        , mt.CUSTOM_PRINTER
                                        , mt.EMAIL
                                        , mt.TWOFACTORAUTHKEY
                                        , mt.TWOFA_AKTIF
                                FROM
                                        magaza_temsilci AS mt
                                        LEFT JOIN depo_yetki_kullanici AS dyk ON dyk.UYE_ID = mt.ID
                                        LEFT JOIN magazalar AS m ON mt.MAGAZA_ID = m.ID
                                        LEFT JOIN depolar AS d ON mt.DEPO_ID = d.ID
                                        LEFT JOIN depo_masa AS dm ON mt.MASA_ID = dm.ID
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                StoreAgent p = new StoreAgent();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.StoreID = Convert.ToInt32(Reader["MAGAZA_ID"]);
                p.Store = Reader["MAGAZA"].ToString();
                p.Active = Reader["AKTIF"] != DBNull.Value ? Reader["AKTIF"].ToBoolean() : false;
                p.TypeID = Convert.ToInt32(Reader["TIP_ID"]);
                p.Telephone = Reader["TELEFON"].ToString();
                p.InternalNumber = Reader["DAHILI"].ToString();
                p.TwoFactorAuthKey = Reader["TWOFACTORAUTHKEY"].ToString();
                p.TwofaActive = Reader["TWOFA_AKTIF"] != DBNull.Value ? Reader["TWOFA_AKTIF"].ToBoolean() : false;
                p.Name = Reader["ISIM"].ToString();
                p.LastName = Reader["SOYISIM"].ToString();
                p.Point = Convert.ToDouble(Reader["PUAN"].ToString());
                p.NumberofScorers = Convert.ToInt32(Reader["PUANVERENSAYISI"].ToString());
                p.Image = Reader["RESIM"] != DBNull.Value ? Reader["RESIM"].ToString() : "";
                // Burada Listeden kullanıcı şifresi için foreach ile dönmemek için direk string.Empty olarak alındı ve SQL sorgusundan o kısım kaldırıldı.
                p.Password = "";
                p.Username = Reader["KULLANICIADI"].ToString();
                p.CargoType = Reader["KARGOTIPI"].ToInt32();
                p.SetNo = Reader["SET_NO"].ToString();
                p.WarehouseID = Reader["DEPO_ID"].ToInt32();
                p.TableID = Reader["MASA_ID"].ToInt32();
                p.Warehouse = Reader["DEPO"].ToString();
                p.Table = Reader["MASA"].ToString();
                p.QuickMenus = Reader["HIZLIMENU"].ToString().ToJsonDeserialize<List<QuickMenu>>();
                p.Rols = Reader["ROLLER"] != DBNull.Value ? Reader["ROLLER"].ToString() : null;
                p.ShadowUserID = Reader["GOLGE_KULLANICI_ID"].ToInt32();
                p.OrderCancelLimit = Reader["SIPARIS_IPTAL_LIMITI"]?.ToInt32() ?? 0;
                p.AuthId = Reader["YETKIGRUP_ID"] != DBNull.Value ? Reader["YETKIGRUP_ID"].ToInt32() : 0;
                p.CustomPrinter = Reader["CUSTOM_PRINTER"] != DBNull.Value ? Reader["CUSTOM_PRINTER"].ToBoolean() : false;
                p.Email = Reader["EMAIL"].ToString();
                liste.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return liste;
        }

        public async Task AddAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO magaza_temsilci
                                            (MAGAZA_ID
                                            , DEPO_ID
                                            , ISIM
                                            , SOYISIM
                                            , AKTIF
                                            , TIP_ID
                                            , TELEFON
                                            , DAHILI
                                            , PUAN
                                            , PUANVERENSAYISI
                                            , RESIM
                                            , KULLANICIADI
                                            , SIFRE
                                            , ROLLER
                                            , SIPARIS_IPTAL_LIMITI
                                            , EMAIL
                                            )
                                    VALUES
                                            (@MAGAZA_ID
                                            , @DEPO_ID
                                            , @ISIM
                                            , @SOYISIM
                                            , @AKTIF
                                            , @TIP_ID
                                            , @TELEFON
                                            , @DAHILI
                                            , @PUAN
                                            , @PUANVERENSAYISI
                                            , @RESIM
                                            , @KULLANICIADI
                                            , @SIFRE
                                            , @ROLLER
                                            , @SIPARIS_IPTAL_LIMITI
                                            , @EMAIL
                                            )";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.VarChar).Value = entity.StoreID;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.VarChar).Value = entity.WarehouseID;
            cmd.Parameters.Add("@ISIM", MySqlDbType.VarChar).Value = entity.Name;
            cmd.Parameters.Add("@SOYISIM", MySqlDbType.VarChar).Value = entity.LastName;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Int16).Value = entity.Active;
            cmd.Parameters.Add("@TIP_ID", MySqlDbType.Int16).Value = entity.TypeID;
            cmd.Parameters.Add("@TELEFON", MySqlDbType.Text).Value = entity.Telephone;
            cmd.Parameters.Add("@DAHILI", MySqlDbType.Text).Value = entity.InternalNumber;
            cmd.Parameters.Add("@PUAN", MySqlDbType.Double).Value = entity.Point;
            cmd.Parameters.Add("@PUANVERENSAYISI", MySqlDbType.Int32).Value = entity.NumberofScorers;
            cmd.Parameters.Add("@RESIM", MySqlDbType.VarChar).Value = entity.Image;
            cmd.Parameters.Add("@KULLANICIADI", MySqlDbType.VarChar).Value = entity.Username;
            cmd.Parameters.Add("@SIFRE", MySqlDbType.VarChar).Value = entity.Password;
            cmd.Parameters.Add("@ROLLER", MySqlDbType.VarChar).Value = entity.Rols;
            cmd.Parameters.Add("@SIPARIS_IPTAL_LIMITI", MySqlDbType.Int32).Value = entity.OrderCancelLimit;
            cmd.Parameters.Add("@EMAIL", MySqlDbType.Int32).Value = entity.Email;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task UpdateAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
//            if (_cnn.State == ConnectionState.Closed)
//            {
//                _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//                await _cnn.OpenAsync(cancellationToken);
//            }


            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE
                                        magaza_temsilci
                                SET
                                        AKTIF = @AKTIF
                                        , TIP_ID = @TIP_ID
                                        , SIPARIS_IPTAL_LIMITI = @SIPARIS_IPTAL_LIMITI
                                        , KARGOTIPI = @KARGOTIPI
                                        , CUSTOM_PRINTER = @CUSTOM_PRINTER
                                        , DEPO_ID = @DEPO_ID
                                        , MAGAZA_ID = @MAGAZA_ID";

            if (!string.IsNullOrEmpty(entity.Name))
            {
                cmd.CommandText += @" , ISIM = @ISIM";
                cmd.Parameters.Add("@ISIM", MySqlDbType.VarChar).Value = entity.Name;
            }

            if (!string.IsNullOrEmpty(entity.LastName))
            {
                cmd.CommandText += @" , SOYISIM = @SOYISIM";
                cmd.Parameters.Add("@SOYISIM", MySqlDbType.VarChar).Value = entity.LastName;
            }

            if (!string.IsNullOrEmpty(entity.Telephone))
            {
                cmd.CommandText += @" , TELEFON = @TELEFON";
                cmd.Parameters.Add("@TELEFON", MySqlDbType.Text).Value = entity.Telephone;
            }

            if (!string.IsNullOrEmpty(entity.InternalNumber))
            {
                cmd.CommandText += @" , DAHILI = @DAHILI";
                cmd.Parameters.Add("@DAHILI", MySqlDbType.Text).Value = entity.InternalNumber;
            }

            if (!string.IsNullOrEmpty(entity.Image))
            {
                cmd.CommandText += @" , RESIM = @RESIM";
                cmd.Parameters.Add("@RESIM", MySqlDbType.VarChar).Value = entity.Image;
            }

            if (!string.IsNullOrEmpty(entity.Username))
            {
                cmd.CommandText += @" , KULLANICIADI = @KULLANICIADI";
                cmd.Parameters.Add("@KULLANICIADI", MySqlDbType.VarChar).Value = entity.Username;
            }

            if (!string.IsNullOrEmpty(entity.Password))
            {
                cmd.CommandText += @" , SIFRE = @SIFRE";
                cmd.Parameters.Add("@SIFRE", MySqlDbType.VarChar).Value = entity.Password;
            }

            if (!string.IsNullOrEmpty(entity.Email))
            {
                cmd.CommandText += @" , EMAIL = @EMAIL";
                cmd.Parameters.Add("@EMAIL", MySqlDbType.VarChar).Value = entity.Email;
            }

            cmd.CommandText += @" WHERE
                                        ID = @ID ";

            cmd.Parameters.Add("@AKTIF", MySqlDbType.Int16).Value = entity.Active;
            cmd.Parameters.Add("@TIP_ID", MySqlDbType.Int32).Value = entity.TypeID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            cmd.Parameters.Add("@KARGOTIPI", MySqlDbType.Int32).Value = entity.CargoType;
            cmd.Parameters.Add("@SIPARIS_IPTAL_LIMITI", MySqlDbType.Int32).Value = entity.OrderCancelLimit;
            cmd.Parameters.Add("@CUSTOM_PRINTER", MySqlDbType.Int16).Value = entity.CustomPrinter;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = entity.StoreID;


            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task DeleteAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM magaza_temsilci WHERE ID = @ID ";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND MAGAZA_ID = @MAGAZA_ID AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(StoreAgentFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(mt.ID)
                                , dyk.YETKIGRUP_ID
                                FROM magaza_temsilci AS mt
                                LEFT JOIN depo_yetki_kullanici AS dyk ON dyk.UYE_ID = mt.ID
                                WHERE 1 ";

            if (WebSiteInfo.User.Value != null && !WebSiteInfo.User.Value.isTicimaxUser)
                cmd.CommandText += " AND mt.KULLANICIADI NOT LIKE '%@p.ticimax.com%'";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return count;
        }

        public async Task<int> AddIDAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"INSERT INTO magaza_temsilci
                                            (MAGAZA_ID
                                            , DEPO_ID
                                            , ISIM
                                            , SOYISIM
                                            , AKTIF
                                            , TIP_ID
                                            , TELEFON
                                            , DAHILI
                                            , PUAN
                                            , PUANVERENSAYISI
                                            , RESIM
                                            , KULLANICIADI
                                            , SIFRE
                                            , ROLLER
                                            , SIPARIS_IPTAL_LIMITI
                                            , CUSTOM_PRINTER
                                            , EMAIL
                                            )
                                    VALUES
                                            (@MAGAZA_ID
                                            , @DEPO_ID
                                            , @ISIM
                                            , @SOYISIM
                                            , @AKTIF
                                            , @TIP_ID
                                            , @TELEFON
                                            , @DAHILI
                                            , @PUAN
                                            , @PUANVERENSAYISI
                                            , @RESIM
                                            , @KULLANICIADI
                                            , @SIFRE
                                            , @ROLLER
                                            , @SIPARIS_IPTAL_LIMITI
                                            , @CUSTOM_PRINTER
                                            , @EMAIL
                                            )";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.VarChar).Value = entity.StoreID;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.VarChar).Value = entity.WarehouseID;
            cmd.Parameters.Add("@ISIM", MySqlDbType.VarChar).Value = entity.Name;
            cmd.Parameters.Add("@SOYISIM", MySqlDbType.VarChar).Value = entity.LastName;
            cmd.Parameters.Add("@AKTIF", MySqlDbType.Int16).Value = entity.Active;
            cmd.Parameters.Add("@TIP_ID", MySqlDbType.Int16).Value = entity.TypeID;
            cmd.Parameters.Add("@TELEFON", MySqlDbType.Text).Value = entity.Telephone;
            cmd.Parameters.Add("@DAHILI", MySqlDbType.Text).Value = entity.InternalNumber;
            cmd.Parameters.Add("@PUAN", MySqlDbType.Double).Value = entity.Point;
            cmd.Parameters.Add("@PUANVERENSAYISI", MySqlDbType.Int32).Value = entity.NumberofScorers;
            cmd.Parameters.Add("@RESIM", MySqlDbType.VarChar).Value = entity.Image;
            cmd.Parameters.Add("@KULLANICIADI", MySqlDbType.VarChar).Value = entity.Username;
            cmd.Parameters.Add("@SIFRE", MySqlDbType.VarChar).Value = entity.Password;
            cmd.Parameters.Add("@ROLLER", MySqlDbType.VarChar).Value = entity.Rols;
            cmd.Parameters.Add("@SIPARIS_IPTAL_LIMITI", MySqlDbType.Int32).Value = entity.OrderCancelLimit;
            cmd.Parameters.Add("@CUSTOM_PRINTER", MySqlDbType.Bit).Value = entity.CustomPrinter;
            cmd.Parameters.Add("@EMAIL", MySqlDbType.VarChar).Value = entity.Email;
            await cmd.ExecuteTransactionCommandAsync();

//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            if (_cnn.State != ConnectionState.Open)
//                await _cnn.OpenAsync(cancellationToken);

            cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText += $@"SET @@SESSION.information_schema_stats_expiry = 0; 
                                        SELECT AUTO_INCREMENT
                                        FROM  INFORMATION_SCHEMA.TABLES
                                        WHERE TABLE_SCHEMA = '{Info.DomainName.Value.Replace(".", string.Empty).Replace("-", string.Empty)}'
                                        AND TABLE_NAME = 'magaza_temsilci';";

            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            

            return result.ToInt32();
        }

        public async Task UpdateSetNoAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE
                                        magaza_temsilci
                                SET
                                        SET_NO = @SETNO
                                WHERE
                                        ID = @ID ";

            cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = entity.SetNo;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND MAGAZA_ID = @MAGAZAID AND DEPO_ID = @DEPOID";
                cmd.Parameters.Add("@MAGAZAID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateTableIDAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE magaza_temsilci SET MASA_ID = @MASAID WHERE ID = @ID ";
            cmd.Parameters.Add("@MASAID", MySqlDbType.Int32).Value = entity.TableID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND MAGAZA_ID = @MAGAZAID AND DEPO_ID = @DEPOID";
                cmd.Parameters.Add("@MAGAZAID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<List<SimpleStoreAgentDto>> GetSimpleAgentListAsync(CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<SimpleStoreAgentDto> liste = new List<SimpleStoreAgentDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  mt.ID
                                      , mt.ISIM
                                      , mt.SOYISIM FROM magaza_temsilci AS mt WHERE 1";

            if (!WebSiteInfo.User.Value.isTicimaxUser)
                cmd.CommandText += " AND mt.KULLANICIADI NOT LIKE '%@p.ticimax.com%' ";

            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND mt.MAGAZA_ID = @MAGAZA_ID AND mt.DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                SimpleStoreAgentDto p = new SimpleStoreAgentDto();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.FullName = Reader["ISIM"] + " " + Reader["SOYISIM"];
                liste.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return liste;
        }

        public async Task<List<SimpleStoreAgentDto>> GetAvailableAgentListAsync(GetAvailableAgentListDto entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<SimpleStoreAgentDto> liste = new List<SimpleStoreAgentDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  mt.ID
                                      , mt.ISIM
                                      , mt.SOYISIM FROM magaza_temsilci AS mt
                                LEFT JOIN depo_araba AS da ON mt.ID = da.KULLANICIID
                                WHERE 1 AND (ISNULL(mt.SET_NO) OR LENGTH(mt.SET_NO) < 1) AND ISNULL(da.KULLANICIID) AND mt.MASA_ID = 0 ";

            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND mt.MAGAZA_ID = @MAGAZA_ID AND mt.DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            if (!WebSiteInfo.User.Value.isTicimaxUser)
                cmd.CommandText += " AND mt.KULLANICIADI NOT LIKE '%@p.ticimax.com%' ";

            if (!string.IsNullOrEmpty(entity.SetNo))
            {
                cmd.CommandText += @"
                                     UNION ALL
                                     SELECT   mt.ID
                                           ,  mt.ISIM
                                           ,  mt.SOYISIM FROM magaza_temsilci AS mt
                                     LEFT JOIN depo_araba as da ON mt.ID = da.KULLANICIID
                                     WHERE 1 AND mt.SET_NO = @SET_NO ";

                if (!WebSiteInfo.User.Value.isTicimaxUser)
                    cmd.CommandText += " AND mt.KULLANICIADI NOT LIKE '%@p.ticimax.com%' ";

                if (!WebSiteInfo.User.Value.IsOneStore)
                    cmd.CommandText += " AND mt.MAGAZA_ID = @MAGAZA_ID AND mt.DEPO_ID = @DEPO_ID";

                cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = entity.SetNo;
            }

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                SimpleStoreAgentDto p = new SimpleStoreAgentDto
                {
                    ID = Convert.ToInt32(Reader["ID"]),
                    FullName = Reader["ISIM"] + " " + Reader["SOYISIM"]
                };

                liste.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            liste = liste.OrderBy(x => x.ID).ToList();
            return liste;
        }

        public async Task UpdateTableQuickMenuAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE
                                        magaza_temsilci
                                SET
                                        HIZLIMENU = @HIZLIMENU
                                WHERE
                                        ID = @ID";

            cmd.Parameters.Add("@HIZLIMENU", MySqlDbType.Text).Value = entity.QuickMenus.ToJsonSerialize();
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND MAGAZA_ID = @MAGAZA_ID AND DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task<List<QuickMenu>> GetQuickMenusAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<QuickMenu> response = new List<QuickMenu>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  mt.ID
                                      , mt.HIZLIMENU FROM magaza_temsilci AS mt WHERE ID = @ID ";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            if (!WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.CommandText += " AND mt.MAGAZA_ID = @MAGAZA_ID AND mt.DEPO_ID = @DEPO_ID";
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            }

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                response = Reader["HIZLIMENU"].ToString().ToJsonDeserialize<List<QuickMenu>>();
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();

            return response;
        }

        public async Task UpdateWarehouseIDAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE magaza_temsilci SET DEPO_ID = @DEPOID WHERE ID = @ID";
            cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task UpdateStoreIDAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE magaza_temsilci SET MAGAZA_ID = @MAGAZAID WHERE ID = @ID";
            cmd.Parameters.Add("@MAGAZAID", MySqlDbType.Int32).Value = entity.StoreID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task UpdateWarehouseAndStoreAsync(StoreAgent entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE magaza_temsilci SET DEPO_ID = @DEPOID,MAGAZA_ID = @MAGAZAID  WHERE ID = @ID";
            cmd.Parameters.Add("@DEPOID", MySqlDbType.Int32).Value = entity.WarehouseID;
            cmd.Parameters.Add("@MAGAZAID", MySqlDbType.Int32).Value = entity.StoreID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }

        public async Task<string> HashedPasswordAsync(StoreAgentFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT mt.SIFRE FROM magaza_temsilci AS mt WHERE 1 ";
            AppendFilter(ref cmd, filter);
            string password = (await cmd.ExecuteScalarAsync(cancellationToken)).ToString();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            
            return password;
        }


        private void AppendFilter(ref MySqlCommand cmd, StoreAgentFilter filter)
        {
            if (WebSiteInfo.User.Value != null && !WebSiteInfo.User.Value.isTicimaxUser)
                cmd.CommandText += " AND mt.KULLANICIADI NOT LIKE '%p.ticimax.com%'";

            if (filter != null)
            {
                if (!WebSiteInfo.User?.Value?.IsOneStore ?? false)
                {
                    if(filter.StoreID > 0 || WebSiteInfo.User.Value.StoreID > 0)
                    {
                        cmd.CommandText += " AND mt.MAGAZA_ID = @MAGAZA_ID";
                        cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = filter.StoreID > 0 ? filter.StoreID : WebSiteInfo.User.Value.StoreID;
                    }
                    if((filter.WarehouseID > 0 || WebSiteInfo.User.Value.WarehouseID > 0) && filter.WareHouseIdControl)
                    {
                        cmd.CommandText += " AND mt.DEPO_ID = @DEPO_ID";
                        cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = filter.WarehouseID > 0 ? filter.WarehouseID : WebSiteInfo.User.Value.WarehouseID;
                    }
                }

                if (filter.IDs != null && filter.IDs.Count > 0)
                {
                    cmd.CommandText += $" AND mt.ID IN ({string.Join(',', filter.IDs)})";
                }

                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND mt.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (filter.Active > -1)
                {
                    cmd.CommandText += " AND mt.AKTIF = @AKTIF";
                    cmd.Parameters.Add("@AKTIF", MySqlDbType.Int32).Value = filter.Active;
                }

                if (filter.TypeID > 0)
                {
                    cmd.CommandText += " AND mt.TIP_ID = @TIP_ID";
                    cmd.Parameters.Add("@TIP_ID", MySqlDbType.Int32).Value = filter.TypeID;
                }

                if (!string.IsNullOrEmpty(filter.Username))
                {
                    cmd.CommandText += " AND KULLANICIADI = @KULLANICIADI";
                    cmd.Parameters.Add("@KULLANICIADI", MySqlDbType.VarChar).Value = filter.Username;
                }

                if (!string.IsNullOrEmpty(filter.UserNameContains))
                {
                    cmd.CommandText += " AND KULLANICIADI LIKE @KULLANICIADICONTAINS";
                    cmd.Parameters.Add("@KULLANICIADICONTAINS", MySqlDbType.VarChar).Value = "%" + filter.UserNameContains + "%";
                }

                if (!string.IsNullOrEmpty(filter.Password))
                {
                    cmd.CommandText += " AND mt.SIFRE = @SIFRE";
                    cmd.Parameters.Add("@SIFRE", MySqlDbType.VarChar).Value = filter.Password;
                }

                if (!string.IsNullOrEmpty(filter.SetNo))
                {
                    cmd.CommandText += " AND mt.SET_NO = @SETNO";
                    cmd.Parameters.Add("@SETNO", MySqlDbType.VarChar).Value = filter.SetNo;
                }

                if (filter.ShadowUserID.HasValue)
                {
                    cmd.CommandText += " AND mt.GOLGE_KULLANICI_ID = @GOLGE_KULLANICI_ID";
                    cmd.Parameters.Add("@GOLGE_KULLANICI_ID", MySqlDbType.Int32).Value = filter.ShadowUserID.Value;
                }

                if (filter.IsCustomPrinter is > -1)
                {
                    cmd.CommandText += " AND mt.CUSTOM_PRINTER = @CUSTOMPRINTER";
                    cmd.Parameters.Add("@CUSTOMPRINTER", MySqlDbType.Int32).Value = filter.IsCustomPrinter;
                }
                if (filter.AuthGroupID.HasValue)
                {
                    cmd.CommandText += " AND dyk.YETKIGRUP_ID = @AUTH_GROUP_ID";
                    cmd.Parameters.Add("@AUTH_GROUP_ID", MySqlDbType.Int32).Value = filter.AuthGroupID.Value;
                }
            }
        }
        public async Task TwoFactorAuthKeyAndActiveUpdateAsync(int personId, string authKey, bool isActive, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE
                                        magaza_temsilci
                                SET
                                        TWOFACTORAUTHKEY = @TWOFACTORAUTHKEY,
                                        TWOFA_AKTIF = @TWOFA_AKTIF
                                WHERE
                                        ID = @ID";

            cmd.Parameters.Add("@TWOFACTORAUTHKEY", MySqlDbType.VarChar).Value = authKey;
            cmd.Parameters.Add("@TWOFA_AKTIF", MySqlDbType.Bit).Value = isActive;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = personId;        
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
        }
    }
}