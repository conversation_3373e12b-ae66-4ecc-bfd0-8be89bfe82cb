using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class WarehouseDal : IWarehouseDal
    {
        private MySqlConnection _cnn;

        public WarehouseDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        #region Public Methods

        public async Task<WarehouseTypes> GetTypes(WarehouseFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            WarehouseTypes list = new WarehouseTypes();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT      d.ID
                                          , d.TANIM
                                          , m.TANIM AS MAGAZATANIM
                                    FROM depolar AS d
                                    LEFT JOIN magazalar as m ON m.ID = d.MAGAZA_ID
                                    WHERE 1 ";

            AppendFilter(ref cmd, filter);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                var storeName = reader["MAGAZATANIM"] != DBNull.Value ? reader["MAGAZATANIM"].ToString() : "";
                list.Types.Add(new WarehouseTypeItem
                {
                    Id = Convert.ToInt32(reader["ID"]),
                    Name = $"{storeName}-{reader["TANIM"]}"
                });
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return list;
        }

        public async Task AddAsync(Entities.Concrete.Warehouse entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depolar
                                                (TANIM
                                                , KOD
                                                , MAGAZA_ID)
                                    VALUES (@TANIM
                                          , @KOD
                                          , @MAGAZA_ID)";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = entity.Code;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = entity.StoreID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task<int> AddID(Entities.Concrete.Warehouse entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depolar
                                                (TANIM
                                                , KOD
                                                , MAGAZA_ID)
                                    VALUES (@TANIM
                                          , @KOD
                                          , @MAGAZA_ID)";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = entity.Code;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = entity.StoreID;
            await cmd.ExecuteNonQueryAsync();
            entity.ID = Convert.ToInt32(cmd.LastInsertedId);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return entity.ID;
        }

        public async Task<int> AddIDAsync(Entities.Concrete.Warehouse entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO depolar
                                                (TANIM
                                                , KOD
                                                , MAGAZA_ID)
                                    VALUES (@TANIM
                                          , @KOD
                                          , @MAGAZA_ID)";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = entity.Code;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = entity.StoreID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            entity.ID = Convert.ToInt32(cmd.LastInsertedId);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return entity.ID;
        }

        public async Task DeleteAsync(Entities.Concrete.Warehouse entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"DELETE FROM depolar WHERE ID = @ID;DELETE FROM urun_raf WHERE RAF_ID IN (SELECT ID FROM raflar WHERE DEPO_ID = @ID); DELETE FROM raflar WHERE DEPO_ID = @ID;";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        public async Task<int> GetCountAsync(WarehouseFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM depolar as d
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<List<Entities.Concrete.Warehouse>> GetListAsync(WarehouseFilter filter = null, WarehousePaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<Entities.Concrete.Warehouse> list = new List<Entities.Concrete.Warehouse>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT      d.ID
                                          , d.TANIM
                                          , d.KOD
                                          , d.MAGAZA_ID
                                          , d.SIRA
                                          , d.AKTIF
                                    FROM depolar as d
                                    WHERE 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                Entities.Concrete.Warehouse p = new Entities.Concrete.Warehouse();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.StoreID = Convert.ToInt32(Reader["MAGAZA_ID"]);
                p.Definition = Reader["TANIM"].ToString();
                p.Code = Reader["KOD"].ToString();
                p.Rank = Reader["SIRA"].ToInt32();
                p.Active = Reader["AKTIF"].ToBoolean();
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return list;
        }

        public async Task UpdateAsync(Entities.Concrete.Warehouse entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE depolar
                                    SET TANIM = @TANIM
                                      , KOD = @KOD
                                      , MAGAZA_ID = @MAGAZA_ID
                                    WHERE ID = @ID";

            cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = entity.Code;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = entity.StoreID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
        }

        #endregion Public Methods

        private void AppendFilter(ref MySqlCommand cmd, WarehouseFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND d.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (filter.StoreID > 0)
                {
                    cmd.CommandText += " AND d.MAGAZA_ID = @MAGAZA_ID";
                    cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = filter.StoreID;
                }

                if (!string.IsNullOrEmpty(filter.Code))
                {
                    cmd.CommandText += " AND d.KOD = @KOD";
                    cmd.Parameters.Add("@KOD", MySqlDbType.VarChar).Value = filter.Code;
                }

                if (!string.IsNullOrEmpty(filter.Definition))
                {
                    cmd.CommandText += " AND d.TANIM LIKE @TANIM";
                    cmd.Parameters.Add("@TANIM", MySqlDbType.VarChar).Value = "%" + filter.Definition + "%";
                }

                if (filter.Active.HasValue)
                {
                    cmd.CommandText += " AND d.AKTIF = @AKTIF";
                    cmd.Parameters.Add("@AKTIF", MySqlDbType.Int16).Value = filter.Active.Value;
                }
            }
        }
    }
}