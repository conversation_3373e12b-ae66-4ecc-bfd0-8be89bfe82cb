using MySqlConnector;
using Org.BouncyCastle.Asn1.Ocsp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class ReturnOrderRequestDal : IReturnOrderRequestDal
    {
        private MySqlConnection _cnn;

        public ReturnOrderRequestDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<ReturnOrderProductRequest>> GetListProductsAsync(ReturnOrderProductRequestFilter filter = null, ReturnOrderProductRequestPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<ReturnOrderProductRequest> list = new List<ReturnOrderProductRequest>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT      itu.ID
                                           ,itu.SIPARIS_ID
                                           ,itu.SIPARIS_URUN_ID
                                           ,itu.URUNKARTI_ID
                                           ,itu.URUN_ID
                                           ,itu.ADET
                                           ,itu.IPTALIADENEDEN_ID
                                           ,iin.Tanim as IPTALIADENEDENTANIM
                                           ,itu.IPTALIADETIP_ID
                                           ,iit.Tanim AS IPTALIADETIPTANIM
                                           ,itu.DURUM_ID
                                           ,itu.CEVAP
                                           ,itu.CEVAPTARIHI
                                    FROM iadetalep_urun AS itu
                                    INNER JOIN iptaliadeneden as iin ON 
                                    iin.ID = itu.IPTALIADENEDEN_ID
                                    INNER JOIN iptaliadetip as iit ON 
                                    iit.ID = itu.IPTALIADETIP_ID
                                    WHERE 1=1 ";

            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                ReturnOrderProductRequest returnOrderProductRequest = new ReturnOrderProductRequest();
                returnOrderProductRequest.Id = reader["ID"].ToInt32();
                returnOrderProductRequest.OrderId = reader["SIPARIS_ID"].ToInt32();
                returnOrderProductRequest.OrderProductId = reader["SIPARIS_URUN_ID"].ToInt32();
                returnOrderProductRequest.ProductCardId = reader["URUNKARTI_ID"].ToInt32();
                returnOrderProductRequest.ProductId = reader["URUN_ID"].ToInt32();
                returnOrderProductRequest.Quantity = reader["ADET"].ToDouble();
                returnOrderProductRequest.CancelReturnRequestId = reader["IPTALIADENEDEN_ID"].ToInt32();
                returnOrderProductRequest.CancelReturnRequestName = reader["IPTALIADENEDENTANIM"].ToString();
                returnOrderProductRequest.CancelReturnReasonTypeName = reader["IPTALIADETIPTANIM"].ToString();
                returnOrderProductRequest.CancelReturnTypeId = reader["IPTALIADETIP_ID"].ToInt32();
                returnOrderProductRequest.StatusId = reader["DURUM_ID"].ToInt32();
                returnOrderProductRequest.Reply = reader["CEVAP"].ToString();
                returnOrderProductRequest.ReplyDate = reader["CEVAPTARIHI"].ToDateTime();
                list.Add(returnOrderProductRequest);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return list;
        }

        public async Task<ReturnOrderRequest> GetRequestByOrderId(int? orderId, string? cargoCode, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            ReturnOrderRequest item = null;
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT   it.ID
                                        ,it.SIPARIS_ID
                                        ,it.NOTLAR
                                        ,it.IBAN_ISIMSOYISIM
                                        ,it.IBAN
                                    FROM iadetalep AS it
                                    WHERE 1 = 1";
            if (orderId.HasValue)
            {
                cmd.CommandText += " AND it.SIPARIS_ID = @SIPARIS_ID ";
                cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderId.Value;
            }

            if (!string.IsNullOrEmpty(cargoCode))
            {
                cmd.CommandText += " AND (it.KARGOKODU = @KARGOKODU OR KARGOCAGRIKODU = @KARGOKODU) ";
                cmd.Parameters.Add("@KARGOKODU", MySqlDbType.VarChar).Value = cargoCode;
            }

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                item = new ReturnOrderRequest();
                item.Id = reader["ID"].ToInt32();
                item.OrderId = reader["SIPARIS_ID"].ToInt32();
                item.Note = reader["NOTLAR"].ToString();
                item.IbanName = reader["IBAN_ISIMSOYISIM"].ToString();
                item.Iban = reader["IBAN"].ToString();
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return item;
        }

        public async Task<int> Add(ReturnOrderRequest entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"INSERT INTO iadetalep
                                                    (UYE_ID
                                                     , UYEADI
                                                     , SIPARIS_ID
                                                     , NOTLAR
                                                     , PARAIADETIPI
                                                     , DURUM_ID
                                                     , EKLEMETARIHI
                                                     , KARGOFIRMA_ID
                                                     , KARGOENTEGRASYON_ID
                                                     , KARGOKODU
                                                     , IPTALIADENEDEN_ID
                                                     , IPTALIADETIP_ID
                                                     , KARGOCAGRIKODU
                                                     , HEDIYECEKID
                                                     , HEDIYECEKKODU
                                                     , ACIKLAMA
                                                     , IBAN_ISIMSOYISIM
                                                     , IBAN
                                                     , TUTAR
                                                     , PARABIRIMI
                                                     , MAGAZA_ID
                                                     , MAGAZAIADEKODU
                                                     , PAZARYERIAKTARILDI
                                                     , PAZARYERI_IADEID
                                                     )
                                        VALUES (@UYE_ID
                                                , @UYEADI
                                                , @SIPARIS_ID
                                                , @NOTLAR
                                                , @PARAIADETIPI
                                                , @DURUM_ID
                                                , @EKLEMETARIHI
                                                , @KARGOFIRMA_ID
                                                , @KARGOENTEGRASYON_ID
                                                , @KARGOKODU
                                                , @IPTALIADENEDEN_ID
                                                , @IPTALIADETIP_ID
                                                , @KARGOCAGRIKODU
                                                , @HEDIYECEKID
                                                , @HEDIYECEKKODU
                                                , @ACIKLAMA
                                                , @IBAN_ISIMSOYISIM
                                                , @IBAN
                                                , @TUTAR
                                                , @PARABIRIMI
                                                , @MAGAZA_ID
                                                , @MAGAZAIADEKODU
                                                , @PAZARYERIAKTARILDI
                                                , @PAZARYERI_IADEID
                                                )";

            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = entity.MemberId;
            cmd.Parameters.Add("@UYEADI", MySqlDbType.VarChar).Value = entity.MemberName;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderId;
            cmd.Parameters.Add("@NOTLAR", MySqlDbType.VarChar).Value = entity.Note;
            cmd.Parameters.Add("@PARAIADETIPI", MySqlDbType.Int32).Value = entity.RefundType;
            cmd.Parameters.Add("@DURUM_ID", MySqlDbType.Int32).Value = entity.StatusId;
            cmd.Parameters.Add("@EKLEMETARIHI", MySqlDbType.DateTime).Value = entity.CreatedDate;
            cmd.Parameters.Add("@KARGOFIRMA_ID", MySqlDbType.Int32).Value = entity.CargoCompanyId;
            cmd.Parameters.Add("@KARGOENTEGRASYON_ID", MySqlDbType.Int32).Value = entity.CargoIntegrationId;
            cmd.Parameters.Add("@KARGOKODU", MySqlDbType.VarChar).Value = entity.CargoCode;
            cmd.Parameters.Add("@IPTALIADENEDEN_ID", MySqlDbType.VarChar).Value = entity.ReasonForCancellationId;
            cmd.Parameters.Add("@IPTALIADETIP_ID", MySqlDbType.VarChar).Value = entity.ReasonForCancellationTypeId;
            cmd.Parameters.Add("@KARGOCAGRIKODU", MySqlDbType.VarChar).Value = entity.CargoCallCode;
            cmd.Parameters.Add("@HEDIYECEKID", MySqlDbType.Int32).Value = entity.GiftCertificateId;
            cmd.Parameters.Add("@HEDIYECEKKODU", MySqlDbType.VarChar).Value = entity.GiftCertificateCode;
            cmd.Parameters.Add("@ACIKLAMA", MySqlDbType.Text).Value = entity.Description;
            cmd.Parameters.Add("@IBAN_ISIMSOYISIM", MySqlDbType.Text).Value = entity.IbanName;
            cmd.Parameters.Add("@IBAN", MySqlDbType.Text).Value = entity.Iban;
            cmd.Parameters.Add("@TUTAR", MySqlDbType.Double).Value = entity.Amount;
            cmd.Parameters.Add("@PARABIRIMI", MySqlDbType.VarChar).Value = entity.Currency;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = entity.StoreId;
            cmd.Parameters.Add("@MAGAZAIADEKODU", MySqlDbType.VarChar).Value = entity.StoreReturnCode;
            cmd.Parameters.Add("@PAZARYERIAKTARILDI", MySqlDbType.Bit).Value = entity.MarketplaceTransferred;
            cmd.Parameters.Add("@PAZARYERI_IADEID", MySqlDbType.VarChar).Value = entity.MarketplaceReturnId;

            await cmd.ExecuteTransactionCommandAsync(false);
            entity.Id = Convert.ToInt32(cmd.LastInsertedId);

            await OrderRefundRequestCreatedUpdate(entity.OrderId, false, cancellationToken);
            return entity.Id;

        }


        public async Task AddProduct(ReturnOrderProductRequest entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"INSERT INTO iadetalep_urun
                                                    (IADETALEP_ID
                                                      , SIPARIS_ID
                                                      , SIPARIS_URUN_ID
                                                      , URUNKARTI_ID
                                                      , URUN_ID
                                                      , ADET
                                                      , IPTALIADENEDEN_ID
                                                      , IPTALIADETIP_ID
                                                      , CEVAP
                                                      , CEVAPTARIHI
                                                      , DURUM_ID
                                                     )
                                        VALUES (@IADETALEP_ID
                                                  , @SIPARIS_ID
                                                  , @SIPARIS_URUN_ID
                                                  , @URUNKARTI_ID
                                                  , @URUN_ID
                                                  , @ADET
                                                  , @IPTALIADENEDEN_ID
                                                  , @IPTALIADETIP_ID
                                                  , @CEVAP
                                                  , @CEVAPTARIHI
                                                  , @DURUM_ID
                                                )";

            cmd.Parameters.Add("@IADETALEP_ID", MySqlDbType.Int32).Value = entity.ReturnRequestId;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderId;
            cmd.Parameters.Add("@SIPARIS_URUN_ID", MySqlDbType.Int32).Value = entity.OrderProductId;
            cmd.Parameters.Add("@URUNKARTI_ID", MySqlDbType.Int32).Value = entity.ProductCardId;
            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = entity.ProductId;
            cmd.Parameters.Add("@ADET", MySqlDbType.Double).Value = entity.Quantity;
            cmd.Parameters.Add("@IPTALIADENEDEN_ID", MySqlDbType.Int32).Value = entity.CancelReturnRequestId;
            cmd.Parameters.Add("@IPTALIADETIP_ID", MySqlDbType.Int32).Value = entity.CancelReturnTypeId;
            cmd.Parameters.Add("@CEVAP", MySqlDbType.VarChar, 500).Value = entity.Answer;
            cmd.Parameters.Add("@CEVAPTARIHI", MySqlDbType.Date).Value = entity.ReplyDate;
            cmd.Parameters.Add("@DURUM_ID", MySqlDbType.Int32).Value = (entity.StatusId <= 0 ? 1 : entity.StatusId);

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task OrderRefundRequestCreatedUpdate(int orderId, bool returnRequestCanBeCreated, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE siparis SET IADETALEPOLUSTURULABILIR = @IADETALEPOLUSTURULABILIR, DUZENLEMETARIHI= NOW() WHERE ID = @ID";
            cmd.Parameters.Add("@IADETALEPOLUSTURULABILIR", MySqlDbType.Int16).Value = returnRequestCanBeCreated.ParseToInt();
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderId;
            await cmd.ExecuteTransactionCommandAsync();
        }



        private void AppendFilter(ref MySqlCommand cmd, ReturnOrderProductRequestFilter filter)
        {
            if (filter != null)
            {
                if (filter.OrderId > 0)
                {
                    cmd.CommandText += " AND SIPARIS_ID = @SIPARIS_ID";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderId;
                }
            }
        }

    }
}