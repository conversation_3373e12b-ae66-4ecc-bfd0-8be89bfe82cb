using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete
{
    public class DynamicFormFileDal : IDynamicFormFileDal
    {
        private MySqlConnection _cnn;

        public DynamicFormFileDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<int> GetCountAsync(DynamicFormFileFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM dinamik_formdosya
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<List<DynamicFormFile>> GetListAsync(DynamicFormFileFilter filter = null, DynamicFormFilePaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<DynamicFormFile> formDosyalar = new List<DynamicFormFile>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                      ID,
                                      FORMID,
                                      FORMDATAID,
                                      FORMSORU,
                                      DOSYAADI,
                                      EKLEMETARIHI,
                                      SIRA
                                    FROM
                                       dinamik_formdosya
                                    WHERE 1";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync();
            while (await Reader.ReadAsync())
            {
                DynamicFormFile p = new DynamicFormFile();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.FileName = Reader["DOSYAADI"].ToString();
                p.FormQuestion = Reader["FORMSORU"].ToString();
                p.AddingDate = Convert.ToDateTime(Reader["EKLEMETARIHI"]);
                p.FormDataID = Convert.ToInt32(Reader["FORMDATAID"]);
                p.FormID = Convert.ToInt32(Reader["FORMID"]);
                p.Rank = Convert.ToInt32(Reader["SIRA"]);
                p.FileRoad = "/Uploads/FormDosyalar/" + p.FormID + "/" + p.FileName;

                formDosyalar.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();

            return formDosyalar;
        }



        private void AppendFilter(ref MySqlCommand cmd, DynamicFormFileFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (filter.FormDataID > -1)
                {
                    cmd.CommandText += " AND FORMDATAID = @FORMDATAID";
                    cmd.Parameters.Add("@FORMDATAID", MySqlDbType.Int32).Value = filter.FormDataID;
                }

                if (filter.FormID > -1)
                {
                    cmd.CommandText += " AND FORMID = @FORMID";
                    cmd.Parameters.Add("@FORMID", MySqlDbType.Int32).Value = filter.FormID;
                }

                if (!string.IsNullOrEmpty(filter.DosyaAdi))
                {
                    cmd.CommandText += " AND DOSYAADI = @DOSYAADI";
                    cmd.Parameters.Add("@DOSYAADI", MySqlDbType.VarChar).Value = filter.DosyaAdi;
                }

                if (filter.FormDataIDs != null && filter.FormDataIDs.Count > 0)
                {
                    cmd.CommandText += $" AND ID IN ({string.Join(",", filter.FormDataIDs)})";
                }
            }
        }
    }
}