using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Enums;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.MySQL
{
    public class DynamicFormDal : IDynamicFormDal
    {
        private MySqlConnection _cnn;

        public DynamicFormDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<int> GetCountAsync(DynamicFormFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM dinamik_form
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var count = await cmd.ExecuteScalarAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return Convert.ToInt32(count);
        }

        public async Task<List<DynamicForm>> GetListAsync(DynamicFormFilter filter = null, DynamicFormPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            await _cnn.OpenAsync(cancellationToken);
            List<DynamicForm> formlar = new List<DynamicForm>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                    ID,
                                    BASLIK,
                                    FORMDATA,
	                                ONBILGI,
                                    TIP,
                                    AKTIF,
                                    FORMAYARLARI,
                                    MAILAYARLARI
                                 FROM dinamik_form  WHERE 1";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                DynamicForm p = new DynamicForm();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.Type = Convert.ToInt32(Reader["TIP"]);
                p.TypeText = ((DynamicFormType)p.Type).GetStringValue();
                p.Active = Convert.ToBoolean(Reader["AKTIF"]);
                p.Title = Reader["BASLIK"].ToString();
                p.FormData = Reader["FORMDATA"].ToString();
                //Null gelebilme ihtimali karşı kontrol eklendi.
                p.FormData = !string.IsNullOrEmpty(p.FormData) ? p.FormData : "[]";
                p.PreNotification = Reader["ONBILGI"].ToString();

                try
                {
                    p.Settings = !string.IsNullOrEmpty(Reader["FORMAYARLARI"].ToString()) ? Reader["FORMAYARLARI"].ToString().ToJsonDeserialize<DynamicFormSettings>() : new DynamicFormSettings();
                }
                catch (Exception)
                {
                    p.Settings = new DynamicFormSettings();
                }

                formlar.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            return formlar;
        }



        private void AppendFilter(ref MySqlCommand cmd, DynamicFormFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND ID = @ID ";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (filter.Type > -1)
                {
                    cmd.CommandText += " AND TIP = @TIP ";
                    cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = filter.Type;
                }

                if (filter.Active > 0)
                {
                    cmd.CommandText += " AND AKTIF = @AKTIF ";
                    cmd.Parameters.Add("@AKTIF", MySqlDbType.Int32).Value = filter.Active;
                }
            }
        }
    }
}