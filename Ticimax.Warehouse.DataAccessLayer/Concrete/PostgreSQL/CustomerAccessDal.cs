using Microsoft.Extensions.Options;
using Npgsql;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.DataAccessLayer.Abstract.PostgreSQL;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.PostgreSQL
{
    public class CustomerAccessDal : ICustomerAccessDal
    {
        private readonly string _connectionString;

        public CustomerAccessDal(IOptions<NpgsqlConnection> cnn)
        {
            _connectionString = cnn.Value.ConnectionString;
        }

        public async Task Add(CustomerAccess entity, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            NpgsqlCommand cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "INSERT INTO customer_access (domain, status) VALUES (@domain, @status)";
            cmd.Parameters.Add("@domain", NpgsqlTypes.NpgsqlDbType.Varchar).Value = entity.Domain;
            cmd.Parameters.Add("@status", NpgsqlTypes.NpgsqlDbType.Boolean).Value = entity.Status;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
        }

        public async Task Delete(CustomerAccess entity, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            NpgsqlCommand cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "DELETE FROM customer_access WHERE (id = @id OR domain = @domain)";
            cmd.Parameters.Add("@id", NpgsqlTypes.NpgsqlDbType.Integer).Value = entity.ID;
            cmd.Parameters.Add("@domain", NpgsqlTypes.NpgsqlDbType.Varchar).Value = entity.Domain;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
        }

        public async Task<int> GetCount(CustomerAccessFilter filter = null, CancellationToken cancellationToken = default)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            NpgsqlCommand cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT COUNT(id) FROM customer_access WHERE 1=1";
            AppendFilter(ref cmd, filter);
            int count = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return count;
        }

        public async Task<IList<CustomerAccess>> GetList(CustomerAccessFilter filter = null, CustomerAccessPaging paging = null, CancellationToken cancellationToken = default)
        {
            List<CustomerAccess> list = new List<CustomerAccess>();
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            NpgsqlCommand cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT id, domain, status FROM customer_access WHERE 1=1 ";
            AppendFilter(ref cmd, filter);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);

            while (await reader.ReadAsync(cancellationToken))
                list.Add(
                    new CustomerAccess
                    {
                        ID = reader["id"].ToInt32(),
                        Domain = reader["domain"].ToString(),
                        Status = reader["status"].ToBoolean()
                    });

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return list;
        }

        public async Task Update(CustomerAccess entity, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            NpgsqlCommand cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE customer_access SET status = @status WHERE id = @id OR domain = @domain";
            cmd.Parameters.Add("@id", NpgsqlTypes.NpgsqlDbType.Integer).Value = entity.ID;
            cmd.Parameters.Add("@domain", NpgsqlTypes.NpgsqlDbType.Varchar).Value = entity.Domain;
            cmd.Parameters.Add("@status", NpgsqlTypes.NpgsqlDbType.Boolean).Value = entity.Status;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
        }

        public async Task<int> GetAverageCustomerCount(string domainName, bool filterTestDomain, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"
                SELECT AVG(t.user_count) AS average_user_count
                FROM(
	                SELECT 
	                     COUNT(ui.Id) AS user_count
	                 FROM customer_access AS ca 
	                 LEFT JOIN user_information ui ON ui.domain_name = ca.domain
	                 WHERE 1 = 1 ";

            if (!string.IsNullOrEmpty(domainName))
            {
                cmd.CommandText += " AND ca.\"domain\" = @domainName ";
                cmd.Parameters.Add("@domainName", NpgsqlTypes.NpgsqlDbType.Varchar).Value = domainName;
            }

            if (filterTestDomain)
                cmd.CommandText += " AND ca.\"domain\" NOT LIKE '%ticimax%'";

            cmd.CommandText += @"
                GROUP BY ui.domain_name, ca.id, ca.status 
	            ORDER BY ca.domain
            ) as t";

            await _cnn.OpenAsync(cancellationToken);
            var count = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();
            
            return count;
        }

        public async Task<List<CustomerCountsDto>> GetCustomerCountReport(string domainName, bool filterTestDomain, int pageSize, int pageIndex, CancellationToken cancellationToken)
        {
            List<CustomerCountsDto> records = new List<CustomerCountsDto>();

            var _cnn = new NpgsqlConnection(_connectionString);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"
                SELECT 
                    ca.id, 
                    ca.""domain"", 
                    COUNT(ui.domain_name) AS user_count, 
                    ca.status 
                FROM customer_access AS ca 
                LEFT JOIN user_information ui ON ui.domain_name = ca.""domain"" 
                WHERE 1 = 1";

            if (!string.IsNullOrEmpty(domainName))
            {
                cmd.CommandText += " AND ca.\"domain\" = @domainName ";
                cmd.Parameters.Add("@domainName", NpgsqlTypes.NpgsqlDbType.Varchar).Value = domainName;
            }

            if (filterTestDomain)
                cmd.CommandText += " AND ca.\"domain\" NOT LIKE '%ticimax%'";

            cmd.CommandText += @"
                GROUP BY ui.domain_name, ca.id, ca.status 
                ORDER BY ca.""domain"" ";

            if (pageSize > 0 && pageIndex > 0)
                cmd.CommandText += $"OFFSET {(pageIndex - 1) * pageSize} LIMIT {pageSize} ";

            await _cnn.OpenAsync(cancellationToken);
            NpgsqlDataReader ndr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await ndr.ReadAsync(cancellationToken))
            {
                records.Add(new CustomerCountsDto
                {
                    Id = ndr["id"].ToInt32(),
                    DomainName = ndr["domain"].ToString(),
                    UserCount = ndr["user_count"].ToInt32(),
                    Status = ndr["status"].ToBoolean()
                });
            }

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            

            return records;
        }

        public async Task<int> GetCustomerCountReportCount(string domainName, bool filterTestDomain, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);

            NpgsqlCommand cmd = new NpgsqlCommand(string.Empty, _cnn);

            cmd.CommandText = "SELECT COUNT(ca.id) FROM customer_access AS ca WHERE 1 = 1";

            if (!string.IsNullOrEmpty(domainName))
            {
                cmd.CommandText += "AND ca.\"domain\" = @domainName ";
                cmd.Parameters.Add("@domainName", NpgsqlTypes.NpgsqlDbType.Varchar).Value = domainName;
            }

            if (filterTestDomain)
                cmd.CommandText += " AND ca.\"domain\" NOT LIKE '%ticimax%'";

            await _cnn.OpenAsync(cancellationToken);
            int count = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            

            return count;
        }

        public async Task<bool> ChangeActive(int id, bool status, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            NpgsqlCommand cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE customer_access SET status = @status WHERE id = @id";
            cmd.Parameters.Add("@id", NpgsqlTypes.NpgsqlDbType.Integer).Value = id;
            cmd.Parameters.Add("@status", NpgsqlTypes.NpgsqlDbType.Boolean).Value = status;

            await _cnn.OpenAsync(cancellationToken);
            var affectedRecordCount = await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            

            if (affectedRecordCount > 0)
                return true;

            return false;
        }

        public async Task<int> GetCustomerCount(bool filterTestDomain, CancellationToken cancellationToken)
        {
            List<CustomerCountsDto> records = new List<CustomerCountsDto>();

            var _cnn = new NpgsqlConnection(_connectionString);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"
                SELECT COUNT(1)
                FROM customer_access AS ca 
                WHERE 1 = 1";

            if (filterTestDomain)
                cmd.CommandText += " AND ca.\"domain\" NOT LIKE '%ticimax%'";

            await _cnn.OpenAsync(cancellationToken);
            int result = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            

            return result;
        }

        private void AppendFilter(ref NpgsqlCommand cmd, CustomerAccessFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND id = @id";
                    cmd.Parameters.Add("@id", NpgsqlTypes.NpgsqlDbType.Integer).Value = filter.ID;
                }

                if (!string.IsNullOrEmpty(filter.Domain))
                {
                    cmd.CommandText += " AND domain = @domain";
                    cmd.Parameters.Add("@domain", NpgsqlTypes.NpgsqlDbType.Varchar).Value = filter.Domain;
                }

                if (filter.Status.HasValue)
                {
                    cmd.CommandText += " AND status = @status";
                    cmd.Parameters.Add("@status", NpgsqlTypes.NpgsqlDbType.Boolean).Value = filter.Status.Value;
                }
            }
        }

    }
}