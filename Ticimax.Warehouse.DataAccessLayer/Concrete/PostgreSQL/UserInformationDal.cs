using Microsoft.Extensions.Options;
using Npgsql;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.DataAccessLayer.Abstract.PostgreSQL;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Pagings;

namespace Ticimax.Warehouse.DataAccessLayer.Concrete.PostgreSQL
{
    public class UserInformationDal : IUserInformationDal
    {
        private readonly string _connectionString;

        public UserInformationDal(IOptions<NpgsqlConnection> cnn)
        {
            _connectionString = cnn.Value.ConnectionString;
        }

        public async Task Add(UserInformation entity, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"INSERT INTO user_information(token
                                                            ,user_id
                                                            ,domain_name
                                                            ,login_date
                                                            ,modify_date)
                                                      VALUES(@token
                                                            ,@user_id
                                                            ,@domain_name
                                                            ,NOW()
                                                            ,NOW())";

            cmd.Parameters.Add("@token", NpgsqlTypes.NpgsqlDbType.Varchar).Value = entity.Token;
            cmd.Parameters.Add("@user_id", NpgsqlTypes.NpgsqlDbType.Integer).Value = entity.UserID;
            cmd.Parameters.Add("@domain_name", NpgsqlTypes.NpgsqlDbType.Varchar).Value = entity.DomainName;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
        }

        public async Task Delete(UserInformation entity, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "DELETE FROM user_information AS ui WHERE ui.id = @id OR ui.token = @token";
            cmd.Parameters.Add("@id", NpgsqlTypes.NpgsqlDbType.Integer).Value = entity.ID;
            cmd.Parameters.Add("@token", NpgsqlTypes.NpgsqlDbType.Varchar).Value = entity.Token;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
        }

        public async Task<int> GetCount(UserInformationFilter filter = null, CancellationToken cancellationToken = default)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT COUNT(ui.id) FROM user_information AS ui WHERE 1";
            AppendFilter(ref cmd, filter);
            var count = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return count;
        }

        public async Task<IList<UserInformation>> GetList(UserInformationFilter filter = null, UserInformationPaging paging = null, CancellationToken cancellationToken = default)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            List<UserInformation> userInformations = new List<UserInformation>();
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ui.id
                                      ,ui.user_id
                                      ,ui.token
                                      ,ui.domain_name
                                      ,ui.login_date
                                      ,ui.modify_date
                                      FROM user_information AS ui WHERE 1=1 ";

            AppendFilter(ref cmd, filter);

            NpgsqlDataReader ndr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await ndr.ReadAsync(cancellationToken))
            {
                userInformations.Add(new UserInformation
                {
                    ID = ndr["id"].ToInt32(),
                    DomainName = ndr["domain_name"].ToString(),
                    Token = ndr["token"].ToString(),
                    UserID = ndr["user_id"].ToInt32(),
                    LoginDate = ndr["login_date"].ToDateTime(),
                    ModifyDate = ndr["modify_date"].ToDateTime()
                });
            }

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
            return userInformations;
        }

        public async Task Update(UserInformation entity, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            await _cnn.OpenAsync(cancellationToken);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE user_information SET domain_name = @domain_name, token = @token, user_id = @user_id, modify_date = NOW() WHERE id = @id";
            cmd.Parameters.Add("@id", NpgsqlTypes.NpgsqlDbType.Integer).Value = entity.ID;
            cmd.Parameters.Add("@domain_name", NpgsqlTypes.NpgsqlDbType.Varchar).Value = entity.DomainName;
            cmd.Parameters.Add("@token", NpgsqlTypes.NpgsqlDbType.Varchar).Value = entity.Token;
            cmd.Parameters.Add("@user_id", NpgsqlTypes.NpgsqlDbType.Integer).Value = entity.UserID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            
        }

        public async Task<List<CustomerUserCountsDto>> GetCustomerUserCountsReport(string domainName, bool filterTestDomain, CancellationToken cancellationToken)
        {
            List<CustomerUserCountsDto> result = new List<CustomerUserCountsDto>();

            var _cnn = new NpgsqlConnection(_connectionString);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"
                SELECT  
	                ui.domain_name, 
	                COUNT(1) AS user_count 
                FROM user_information ui 
                WHERE 1 = 1";

            if (!string.IsNullOrEmpty(domainName))
            {
                cmd.CommandText += "AND ui.domain_name = @domainName ";
                cmd.Parameters.Add("@domainName", NpgsqlTypes.NpgsqlDbType.Varchar).Value = domainName;
            }

            if (filterTestDomain)
                cmd.CommandText += " AND ui.domain_name NOT LIKE '%ticimax%'";

            cmd.CommandText += @"
                GROUP BY ui.domain_name 
                ORDER BY ui.domain_name ";

            await _cnn.OpenAsync(cancellationToken);
            NpgsqlDataReader ndr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await ndr.ReadAsync(cancellationToken))
            {
                result.Add(new CustomerUserCountsDto
                {
                    DomainName = ndr["domain_name"].ToString(),
                    UserCount = ndr["user_count"].ToInt32()
                });
            }

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            

            return result;
        }

        public async Task<int> GetUserCount(bool filterTestDomain, CancellationToken cancellationToken)
        {
            var _cnn = new NpgsqlConnection(_connectionString);
            var cmd = new NpgsqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"
                SELECT COUNT(1) AS user_count 
                FROM user_information ui 
                WHERE 1 = 1";

            if (filterTestDomain)
                cmd.CommandText += " AND ui.domain_name NOT LIKE '%ticimax%'";

            await _cnn.OpenAsync(cancellationToken);
            var result = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();

            await cmd.DisposeAsync();
            await _cnn.CloseAsync();
            

            return result;
        }

        private void AppendFilter(ref NpgsqlCommand cmd, UserInformationFilter filter)
        {
            if (filter != null)
            {
                if (filter.IDs != null && filter.IDs.Count > 0)
                {
                    cmd.CommandText += $" AND ui.user_id IN ({string.Join(',', filter.IDs)})";
                }

                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND ui.id = @id";
                    cmd.Parameters.Add("@id", NpgsqlTypes.NpgsqlDbType.Integer).Value = filter.ID;
                }

                if (!string.IsNullOrEmpty(filter.DomainName))
                {
                    cmd.CommandText += " AND ui.domain_name = @domain_name";
                    cmd.Parameters.Add("@domain_name", NpgsqlTypes.NpgsqlDbType.Varchar).Value = filter.DomainName;
                }

                if (!string.IsNullOrEmpty(filter.Token))
                {
                    cmd.CommandText += " AND ui.token = @token";
                    cmd.Parameters.Add("@token", NpgsqlTypes.NpgsqlDbType.Varchar).Value = filter.Token;
                }

                if (filter.UserID > 0)
                {
                    cmd.CommandText += " AND ui.user_id = @user_id";
                    cmd.Parameters.Add("@user_id", NpgsqlTypes.NpgsqlDbType.Integer).Value = filter.UserID;
                }
            }
        }
    }
}