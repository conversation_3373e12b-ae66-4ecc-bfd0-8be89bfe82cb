using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement;

namespace Ticimax.Core.Product.Business.Abstract
{
    #nullable enable
    public interface IProductMovementService
    {
        Task<Nextable<ProductMovementAggregate>> GetByProductId(int productId, int? memberId, string? processType, long? startDate, long? endDate, int pageIndex, int pageSize, CancellationToken cancellationToken);

        Task CreateMovement(int productId, CreateProductMovementRequest request, CancellationToken cancellationToken);
    }
}