using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.Core.Product.Entities.Concrete;
using Ticimax.Core.Product.Entities.Dtos;

namespace Ticimax.Core.Product.Business.Abstract
{
    public interface IProductService
    {
        Task<DataResult<List<Entities.Concrete.Product>>> GetList(ProductGetListDto request, Paging paging = null, CancellationToken cancellationToken = default);

        Task<ErrorResponse> AddStock(ProductAddStockDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> OptimizeStock(ProductAddStockDto request, CancellationToken cancellationToken);

        Task<List<Entities.Concrete.Product>> GetListAsync(ProductGetListDto request, Paging paging = null, CancellationToken cancellationToken = default);

        Task<DataResult<int>> GetCount(ProductGetListDto request, CancellationToken cancellationToken);

        Task<ReduceStockResponseDto> ReduceStock(ProductUpdateStockDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> StoreStockGetCount(StoreStockFilter request, CancellationToken cancellationToken);

        Task<DataResult<List<StoreStock>>> StoreStockGetList(StoreStockFilter request, CancellationToken cancellationToken);
        
        Task ReduceConsignmentStock(int productId, double piece, bool consignmentModuleActive, CancellationToken cancellationToken);
        
        Task UpdateWebStockStock(int productId, double? webStock, CancellationToken cancellationToken);

    }
}