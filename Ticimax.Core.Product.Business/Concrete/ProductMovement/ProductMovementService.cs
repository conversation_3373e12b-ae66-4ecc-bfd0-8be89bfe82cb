using iText.Layout.Element;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Product.Business.Abstract;
using Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request;
using Ticimax.Core.Product.DataAccessLayer.Abstract;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Core.Product.Business.Concrete.ProductMovement
{
#nullable enable
    public class ProductMovementService : IProductMovementService
    {
        private readonly IProductMovementDal _productMovementDal;
        private readonly IShelfProductDetailService _shelfProductDetailService;

        public ProductMovementService(IProductMovementDal productMovementDal, IShelfProductDetailService shelfProductDetailService)
        {
            _productMovementDal = productMovementDal;
            _shelfProductDetailService = shelfProductDetailService;
        }

        public async Task<Nextable<ProductMovementAggregate>> GetByProductId(int productId, int? memberId, string? processType, long? startDate, long? endDate, int pageIndex, int pageSize, CancellationToken cancellationToken)
        {
            if (productId <= 0)
                throw new BusinessException("PRODUCT_ID_IS_NOT_EQUALS_ZERO");


            List<string> procesTypeList = new();
            if (!string.IsNullOrEmpty(processType))
            {
                procesTypeList = processType.Split(',').ToList();
                foreach (var process in procesTypeList)
                {
                    if (!ProductMovementProcessType.IsValid(process))
                        throw new BusinessException("PROCESS_TYPE_IS_NOT_VALID");
                }
            }



            var movements = await _productMovementDal.GetList(productId, memberId, procesTypeList, startDate, endDate, pageIndex, pageSize + 1, cancellationToken);

            var isNext = movements.Count > pageSize;
            return new Nextable<ProductMovementAggregate>(isNext, movements.Take(pageSize).ToList());
        }

        public async Task CreateMovement(int productId, CreateProductMovementRequest request, CancellationToken cancellationToken)
        {
            //Burayı microservise taşıyacağız.
            //var currentStock = await _shelfProductDetailService.GetShelfProductAmount(productId, true, true, cancellationToken);
            var aggregate = new ProductMovementAggregate(productId, WebSiteInfo.User.Value.ID, WebSiteInfo.User.Value.Name, request.ProcessType, request.Piece, request.Message, 0, request.Note);
            await _productMovementDal.Create(aggregate, cancellationToken);
        }
    }
}