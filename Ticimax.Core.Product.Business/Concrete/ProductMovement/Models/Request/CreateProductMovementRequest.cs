namespace Ticimax.Core.Product.Business.Concrete.ProductMovement.Models.Request
{
    public class CreateProductMovementRequest
    {
        public CreateProductMovementRequest()
        {
            
        }

        public CreateProductMovementRequest(string processType, double piece, string message, string? note)
        {
            ProcessType = processType;
            Piece = piece;
            Message = message;
            Note = note;
        }
        
        public string ProcessType { get; set; }

        public double Piece { get; set; }

        public string Message { get; set; }
        public string? Note { get; set; }
    }
}