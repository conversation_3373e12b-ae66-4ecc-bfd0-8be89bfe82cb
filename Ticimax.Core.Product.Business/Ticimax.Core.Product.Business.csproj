<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" Version="6.4.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Autofac.Extras.DynamicProxy" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Ticimax.Core.Business\Ticimax.Core.Business.csproj" />
    <ProjectReference Include="..\Ticimax.Core.Product.DataAccessLayer\Ticimax.Core.Product.DataAccessLayer.csproj" />
    <ProjectReference Include="..\Ticimax.Warehouse.Entities\Ticimax.Warehouse.Entities.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="obj\Debug\netstandard2.1" />
  </ItemGroup>

</Project>
