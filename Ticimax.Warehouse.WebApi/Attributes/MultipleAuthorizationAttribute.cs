using System.Linq;
using System.Threading;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.WebApi.Attributes
{
    public class MultipleAuthorizationAttribute : ActionFilterAttribute
    {
        public string[] AuthorityCodes { get; set; }

        public MultipleAuthorizationAttribute(string[] authorityCodes)
        {
            AuthorityCodes = authorityCodes;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            if (!WebSiteInfo.User.Value.isTicimaxUser)
            {
                var authorities = WebSiteInfo.User?.Value?.Authority.Where(x => AuthorityCodes.Any(y => y == x)).ToList();
                if (authorities == null || authorities.Count == 0)
                {
                    context.Result = new UnauthorizedObjectResult(new ErrorResponse { IsError = true, ErrorMessage = "IslemYetkinizYok" });
                    return;
                }

                var serviceProvider = context.HttpContext.RequestServices;
                var _warehouseAuthGroupUserService = serviceProvider.GetRequiredService<IWarehouseAuthGroupUserService>();
                var _warehouseAuthGroupDefinitionService = serviceProvider.GetRequiredService<IWarehouseAuthGroupDefinitionService>();
                var user = WebSiteInfo.User.Value;

                var warehouseAuthorityUser = _warehouseAuthGroupUserService.GetAuthGroupUser(new WarehouseAuthGroupUserFilter { UserID = user.ID }, CancellationToken.None).GetAwaiter().GetResult().Model;
                var warehouseAuthorityGroupDefinition = _warehouseAuthGroupDefinitionService.GetList(
                    new WarehouseAuthGroupDefinitionGetListDto
                    {
                        AuthGroupID = warehouseAuthorityUser.AuthGroupID
                    }, ApiVersion.SoftwareVersion.ToString(), CancellationToken.None).GetAwaiter().GetResult().Model;

                if (!warehouseAuthorityGroupDefinition.Any(x => AuthorityCodes.Any(y => y == x.AuthCode)))
                {
                    context.Result = new UnauthorizedObjectResult(new ErrorResponse { IsError = true, ErrorMessage = "IslemYetkinizYok" });
                    return;
                }
            }

            base.OnActionExecuting(context);
        }
    }
}