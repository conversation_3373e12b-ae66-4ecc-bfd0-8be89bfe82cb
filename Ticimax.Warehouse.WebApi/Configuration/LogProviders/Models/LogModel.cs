using System.Text.Json.Serialization;

namespace Ticimax.Warehouse.WebApi.Configuration.LogProviders.Models
{
    public class LogModel
    {
        public LogModel(string? severity, string? correlationId, string? domainName, string? agentName, string? app, string? service, string? env, string? host, string? message, string? date, string? timestamp, string? stackTrace)
        {
            Severity = severity;
            CorrelationId = correlationId;
            DomainName = domainName;
            AgentName = agentName;
            App = app;
            Service = service;
            Env = env;
            Host = host;
            Message = message;
            Date = date;
            Timestamp = timestamp;
            StackTrace = stackTrace;
        }

        public string? CorrelationId { get; set; }
        public string? DomainName { get; set; }

        public string? AgentName { get; set; }

        public string? Severity { get; set; }

        public string? App { get; set; }

        public string? Service { get; set; }

        public string? Env { get; set; }

        public string? Host { get; set; }

        public string? Message { get; set; }

        public string? StackTrace { get; set; }

        public string? Date { get; set; }

        [JsonPropertyName("@timestamp")]
        public string? Timestamp { get; set; }
    }
}
