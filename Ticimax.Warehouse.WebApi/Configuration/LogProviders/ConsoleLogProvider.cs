using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System;
using Ticimax.Warehouse.WebApi.Configuration.LogProviders.Models;
using CorrelationId;

namespace Ticimax.Warehouse.WebApi.Configuration.LogProviders
{
    public class ConsoleLogProvider : ILoggerProvider
    {
        private readonly IDisposable? _onChangeToken;
        private ConsoleLoggerConfiguration _currentConfig;
        private readonly ICorrelationContextAccessor _correlationContextAccessor;
        private readonly IHttpContextAccessor _httpContextAccessor;

        private readonly ConcurrentDictionary<string, ConsoleLogger> _loggers =
            new(StringComparer.OrdinalIgnoreCase);

        public ConsoleLogProvider(
            IOptionsMonitor<ConsoleLoggerConfiguration> config, ICorrelationContextAccessor correlationContextAccessor, IHttpContextAccessor httpContextAccessor)
        {
            _correlationContextAccessor = correlationContextAccessor;
            _httpContextAccessor = httpContextAccessor;
            _currentConfig = config.CurrentValue;
            _onChangeToken = config.OnChange(updatedConfig => _currentConfig = updatedConfig);
        }

        private ConsoleLoggerConfiguration GetCurrentConfig() => _currentConfig;

        public void Dispose()
        {
            _loggers.Clear();
            _onChangeToken?.Dispose();
        }

        public ILogger CreateLogger(string categoryName) =>
            _loggers.GetOrAdd(categoryName, name => new ConsoleLogger(name, GetCurrentConfig, _correlationContextAccessor, _httpContextAccessor));
    }
}