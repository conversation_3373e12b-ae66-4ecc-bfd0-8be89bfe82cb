using System;
using System.Collections.Generic;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Microsoft.IdentityModel.Tokens;
using MySqlConnector;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.CrossCuttingConcerns.Caching;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.IoC;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.CRMHelper;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Configuration;
using Ticimax.Warehouse.Entities.BusinessEntities;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Static;
using static iText.StyledXmlParser.Jsoup.Select.Evaluator;

namespace Ticimax.Warehouse.WebApi.Middlewares
{
    public class WebSiteInfoMiddleWare
    {
        private readonly RequestDelegate _next;
        private readonly ICacheManager _cacheManager;
        private readonly IDomainBasedLockService _domainBasedLockService;
        private readonly ILogger<WebSiteInfoMiddleWare> _logger;
        private readonly string _secretKey;

        public WebSiteInfoMiddleWare(RequestDelegate next, IDomainBasedLockService domainBasedLockService, IConfiguration configuration, ILogger<WebSiteInfoMiddleWare> logger)
        {
            _next = next;
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
            _domainBasedLockService = domainBasedLockService;
            _secretKey = configuration.GetValue<string>(ConfigKeys.SecretKey);
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IDbConnection cnn, ITransactionalCommandService transactionalCommandService, IStoreAgentService storeAgentService)
        {
            if (context.Request.Headers.ContainsKey("Origin"))
            {
                StringValues _value = new();
                context.Request.Headers.TryGetValue("Origin", out _value);
                context.Response.Headers.Append("Access-Control-Allow-Origin", new StringValues(IsAllowedRequestUrl(_value)));
            }

            context.Response.Headers.Append("Access-Control-Allow-Headers", new StringValues("Access-Control-Allow-Headers, Origin,Accept, X-Requested-With, Content-Type, Access-Control-Request-Method, Access-Control-Request-Headers,UserToken,DomainName"));
            context.Response.Headers.Append("Access-Control-Allow-Credentials", new StringValues("true"));
            context.Response.Headers.Append("Access-Control-Expose-Headers", new StringValues("CheckUser"));
            context.Response.Headers.Append("Access-Control-Allow-Methods", new StringValues("GET,PUT,POST,DELETE,PATCH"));
            context.Response.Headers.Append("Strict-Transport-Security", new StringValues("max-age=31536000; includeSubDomains; preload"));

            if (context.Request.Method == "OPTIONS" && context.Request.Headers.ContainsKey("Origin") && context.Request.Headers.ContainsKey("Access-Control-Request-Method"))
                return;

            context.Request.EnableBuffering();
            var requestPath = context.Request.Path;
            if (requestPath.StartsWithSegments("/api/Member/Login"))
            {
                var buffer = new byte[Convert.ToInt32(context.Request.ContentLength)];
                await context.Request.Body.ReadAsync(buffer, 0, buffer.Length);
                var requestContent = Encoding.UTF8.GetString(buffer);

                context.Request.Body.Position = 0;

                StoreAgentLoginDto response = requestContent.ToJsonDeserialize<StoreAgentLoginDto>();
                Info.DomainName.Value = response.DomainName;
            }
            var sessionID = context.Request.Headers["userToken"].ToString();
            Info.Version.Value = ApiVersion.SoftwareVersion;
            if (!string.IsNullOrWhiteSpace(sessionID) && sessionID != "null")
            {
                var token = await TokenValidate(sessionID);
                if (token == null)
                {
                    await context.Response.WriteAsJsonAsync(new ErrorResponse { IsError = true, ErrorMessage = "NotLogin" });
                    return;
                }

                WebSiteInfo.User.Value = await _cacheManager.Get<User>(sessionID);
                if (WebSiteInfo.User.Value == null)
                {
                    await context.Response.WriteAsJsonAsync(new ErrorResponse { IsError = true, ErrorMessage = "NotLogin" });
                    return;
                }
                Info.LoginCacheKey.Value = sessionID;

                if (token.Claims.FirstOrDefault(x => x.Type == "dName")?.Value.Trim() != WebSiteInfo.User.Value.DomainName.Trim())
                {
                    await context.Response.WriteAsJsonAsync(new ErrorResponse { IsError = true, ErrorMessage = "NotLogin" });
                    return;
                }

                Info.DomainName.Value = WebSiteInfo.User.Value.DomainName.Trim();
                WebSiteInfo.User.Value.IpAddress = GetClientIP(context);
                WebSiteInfo.User.Value.Url = context.Request.GetDisplayUrl();
                WebSiteInfo.User.Value.Version = ApiVersion.SoftwareVersion.ToString();
            }

            if (context.Request.Headers.Any(x => x.Key.ToLower() == "referer" && x.Value == "bulkHandler") ||
                context.Request.Headers.Any(x => x.Key.ToLower() == "referer" && x.Value == "inbound"))
            {
                var domainName = context.Request.Headers["DomainName"].ToString().Trim();
                WebSiteInfo.OutSourceJobId.Value = context.Request.Headers["jobId"].ToString().Trim();
                WebSiteInfo.OutSourceTransactionId.Value = context.Request.Headers["transactionId"].ToString().Trim();
                var authToken = context.Request.Headers.Authorization.ToString();
                if (authToken == AccessTokenExtension.Generate(domainName))
                {
                    Info.DomainName.Value = domainName;
                    var id = context.Request.Query.FirstOrDefault(x => x.Key == "creatorId").Value.FirstOrDefault().ToInt32();
                    if (id > 0)
                    {
                        cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
                        if (cnn.State != ConnectionState.Open)
                            await ((MySqlConnection)cnn).OpenAsync();
                        var user = await storeAgentService.FillWebSiteInfoUser(id, CancellationToken.None);
                        WebSiteInfo.User.Value = user;
                    }
                }
            }
            if (context.Request.Headers.Any(x => x.Key.ToLower() == "referer" && x.Value == "jobzone"))
            {
                var domainName = context.Request.Headers["DomainName"].ToString().Trim();
                var authToken = context.Request.Headers.Authorization.ToString();

                if (authToken == AccessTokenExtension.Generate(domainName))
                {
                    Info.DomainName.Value = domainName;
                    var id = context.Request.Headers["creatorId"].ToString();
                    int creatorId = id.ToInt32();
                    if (creatorId > 0)
                    {
                        cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
                        if (cnn.State != ConnectionState.Open)
                            await ((MySqlConnection)cnn).OpenAsync();
                        var user = await storeAgentService.FillWebSiteInfoUser(creatorId, CancellationToken.None);
                        WebSiteInfo.User.Value = user;
                    }
                }
            }


            context.Response.Headers.Append("UserToken", sessionID);
            context.Response.Headers.Append("DomainName", new StringValues(Info.DomainName.Value));
            context.Response.Headers.Append("Version", ApiVersion.SoftwareVersion.ToString());
            context.Response.Headers.Append("CheckUser", new StringValues(WebSiteInfo.User.Value != null ? WebSiteInfo.User.Value.CheckUser.ToString().ToLower() : "false"));

            bool pathCheck = requestPath.StartsWithSegments("/api") &&
                               !(requestPath.Value == "/api/Member/Login" || requestPath.Value == "/users/forgot-password" || requestPath.Value == "/api/Member/CaptchaReset" || requestPath.Value.Contains("/swagger") || requestPath.Value.Contains("api/Language/GetLanguage") || requestPath.Value.Contains("api/localizations"));

            if ((WebSiteInfo.User.Value == null || WebSiteInfo.User.Value.ID == 0) && pathCheck)
            {
                await context.Response.WriteAsJsonAsync(new ErrorResponse { IsError = true, ErrorMessage = "NotLogin" });
            }
            else
            {
                bool rateCheck = true;
                //RateLimit Check
                if (
                    isAllowedRateLimit(requestPath.Value) &&
                    WebSiteInfo.User.Value.RateLimit.LastRequestPath == requestPath.Value &&
                    DateTime.Now < WebSiteInfo.User.Value.RateLimit.RequestEndTime &&
                    WebSiteInfo.User.Value.RateLimit.RequestStartTime.HasValue &&
                    WebSiteInfo.User.Value.RateLimit.RequestEndTime.HasValue &&
                    WebSiteInfo.User.Value.RateLimit.RequestStartTime < WebSiteInfo.User.Value.RateLimit.RequestEndTime
                )
                {
                    rateCheck = false;
                    await context.Response.WriteAsJsonAsync(new ErrorResponse { IsError = true, ErrorMessage = "IstekSureSiniriHatasi" });
                }

                if (rateCheck)
                {
                    bool isError = false;
                    Stream originBody = null;
                    try
                    {
                        if (string.IsNullOrEmpty(Info.DomainName.Value) && pathCheck)
                            throw new Exception("Bağlantı hatası");

                        var path = requestPath.ToString();
                        if (WebSiteInfo.User.Value != null && WebSiteInfo.User.Value.StoreID == 0 && WebSiteInfo.User.Value.WarehouseID == 0 &&
                                 !path.Contains("UpdateStoreID") && !path.Contains("UpdateWarehouseID") &&
                                 !path.Contains("stores") && !path.Contains("warehouses") &&
                                 !path.Contains("CountryCityDistrict") && !path.Contains("setup-wizard") &&
                                 !path.Contains("StoreAgent"))
                            throw new ForbiddenAccessException("MagazaYadaDepoBilginizBulunamadi");

                        originBody = ReplaceBody(context.Response);

                        bool isPeriodicTask = requestPath.Value.ToLower().Contains("periodic-tasks/clear-empty-stock-internal")
                             || requestPath.Value.ToLower().Contains("report-invalidates/history-stocks-internal");
                        if (isPeriodicTask)
                            Info.DomainName.Value = context.Request.Headers["DomainName"].ToString().Trim();

                        bool isPeriodicTaskProductStockFix = requestPath.Value.ToLower().Contains("product-stocks-fix");
                        if (isPeriodicTaskProductStockFix && string.IsNullOrEmpty(Info.DomainName.Value))
                            Info.DomainName.Value = "denizbutik.com";

                        if (pathCheck ||
                            requestPath.Value == "/api/Member/Login" ||
                            requestPath.Value.ToLower().Contains("bulk-handlers") ||
                            requestPath.Value.ToLower().Contains("conversations") ||
                            isPeriodicTaskProductStockFix || isPeriodicTask)
                        {
                            if (cnn.State != ConnectionState.Open)
                            {
                                cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
                                await ((MySqlConnection)cnn).OpenAsync();
                            }
                        }

                        await _next(context);

                        await transactionalCommandService.TransactionalExecuteAsync();

                        if ((pathCheck || requestPath.Value == "/api/Member/Login") && cnn.State != ConnectionState.Closed)
                            await ((MySqlConnection)cnn).CloseAsync();

                        await ReturnBody(context, originBody, null);
                    }
                    catch (Exception ex)
                    {
                        isError = true;

                        var exType = ex.GetType();
                        if (exType == typeof(NotFoundException) || exType == typeof(BusinessException))
                        {
                            isError = false;
                            await transactionalCommandService.TransactionalExecuteAsync();
                        }

                        if (WebSiteInfo.User?.Value?.Commands?.Count > 0)
                        {
                            WebSiteInfo.User.Value.Commands.ForEach(x =>
                            {
                                if (x.Transaction != null)
                                {
                                    x.Transaction.Rollback();
                                    x.Transaction.Dispose();
                                }

                                x.Dispose();
                            });
                        }

                        if (!string.IsNullOrEmpty(WebSiteInfo.User?.Value?.DomainBasedLockerKey))
                        {
                            await _cacheManager.Remove(WebSiteInfo.User.Value.DomainBasedLockerKey);
                            await _domainBasedLockService.RedisUnlockByKeyAsync("-DistributionProductAsync");
                            await _domainBasedLockService.RedisUnlockByKeyAsync("-DistributionProductAsync-" + WebSiteInfo.User.Value.Username);
                        }

                        await HandleExceptionModelAsync(context, ex, originBody);
                    }
                    finally
                    {
                        if (cnn.State != ConnectionState.Closed)
                            await ((MySqlConnection)cnn).CloseAsync();

                        if (WebSiteInfo.User.Value != null && WebSiteInfo.User?.Value?.ID > 0 && WebSiteInfo.User?.Value != null && !isError && (!string.IsNullOrWhiteSpace(Info.LoginCacheKey.Value) || !string.IsNullOrEmpty(WebSiteInfo.User.Value.OutSourceUserCacheKey)))
                        {
                            WebSiteInfo.User.Value.Commands = new List<IDbCommand>();
                            WebSiteInfo.User.Value.Events = new List<DomainEvent>();
                            WebSiteInfo.User.Value.CheckUser = false;
                            WebSiteInfo.User.Value.RateLimit.LastRequestPath = requestPath.Value;
                            WebSiteInfo.User.Value.RateLimit.RequestStartTime = DateTime.Now;
                            WebSiteInfo.User.Value.RateLimit.RequestEndTime = DateTime.Now.AddSeconds(5);
                            if (!string.IsNullOrEmpty(Info.LoginCacheKey.Value))
                                await _cacheManager.Add(Info.LoginCacheKey.Value, WebSiteInfo.User.Value, 60 * 60 * 24);
                            else if (!string.IsNullOrEmpty(WebSiteInfo.User.Value.OutSourceUserCacheKey))
                                await _cacheManager.Add(WebSiteInfo.User.Value.OutSourceUserCacheKey, WebSiteInfo.User.Value, 60 * 60 * 24);
                        }

                    }
                }
            }
        }

        private Stream ReplaceBody(HttpResponse response)
        {
            var originBody = response.Body;
            response.Body = new MemoryStream();
            return originBody;
        }


        private async Task ReturnBody(HttpContext context, Stream originBody, object body)
        {
            if (context.Response.Body.CanRead)
            {
                using (var streamReader = new StreamReader(context.Response.Body))
                {
                    // Read the body
                    context.Response.Body.Seek(0, SeekOrigin.Begin);
                    var responseBody = await streamReader.ReadToEndAsync();

                    // Replace [[[Bananas]]] with translated texts - or Bananas if a translation is missing
                    responseBody = body != null ? body.ToJsonSerialize() : responseBody;

                    // Create a new stream with the modified body, and reset the content length to match the new stream
                    var requestContent = new StringContent(responseBody, Encoding.UTF8, "application/json");
                    context.Response.Body = await requestContent.ReadAsStreamAsync(); //modified stream
                    context.Response.ContentLength = context.Response.Body.Length;
                    if (!context.Request.Path.StartsWithSegments("/swagger"))
                        context.Response.ContentType = "application/json";
                    //context.Response.StatusCode = body != null ? 400 : 200;
                }

                context.Response.Body.Seek(0, SeekOrigin.Begin);
                await context.Response.Body.CopyToAsync(originBody);
                context.Response.Body = originBody;
            }
            else
            {
                var bytes = Encoding.UTF8.GetBytes(body.ToJsonSerialize());
                context.Response.ContentType = "application/json";
                //context.Response.StatusCode = 400;
                await context.Response.Body.WriteAsync(bytes, 0, bytes.Length);
            }
        }

        private bool isAllowedRateLimit(string path)
        {
            List<string> AllowedMember = new List<string>
            {
                "/Shelf/Add",
                "/QualityControl/OrderCompleted",
                "/PickingProduct/OrderSelect"
            };

            return AllowedMember.Contains(path.Replace("/api", ""));
        }

        private async Task<JwtSecurityToken> TokenValidate(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);
            var tokenResult = await tokenHandler.ValidateTokenAsync(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
            });

            return (JwtSecurityToken)tokenResult.SecurityToken;
        }

        private string IsAllowedRequestUrl(string url)
        {
            List<string> allowedMember = new List<string>
            {
                "https://wms.ticimax.cloud",
                "http://wms.ticimax.cloud",
                "https://localhost:3000",
                "http://localhost:3000",
                "https://wms-dev.ticimax.cloud",
                "http://wms-dev.ticimax.cloud",
                "http://wms.prod.ticimax.net",
                "https://wms.prod.ticimax.net"
            };

            return allowedMember.Contains(url) ? url : "";
        }

        private void HandleExceptionAsync(HttpContext context, Exception ex)
        {
            var exception = ex.ToString();
            if (exception.Contains("TaskCanceledException") || exception.Contains("Bağlantı hatası"))
                return;

            _logger.LogError(ex, ex.Message);

            if (string.IsNullOrEmpty(Info.DomainName.Value) || Info.DomainName.Value == null || Info.DomainName == null)
                Info.DomainName.Value = "WMS";

            string alanAdi = Info.DomainName.Value;

            User kullanici = null;
            if (WebSiteInfo.User.Value != null)
            {
                kullanici = WebSiteInfo.User.Value;
                alanAdi = WebSiteInfo.User.Value.DomainName;
            }
            if (IsCriticalException(ex))
            {
                ErrorLog log = new ErrorLog();
                log.CorrelationId = context.Response?.Headers["X-Correlationid"];
                log.AgentName = context.Request.Headers["User-Agent"];
                log.Severity = "Critical";
                log.DomainName = alanAdi;
                log.App = ApiVersion.SoftwareVersion + "-WMSAPI";
                log.Env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                log.Message = string.Join(" -> ", ex?.GetInnerExceptions().Select(x => x.Message));
                log.StackTrace = ex?.StackTrace;
                log.Timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd hh:mm:ss.fffffffK");
                log.Date = DateTime.UtcNow.ToString("yyyy-MM-dd hh:mm:ss.fffffffK");
                log.Host = Environment.MachineName;
                Console.WriteLine($"{System.Text.Json.JsonSerializer.Serialize(log, new JsonSerializerOptions() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase })}");
            }
        }

        public class ErrorLog
        {
            public string? CorrelationId { get; set; }

            public string? AgentName { get; set; }

            public string? Severity { get; set; }

            public string? App { get; set; }

            public string? Service { get; set; }

            public string? Env { get; set; }

            public string? Host { get; set; }

            public string? Message { get; set; }

            public string? StackTrace { get; set; }

            public string? Date { get; set; }

            [JsonPropertyName("@timestamp")]
            public string? Timestamp { get; set; }
            public string? DomainName { get; set; }

        }

        public bool IsCriticalException(Exception ex)
        {
            bool isCritical = false;

            if (ex.Message.Contains("is marked as crashed"))
                isCritical = true;
            else if (ex.Message.Contains("Unknown column"))
                isCritical = true;
            else if (ex.Message.Contains("doesn't exist"))
                isCritical = true;

            return isCritical;
        }

        protected virtual async Task HandleExceptionModelAsync(HttpContext httpContext, Exception ex, Stream originBody)
        {
            HttpStatusCode statusCode;
            var exType = ex.GetType();
            object content = null;
            switch (exType)
            {
                case { } when exType == typeof(NotFoundException):
                    statusCode = HttpStatusCode.NotFound;
                    var notFoundException = ex as NotFoundException;
                    if (!string.IsNullOrEmpty(notFoundException.Key))
                        content = new ExceptionResponse(notFoundException);

                    break;

                case { } when exType == typeof(UnauthorizedAccessException):
                    statusCode = HttpStatusCode.Unauthorized;
                    break;

                case { } when exType == typeof(OptimisticLockException) ||
                              exType == typeof(DbUpdateConcurrencyException) ||
                              exType == typeof(DBConcurrencyException):
                    statusCode = HttpStatusCode.Conflict;
                    _logger.LogWarning(new EventId(0, "OPTIMISTIC_LOCK_EXCEPTION"), ex, $"optimistic lock exception: {ex.Message}");

                    break;

                case { } when exType == typeof(BusinessException) || exType.BaseType == typeof(BusinessException):
                    statusCode = HttpStatusCode.BadRequest;
                    var errorResponse = new ExceptionResponse(ex as BusinessException);
                    content = errorResponse;

                    _logger.LogWarning(ex, $"[Bad Request] {JsonConvert.SerializeObject(errorResponse)}, ex: {ex.Message}, inner ex: {ex.InnerException?.Message}");

                    break;

                case { } when exType == typeof(ForbiddenAccessException):
                    statusCode = HttpStatusCode.Forbidden;
                    content = new ExceptionResponse(ex as ForbiddenAccessException);
                    break;

                default:
                    statusCode = HttpStatusCode.InternalServerError;
                    content = new { Key = "BeklenmeyenBirHataOlustu" };
                    _logger.LogCritical(new EventId(0, "UNKNOWN_EXCEPTION"), ex, "unknown exception: " + ex.Message);
                    HandleExceptionAsync(httpContext, ex);
                    break;
            }

            httpContext.Response.StatusCode = statusCode.GetHashCode();

            if (content != null)
                await ReturnBody(httpContext, originBody, content);

            await httpContext.Response.CompleteAsync();
        }

        public string GetClientIP(HttpContext context)
        {
            //bool isCitrix = ConfigurationManager.AppSettings["Citrix"] != null ? Convert.ToBoolean(ConfigurationManager.AppSettings["Citrix"]) : false;
            string returnIp = context.Connection.RemoteIpAddress.ToString();
            var headers = context.Request.Headers;
            //checkout gateweden gelen istekler için eklenmiştir.
            if (!string.IsNullOrEmpty(headers["ip_address"]))
            {
                returnIp = headers["ip_address"];
            }
            else if (!string.IsNullOrEmpty(headers["X-Forwarded-For"]))
            {
                returnIp = headers["X-Forwarded-For"];
            }
            else if (!string.IsNullOrEmpty(headers["True-Client-IP"]))
            {
                returnIp = headers["True-Client-IP"];
            }
            else if (!string.IsNullOrEmpty(headers["CF-Connecting-IP"]))
            {
                returnIp = headers["CF-Connecting-IP"];
            }

            if (returnIp != null && returnIp.Contains(","))
                returnIp = returnIp.Split(',').First().Trim();
            returnIp = ConvertToIPv6(returnIp);
            return returnIp;
        }

        private string ConvertToIPv6(string returnIp)
        {
            if (returnIp == "127.0.0.1")
                returnIp = "::1";
            return returnIp;
        }
    }
}