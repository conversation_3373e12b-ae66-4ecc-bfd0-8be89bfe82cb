using System;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Ticimax.Core.CrossCuttingConcerns.Caching;
using Ticimax.Core.Entities;
using Ticimax.CRMHelper;
using Ticimax.Warehouse.Business.Abstract.PublicServices;
using Ticimax.Warehouse.Entities.Dtos.PublicDtos.AppSettingsDto;

namespace Ticimax.Warehouse.WebApi.Middlewares.PublicMiddleware
{
    public class JwtMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IAppSettingsService _appSettingsService;
        private readonly ICacheManager _cache;
        private readonly ILoadBalancerService _loadBalancerService;

        public JwtMiddleware(RequestDelegate next, IAppSettingsService appSettingsService, ICacheManager cache, ILoadBalancerService loadBalancerService)
        {
            _next = next;
            _appSettingsService = appSettingsService;
            _cache = cache;        
            _loadBalancerService = loadBalancerService;
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                if (!context.Request.Path.Value.StartsWith("/it"))
                {
                    var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();

                    if (string.IsNullOrEmpty(token))
                    {
                        context.Response.Clear();
                        context.Response.ContentType = "application/json";
                        context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                        await context.Response.WriteAsync(JsonConvert.SerializeObject(new ErrorResponse { IsError = true, ErrorMessage = "YetkisizErisim" }));
                        return;
                    }

                    string secretKey = _cache.Get<GetAppSettingsDto>(token).GetAwaiter().GetResult().SecretKey;
                    if (string.IsNullOrEmpty(secretKey) || !await AttachClientToContextAsync(token, secretKey))
                    {
                        context.Response.Clear();
                        context.Response.ContentType = "application/json";
                        context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                        await context.Response.WriteAsync(JsonConvert.SerializeObject(new ErrorResponse { IsError = true, ErrorMessage = "YetkisizErisim" }));
                        return;
                    }

                    await _next(context);
                }
                else
                {
                    var response = await _loadBalancerService.CheckApplicationHealth();
                    if (response.IsError)
                    {
                        context.Response.Clear();
                        context.Response.StatusCode = (int)Enum.Parse(typeof(HttpStatusCode), response.Model.ToInt32().ToString());
                        return;
                    }

                    await _next(context);
                }
            }
            catch (Exception ex)
            {
                //HandleException(context, ex);
                
                await context.Response.WriteAsJsonAsync(new ErrorResponse { IsError = true, ErrorMessage = "BeklenmeyenBirHataOlustu" });
            }
        }

        private async Task<bool> AttachClientToContextAsync(string token, string secretKey)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(secretKey);
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
            }, out SecurityToken validatedToken);

            var jwt = (JwtSecurityToken)validatedToken;
            var appSettings = await _appSettingsService.GetAsync(
                new GetAppSettingsDto
                {
                    SecretKey = jwt.Claims.FirstOrDefault(x => x.Type == "SecretKey").Value,
                    AccessKey = jwt.Claims.FirstOrDefault(x => x.Type == "AccessKey").Value,
                    TargetID = jwt.Claims.FirstOrDefault(x => x.Type == "Target").Value.ToInt32()
                });

            return appSettings != null;
        }

        private void HandleException(HttpContext context, Exception ex)
        {
            string alanAdi = "wms500.ticimax.dev";
            bool KritikHata = false;

            if (IsCriticalException(ex))
            {
                KritikHata = true;
            }

            ErrorLog log = new ErrorLog();
            log.AlanAdi = alanAdi;
            log.SayfaAdresi = context.Request.Scheme + "://" + context.Request.Host + context.Request.Path;
            log.Tarih = DateTime.Now;
            log.IpAdresi = context.Connection.RemoteIpAddress.ToString();
            log.ExMesaj = string.Join(" -> ", ex.GetInnerExceptions().Select(x => x.Message));
            log.ExDetay = ex.StackTrace;
            log.UserAgent = context.Request.Headers["User-Agent"];
            log.Referer = context.Request.Headers["Referer"];
            log.KritikHata = KritikHata;
            log.SoftwareVersion = ApiVersion.SoftwareVersion + "-WMSAPI";

            Thread sender = new Thread(delegate() { Insert(log); });
            sender.IsBackground = true;
            sender.Start();
        }

        public class ErrorLog
        {
            public string AlanAdi { get; set; }

            public string SayfaAdresi { get; set; }

            public DateTime Tarih { get; set; }

            public int UyeId { get; set; }

            public string IpAdresi { get; set; }

            public string ExMesaj { get; set; }

            public string ExDetay { get; set; }

            public string UserAgent { get; set; }

            public string Referer { get; set; }

            public bool KritikHata { get; set; }

            public string SoftwareVersion { get; set; }
        }

        public static void Insert(ErrorLog Obje)
        {
            var request = (HttpWebRequest)WebRequest.Create("https://appservices.ticimax.net/api/Log/InsertErrorLog");
            //var request = (HttpWebRequest)WebRequest.Create("http://localhost:1869/api/Log/InsertErrorLog");
            var objeToSerialize = JsonConvert.SerializeObject(Obje);
            var bytes = Encoding.UTF8.GetBytes(objeToSerialize);
            request.Method = "POST";
            request.ContentType = "text/json";
            request.ContentLength = bytes.Length;
            using (var streamWriter = new StreamWriter(request.GetRequestStream()))
            {
                streamWriter.Write(objeToSerialize);
                streamWriter.Flush();
            }

            var httpResponse = (HttpWebResponse)request.GetResponse();
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                streamReader.ReadToEnd();
            }
        }

        public bool IsCriticalException(Exception ex)
        {
            bool isCritical = false;

            if (ex.Message.Contains("is marked as crashed"))
            {
                isCritical = true;
            }
            else if (ex.Message.Contains("Unknown column"))
            {
                isCritical = true;
            }
            else if (ex.Message.Contains("doesn't exist"))
            {
                isCritical = true;
            }

            return isCritical;
        }
    }
}