using System;
using System.Configuration;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using CorrelationId;
using Elastic.Apm;
using Elastic.Apm.AspNetCore;
using Elastic.Apm.DiagnosticSource;
using Elastic.Apm.EntityFrameworkCore;
using Elastic.Apm.NetCoreAll;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Serialization;
using Ticimax.Core.DependencyResolvers;
using Ticimax.Core.Entities.Concrete;
using Ticimax.Core.Extensions;
using Ticimax.Core.Utilities.IoC;
using Ticimax.Warehouse.WebApi.Configuration;
using Ticimax.Warehouse.WebApi.Extensions;
using Ticimax.Warehouse.WebApi.Middlewares;
using Ticimax.Warehouse.WebApi.Middlewares.PublicMiddleware;

namespace Ticimax.Warehouse.WebApi
{
    public class Startup
    {
        public IConfiguration Configuration { get; }

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        // For more information on how to configure your application, visit https://go.microsoft.com/fwlink/?LinkID=398940
        public void ConfigureServices(IServiceCollection services)
        {
            services.Configure<FirebaseConfiguration>(Configuration.GetSection("FirebaseConfiguration"));
            services.AddMvc();

            services.AddCors(o => o.AddPolicy("MyPolicy", builder => { builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader(); }));

            services.AddControllers()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.DateFormatString = "HH:mm dd.MM.yyyy";
                    options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
                })
                .AddJsonOptions(options => { options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase; }).AddFluentValidation(x =>
                {
                    x.RegisterValidatorsFromAssembly(typeof(Program).Assembly);
                    x.ImplicitlyValidateChildProperties = true;
                });

            var redisPassword = Configuration.GetValue<string>(ConfigKeys.RedisPassword);
            var redisPort = Configuration.GetValue<string>(ConfigKeys.RedisPort);
            var redisHost = Configuration.GetValue<string>(ConfigKeys.RedisEndpoints).Split(',').ToList().FirstOrDefault();
            services.AddSignalR().
             AddStackExchangeRedis($"password={redisPassword},abortConnect=false,ssl=false,{redisHost}:{redisPort}",
             options => 
            {
                options.Configuration.ChannelPrefix = "WarehouseChat";
            });
            services.AddTransient<IValidatorInterceptor, ValidatorInterceptor>();
            services.AddCache(Configuration);
            services.AddDatabase(Configuration);
            services.AddCorrelationId();
            services.AddSwagger(Configuration);
            services.AddHttpContextAccessor();
            services.AddHealthCheck(Configuration);
            services.AddDependencyResolvers(new ICoreModule[]
            {
                new CoreModule(),
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseCorrelationId(new CorrelationIdOptions
            {
                Header = "x-correlationid",
                UseGuidForCorrelationId = true,
                UpdateTraceIdentifier = true
            });

            var cultureInfo = new CultureInfo("en-US");
            CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
            CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

            app.UseMiddleware<RequestResponseLoggingMiddleware>().UseMiddleware<WebSiteInfoMiddleWare>();

            app.UseRouting();
            app.UseStaticFiles();
            app.UseElasticApm(Configuration, new HttpDiagnosticsSubscriber(), new EfCoreDiagnosticsSubscriber());
            Agent.AddFilter(transaction =>
            {
                var path = transaction.Context?.Request?.Url?.PathName?.ToLower();

                //Loglanmak istenmeyen e.p'ler buraya eklenebilir.
                if (path != null && path.StartsWith("/warehousechathub"))
                {
                    return null; 
                }

                return transaction;
            });

            app.UseWhen(x =>
                    x.Request.Path.StartsWithSegments("/it", StringComparison.OrdinalIgnoreCase),
                builder =>
                {
                    builder.UseMiddleware<JwtMiddleware>();
                    builder.UseAuthentication();
                });

            // app.UseWhen(x =>
            //         x.Request.Path.StartsWithSegments("/crm", StringComparison.OrdinalIgnoreCase),
            //     builder =>
            //     {
            //         builder.UseAuthentication();
            //     });


            app.UseRouting();
            app.UseCors("MyPolicy");
            app.UseEndpoints(endpoints => { endpoints.MapControllerRoute("default_route", "{controller}/{action}/{id?}").RequireCors("MyPolicy"); });
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapHub<Ticimax.Warehouse.WebApi.WarehousesChatHub.WarehousesChatHub>("/warehouseChatHub");
            });
            app.UseSwagger();
            app.UseSwaggerUI(c => { c.SwaggerEndpoint("/swagger/v1/swagger.json", "TicimaxWarehouseAPI V1"); });

            app.UseHealthCheck();
        }
    }
}