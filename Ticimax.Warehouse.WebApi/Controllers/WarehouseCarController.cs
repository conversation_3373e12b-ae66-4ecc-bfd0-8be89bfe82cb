using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class WarehouseCarController : ControllerBase
    {
        private readonly IWarehouseCarService _warehouseCarService;

        public WarehouseCarController(IWarehouseCarService warehouseCarService)
        {
            _warehouseCarService = warehouseCarService;
        }

        [HttpGet]
        [Authorization("ProcessManagement.CarAuthority")]
        public async Task<IActionResult> GetList([FromQuery] WarehouseCarGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.GetList(request, paging, cancellationToken));
        }

        [HttpGet]
        [Authorization("ProcessManagement.CarAuthority")]
        public async Task<IActionResult> GetCount([FromQuery] WarehouseCarGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.GetCount(request, cancellationToken));
        }

        [HttpGet]
        [Authorization("WarehouseManagement.CarManager.ShowPage")]
        public async Task<IActionResult> CarManagement([FromQuery] WarehouseCarGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.GetList(request, paging, cancellationToken));
        }

        [HttpGet]
        [Authorization("WarehouseManagement.CarManager.ShowPage")]
        public async Task<IActionResult> CarManagementCount([FromQuery] WarehouseCarGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.GetCount(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.CarManager.EditCar")]
        public async Task<IActionResult> Update([FromBody] WarehouseCarEditDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.Update(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.CarManager.AddCar")]
        public async Task<IActionResult> Add([FromBody] WarehouseCarAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.Add(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.CarManager.DeleteCar")]
        public async Task<IActionResult> Delete([FromBody] WarehouseCarDeleteDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.Delete(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("ProcessManagement.CarAuthority")]
        public async Task<IActionResult> AssignCar([FromBody] WarehouseCarAssingnCarDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.AssignCar(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("ProcessManagement.CarAuthority")]
        public async Task<IActionResult> LeaveCar([FromBody] WarehouseCarLeaveCarDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.LeaveCar(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.CarManager.CarCleaner")]
        public async Task<IActionResult> CarCleaner([FromBody] WarehouseCarCleanerDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.CarCleaner(request, cancellationToken));
        }


        [HttpPut("{id}/user/{userId}")]
        [Authorization("OrderManagement.SetManager.CarSetMatching")]
        public async Task<IActionResult> CarUserSetNoSync([FromRoute] int id, [FromRoute] int userId, [FromBody] CarUserSetNoSyncDto request, CancellationToken cancellationToken)
        {
            await _warehouseCarService.CarUserSetNoSync(id, userId, request, cancellationToken);
            return NoContent();
        }
    }
}