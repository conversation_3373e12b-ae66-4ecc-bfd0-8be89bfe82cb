using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class OrderProductReturnCauseController : ControllerBase
    {
        private readonly IOrderProductReturnCauseService _orderProductReturnCauseService;

        public OrderProductReturnCauseController(IOrderProductReturnCauseService orderProductReturnCauseService)
        {
            _orderProductReturnCauseService = orderProductReturnCauseService;
        }

        [HttpGet]
        public async Task<IActionResult> GetList([FromQuery] OrderProductReturnCauseGetListDto request, [FromQuery] Paging paging, CancellationToken cancellationToken)
        {
            return Ok(await _orderProductReturnCauseService.GetList(request, paging, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetCount([FromQuery] OrderProductReturnCauseGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _orderProductReturnCauseService.GetCount(request, cancellationToken));
        }
    }
}