using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.EInvoiceError.Request;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.EInvoiceError.Response;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.OrderStatusJob.Request;
using Ticimax.Warehouse.Business.Concrete.TicimaxInvalidator.OrderStatusJob.Response;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorization("ReportManagement.NotBillPrintedOrders")]
    public class InvalidatorController : ControllerBase
    {
        private readonly ITicimaxInvalidatorService _ticimaxInvalidatorService;

        public InvalidatorController(ITicimaxInvalidatorService ticimaxInvalidatorService)
        {
            _ticimaxInvalidatorService = ticimaxInvalidatorService;
        }

        [HttpGet("invoice-errors")]
        [ProducesResponseType(typeof(Pageable<EInvoiceErrorResponse>), 200)]
        public async Task<IActionResult> EInvoiceError([FromQuery] EInvoiceErrorRequest request, CancellationToken cancellationToken = default)
        {
            var result = await _ticimaxInvalidatorService.GetEInvoiceError(request, cancellationToken);
            return Ok(result);
        }

        [HttpGet("order-status")]
        [ProducesResponseType(typeof(Pageable<OrderStatusJobResponse>), 200)]
        public async Task<IActionResult> OrderStatus([FromQuery] OrderStatusJobRequest request, CancellationToken cancellationToken = default)
        {
            var result = await _ticimaxInvalidatorService.GetOrderStatus(request, cancellationToken);
            return Ok(result);
        }

        [HttpPost("orders/{orderId}/re-create-einvoice")]
        public async Task<IActionResult> ReCreateEInvoice([FromRoute] int orderId, CancellationToken cancellationToken = default)
        {
            await _ticimaxInvalidatorService.ReCreateEInvoice(orderId, cancellationToken);
            return Ok();
        }

        [HttpPost("orders/re-create-einvoice/bulk")]
        public async Task<IActionResult> ReCreateEInvoiceBulk([FromBody] ReCreateEInvoiceBulkRequest request, CancellationToken cancellationToken = default)
        {            
            await _ticimaxInvalidatorService.ReCreateEInvoiceBulk(request, cancellationToken);
            return Ok();
        }
    }
}