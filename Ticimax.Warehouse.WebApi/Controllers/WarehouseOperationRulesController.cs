using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.ShelfDtos;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/warehouse-operation-rules")]
    [ApiController]
    public class WarehouseOperationRulesController : ControllerBase
    {
        private readonly IWarehouseOperationRulesService _warehouseOperationRulesService;

        public WarehouseOperationRulesController(IWarehouseOperationRulesService warehouseOperationRulesService)
        {
            _warehouseOperationRulesService = warehouseOperationRulesService;
        }
        [MultipleAuthorization(new[] { "WarehouseManagement.OperationRules.ShowPage", "RefundManager.ShowPage" })]
        [HttpGet]
        public async Task<IActionResult> GetList([FromQuery] WarehouseOperationRulesGetListDto request, [FromQuery] WarehouseOperationRulesPaging paging, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseOperationRulesService.GetList(request, paging, cancellationToken));
        }

        [HttpPut("{id}")]
        [Authorization("WarehouseManagement.OperationRules.EditRule")]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] WarehouseOperationRulesUpdateDto request, CancellationToken cancellationToken)
        {
            await _warehouseOperationRulesService.Update(id, request, cancellationToken);
            return Ok();
        }
        [HttpPost]
        [Authorization("WarehouseManagement.OperationRules.AddRule")]
        public async Task<IActionResult> Add([FromBody] WarehouseOperationRulesAddDto request, CancellationToken cancellationToken)
        {
            await _warehouseOperationRulesService.Add(request, cancellationToken);
            return Ok();
        }
        [HttpDelete("{id}")]
        [Authorization("WarehouseManagement.OperationRules.DeleteRule")]
        public async Task<IActionResult> Delete([FromRoute] int id, CancellationToken cancellationToken)
        {
            await _warehouseOperationRulesService.Delete(id, cancellationToken);
            return Ok();
        }

        [Authorization("WarehouseManagement.OperationRules.ShowPage")]
        [HttpGet("cancel-return-reasons")]
        public async Task<IActionResult> GetCancelReturnReasonsList([FromQuery] CancelReturnReasonGetListDto request, [FromQuery] CancelReturnReasonPaging paging, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseOperationRulesService.GetCancelReturnReason(request, paging, cancellationToken));
        }
    }
}