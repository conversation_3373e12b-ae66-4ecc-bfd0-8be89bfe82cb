using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class StoreAgentController : ControllerBase
    {
        private readonly IStoreAgentService _storeAgentService;

        public StoreAgentController(IStoreAgentService storeAgentService)
        {
            _storeAgentService = storeAgentService;
        }

        [HttpGet]
        [MultipleAuthorization(new[] { "WarehouseManagement.RepresenterManager.ShowPage", "Settings.MyAccount.ShowPage" })]
        public async Task<IActionResult> GetList([FromQuery] StoreAgentGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.GetList(request, paging));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.RepresenterManager.AddRepresenter")]
        public async Task<IActionResult> Add([FromBody] StoreAgentAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.Add(request, cancellationToken));
        }

        [HttpPost]
        [MultipleAuthorization(new[] { "WarehouseManagement.RepresenterManager.EditRepresenter", "Settings.MyAccount.ChangeUsername", "Settings.MyAccount.ChangePassword" })]
        public async Task<IActionResult> Update([FromBody] StoreAgentUpdateDto request, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.Update(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.RepresenterManager.DeleteRepresenter")]
        public async Task<IActionResult> Delete([FromBody] StoreAgentDeleteDto request, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.Delete(request, cancellationToken));
        }

        [HttpPost]
        [MultipleAuthorization(new[] { "WarehouseManagement.RepresenterManager.EditRepresenter", "Settings.MyAccount.ChangeUsername", "Settings.MyAccount.ChangePassword" })]
        public async Task<IActionResult> UpdateMyUser([FromBody] StoreAgentUpdateMyUserDto request, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.UpdateMyUser(request, cancellationToken));
        }

        [HttpGet]
        [Authorization("WarehouseManagement.RepresenterManager.ShowPage")]
        public async Task<IActionResult> GetCount([FromQuery] StoreAgentGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.GetCount(request, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetSimpleAgentList(CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.GetSimpleAgentList(cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetAvailableAgentList([FromQuery] GetAvailableAgentListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.GetAvailableAgentList(request, cancellationToken));
        }

        [HttpGet]
        [Authorization("OrderManagement.QualityControl.ShowPage")]
        public async Task<IActionResult> GetAgentTable(CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.GetTable(cancellationToken));
        }

        [HttpPost]
        [Authorization("ProcessManagement.TableAuthority")]
        public async Task<IActionResult> LeaveTable(CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.LeaveTable(cancellationToken));
        }
        
        [HttpGet]
        [Authorization("Settings.MyAccount.WarehouseChanging")]
        public async Task<IActionResult> UpdateWarehouseID([FromQuery] StoreAgentUpdateWarehouseIDDto request, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.UpdateWarehouseID(request, cancellationToken));
        }

        [HttpGet]
        [Authorization("Settings.MyAccount.WarehouseChanging")]
        public async Task<IActionResult> UpdateStoreID([FromQuery] StoreAgentUpdateStoreIDDto request, CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.UpdateStoreID(request, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetAllInformationsOfUser(CancellationToken cancellationToken)
        {
            var result = await _storeAgentService.AllNewInformationsOfUser(ApiVersion.SoftwareVersion.ToString(), cancellationToken);
            return Ok(result);
        }

        [HttpGet]
        [Authorization("Settings.MyAccount.ShowPage")]
        public async Task<IActionResult> MyAccount(CancellationToken cancellationToken)
        {
            return Ok(await _storeAgentService.GetMyAccountInfo(cancellationToken));
        }
    }
}