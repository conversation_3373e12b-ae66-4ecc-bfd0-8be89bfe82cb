using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Models.Requests.LatestFeature;
using Ticimax.Warehouse.Entities.Models.Responses.LatestFeature;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/latest-features")]
    [ApiController]
    public class LatestFeatureController : ControllerBase
    {
        private readonly ILatestFeatureService _latestFeatureService;

        public LatestFeatureController(ILatestFeatureService latestFeatureService)
        {
            _latestFeatureService = latestFeatureService;
        }

        [HttpGet]
        [ProducesResponseType(typeof(Pageable<LatestFeatureModel>), 200)]
        public async Task<IActionResult> Get([FromQuery] FilterLatestFeatureRequest request, CancellationToken cancellationToken)
        {
            var result = await _latestFeatureService.GetLatestFeatures(request, cancellationToken);
            return Ok(result);
        }
    }
}
