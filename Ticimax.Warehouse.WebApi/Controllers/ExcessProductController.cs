using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorization("ProductManagement.ExcessProductManager.ShowPage")]
    public class ExcessProductController : ControllerBase
    {
        private readonly IExcessProductService _excessProductService;

        public ExcessProductController(IExcessProductService excessProductService)
        {
            _excessProductService = excessProductService;
        }

        [HttpGet]
        public async Task<IActionResult> GetList([FromQuery] ExcessProductGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _excessProductService.GetList(request, paging, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetListDto([FromQuery] ExcessProductGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _excessProductService.GetListItemDto(request, paging, cancellationToken));
        }

        [HttpPost]
        public async Task<IActionResult> Update([FromBody] ExcessProductUpdateDto request, CancellationToken cancellationToken)
        {
            return Ok(await _excessProductService.Update(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("ProductManagement.ExcessProductManager.AddExcessProduct")]
        public async Task<IActionResult> Add([FromBody] ExcessProductAddListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _excessProductService.Add(request, cancellationToken));
        }

        [HttpPost]
        public async Task<IActionResult> Delete([FromBody] ExcessProductDeleteDto request, CancellationToken cancellationToken)
        {
            return Ok(await _excessProductService.Delete(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("ProductManagement.ExcessProductManager.AddExitProduct")]
        public async Task<IActionResult> ExitProduct([FromBody] ExcessProductExitDto request, CancellationToken cancellationToken)
        {
            return Ok(await _excessProductService.ExitProduct(request, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetCount([FromQuery] ExcessProductGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _excessProductService.GetCount(request, cancellationToken));
        }
    }
}