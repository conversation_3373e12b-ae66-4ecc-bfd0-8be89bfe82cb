using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.ShelfProductDtos;
using Ticimax.Warehouse.Entities.Pagings;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorization("ProductManagement.ShelfProductAdd.ShowPage")]
    public class ShelfProductController : ControllerBase
    {
        private readonly IShelfProductService _shelfProductService;

        public ShelfProductController(IShelfProductService shelfProductService)
        {
            _shelfProductService = shelfProductService;
        }

        [HttpGet("GetList")]
        public async Task<IActionResult> GetList([FromQuery] ShelfProductGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _shelfProductService.GetList(request, paging, cancellationToken));
        }

        [HttpPost("Update")]
        [Authorization("ProductManagement.ShelfProductUpdate.ShowPage")]
        public async Task<IActionResult> Update([FromBody] ShelfProductUpdateDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfProductService.Update(request, cancellationToken));
        }

        [HttpPost("UpdateStock")]
        [Authorization("ProductManagement.ShelfProductUpdate.ShowPage")]
        public async Task<IActionResult> UpdateStock([FromBody] ShelfProductUpdateStockDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfProductService.UpdateStock(request, cancellationToken));
        }

        [HttpPost("Add")]
        [MultipleAuthorization(new[] { "ProductManagement.ShelfProductAdd.ShowPage", "ProductManagement.ShelfProductAdd.ShelfContentDrawer", "ProductManagement.ShelfProductAdd.BoxAdd" })]
        public async Task<IActionResult> Add([FromBody] ShelfProductAddDto request, CancellationToken cancellationToken)
        {            
            await _shelfProductService.Add(request, cancellationToken);
            return NoContent();
        }

        [HttpPost("Delete")]
        [Authorization("ProductManagement.ShelfProductReduce.ShowPage")]
        public async Task<IActionResult> Delete([FromBody] ShelfProductDeleteDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfProductService.Delete(request, cancellationToken));
        }

        [HttpGet("GetCount")]
        public async Task<IActionResult> GetCount([FromQuery] ShelfProductGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfProductService.GetCount(request, cancellationToken));
        }

        [HttpPost("ShelfProductReduce")]
        [Authorization("ProductManagement.ShelfProductReduce.ShowPage")]
        public async Task<IActionResult> Reduce([FromBody] ShelfProductReduceDto request, CancellationToken cancellationToken)
        {
            await _shelfProductService.ShelfProductReduce(request, cancellationToken);
            return NoContent();
        }

        [HttpPost("ShelfProductReduceList")]
        [Authorization("ProductManagement.ShelfProductReduce.ShowPage")]
        public async Task<IActionResult> ReduceList([FromBody] ShelfProductReduceListDto request, CancellationToken cancellationToken)
        {
            await _shelfProductService.ShelfProductReduceList(request, cancellationToken);
            return NoContent();
        }

        [HttpPost("DeleteByShelfID")]
        [Authorization("ProductManagement.ShelfProductReduce.ShowPage")]
        public async Task<IActionResult> DeleteByShelfID([FromBody] ShelfProductDeleteByShelfIDDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfProductService.DeleteByShelfID(request, cancellationToken));
        }

        [HttpPost("ProductShelfChange")]
        [Authorization("ProductManagement.ShelfProductReduce.ShowPage")]
        public async Task<IActionResult> ProductShelfChange([FromBody] ProductShelfChangeDto request, CancellationToken cancellationToken)
        {
            await _shelfProductService.ProductShelfChange(request, cancellationToken);
            return NoContent();
        }

        [HttpGet("stock")]
        [ProducesResponseType(typeof(ShelfStockResponse), 200)]
        public async Task<IActionResult> GetStockCount([FromQuery] ShelfProductGetListDto request, CancellationToken cancellationToken)
        {
            var result = await _shelfProductService.GetStockCount(request, cancellationToken);
            return Ok(result);
        }

        [HttpGet("critical-stock-products")]
        public async Task<IActionResult> GetCriticalStock([FromQuery] CriticalStockShelfProductPaging paging, CancellationToken cancellationToken)
        {
            var result = await _shelfProductService.GetCriticalStockProductsAsync(paging, cancellationToken);
            return Ok(result);
        }
    }
}