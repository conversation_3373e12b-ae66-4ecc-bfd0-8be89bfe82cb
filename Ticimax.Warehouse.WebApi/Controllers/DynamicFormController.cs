using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class DynamicFormController : ControllerBase
    {
        private readonly IDynamicFormService _dynamicFormService;
        private readonly IDynamicFormDataService _dynamicFormDataService;
        private readonly IDynamicFormFileService _dynamicFormFileService;

        public DynamicFormController(IDynamicFormService dynamicFormService, IDynamicFormDataService dynamicFormDataService, IDynamicFormFileService dynamicFormFileService)
        {
            _dynamicFormService = dynamicFormService;
            _dynamicFormDataService = dynamicFormDataService;
            _dynamicFormFileService = dynamicFormFileService;
        }

        [HttpGet]
        public async Task<IActionResult> GetList([FromQuery] DynamicFormGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            var result = await _dynamicFormService.GetList(request, paging, cancellationToken);
            return Ok(result);
        }

        [HttpGet]
        public async Task<IActionResult> GetListData([FromQuery] DynamicFormDataGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _dynamicFormDataService.GetList(request, paging, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetListFile([FromQuery] DynamicFormFileGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _dynamicFormFileService.GetList(request, paging, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetCount([FromQuery] DynamicFormGetListDto request, CancellationToken cancellationToken)
        {
            var result = await _dynamicFormService.GetCount(request, cancellationToken);
            return Ok(result);
        }

        [HttpPost]
        public async Task<IActionResult> GetProductDynamicForm([FromBody] GetProductDynamicFormDto request, CancellationToken cancellationToken)
        {
            var result = await _dynamicFormService.GetProductDynamicForm(request, cancellationToken);
            return Ok(result);
        }
    }
}