using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Review.Request;
using static iText.StyledXmlParser.Jsoup.Select.Evaluator;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/notifications")]
    [ApiController]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        public NotificationController(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        [HttpGet]
        public async Task<IActionResult> Get([FromQuery] FilterNotificationRequest request, CancellationToken cancellationToken)
        {
            var result = await _notificationService.GetNotifications(request, cancellationToken);
            return Ok(result);
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateNotificationRequest request, CancellationToken cancellationToken)
        {
            await _notificationService.CreateNotification(request, cancellationToken);
            return Ok();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update([FromRoute(Name = "id")] Guid id, [FromBody] UpdateNotificationRequest request, CancellationToken cancellationToken)
        {
            var result = await _notificationService.UpdateNotification(id, request, cancellationToken);
            return Ok(result);
        }

        [HttpPut("{id}/confirm")]
        public async Task<IActionResult> Confirm([FromRoute(Name = "id")] Guid id, CancellationToken cancellationToken)
        {
            await _notificationService.ConfirmNotification(id, cancellationToken);
            return Ok();
        }

        [HttpPut("{id}/read")]
        public async Task<IActionResult> Read([FromRoute(Name = "id")] Guid id, CancellationToken cancellationToken)
        {
            await _notificationService.ReadNotification(id, cancellationToken);
            return Ok();
        }

        [HttpPut("all/read")]
        public async Task<IActionResult> ReadAll([FromBody] AllReadNotificationRequest request, CancellationToken cancellationToken)
        {
            await _notificationService.ReadAllNotification(request, cancellationToken);
            return Ok();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete([FromRoute(Name = "id")] Guid id, CancellationToken cancellationToken)
        {
            await _notificationService.DeleteNotification(id, cancellationToken);
            return Ok();
        }
    }
}
