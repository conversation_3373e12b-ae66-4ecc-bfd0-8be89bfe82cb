using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Models.Requests.WarehouseReview;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.WebApi.Controllers;

[Route("api/warehouse-reviews")]
[ApiController]
public class WarehouseReviewController : ControllerBase
{
    private readonly IWarehouseReviewService _warehouseReviewService;

    public WarehouseReviewController(IWarehouseReviewService warehouseReviewService)
    {
        _warehouseReviewService = warehouseReviewService;
    }

    /// <summary>
    /// Warehouse Review Services create endpoint
    /// </summary>
    /// <param name="request"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(200)]
    public async Task<IActionResult> Create([FromBody] CreateWarehouseReviewRequest request, CancellationToken cancellationToken)
    {
        await _warehouseReviewService.Create(request, cancellationToken);
        return Ok();
    }

    [HttpGet("chat")]
    [ProducesResponseType(200)]
    public async Task<IActionResult> GetWarehouseChatFilter([FromQuery] Guid connectionId, [FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 20, CancellationToken cancellationToken = default)
    {
        var chats = await _warehouseReviewService.GetWarehouseChatHistory(WebSiteInfo.User.Value.DomainName, connectionId, pageSize, pageIndex, cancellationToken);
        return Ok(chats);
    }

    [HttpGet("chat/groupped")]
    [ProducesResponseType(200)]
    public async Task<IActionResult> GetWarehouseChatGroupped([FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 20, CancellationToken cancellationToken = default)
    {
        var chats = await _warehouseReviewService.GetWarehouseChatGroupped(WebSiteInfo.User.Value.DomainName, WebSiteInfo.User.Value.ID, pageSize, pageIndex, cancellationToken);
        return Ok(chats);
    }

    [HttpGet("chat/unread-count")]
    [ProducesResponseType(200)]
    public async Task<IActionResult> GetUserChatUnreadCount(CancellationToken cancellationToken)
    {
        var chatCount = await _warehouseReviewService.GetUserChatUnReadCount(cancellationToken);
        return Ok(chatCount);
    }

    [HttpPut("chat/all-read")]
    [ProducesResponseType(200)]
    public async Task<IActionResult> WarehouseChatRead([FromBody] WarehouseChatReadRequest request, CancellationToken cancellationToken)
    {
        await _warehouseReviewService.WarehouseChatRead(request, cancellationToken);
        return Ok();
    }

    [HttpPost("chat-connection")]
    [ProducesResponseType(200)]
    public async Task<IActionResult> WarehouseChatConnection([FromBody] CreateWarehouseChatConnectionRequest request, CancellationToken cancellationToken)
    {
        var connection = await _warehouseReviewService.CreateWarehouseChatConnection(request, cancellationToken);
        return Ok(connection);
    }
}