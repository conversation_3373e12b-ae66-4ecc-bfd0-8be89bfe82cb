using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class EInvoiceController : ControllerBase
    {
        private readonly IEInvoiceService _eInvoiceService;

        public EInvoiceController(IEInvoiceService eInvoiceService)
        {
            _eInvoiceService = eInvoiceService;
        }

        [HttpGet]
        public async Task<IActionResult> GetResult([FromQuery] EInvoiceGetResultDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _eInvoiceService.GetResult(request, paging, cancellationToken));
        }
    }
}