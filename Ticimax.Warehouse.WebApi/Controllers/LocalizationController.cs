using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Warehouse.Business.Abstract;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/localizations")]
    [ApiController]
    public class LocalizationController : ControllerBase
    {
        private readonly ILocalizationService _localizationService;
        public LocalizationController(ILocalizationService localizationService)
        {
            _localizationService = localizationService;
        }

        [HttpGet("{language}")]
        public async Task<IActionResult> Get([FromRoute] string language, CancellationToken cancellationToken)
        {
            var result = await _localizationService.GetLocalizations(language, cancellationToken);
            return Ok(result);
        }
    }
}
