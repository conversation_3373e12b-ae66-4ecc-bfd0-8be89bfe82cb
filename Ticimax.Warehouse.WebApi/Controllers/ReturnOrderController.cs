using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/return-orders")]
    [ApiController]
    [Authorization("RefundManager.ShowPage")]
    public class ReturnOrderController : ControllerBase
    {
        private readonly IReturnOrderService _returnOrderService;
        private readonly IReturnOrderRequestService _returnOrderRequestService;
        public ReturnOrderController(IReturnOrderService returnOrderService, IReturnOrderRequestService returnOrderRequestService)
        {
            _returnOrderService = returnOrderService;
            _returnOrderRequestService = returnOrderRequestService;
        }
        /// <summary>
        /// Return Order order filter endpoint
        /// </summary>        
        /// <param name="barcode"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet("barcodes/{barcode}")]
        [ProducesResponseType(typeof(DataResult<ReturnOrderGetOrderResponseDto>), 200)]
        public async Task<IActionResult> GetOrder([FromRoute] string barcode, CancellationToken cancellationToken)
        {
            var result = await _returnOrderService.GetOrder(barcode, cancellationToken);
            return Ok(result);
        }

        [HttpGet("cargo/{cargocode}")]
        [ProducesResponseType(typeof(DataResult<ReturnOrderGetOrderResponseDto>), 200)]
        public async Task<IActionResult> GetOrderByCargoCode([FromRoute] string cargocode, CancellationToken cancellationToken)
        {
            var result = await _returnOrderRequestService.GetReturnRequest(null, cargocode, cancellationToken);
            return Ok(result);
        }

        /// <summary>
        /// Return Order completed endpoint
        /// </summary>        
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("{id}/completed")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> Completed([FromRoute] int id, [FromBody] ReturnOrderCompleatedDto request, CancellationToken cancellationToken)
        {
            await _returnOrderService.Completed(id, request, cancellationToken);
            return NoContent();
        }

        [HttpGet("return-request/order/{orderId}")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> GetOrderReturnRequest([FromRoute] int orderId, CancellationToken cancellationToken)
        {
            var result = await _returnOrderService.GetReturnRequest(orderId, cancellationToken);
            return Ok(result);
        }
    }
}