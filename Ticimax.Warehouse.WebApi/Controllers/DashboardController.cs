using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.Dashboard;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorization("Dashboard.ShowPage")]
    public class DashboardController : ControllerBase
    {
        private readonly IDashboardService _dashboardService;

        public DashboardController(IDashboardService dashboardService)
        {
            _dashboardService = dashboardService;
        }

        [HttpGet("route-map")]
        [Authorization("Dashboard.ActiveSetsStatistics")]
        public async Task<IActionResult> RouteMap(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.RouteMap(cancellationToken);
            return Ok(result);
        }

        [HttpGet("order-counts")]
        [Authorization("Dashboard.OrderCountChart")]
        public async Task<IActionResult> OrderProductTypeTemplate(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.OrderProductTypeTemplate(false, cancellationToken);
            return Ok(result);
        }

        [HttpGet("reset-cache")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> ResetCache(CancellationToken cancellationToken)
        {
            await _dashboardService.ResetCache(cancellationToken);
            return NoContent();
        }

        [HttpGet("quality-personal-statics")]
        [Authorization("Dashboard.PersonalStatistics")]
        public async Task<IActionResult> QualityPersonalStatics(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.QualityPersonalStatics(cancellationToken);
            return Ok(result);
        }

        [HttpGet("pick-personal-statics")]
        [Authorization("Dashboard.PersonalStatistics")]
        public async Task<IActionResult> PickPersonalStatics(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.PickPersonalStatics(cancellationToken);
            return Ok(result);
        }

        [HttpGet("warehouse-statics")]
        [Authorization("Dashboard.TotalQualityControlAndCollectedProductWidget")]
        public async Task<IActionResult> ManagerQualityControlAndCollectedProductGetCount(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.ManagerQualityControlAndCollectedProductGetCount(false, cancellationToken);
            return Ok(result);
        }

        [HttpGet("operation-speeds")]
        [Authorization("Dashboard.OrderOperationSpeedWidget")]
        public async Task<IActionResult> OrderOperationSpeedGetCount(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.OrderOperationSpeedGetCount(false, cancellationToken);
            return Ok(result);
        }

        [HttpGet("order-sources")]
        [Authorization("Dashboard.OrderSourceChart")]
        public async Task<IActionResult> OrderSourceIncomingOrderGetCount(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.OrderSourceIncomingOrderGetCount(false, cancellationToken);
            return Ok(result);
        }

        [HttpGet("pickable-order")]
        [Authorization("Dashboard.OrderCountChart")]
        [ProducesResponseType(typeof(PickableOrderDto), 200)]
        public async Task<IActionResult> OrderPickableCount(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.PickableOrder(false, 0, cancellationToken);
            return Ok(result);
        }

        [HttpGet("warehouse-product-transfer")]
        [ProducesResponseType(typeof(Pageable<WarehouseProductTransferReportDto>), 200)]
        public async Task<IActionResult> WarehouseProductTranser(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.WarehouseProductTranser(false, cancellationToken);
            return Ok(result);
        }

        [HttpGet("order-cargo-information")]
        [Authorization("Dashboard.CargoTrackingChart")]
        [ProducesResponseType(typeof(OrderCargoInfoDto), 200)]
        public async Task<IActionResult> OrderProductTypeTemplate([FromQuery] int cargoCompanyId, CancellationToken cancellationToken)
        {
            var result = await _dashboardService.GetOrderCargoInfoAsync(cargoCompanyId, cancellationToken);
            return Ok(result);
        }
    }
}