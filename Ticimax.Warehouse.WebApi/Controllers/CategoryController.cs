using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/categories")]
    [ApiController]
    public class CategoryController : ControllerBase
    {
        private readonly ICategoryService _categoryService;

        public CategoryController(ICategoryService categoryService)
        {
            _categoryService = categoryService;
        }

        [HttpGet]
        [ProducesResponseType(typeof(DataResult<List<Category>>), 200)]
        public async Task<IActionResult> GetList([FromQuery] CategoryGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            var result = await _categoryService.GetList(request, paging, cancellationToken);
            return Ok(result);
        }

    }
}