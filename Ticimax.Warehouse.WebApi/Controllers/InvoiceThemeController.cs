using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/invoice-themes")]
    [ApiController]
    public class InvoiceThemeController : ControllerBase
    {
        private readonly IInvoiceThemeService _invoiceThemeService;

        public InvoiceThemeController(IInvoiceThemeService invoiceThemeService)
        {
            _invoiceThemeService = invoiceThemeService;
        }

        /// <summary>
        /// Invoice Theme Services filter endpoint
        /// </summary>
        /// <param name="request"></param>
        /// <param name="paging"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetList([FromQuery] InvoiceThemeGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _invoiceThemeService.GetList(request, paging, cancellationToken));
        }

        /// <summary>
        /// Invoice Theme Services update endpoint
        /// </summary>
        /// <param name="request"></param>
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] InvoiceThemeUpdateDto request, CancellationToken cancellationToken)
        {
            return Ok(await _invoiceThemeService.Update(id, request, cancellationToken));
        }

        /// <summary>
        /// Invoice Theme Services create endpoint
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(200)]
        public async Task<IActionResult> Add([FromBody] InvoiceThemeAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _invoiceThemeService.Add(request, cancellationToken));
        }

        /// <summary>
        /// Invoice Theme Services delete endpoint
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> Delete([FromRoute] int id, CancellationToken cancellationToken)
        {
            await _invoiceThemeService.Delete(id, cancellationToken);
            return NoContent();
        }

        //[HttpGet]
        //public async Task<IActionResult> GetCount([FromQuery] InvoiceThemeGetListDto request)
        //{
        //    return Ok(_invoiceThemeService.GetCount(request));
        //}

        /// <summary>
        /// Invoice Theme Services get ıtem endpoint
        /// </summary>
        /// <param name="type"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet("{type}/items")]
        public async Task<IActionResult> GetInvoiceItems([FromRoute] string type, CancellationToken cancellationToken)
        {
            var result = await _invoiceThemeService.GetItems(type, cancellationToken);
            return Ok(result);
        }
    }
}