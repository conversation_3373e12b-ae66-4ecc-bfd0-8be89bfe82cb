using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Request;
using Ticimax.Warehouse.Business.Concrete.WarehouseJobZone.Models.Response;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/warehouse-job-zone")]
    [ApiController]
    public class WarehouseJobZoneController : ControllerBase
    {
        private readonly IWarehouseJobZoneService _warehouseJobZoneService;

        public WarehouseJobZoneController(IWarehouseJobZoneService warehouseJobZoneService)
        {
            _warehouseJobZoneService = warehouseJobZoneService;
        }

        [HttpPost]
        [ProducesResponseType(200)]
        public async Task<IActionResult> CreateWarehouseJob([FromBody] CreateWarehouseJobRequest request, CancellationToken cancellationToken)
        {
            await _warehouseJobZoneService.CreateWarehouseJob(request, cancellationToken);
            return Ok();
        }

        [HttpGet("{jobId}")]
        [ProducesResponseType(typeof(WarehouseJobZoneAggregate), 200)]
        public async Task<IActionResult> GetJobById([FromRoute] Guid jobId, CancellationToken cancellationToken = default)
        {
            var result = await _warehouseJobZoneService.GetJobById(jobId, cancellationToken);
            return Ok(result);
        }

        [HttpGet]
        [ProducesResponseType(typeof(Pageable<WarehouseJobZoneAggregate>), 200)]
        public async Task<IActionResult> FilterJobs([FromQuery] FilterWarehouseJobFilter filter, CancellationToken cancellationToken = default)
        {
            var result = await _warehouseJobZoneService.FilterJobs(filter, cancellationToken);
            return Ok(result);
        }

        [HttpDelete("{jobId}")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> DeleteJob([FromRoute] Guid jobId, CancellationToken cancellationToken = default)
        {
            var result = await _warehouseJobZoneService.DeleteWarehouseJob(jobId, cancellationToken);
            return Ok(result);
        }

        [HttpPut("{jobId}")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> FilterJobs([FromRoute] Guid jobId, [FromBody] UpdateWarehouseJobRequest request, CancellationToken cancellationToken = default)
        {
            await _warehouseJobZoneService.UpdateWarehouseJob(jobId, request, cancellationToken);
            return Ok();
        }

        [HttpGet("flows")]
        [ProducesResponseType(typeof(List<FlowTypesResponse>), 200)]
        public async Task<IActionResult> GetFlows(CancellationToken cancellationToken = default)
        {
            var result = await _warehouseJobZoneService.GetFlowType(cancellationToken);
            return Ok(result);
        }
        [HttpGet("condition-types")]
        [ProducesResponseType(typeof(List<ConditionTypesResponse>), 200)]
        public async Task<IActionResult> GetConditionTypes(CancellationToken cancellationToken = default)
        {
            var result = await _warehouseJobZoneService.GetConditionTypes(cancellationToken);
            return Ok(result);
        }

        [HttpGet("condition-operators")]
        [ProducesResponseType(typeof(List<ConditionOperatorsResponse>), 200)]
        public async Task<IActionResult> GetConditionOperators(CancellationToken cancellationToken = default)
        {
            var result = await _warehouseJobZoneService.GetConditionOperators(cancellationToken);
            return Ok(result);
        }
        [HttpGet("condition-conjunctions")]
        [ProducesResponseType(typeof(List<ConditionConjunctionsResponse>), 200)]
        public async Task<IActionResult> GetConjunctions(CancellationToken cancellationToken = default)
        {
            var result = await _warehouseJobZoneService.GetConditionConjunctions(cancellationToken);
            return Ok(result);
        }
    }
}