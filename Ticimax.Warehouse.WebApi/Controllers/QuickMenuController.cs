using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class QuickMenuController : ControllerBase
    {
        private readonly IQuickMenuService _quickMenuService;

        public QuickMenuController(IQuickMenuService quickMenuService)
        {
            _quickMenuService = quickMenuService;
        }

        [HttpPost]
        public async Task<IActionResult> Update([FromBody] QuickMenuUpdateDto request, CancellationToken cancellationToken)
        {
            return Ok(await _quickMenuService.UpdateTableQuickMenu(request, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetQuickMenus(CancellationToken cancellationToken)
        {
            return Ok(await _quickMenuService.GetQuickMenus(cancellationToken));
        }
    }
}