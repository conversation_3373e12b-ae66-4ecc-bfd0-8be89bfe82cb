using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorization("WarehouseManagement.SupplierManager.ShowPage")]
    public class SupplierController : ControllerBase
    {
        private readonly ISupplierService _supplier;

        public SupplierController(ISupplierService supplier)
        {
            _supplier = supplier;
        }

        [HttpGet]
        public async Task<IActionResult> GetList([FromQuery] SupplierGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _supplier.GetList(request, paging, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> SimpleSupplierGetList([FromQuery] SupplierGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _supplier.SimpleSupplierGetList(request, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetCount([FromQuery] SupplierGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _supplier.GetCount(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.SupplierManager.EditSupplier")]
        public async Task<IActionResult> Update([FromBody] SupplierEditDto request, CancellationToken cancellationToken)
        {
            return Ok(await _supplier.Update(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.SupplierManager.RemoveSupplier")]
        public async Task<IActionResult> Delete([FromBody] SupplierDeleteDto request, CancellationToken cancellationToken)
        {
            return Ok(await _supplier.Delete(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.SupplierManager.AddSupplier")]
        public async Task<IActionResult> Add([FromBody] SupplierAddDto request, CancellationToken cancellationToken)
        {
            var result = await _supplier.Add(request, cancellationToken);
            return Ok(new { Id = result });
        }

        [HttpPost]
        [Authorization("WarehouseManagement.SupplierManager.ExcelSupplierAdd")]
        public async Task<IActionResult> MultipleAdd([FromBody] MultipleSupplierAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _supplier.MultipleAdd(request, cancellationToken));
        }
    }
}