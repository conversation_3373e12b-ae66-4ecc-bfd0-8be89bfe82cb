using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class LanguageController : ControllerBase
    {
        private readonly ILanguageService _languageService;

        public LanguageController(ILanguageService languageService)
        {
            _languageService = languageService;
        }

        [HttpGet]
        public async Task<IActionResult> GetLanguage([FromQuery] GetLanguageDto request, CancellationToken cancellationToken)
        {
            if (request.Timeout > 0)
                Thread.Sleep(request.Timeout);

            request.Version = ApiVersion.SoftwareVersion.ToString();
            return Ok(await _languageService.GetLanguage(request, cancellationToken));
        }
    }
}