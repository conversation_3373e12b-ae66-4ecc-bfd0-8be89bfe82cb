using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.ShelfDtos;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/shelfs")]
    [ApiController]
    [Authorization("WarehouseManagement.ShelfManager.ShowPage")]
    public class ShelfController : ControllerBase
    {
        private readonly IShelfService _shelfService;
        private readonly IWarehouseShelfMovementService _warehouseShelfMovementService;

        public ShelfController(IShelfService shelfService, IWarehouseShelfMovementService warehouseShelfMovementService)
        {
            _shelfService = shelfService;
            _warehouseShelfMovementService = warehouseShelfMovementService;
        }
        /// <summary>
        /// Shelf Services shelf filter endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="paging"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        [MultipleAuthorization(new[] { "WarehouseManagement.ShelfManager.ShowPage", "ProductManagement.ShelfCounting.ShowPage" })]
        public async Task<IActionResult> GetList([FromQuery] ShelfGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _shelfService.GetList(request, paging, cancellationToken));
        }

        /// <summary>
        /// Shelf Services shelf child count endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet("includes-child-count")]
        [MultipleAuthorization(new[] { "WarehouseManagement.ShelfManager.ShowPage", "ProductManagement.ShelfCounting.ShowPage" })]
        [ProducesResponseType(typeof(Pageable<ShelfIncludesChildCountDto>), 200)]
        public async Task<IActionResult> GetIncludesChildCount([FromQuery] ShelfGetListDto request, [FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 20, CancellationToken cancellationToken = default)
        {
            return Ok(await _shelfService.GetShelfIncludesChildCount(request, pageSize, pageIndex, cancellationToken));
        }
        /// <summary>
        /// Shelf Services shelf child list endpoint
        /// </summary>        
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [MultipleAuthorization(new[] { "WarehouseManagement.ShelfManager.ShowPage", "ProductManagement.ShelfCounting.ShowPage" })]
        [ProducesResponseType(typeof(ShelfIncludesChildDto), 200)]
        public async Task<IActionResult> GetIncludesChild([FromRoute] int id, CancellationToken cancellationToken)
        {
            return Ok(await _shelfService.GetShelfById(id, cancellationToken));
        }

        /// <summary>
        /// Shelf Services update endpoint
        /// </summary>        
        /// <param name="id"></param>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        [Authorization("WarehouseManagement.ShelfManager.EditShelf")]
        public async Task<IActionResult> Update([FromRoute] int id, [FromBody] ShelfUpdateDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfService.Update(id, request, cancellationToken));
        }

        /// <summary>
        /// Shelf Services shelf open for sale endpoint
        /// </summary>        
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("{id}/open-for-sale")]
        [ProducesResponseType(204)]
        [Authorization("WarehouseManagement.ShelfManager.EditShelf")]
        public async Task<IActionResult> OpenForSale([FromRoute] int id, CancellationToken cancellationToken)
        {
            await _shelfService.OpenForSale(id, cancellationToken);
            return NoContent();
        }

        /// <summary>
        /// Shelf Services shelf close for sale endpoint
        /// </summary>        
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("{id}/close-for-sale")]
        [ProducesResponseType(204)]
        [Authorization("WarehouseManagement.ShelfManager.EditShelf")]
        public async Task<IActionResult> CloseForSale([FromRoute] int id, CancellationToken cancellationToken)
        {
            await _shelfService.CloseForSale(id, cancellationToken);
            return NoContent();
        }


        [HttpPut("{id}/close-for-picking")]
        [ProducesResponseType(204)]
        [Authorization("WarehouseManagement.ShelfManager.EditShelf")]
        public async Task<IActionResult> CloseForPicking([FromRoute] int id, CancellationToken cancellationToken)
        {
            await _shelfService.CloseForPicking(id, cancellationToken);
            return NoContent();
        }

        [HttpPut("{id}/open-for-picking")]
        [ProducesResponseType(204)]
        [Authorization("WarehouseManagement.ShelfManager.EditShelf")]
        public async Task<IActionResult> OpenForPicking([FromRoute] int id, CancellationToken cancellationToken)
        {
            await _shelfService.OpenForPicking(id, cancellationToken);
            return NoContent();
        }

        /// <summary>
        /// Shelf Services create endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorization("WarehouseManagement.ShelfManager.AddShelf")]
        public async Task<IActionResult> Add([FromBody] ShelfAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfService.Add(request, cancellationToken));
        }

        /// <summary>
        /// Shelf Services delete endpoint
        /// </summary>        
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        [Authorization("WarehouseManagement.ShelfManager.DeleteShelf")]
        public async Task<IActionResult> Delete([FromRoute] int id, CancellationToken cancellationToken)
        {
            return Ok(await _shelfService.Delete(id, cancellationToken));
        }

        /// <summary>
        /// Shelf Services count filter endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet("count")]
        public async Task<IActionResult> GetCount([FromQuery] ShelfGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfService.GetCount(request, cancellationToken));
        }

        /// <summary>
        /// Shelf Services add with excel endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost("excel")]
        [Authorization("WarehouseManagement.ShelfManager.ExcelShelfAdd")]
        public async Task<IActionResult> AddList([FromBody] ShelfAddListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfService.MultipleAdd(request, cancellationToken));
        }

        [HttpGet("movements")]
        [MultipleAuthorization(new[] { "WarehouseManagement.ShelfManager.ShowPage", "ProductManagement.ShelfCounting.ShowPage" })]
        public async Task<IActionResult> GetListMovement([FromQuery] WarehouseShelfMovementGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseShelfMovementService.GetListAsync(request, paging, cancellationToken));
        }
    }
}