using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/table-order-combines")]
    [ApiController]
    [Authorization("OrderManagement.OrderCombine.ShowPage")]
    public class TableOrderCombineController : ControllerBase
    {
        private readonly ITableOrderCombineService _tableOrderCombineService;

        public TableOrderCombineController(ITableOrderCombineService tableOrderCombineService)
        {
            _tableOrderCombineService = tableOrderCombineService;
        }

        /// <summary>
        /// Table Order Combine Services order combine endpoint
        /// </summary>        
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> OrderCombine(CancellationToken cancellationToken)
        {
            var result = await _tableOrderCombineService.OrderCombine(cancellationToken);
            return Ok(result);
        }

        /// <summary>
        /// Table Order Combine Services table selection endpoint
        /// </summary>        
        /// <param name="barcode"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("tables/{barcode}")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> TableSelection([FromRoute] string barcode, CancellationToken cancellationToken)
        {
            await _tableOrderCombineService.TableSelection(barcode, cancellationToken);
            return NoContent();
        }

        /// <summary>
        /// Table Order Combine Services assign product endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost("assign-product")]
        [ProducesResponseType(typeof(DataResult<TableOrderCombineTableProductAssignResponseDto>), 200)]
        public async Task<IActionResult> TableProductAssign([FromBody] TableOrderCombineTableProductAssignDto request, CancellationToken cancellationToken)
        {
            var result = await _tableOrderCombineService.TableProductAssign(request, cancellationToken);
            return Ok(result);
        }

        [HttpPost("completed")]
        [ProducesResponseType(200)]
        public async Task<IActionResult> OrderCombineCompleted([FromBody] OrderCombineCompletedDto request, CancellationToken cancellationToken)
        {
            await _tableOrderCombineService.OrderCombineCompleted(request, cancellationToken);
            return Ok();
        }


        /// <summary>
        /// Table Order Combine Services missing box assign endpoint
        /// </summary>        
        /// <param name="tableId"></param>
        /// <param name="parcelId"></param>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("tables/{tableId}/parcels/{parcelId}")]
        public async Task<IActionResult> MissingBoxAssign([FromRoute] int tableId, [FromRoute] int parcelId, [FromBody] MissingBoxAssignRequestDto request, CancellationToken cancellationToken)
        {
            await _tableOrderCombineService.MissingBoxAssign(tableId, parcelId, request, cancellationToken);
            return NoContent();
        }
    }
}