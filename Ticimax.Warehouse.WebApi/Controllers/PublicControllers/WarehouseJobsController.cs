using Amazon.Runtime.Internal.Util;
using k8s.Models;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete;
using Ticimax.Warehouse.Business.Concrete.Inbound.Models;
using Ticimax.Warehouse.Business.Concrete.Inbound.Models.Request;
using Ticimax.Warehouse.Business.Concrete.OrderWaiting.Models.Request;
using Ticimax.Warehouse.Business.Concrete.PickingProducts.Models.Request;
using Ticimax.Warehouse.Business.Concrete.ShelfCountingFile.Models.Request;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer.Models.Requests;
using Ticimax.Warehouse.Entities.Concrete.OrderWaiting;
using Ticimax.Warehouse.Entities.Concrete.ShelfCounting.File;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Static;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers.PublicControllers
{
    [Route("warehouse-jobs")]
    [ApiController]
    public class WarehouseJobsController : ControllerBase
    {
        private readonly IShelfCountingFilesService _shelfCountingFilesService;
        private readonly IWarehouseProductTransferService _warehouseProductTransferService;
        private readonly IShelfProductService _shelfProductService;
        private readonly IPurchaseOrderService _purchaseOrderService;
        private readonly IOrderService _orderService;
        private readonly IWarehouseTableService _warehouseTableService;
        private readonly IWarehouseCarService _warehouseCarService;
        private readonly IShelfService _shelfService;
        private readonly IWaitingOrderService _waitingOrderService;
        private readonly IPickingProductService _pickingProductService;
        private readonly IUserInformationService _userInformationService;

        public WarehouseJobsController(IShelfCountingFilesService shelfCountingFilesService, IWarehouseProductTransferService warehouseProductTransferService, IShelfProductService shelfProductService, IPurchaseOrderService purchaseOrderService, IOrderService orderService, IWarehouseTableService warehouseTableService, IShelfService shelfService, IWaitingOrderService waitingOrderService, IPickingProductService pickingProductService, IUserInformationService userInformationService)
        {
            _shelfCountingFilesService = shelfCountingFilesService;
            _warehouseProductTransferService = warehouseProductTransferService;
            _shelfProductService = shelfProductService;
            _purchaseOrderService = purchaseOrderService;
            _orderService = orderService;
            _warehouseTableService = warehouseTableService;
            _shelfService = shelfService;
            _waitingOrderService = waitingOrderService;
            _pickingProductService = pickingProductService;
            _userInformationService = userInformationService;
        }

        /// <summary>
        /// Shelf Counting File Services create endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost("shelf-counting-file")]
        [TimeAuthorization]
        [ProducesResponseType(typeof(ShelfCountingFileAggregate), 200)]
        public async Task<IActionResult> CreateShelfCountingFile([FromBody] CreateShelfCountingFileRequest request, CancellationToken cancellationToken)
        {
            var result = await _shelfCountingFilesService.Create(request, cancellationToken);
            return Ok(result);
        }

        /// <summary>
        /// Warehouse Product Transfer create transfer endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost("warehouse-transfer-file")]
        [TimeAuthorization]
        [ProducesResponseType(typeof(WarehouseProductTransferFileAggregate), 200)]
        public async Task<IActionResult> CreateWarehouseTransferFile([FromBody] CreateWarehouseProductTransferRequest request, CancellationToken cancellationToken)
        {
            var result = await _warehouseProductTransferService.CreateFile(request, cancellationToken);
            return Ok(result);
        }

        [HttpPost("add-shelf-product")]
        [TimeAuthorization]
        [ProducesResponseType(204)]
        public async Task<IActionResult> AddShelfProduct([FromBody] ShelfProductAddDto request, CancellationToken cancellationToken)
        {
            await _shelfProductService.Add(request, cancellationToken);
            return NoContent();
        }

        [HttpPost("reduce-shelf-product")]
        [TimeAuthorization]
        [ProducesResponseType(204)]
        public async Task<IActionResult> ReduceShelfProduct([FromBody] ShelfProductReduceListDto request, CancellationToken cancellationToken)
        {
            await _shelfProductService.ShelfProductReduceList(request, cancellationToken);
            return NoContent();
        }

        /// <summary>
        /// Purchase Order create endpoint
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost("create-purchase-order")]
        [TimeAuthorization]
        [ProducesResponseType(typeof(PurchaseOrderModel), 200)]
        public async Task<IActionResult> CreatePurchaseOrder([FromBody] CreatePurchaseOrderRequest request, CancellationToken cancellationToken)
        {
            var result = await _purchaseOrderService.CreatePurchaseOrder(request, cancellationToken);
            return Ok(result);
        }

        [HttpPost("orders/reset")]
        [TimeAuthorization]
        [ProducesResponseType(typeof(ErrorResponse), 200)]
        public async Task<IActionResult> OrderReset([FromBody] OrderResetRequestDto request, CancellationToken cancellationToken)
        {
            return Ok(await _orderService.OrderReset(request, cancellationToken));
        }

        [HttpPost("orders/{id}/waiting")]
        [TimeAuthorization]
        [ProducesResponseType(typeof(WaitingOrderAggregate), 200)]
        public async Task<IActionResult> CreateWaitingOrder([FromRoute] int id, [FromBody] WaitingOrderRequest request, CancellationToken cancellationToken)
        {
            var result = await _waitingOrderService.Create(id, request, cancellationToken);
            return Ok(result);
        }

        [HttpPost("warehouse-table")]
        [TimeAuthorization]
        [ProducesResponseType(typeof(ErrorResponse), 200)]
        public async Task<IActionResult> CreateWarehouseTable([FromBody] WarehouseTableAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseTableService.AddAsync(request, cancellationToken));
        }

        [HttpPost("warehouse-car")]
        [TimeAuthorization]
        [ProducesResponseType(typeof(ErrorResponse), 200)]
        public async Task<IActionResult> CreateWarehouseCar([FromBody] WarehouseCarAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseCarService.Add(request, cancellationToken));
        }

        /// <summary>
        /// Shelf Services create endpoint
        /// </summary>        
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost("shelfs")]
        [TimeAuthorization]
        [ProducesResponseType(typeof(ErrorResponse), 200)]
        public async Task<IActionResult> CreateShelf([FromBody] ShelfAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _shelfService.Add(request, cancellationToken));
        }

        /// <summary>
        /// Shelf Services shelf close for sale endpoint
        /// </summary>        
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("shelfs/{id}/close-for-sale")]
        [TimeAuthorization]
        [ProducesResponseType(204)]
        public async Task<IActionResult> CloseShelfForSale([FromRoute] int id, CancellationToken cancellationToken)
        {
            await _shelfService.CloseForSale(id, cancellationToken);
            return NoContent();
        }

        /// <summary>
        /// Shelf Services shelf open for sale endpoint
        /// </summary>        
        /// <param name="id"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPut("shelfs/{id}/open-for-sale")]
        [TimeAuthorization]
        [ProducesResponseType(204)]
        public async Task<IActionResult> OpenForSale([FromRoute] int id, CancellationToken cancellationToken)
        {
            await _shelfService.OpenForSale(id, cancellationToken);
            return NoContent();
        }

        [HttpPost("picking-products/select")]
        [TimeAuthorization]
        public async Task<IActionResult> OrderSelect([FromBody] CreateOrderSelectRequest request, CancellationToken cancellationToken)
        {
            await _pickingProductService.DistributionProductControlAsync(
              new PickingProductServiceDistributionProductDto
              {
                  Type = request.Type
              }, cancellationToken);
            var agentUser = (await _userInformationService.GetList(new Entities.Filters.UserInformationFilter()
            {
                DomainName = Info.DomainName.Value,
                UserID = WebSiteInfo.User.Value.ID
            }, null, cancellationToken)).Model.FirstOrDefault();
            if (agentUser != null)
            {
                //Info.LoginCacheKey.Value = agentUser.Token;
                WebSiteInfo.User.Value.OutSourceUserCacheKey = agentUser.Token;
                await _userInformationService.UpdateTokenInformation(agentUser.Token,
                    WebSiteInfo.User.Value, cancellationToken);
            }
            return NoContent();
        }
    }
}
