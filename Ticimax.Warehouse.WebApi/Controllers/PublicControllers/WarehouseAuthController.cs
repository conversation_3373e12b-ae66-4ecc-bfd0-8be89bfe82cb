using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.WarehouseAuth.Models;
using Ticimax.Warehouse.DataAccessLayer.Abstract.PostgreSQL;
using Ticimax.Warehouse.Entities.BusinessEntities;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Static;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers.PublicControllers
{
    [Route("warehouse-auths")]
    [ApiController]
    public class WarehouseAuthController : ControllerBase
    {
        private readonly IWarehouseAuthGroupDefinitionService _warehouseAuthGroupDefinitionService;
        private readonly ICustomerAccessDal _customerAccessDal;
        private readonly ITransactionalCommandService _transactionalCommandService;
        public WarehouseAuthController(IWarehouseAuthGroupDefinitionService warehouseAuthGroupDefinitionService, ICustomerAccessDal customerAccessDal, ITransactionalCommandService transactionalCommandService)
        {
            _warehouseAuthGroupDefinitionService = warehouseAuthGroupDefinitionService;
            _customerAccessDal = customerAccessDal;
            _transactionalCommandService = transactionalCommandService;
        }

        [HttpPost("sync")]
        [TimeAuthorization]
        public async Task<IActionResult> SyncWarehouseAuth(WarehouseAuthModel model, CancellationToken cancellationToken)
        {
            if (WebSiteInfo.User.Value == null)
                WebSiteInfo.User.Value = new User();

            var customers = await _customerAccessDal.GetList(new CustomerAccessFilter() { Status = true }, null, cancellationToken);
            foreach (var customer in customers)
            {
                try
                {
                    Info.DomainName.Value = customer.Domain;
                    await _warehouseAuthGroupDefinitionService.SyncWarehouseAuth(model, cancellationToken);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }

            await _transactionalCommandService.TransactionalExecuteAsync();

            return NoContent();
        }
    }
}
