using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.Report.Models;
using Ticimax.Warehouse.DataAccessLayer.Abstract.PostgreSQL;
using Ticimax.Warehouse.Entities.BusinessEntities;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.WebApi.Controllers.PublicControllers
{
    [Route("periodic-tasks")]
    [ApiController]
    public class PeriodicTaskController : ControllerBase
    {
        private readonly ICustomerAccessDal _customerAccessDal;
        private readonly IPeriodicTaskService _periodicTaskService;
        private readonly IReportService _reportService;
        private readonly ITransactionalCommandService _transactionalCommandService;
        private readonly IStoreAgentService _storeAgentService;
        public PeriodicTaskController(ICustomerAccessDal customerAccessDal, IPeriodicTaskService periodicTaskService, ITransactionalCommandService transactionalCommandService, IReportService reportService, IStoreAgentService storeAgentService)
        {
            _customerAccessDal = customerAccessDal;
            _periodicTaskService = periodicTaskService;
            _transactionalCommandService = transactionalCommandService;
            _reportService = reportService;
            _storeAgentService = storeAgentService;
        }

        [HttpPost("clear-empty-stocks")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> ClearEmptyStocks(CancellationToken cancellationToken)
        {
            var customers = await _customerAccessDal.GetList(new CustomerAccessFilter() { Status = true }, null, cancellationToken);
            foreach (var customer in customers)
            {
                try
                {
                    await _periodicTaskService.ClearEmptyStocksSend(customer.Domain, cancellationToken);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            }

            await _transactionalCommandService.TransactionalExecuteAsync();

            return NoContent();
        }

        [HttpPost("clear-empty-stock-internal")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> ClearEmptyStocksInternal(CancellationToken cancellationToken)
        {
            try
            {
                await _periodicTaskService.ClearEmptyStocks(Info.DomainName.Value, cancellationToken);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }


            await _transactionalCommandService.TransactionalExecuteAsync();

            return NoContent();
        }

        [HttpPost("product-stocks-fix")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> ProductStocksFix(CancellationToken cancellationToken)
        {
            if (WebSiteInfo.User.Value == null)
                WebSiteInfo.User.Value = await _storeAgentService.FillWebSiteInfoUser(21, CancellationToken.None);

            try
            {
                var products = await _reportService.GetProductsWithStockErrorsReport(new FilterProductsWithStockErrorRequest() { OnlyWrong = true }, 0, int.MaxValue, cancellationToken);
                var productIds = products.Contents.Select(x => x.ProductId).ToList();
                foreach (var chunkedList in productIds.Chunk(500))
                {
                    await _reportService.HaveAStockProblemProductsFix(new HaveAStockProblemProductsFixRequest() { ProductIds = chunkedList.ToList() }, cancellationToken);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            await _transactionalCommandService.TransactionalExecuteAsync();

            return NoContent();
        }
    }
}
