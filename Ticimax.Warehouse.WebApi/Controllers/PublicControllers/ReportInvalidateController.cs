using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.DataAccessLayer.Abstract.PostgreSQL;
using Ticimax.Warehouse.Entities.BusinessEntities;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.WebApi.Controllers.PublicControllers;

[Route("report-invalidates")]
[ApiController]
public class ReportInvalidateController : ControllerBase
{
    private readonly IReportService _reportService;
    private readonly ICustomerAccessDal _customerAccessDal;
    private readonly ITransactionalCommandService _transactionalCommandService;


    public ReportInvalidateController(IReportService reportService, ICustomerAccessDal customerAccessDal, ITransactionalCommandService transactionalCommandService)
    {
        _reportService = reportService;
        _customerAccessDal = customerAccessDal;
        _transactionalCommandService = transactionalCommandService;
    }

    [HttpPost("history-stocks")]
    [ProducesResponseType(204)]
    public async Task<IActionResult> HistoryStock(CancellationToken cancellationToken)
    {
        var customers = await _customerAccessDal.GetList(new CustomerAccessFilter() { Status = true }, null, cancellationToken);
        foreach (var customer in customers)
        {
            await _reportService.CreateInvalidateRequestForHistoryStocksSend(customer.Domain, cancellationToken);
        }

        await _transactionalCommandService.TransactionalExecuteAsync();

        return NoContent();
    }

    [HttpPost("history-stocks-internal")]
    [ProducesResponseType(204)]
    public async Task<IActionResult> HistoryStockInternal(CancellationToken cancellationToken)
    {
        if (WebSiteInfo.User.Value == null)
            WebSiteInfo.User.Value = new User();

        await _reportService.CreateInvalidateRequestForHistoryStocks(Info.DomainName.Value, cancellationToken);
        await _transactionalCommandService.TransactionalExecuteAsync();

        return NoContent();
    }

}