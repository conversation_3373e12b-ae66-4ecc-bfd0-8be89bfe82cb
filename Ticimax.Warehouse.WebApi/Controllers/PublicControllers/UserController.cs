using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Threading;
using System;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Business.Abstract;

namespace Ticimax.Warehouse.WebApi.Controllers.PublicControllers
{
    [Route("users")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IStoreAgentService _storeAgentService;
        public UserController(IStoreAgentService storeAgentService)
        {
            _storeAgentService = storeAgentService;
        }

        [HttpPost("forgot-password")]
        [ProducesResponseType(typeof(ForgotPasswordResultDto), 200)]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordDto request, CancellationToken cancellationToken)
        {
            var result = await _storeAgentService.GeneratePasswordToken(request, cancellationToken);
            return Ok(result);
        }

        [HttpPost("change-password/{guid}/{hash}")]
        [ProducesResponseType(204)]
        public async Task<IActionResult> ChangePassword([FromRoute] Guid guid, [FromRoute] string hash, [FromQuery] string password, CancellationToken cancellationToken)
        {
            await _storeAgentService.ChangePassword(guid, hash, password, cancellationToken);
            return NoContent();
        }
    }
}
