using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Business.Concrete.WarehouseProductTransfer;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File;
using Ticimax.Warehouse.Entities.Dtos.Dashboard;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorization("Dashboard.AllWarehouseDashboard")]
    public class AdminDashboardController : ControllerBase
    {
        private readonly IDashboardService _dashboardService;

        public AdminDashboardController(IDashboardService dashboardService)
        {
            _dashboardService = dashboardService;
        }

        [HttpGet("order-counts")]
        public async Task<IActionResult> OrderProductTypeTemplate(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.OrderProductTypeTemplate(true, cancellationToken);
            return Ok(result);
        }

        [HttpGet("pickable-order")]
        [ProducesResponseType(typeof(PickableOrderDto), 200)]
        public async Task<IActionResult> OrderPickableCount([FromQuery] int storeId, CancellationToken cancellationToken)
        {
            var result = await _dashboardService.PickableOrder(true, storeId, cancellationToken);
            return Ok(result);
        }

        [HttpGet("order-sources")]
        public async Task<IActionResult> OrderSourceIncomingOrderGetCount(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.OrderSourceIncomingOrderGetCount(true, cancellationToken);
            return Ok(result);
        }

        [HttpGet("warehouse-product-transfer")]
        [ProducesResponseType(typeof(Pageable<WarehouseProductTransferReportDto>), 200)]
        public async Task<IActionResult> WarehouseProductTranser(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.WarehouseProductTranser(true, cancellationToken);
            return Ok(result);
        }
        [HttpGet("operation-speeds")]
        public async Task<IActionResult> OrderOperationSpeedGetCount(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.OrderOperationSpeedGetCount(true, cancellationToken);
            return Ok(result);
        }

        [HttpGet("warehouse-statics")]
        public async Task<IActionResult> ManagerQualityControlAndCollectedProductGetCount(CancellationToken cancellationToken)
        {
            var result = await _dashboardService.ManagerQualityControlAndCollectedProductGetCount(true, cancellationToken);
            return Ok(result);
        }
    }
}