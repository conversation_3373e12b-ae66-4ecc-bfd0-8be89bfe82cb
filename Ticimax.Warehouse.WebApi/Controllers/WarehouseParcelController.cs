using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Ticimax.Warehouse.Business.Abstract;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.WebApi.Attributes;

namespace Ticimax.Warehouse.WebApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorization("WarehouseManagement.TableManager.ShowTableContent.ShowParcelContent")]
    public class WarehouseParcelController : ControllerBase
    {
        private readonly IWarehouseParcelService _warehouseParcelService;
        private readonly IOrderCollectionService _orderCollectionService;

        public WarehouseParcelController(IWarehouseParcelService warehouseParcelService, IOrderCollectionService orderCollectionService)
        {
            _warehouseParcelService = warehouseParcelService;
            _orderCollectionService = orderCollectionService;
        }

        [HttpGet]
        public async Task<IActionResult> GetList([FromQuery] WarehouseParcelGetListDto request, [FromQuery] PagingDto paging, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseParcelService.GetList(request, paging, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.TableManager.ShowTableContent.EditParcel")]
        public async Task<IActionResult> Update([FromBody] WarehouseParcelEditDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseParcelService.Update(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.TableManager.ShowTableContent.AddParcel")]
        public async Task<IActionResult> Add([FromBody] WarehouseParcelAddDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseParcelService.Add(request, cancellationToken));
        }

        [HttpPost]
        [Authorization("WarehouseManagement.TableManager.ShowTableContent.DeleteParcel")]
        public async Task<IActionResult> Delete([FromBody] WarehouseParcelDeleteDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseParcelService.Delete(request, cancellationToken));
        }

        [HttpGet]
        public async Task<IActionResult> GetProducts([FromQuery] WarehouseParcelGetProductsDto request, CancellationToken cancellationToken)
        {
            var result = await _orderCollectionService.GetCollectionSet(
                new GetOrderCollectionSetDto
                {
                    Filter = new OrderCollectionSetFilter
                    {
                        BoxID = request.ID,
                        isGrouping = false,
                        PreparationStatus = 1
                    }
                }, cancellationToken);

            return Ok(result);
        }

        [HttpGet]
        public async Task<IActionResult> GetCount([FromQuery] WarehouseParcelGetListDto request, CancellationToken cancellationToken)
        {
            return Ok(await _warehouseParcelService.GetCount(request, cancellationToken));
        }
    }
}