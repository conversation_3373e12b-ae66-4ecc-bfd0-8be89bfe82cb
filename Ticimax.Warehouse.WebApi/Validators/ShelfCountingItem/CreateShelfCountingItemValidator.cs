using System;
using FluentValidation;
using Ticimax.Warehouse.Business.Concrete.ShelfCountingItem.Models.Request;

namespace Ticimax.Warehouse.WebApi.Validators.ShelfCountingItem
{
    public class CreateShelfCountingItemValidator : AbstractValidator<CreateShelfCountingItemRequest>
    {
        public CreateShelfCountingItemValidator()
        {
            RuleFor(x => x.FileId).NotEmpty().NotNull().NotEqual(Guid.Empty).WithErrorCode(ErrorCodes.ShelfCountingFileShelfIdsNotEmpty);
            RuleFor(x => x.ShelfId).NotEmpty().NotNull().WithErrorCode(ErrorCodes.ShelfCountingItemShelfIdNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfCountingItemShelfIdGreaterThan0);
            RuleFor(x => x.ProductId).NotEmpty().NotNull().WithErrorCode(ErrorCodes.ShelfCountingItemProductIdNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfCountingItemProductIdGreaterThan0);
            RuleFor(x => x.ProductOldCount).NotNull().WithErrorCode(ErrorCodes.ShelfCountingItemProductOldCountNotEmpty);
            RuleFor(x => x.ProductCount).NotNull().WithErrorCode(ErrorCodes.ShelfCountingItemProductCountNotEmpty);
        }
    }
}