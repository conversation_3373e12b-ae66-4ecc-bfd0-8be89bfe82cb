using FluentValidation;
using Ticimax.Warehouse.Business.Concrete.ShelfCountingItem.Models.Request;

namespace Ticimax.Warehouse.WebApi.Validators.ShelfCountingItem
{
    public class PickShelfCountingItemValidator : AbstractValidator<PickShelfCountingItemRequest>
    {
        public PickShelfCountingItemValidator()
        {
            RuleFor(x => x.Quantity).NotEmpty().NotNull().WithErrorCode(ErrorCodes.ShelfCountingItemQuantityNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfCountingItemQuantityGreaterThan0);
        }
    }
}