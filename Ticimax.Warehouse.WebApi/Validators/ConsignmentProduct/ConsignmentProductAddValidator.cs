using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.ConsignmentProduct
{
    public class ConsignmentProductAddValidator : AbstractValidator<ConsignmentProductControllerAddDto>
    {
        public ConsignmentProductAddValidator()
        {
            RuleFor(x => x.ProductCardID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ConsignmentProductProductCardIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ConsignmentProductProductCardIDGreaterThan0);
            RuleFor(x => x.ProductID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ConsignmentProductProductIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ConsignmentProductProductIDGreaterThan0);
            RuleFor(x => x.Stock).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ConsignmentProductStockNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ConsignmentProductStockGreaterThan0);
            RuleFor(x => x.SupplyDate).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ConsignmentProductSupplyDateNotEmpty);
        }
    }
}