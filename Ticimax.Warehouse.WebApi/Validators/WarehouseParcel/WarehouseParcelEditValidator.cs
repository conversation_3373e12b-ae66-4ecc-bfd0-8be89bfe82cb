using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseParcel
{
    public class WarehouseParcelEditValidator : AbstractValidator<WarehouseParcelEditDto>
    {
        public WarehouseParcelEditValidator()
        {
            RuleFor(x => x.ID).NotEmpty().WithErrorCode(ErrorCodes.WarehouseParcelIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseParcelIDGreaterThan0);
            RuleFor(x => x.Definition).MinimumLength(1).MaximumLength(50).WithErrorCode(ErrorCodes.WarehouseParcelDefinitionValueToBeRange1And50);
            RuleFor(x => x.Code).MinimumLength(1).MaximumLength(50).WithErrorCode(ErrorCodes.WarehouseParcelCodeValueToBeRange1And50);
            RuleFor(x => x.Barcode).MinimumLength(1).MaximumLength(50).WithErrorCode(ErrorCodes.WarehouseParcelBarcodeValueToBeRange1And50);
            RuleFor(x => x.Limit).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseParcelLimitGreaterThan0);
        }
    }
}