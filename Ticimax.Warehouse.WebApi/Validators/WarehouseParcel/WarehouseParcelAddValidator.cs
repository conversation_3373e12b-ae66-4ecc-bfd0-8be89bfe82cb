using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseParcel
{
    public class WarehouseParcelAddValidator : AbstractValidator<WarehouseParcelAddDto>
    {
        public WarehouseParcelAddValidator()
        {
            RuleFor(x => x.Definition).NotNull().NotEmpty().WithErrorCode(ErrorCodes.WarehouseParcelDefinitionNotEmpty).MinimumLength(1).MaximumLength(50).WithErrorCode(ErrorCodes.WarehouseParcelDefinitionValueToBeRange1And50);
            RuleFor(x => x.Code).NotNull().NotEmpty().WithErrorCode(ErrorCodes.WarehouseParcelCodeNotEmpty).MinimumLength(1).MaximumLength(50).WithErrorCode(ErrorCodes.WarehouseParcelCodeValueToBeRange1And50);
            RuleFor(x => x.Barcode).NotNull().NotEmpty().WithErrorCode(ErrorCodes.WarehouseParcelBarcodeNotEmpty).MinimumLength(1).MaximumLength(50).WithErrorCode(ErrorCodes.WarehouseParcelBarcodeValueToBeRange1And50);
            RuleFor(x => x.Limit).NotNull().NotEmpty().WithErrorCode(ErrorCodes.WarehouseParcelLimitNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseParcelLimitGreaterThan0);
            RuleFor(x => x.TableID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.WarehouseParcelTableIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseParcelTableIDGreaterThan0);
        }
    }
}