using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.ExcessProduct
{
    public class ExcessProductUpdateValidator : AbstractValidator<ExcessProductUpdateDto>
    {
        public ExcessProductUpdateValidator()
        {
            RuleFor(x => x.ExcessProductID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ExcessProductExcessProductIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ExcessProductExcessProductIDGreaterThan0);
            RuleFor(x => x.EntryDescription).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ExcessProductEntryDescriptionNotEmpty).MaximumLength(4000).MinimumLength(1).WithErrorCode(ErrorCodes.ExcessProductEntryDescriptionValueToBeRange1And4000);
            RuleFor(x => x.ExitDescription).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ExcessProductExitDescriptionNotEmpty).MaximumLength(4000).MinimumLength(1).WithErrorCode(ErrorCodes.ExcessProductExitDescriptionValueToBeRange1And4000);
        }
    }
}