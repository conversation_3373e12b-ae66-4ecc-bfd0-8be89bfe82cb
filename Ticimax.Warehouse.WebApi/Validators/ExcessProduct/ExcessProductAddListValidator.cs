using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.ExcessProduct
{
    public class ExcessProductAddListValidator : AbstractValidator<ExcessProductAddListDto>
    {
        public ExcessProductAddListValidator()
        {
            RuleFor(x => x.ExcessProducts).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ExcessProductExcessProductsNotEmpty);
            RuleFor(x => x.EntryDescription).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ExcessProductEntryDescriptionNotEmpty).MaximumLength(4000).MinimumLength(1).WithErrorCode(ErrorCodes.ExcessProductEntryDescriptionValueToBeRange1And4000);
        }
    }
}