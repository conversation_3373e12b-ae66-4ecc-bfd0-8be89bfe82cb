using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseBox
{
    public class WarehouseBoxDeleteValidator : AbstractValidator<WarehouseBoxDeleteDto>
    {
        public WarehouseBoxDeleteValidator()
        {
            RuleFor(x => x.ID).NotEmpty().WithErrorCode(ErrorCodes.WarehouseBoxIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseBoxIDGreaterThan0);
        }
    }
}