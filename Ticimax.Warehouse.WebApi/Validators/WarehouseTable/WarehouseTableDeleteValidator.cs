using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseTable
{
    public class WarehouseTableDeleteValidator : AbstractValidator<WarehouseTableDeleteDto>
    {
        public WarehouseTableDeleteValidator()
        {
            RuleFor(x => x.ID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.WarehouseTableUpdateIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseTableUpdateIDGreaterThan0);
        }
    }
}