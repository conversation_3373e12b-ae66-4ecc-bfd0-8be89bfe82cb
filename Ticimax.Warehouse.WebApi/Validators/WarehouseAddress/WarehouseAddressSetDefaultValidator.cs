using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseAddress
{
    public class WarehouseAddressSetDefaultValidator : AbstractValidator<WarehouseAddressSetDefaultDto>
    {
        public WarehouseAddressSetDefaultValidator()
        {
            RuleFor(x => x.ID).NotEmpty().WithErrorCode(ErrorCodes.WarehouseAddressIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseAddressIDGreaterThan0);
            RuleFor(x => x.TargetID).NotEmpty().WithErrorCode(ErrorCodes.WarehouseAddressTargetIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseAddressTargetIDGreaterThan0);
            RuleFor(x => x.Type).NotEmpty().WithErrorCode(ErrorCodes.WarehouseAddressTypeNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseAddressTypeGreaterThan0);
        }
    }
}