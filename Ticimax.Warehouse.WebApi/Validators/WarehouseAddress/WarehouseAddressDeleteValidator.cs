using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseAddress
{
    public class WarehouseAddressDeleteValidator : AbstractValidator<WarehouseAddressDeleteDto>
    {
        public WarehouseAddressDeleteValidator()
        {
            RuleFor(x => x.ID).NotEmpty().WithErrorCode(ErrorCodes.WarehouseAddressIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseAddressIDGreaterThan0);
        }
    }
}