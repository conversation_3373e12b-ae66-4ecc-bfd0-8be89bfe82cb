using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.CargoHourLimit
{
    public class CargoHourSetDeliveryHourValidator : AbstractValidator<CargoHourSetDeliveryHourDto>
    {
        public CargoHourSetDeliveryHourValidator()
        {
            RuleFor(x => x.OrderID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CargoHourLimitOrderIDNotEmpty);
            RuleFor(x => x.CargoHourID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CargoHourLimitCargoHourIDNotEmpty);
            RuleFor(x => x.DateStr).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CargoHourLimitDateStrNotEmpty);
        }
    }
}