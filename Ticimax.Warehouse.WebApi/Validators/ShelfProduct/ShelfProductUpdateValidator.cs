using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.ShelfProduct
{
    public class ShelfProductUpdateValidator : AbstractValidator<ShelfProductUpdateDto>
    {
        public ShelfProductUpdateValidator()
        {
            RuleFor(x => x.ID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ShelfProductIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfProductIDGreaterThan0);
            RuleFor(x => x.ShelfID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ShelfProductShelfIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfProductShelfIDGreaterThan0);
            RuleFor(x => x.ShelfStock).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ShelfProductShelfStockNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfProductShelfStockGreaterThan0);
        }
    }
}