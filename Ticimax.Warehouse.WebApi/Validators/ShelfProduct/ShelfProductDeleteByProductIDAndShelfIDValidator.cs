using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.ShelfProduct
{
    public class ShelfProductDeleteByProductIDAndShelfIDValidator : AbstractValidator<ShelfProductDeleteByProductIDAndShelfIDDto>
    {
        public ShelfProductDeleteByProductIDAndShelfIDValidator()
        {
            RuleFor(x => x.ProductID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ShelfProductProductIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfProductProductIDGreaterThan0);
            RuleFor(x => x.ShelfID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ShelfProductShelfIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfProductShelfIDGreaterThan0);
        }
    }
}