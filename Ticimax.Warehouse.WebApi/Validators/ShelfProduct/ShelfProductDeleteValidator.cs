using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.ShelfProduct
{
    public class ShelfProductDeleteValidator : AbstractValidator<ShelfProductDeleteDto>
    {
        public ShelfProductDeleteValidator()
        {
            RuleFor(x => x.ID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ShelfProductIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.ShelfProductIDGreaterThan0);
        }
    }
}