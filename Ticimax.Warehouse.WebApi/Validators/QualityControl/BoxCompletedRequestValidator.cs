using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.QualityControl
{
    public class BoxCompletedProductValidator : AbstractValidator<BoxCompletedProduct>
    {
        public BoxCompletedProductValidator()
        {
            RuleFor(x => x.ProductID).GreaterThan(0).WithErrorCode(ErrorCodes.QualityControlProductIDGreaterThan0);
            RuleFor(x => x.OrderID).GreaterThan(0).WithErrorCode(ErrorCodes.QualityControlOrderIDGreaterThan0);
            RuleFor(x => x.Piece).GreaterThan(0).WithErrorCode(ErrorCodes.QualityControlPieceGreaterThan0);
        }
    }
}