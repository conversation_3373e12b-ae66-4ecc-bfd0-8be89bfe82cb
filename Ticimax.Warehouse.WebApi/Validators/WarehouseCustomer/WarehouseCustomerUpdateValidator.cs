using System.Text.RegularExpressions;
using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseCustomer
{
    public class WarehouseCustomerUpdateValidator : AbstractValidator<WarehouseCustomerUpdateDto>
    {
        public WarehouseCustomerUpdateValidator()
        {
            RuleFor(x => x.Mail).EmailAddress().WithErrorCode(ErrorCodes.WarehouseCostumerMailTypeMustBeMail);
            RuleFor(x => x.Telephone).Matches(new Regex(@"^(((90))[-| ]?)?((\d{3})[-| ]?(\d{3})[-| ]?(\d{2})[-| ]?(\d{2}))$")).WithErrorCode(ErrorCodes.WarehouseCostumerTelephoneTypeMustBePhone);
        }
    }
}