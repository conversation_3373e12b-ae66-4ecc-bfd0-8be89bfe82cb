using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.Shelf
{
    public class ShelfUpdateValidator : AbstractValidator<ShelfUpdateDto>
    {
        public ShelfUpdateValidator()
        {
            RuleFor(x => x.ParentId).NotNull().WithErrorCode(ErrorCodes.ShelfParentIdNotEmpty);
            RuleFor(x => x.Definition).NotNull().NotEmpty().WithErrorCode(ErrorCodes.ShelfDefinitionNotEmpty).MinimumLength(1).MaximumLength(100).WithErrorCode(ErrorCodes.ShelfDefinitionValueToBeRange1And100);
            RuleFor(x => x.Code).NotEmpty().WithErrorCode(ErrorCodes.ShelfCodeNotEmpty).MinimumLength(1).MaximumLength(100).WithErrorCode(ErrorCodes.ShelfCodeValueToBeRange1And100);
            RuleFor(x => x.Barcode).NotEmpty().WithErrorCode(ErrorCodes.ShelfBarcodeNotEmpty).MinimumLength(1).MaximumLength(100).WithErrorCode(ErrorCodes.ShelfBarcodeValueToBeRange1And100);
            RuleFor(x => x.Rank).NotNull().WithErrorCode(ErrorCodes.ShelfRankNotEmpty);
            RuleFor(x => x.IsMissingProductShelf).NotNull().WithErrorCode(ErrorCodes.ShelfIsMissingProductShelfNotEmpty);
        }
    }
}