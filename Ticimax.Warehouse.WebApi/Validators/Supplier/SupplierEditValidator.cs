using System.Text.RegularExpressions;
using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.Supplier
{
    public class SupplierEditValidator : AbstractValidator<SupplierEditDto>
    {
        public SupplierEditValidator()
        {
            RuleFor(x => x.Definition).NotNull().NotEmpty().WithErrorCode(ErrorCodes.SupplierDefinitionNotEmpty).MaximumLength(60).MinimumLength(3).WithErrorCode(ErrorCodes.SupplierDefinitionValueToBeRange3And255);
            RuleFor(x => x.MailAddress).EmailAddress().WithErrorCode(ErrorCodes.SupplierMailAddressTypeMustBeMail);
            RuleFor(x => x.TelephoneNumber).MaximumLength(30).MinimumLength(10).WithErrorCode(ErrorCodes.SupplierTelephoneNumberValueToBeRange10And30).Matches(new Regex(@"^(((90))[-| ]?)?((\d{3})[-| ]?(\d{3})[-| ]?(\d{2})[-| ]?(\d{2}))$")).WithErrorCode(ErrorCodes.SupplierTelephoneNumberTypeMustBePhone);
        }
    }
}