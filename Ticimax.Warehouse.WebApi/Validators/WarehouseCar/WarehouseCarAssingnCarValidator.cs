using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseCar
{
    public class WarehouseCarAssingnCarValidator : AbstractValidator<WarehouseCarAssingnCarDto>
    {
        public WarehouseCarAssingnCarValidator()
        {
            RuleFor(x => x.Barcode).NotNull().NotEmpty().WithErrorCode(ErrorCodes.WarehouseCarBarcodeNotEmpty);
        }
    }
}