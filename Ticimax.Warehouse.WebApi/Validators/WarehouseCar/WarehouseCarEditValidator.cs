using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.WarehouseCar
{
    public class WarehouseCarEditValidator : AbstractValidator<WarehouseCarEditDto>
    {
        public WarehouseCarEditValidator()
        {
            RuleFor(x => x.ID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.WarehouseCarIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.WarehouseCarIDGreaterThan0);
            RuleFor(x => x.Barcode).MinimumLength(1).MaximumLength(20).WithErrorCode(ErrorCodes.WarehouseCarBarcodeValueToBeRange1And20);
            RuleFor(x => x.Definition).MinimumLength(1).MaximumLength(100).WithErrorCode(ErrorCodes.WarehouseCarDefinitionValueToBeRange1And100);
        }
    }
}