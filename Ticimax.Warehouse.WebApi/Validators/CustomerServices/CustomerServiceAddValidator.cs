using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.CustomerServices
{
    public class CustomerServiceAddValidator : AbstractValidator<CustomerServiceAddDto>
    {
        public CustomerServiceAddValidator()
        {
            RuleFor(x => x.Type).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesTypeNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.CostumerServicesTypeGreaterThan0);
            RuleFor(x => x.OrderID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesOrderIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.CostumerServicesOrderIDGreaterThan0);
            RuleFor(x => x.OrderNo).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesOrderNoNotEmpty).MaximumLength(50).MinimumLength(1).WithErrorCode(ErrorCodes.CostumerServicesOrderNoValueToBeRange1And50);
            RuleFor(x => x.StatusID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesStatusIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.CostumerServicesStatusIDGreaterThan0);
            RuleFor(x => x.CallByMemberID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesCallByMemberIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.CostumerServicesCallByMemberIDGreaterThan0);
            RuleFor(x => x.CallingMemberName).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesCallingMemberNameNotEmpty).MaximumLength(255).MinimumLength(1).WithErrorCode(ErrorCodes.CostumerServicesCallingMemberNameValueToBeRange1And255);
            RuleFor(x => x.CallByPhoneNumber).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesCallByPhoneNumberNotEmpty).MaximumLength(255).MinimumLength(1).WithErrorCode(ErrorCodes.CostumerServicesCallByPhoneNumberValueToBeRange1And255);
            RuleFor(x => x.OrderDeliveryPhone).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesOrderDeliveryPhoneNotEmpty).MaximumLength(50).MinimumLength(1).WithErrorCode(ErrorCodes.CostumerServicesOrderDeliveryPhoneValueToBeRange1And50);
            RuleFor(x => x.ReturnAmount).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesReturnAmountNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.CostumerServicesReturnAmountGreaterThan0);
            RuleFor(x => x.PaymentType).NotNull().NotEmpty().WithErrorCode(ErrorCodes.CostumerServicesPaymentTypeNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.CostumerServicesPaymentTypeGreaterThan0);
        }
    }
}