using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.PickingProduct
{
    public class OrderCompletedValidator : AbstractValidator<OrderCompletedDto>
    {
        public OrderCompletedValidator()
        {
            RuleFor(x => x.OrderID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.PickingProductOrderIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.PickingProductOrderIDGreaterThan0);
        }
    }
}