using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.PickingProduct
{
    public class MissingProductDeleteRequestValidator : AbstractValidator<MissingProductDeleteRequestDto>
    {
        public MissingProductDeleteRequestValidator()
        {
            RuleFor(x => x.ID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.PickingProductIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.PickingProductIDGreaterThan0);
            RuleFor(x => x.OrderID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.PickingProductOrderIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.PickingProductOrderIDGreaterThan0);
            RuleFor(x => x.OrderProductID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.PickingProductOrderProductIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.PickingProductOrderProductIDGreaterThan0);
            RuleFor(x => x.ProductID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.PickingProductProductIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.PickingProductProductIDGreaterThan0);
        }
    }
}