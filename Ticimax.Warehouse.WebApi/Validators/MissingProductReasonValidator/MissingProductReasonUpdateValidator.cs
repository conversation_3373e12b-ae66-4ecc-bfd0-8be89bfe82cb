using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.MissingProductReasonValidator
{
    public class MissingProductReasonUpdateValidator : AbstractValidator<MissingProductReasonUpdateDto>
    {
        public MissingProductReasonUpdateValidator()
        {
            RuleFor(x => x.ID).NotNull().NotEmpty().WithErrorCode(ErrorCodes.MissingProductReasonIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.MissingProductReasonIDGreaterThan0);
            RuleFor(x => x.Definiton).NotNull().NotEmpty().WithErrorCode(ErrorCodes.MissingProductReasonDefinitionNotEmpty);
            RuleFor(x => x.isActive).NotNull().WithErrorCode(ErrorCodes.MissingProductReasonisActiveNotEmpty);
        }
    }
}