using FluentValidation;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.WebApi.Validators.StoreAgent
{
    public class StoreAgentUpdateValidator : AbstractValidator<StoreAgentUpdateDto>
    {
        public StoreAgentUpdateValidator()
        {
            RuleFor(x => x.ID).NotEmpty().WithErrorCode(ErrorCodes.StoreAgentIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.StoreAgentIDGreaterThan0);
            RuleFor(x => x.Name).MinimumLength(1).MaximumLength(255).WithErrorCode(ErrorCodes.StoreAgentNameValueToBeRange1And255);
            RuleFor(x => x.LastName).MaximumLength(255).WithErrorCode(ErrorCodes.StoreAgentLastNameValueToBeRange1And255);
            RuleFor(x => x.TypeID).NotEmpty().WithErrorCode(ErrorCodes.StoreAgentTypeIDNotEmpty).GreaterThan(0).WithErrorCode(ErrorCodes.StoreAgentTypeIDGreaterThan0);
            RuleFor(x => x.Username).MinimumLength(1).MaximumLength(100).WithErrorCode(ErrorCodes.StoreAgentUsernameValueToBeRange1And100);
            RuleFor(x => x.Password).MinimumLength(1).MaximumLength(100).WithErrorCode(ErrorCodes.StoreAgentPasswordValueToBeRange1And100);
            RuleFor(x => x.Email).NotNull().NotEmpty().WithErrorCode(ErrorCodes.StoreAgentEmailCanNotBeNotEmpty);
        }
    }
}