using Microsoft.AspNetCore.SignalR;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Ticimax.Warehouse.Business.Concrete.WarehouseChatHub.Events;
using Ticimax.Warehouse.Business.Concrete.WarehouseChatHub.Enums;
using Npgsql;
using NpgsqlTypes;
using Microsoft.Extensions.Options;
using Ticimax.Core.CrossCuttingConcerns.Caching;
using Microsoft.Extensions.Logging;

namespace Ticimax.Warehouse.WebApi.WarehousesChatHub
{
    public class WarehousesChatHub : Hub
    {
        private readonly string _postgreConnectionString;
        private readonly ICacheManager _cacheManager;
        private readonly ILogger<WarehousesChatHub> _logger;
        public WarehousesChatHub(IOptions<NpgsqlConnection> cnn, ICacheManager cacheManager, ILogger<WarehousesChatHub> logger)
        {
            _postgreConnectionString = cnn.Value.ConnectionString;
            _cacheManager = cacheManager;
            _logger = logger;
        }
        public override async Task OnConnectedAsync()
        {

            var httpContext = Context.GetHttpContext();
            string domainName = httpContext?.Request.Query["domainName"].ToString();
            int userId = int.Parse(httpContext?.Request.Query["userId"].ToString());
            try
            {
                string registerUserKey = "warehousechat-" + domainName + "-" + userId;
                SignalRCacheResponse cacheResponse = new SignalRCacheResponse(Context.ConnectionId);
                await _cacheManager.Remove(registerUserKey);
                await _cacheManager.Add(registerUserKey, cacheResponse, 60 * 120);
                SignalRConnectionCacheResponse signalRcacheResponse = new SignalRConnectionCacheResponse(registerUserKey);
                await _cacheManager.Remove(Context.ConnectionId);
                await _cacheManager.Add(Context.ConnectionId, signalRcacheResponse, 60 * 120);
            }
            catch (Exception ex)
            {
                _logger.LogError($"[error] SignalR-RegisterUser-{userId} - {domainName} - {ex.Message}");
            }
        }

        public async Task SendMessage(Guid connectionId, string message, int senderId, string receiverIds, string domainName)
        {
            try
            {
                List<int> receiverIdsList = receiverIds.Split(',').Select(x => int.Parse(x)).ToList();
                string signalRConnectionId = string.Empty;

                if (receiverIdsList != null && receiverIdsList.Any())
                {
                    int receiverId = receiverIdsList.FirstOrDefault();
                    string userKey = "warehousechat-" + domainName + "-" + receiverId;
                    var cacheResponse = await _cacheManager.Get<SignalRCacheResponse>(userKey);
                    if (cacheResponse != null)
                    {
                        if (!string.IsNullOrEmpty(cacheResponse.SignalRConnectionId))
                            signalRConnectionId = cacheResponse.SignalRConnectionId;
                    }
                }


                WarehouseChatEventPayload payload =
                    new WarehouseChatEventPayload(domainName, message, senderId, connectionId);


                var postgreConnection = new NpgsqlConnection(_postgreConnectionString);
                await postgreConnection.OpenAsync();

                var postgreTransaction = await postgreConnection.BeginTransactionAsync();


                var cmd = new NpgsqlCommand();
                cmd.CommandTimeout = 600;
                cmd.Connection = postgreConnection;
                cmd.Transaction = postgreTransaction;
                cmd.CommandText = @$"INSERT INTO outbox_message(ID, EVENT_NAME,AGGREGATE_ID,VERSION,EVENT,TIMESTAMP,
                                        CORRELATION_ID,IS_SENT) VALUES (@ID,@EVENT_NAME,@AGGREGATE_ID,@VERSION,@EVENT,@TIMESTAMP,@CORRELATION_ID,@IS_SENT)";

                cmd.Parameters.Add($"@ID", NpgsqlDbType.Uuid).Value = Guid.NewGuid();
                cmd.Parameters.Add($"@EVENT_NAME", NpgsqlDbType.Varchar).Value = WarehouseChatHubEnums.Created;
                cmd.Parameters.Add($"@AGGREGATE_ID", NpgsqlDbType.Uuid).Value = payload.Id;
                cmd.Parameters.Add($"@VERSION", NpgsqlDbType.Bigint).Value = payload.Version;
                cmd.Parameters.Add($"@EVENT", NpgsqlDbType.Text).Value = payload.ToJsonSerialize();
                cmd.Parameters.Add($"@TIMESTAMP", NpgsqlDbType.Bigint).Value = payload.Timestamp;
                cmd.Parameters.Add($"@CORRELATION_ID", NpgsqlDbType.Varchar).Value = payload.CorrelationId ?? "";
                cmd.Parameters.Add($"@IS_SENT", NpgsqlDbType.Boolean).Value = false;


                await cmd.ExecuteNonQueryAsync();
                await cmd.DisposeAsync();

                await postgreTransaction.CommitAsync();
                await postgreTransaction.DisposeAsync();
                await postgreConnection.CloseAsync();

                if (!string.IsNullOrEmpty(signalRConnectionId))
                {
                    await Clients.Client(signalRConnectionId).SendAsync("ReceiveMessage", payload);
                }
            }
            catch (Exception e)
            {
                _logger.LogError($"[error] SignalR-SendMessage-{senderId} - {receiverIds} - {connectionId} - {message} - {domainName} - {e.Message}");

            }
        }

        public async Task AllReadAsync(Guid connectionId, int receiverId, string senderIds, string domainName)
        {
            try
            {
                List<int> senderIdsList = senderIds.Split(',').Select(x => int.Parse(x)).ToList();
                string signalRConnectionId = string.Empty;
                if (senderIdsList != null && senderIdsList.Any())
                {
                    int senderId = senderIdsList.FirstOrDefault();
                    string userKey = "warehousechat-" + domainName + "-" + senderId;
                    var cacheResponse = await _cacheManager.Get<SignalRCacheResponse>(userKey);
                    if (cacheResponse != null)
                    {
                        if (!string.IsNullOrEmpty(cacheResponse.SignalRConnectionId))
                            signalRConnectionId = cacheResponse.SignalRConnectionId;
                        else
                            _logger.LogError($"[error] SignalR-AllReadAsync-NullSignalRConnectionId-{receiverId} - {senderIds} - {domainName}");
                    }
                    else
                        _logger.LogError($"[error] SignalR-AllReadAsync-NullSignalRConnectionId-{receiverId} - {senderIds} - {domainName}");
                }

                WarehouseChatAllReadEventPayload payload =
                    new WarehouseChatAllReadEventPayload(domainName, connectionId, receiverId);

                var postgreConnection = new NpgsqlConnection(_postgreConnectionString);
                await postgreConnection.OpenAsync();

                var postgreTransaction = await postgreConnection.BeginTransactionAsync();

                var cmd = new NpgsqlCommand();
                cmd.CommandTimeout = 600;
                cmd.Connection = postgreConnection;
                cmd.Transaction = postgreTransaction;
                cmd.CommandText = @$"INSERT INTO outbox_message(ID, EVENT_NAME,AGGREGATE_ID,VERSION,EVENT,TIMESTAMP,
                                        CORRELATION_ID,IS_SENT) VALUES (@ID,@EVENT_NAME,@AGGREGATE_ID,@VERSION,@EVENT,@TIMESTAMP,@CORRELATION_ID,@IS_SENT)";

                cmd.Parameters.Add($"@ID", NpgsqlDbType.Uuid).Value = Guid.NewGuid();
                cmd.Parameters.Add($"@EVENT_NAME", NpgsqlDbType.Varchar).Value = WarehouseChatHubEnums.AllRead;
                cmd.Parameters.Add($"@AGGREGATE_ID", NpgsqlDbType.Uuid).Value = payload.Id;
                cmd.Parameters.Add($"@VERSION", NpgsqlDbType.Bigint).Value = payload.Version;
                cmd.Parameters.Add($"@EVENT", NpgsqlDbType.Text).Value = payload.ToJsonSerialize();
                cmd.Parameters.Add($"@TIMESTAMP", NpgsqlDbType.Bigint).Value = payload.Timestamp;
                cmd.Parameters.Add($"@CORRELATION_ID", NpgsqlDbType.Varchar).Value = payload.CorrelationId ?? "";
                cmd.Parameters.Add($"@IS_SENT", NpgsqlDbType.Boolean).Value = false;


                await cmd.ExecuteNonQueryAsync();
                await cmd.DisposeAsync();

                await postgreTransaction.CommitAsync();
                await postgreTransaction.DisposeAsync();
                await postgreConnection.CloseAsync();

                if (!string.IsNullOrEmpty(signalRConnectionId))
                {
                    await Clients.Client(signalRConnectionId).SendAsync("AllReadMessageMessage", payload);
                }
            }
            catch (Exception e)
            {
                _logger.LogError($"[error] SignalR-AllReadAsync-{receiverId} - {senderIds} - {domainName} - {e.Message}");
            }
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            var userKeyCacheResponse =
                await _cacheManager.Get<SignalRConnectionCacheResponse>(Context.ConnectionId);

            if (userKeyCacheResponse != null)
            {
                if (!string.IsNullOrEmpty(userKeyCacheResponse.UserKey))
                {
                    var signalRCacheResponse =
                        await _cacheManager.Get<SignalRCacheResponse>(userKeyCacheResponse.UserKey);
                    if (signalRCacheResponse != null)
                    {
                        if (!string.IsNullOrEmpty(signalRCacheResponse.SignalRConnectionId))
                        {
                            await _cacheManager.Remove(signalRCacheResponse.SignalRConnectionId);
                        }
                    }
                    await _cacheManager.Remove(userKeyCacheResponse.UserKey);
                }
            }
        }
    }
}