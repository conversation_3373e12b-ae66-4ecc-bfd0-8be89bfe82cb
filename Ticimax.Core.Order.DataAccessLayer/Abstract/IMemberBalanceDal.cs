using MySqlConnector;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;

namespace Ticimax.Core.Order.DataAccessLayer.Abstract
{
    public interface IMemberBalanceDal
    {
        Task Add(MemberBalance entity, CancellationToken cancellationToken);

        Task<int> GetCount(MemberBalanceFilter filter = null, CancellationToken cancellationToken = default);

        Task<IList<MemberBalance>> GetList(MemberBalanceFilter filter = null, MemberBalancePaging paging = null, CancellationToken cancellationToken = default);

    }
}