using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;

namespace Ticimax.Core.Order.DataAccessLayer.Abstract
{
    public interface IOrderDal
    {
        Task<List<Entities.Concrete.Order>> GetList(OrderFilter filter = null, OrderPaging paging = null, CancellationToken cancellationToken = default);

        Task<int> GetCount(OrderFilter filter = null, CancellationToken cancellationToken = default);

        Task SetInvoiceNo(Entities.Concrete.Order entity, CancellationToken cancellationToken);

        Task<IList<int>> GetOneProductOrder(CancellationToken cancellationToken);

        Task<IList<int>> GetALotsOfProductOrder(CancellationToken cancellationToken);

        Task SetPackagingStatus(List<int> OrderIDs, PackageStatus status, int statusID, CancellationToken cancellationToken);

        Task SetOrderCompleatedPackageStatus(int orderID, int packagingStatus, CancellationToken cancellationToken);

        Task UpdateOrderCompleatedAmount(int orderID, OrderProduct orderProduct, CancellationToken cancellationToken);

        Task MissingProduct(int orderID, int packagingStatus, CancellationToken cancellationToken);

        Task UpdateAdditionalInformation(Entities.Concrete.Order entity, CancellationToken cancellationToken);

        Task<IList<OrderListItemDto>> GetOrderReport(OrderReportFilter filter, CancellationToken cancellationToken);

        Task<double> GetOrderTotalAmount(OrderReportFilter filter, CancellationToken cancellationToken);

        Task<int> GetOrderTotalCount(OrderReportFilter filter, CancellationToken cancellationToken);

        Task<int> GetCanceledOrderTotalCount(OrderCanceledOrderTotalCountFilter filter, CancellationToken cancellationToken);

        Task<int> GetDeliveredOrderTotalCount(OrderDeliveredOrderTotalCountFilter filter, CancellationToken cancellationToken);

        Task UpdateOrderSpecialField3(int orderID, string field, CancellationToken cancellationToken);

        Task UpdateOrderPreparedID(List<int> OrderIDs, int PreparedID, CancellationToken cancellationToken);

        Task UpdateOrderDeliveryHour(int orderID, DateTime date, string deliveryHour, int cargoHourID, CancellationToken cancellationToken);

        Task UpdateOrderStore(int orderId, int storeId, bool pickingChange, CancellationToken cancellationToken);

        Task<int> OrdersPerPerson(int storeID, int cargoType, bool storeCondition, CancellationToken cancellationToken);

        Task<List<int>> CollectableOrders(int Limit, bool storeCondition, CancellationToken cancellationToken);

        Task UpdateCargoStoreCurrier(int orderID, int currierID, CancellationToken cancellationToken);

        Task UpdateAmount(OrderUpdateAmountDto request, CancellationToken cancellationToken);

        Task OrderReset(int OrderID, CancellationToken cancellationToken);

        Task<OrderDynamicNoteDto> GetOrderDynamicNote(int orderID, CancellationToken cancellationToken);

        Task<OrderGiftNoteDto> GetOrderGiftNote(int orderID, CancellationToken cancellationToken);

        Task UpdateOrderAmounts(UpdateOrderAmountsDto request, CancellationToken cancellationToken);

        Task<IList<string>> GetOrderSources(OrderFilter filter = null, OrderPaging paging = null, CancellationToken cancellationToken = default);

        Task CalculationAmount(int ID, CancellationToken cancellationToken);

        Task<int> GetOrderPreparedUser(int ıD, string username, int storeID, int warehouseID, CancellationToken cancellationToken);

        Task<int> GetCountAsync(OrderFilter filter = null, CancellationToken cancellationToken = default);

        Task OrderProductReset(int orderProductID, int id, CancellationToken cancellationToken);
        Task<List<OrderInvoiced>> GetInvoicedOrderList(InvoicedOrderFilter filter, OrderPaging paging = null, CancellationToken cancellationToken = default);
        Task UpdateOrderCargoIntegration(int orderId, int cargoIntegrationId, CancellationToken cancellationToken);
        Task UpdateOrderCargoIntegrationAndCargoCompany(int orderId, int cargoIntegrationId, int cargoCompanyId, CancellationToken cancellationToken);
        Task<int> GetOrderStatusByOrderIdAsync(int orderId, CancellationToken cancellationToken);
        Task<List<OrderStatusResult>> GetOrderStatusList(OrderStatusFilter filter, CancellationToken cancellationToken = default);

    }
}