using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;

namespace Ticimax.Core.Order.DataAccessLayer.Abstract
{
    public interface IOrderPaymentDal
    {
        Task Add(OrderPayment entity, CancellationToken cancellationToken);

        Task<int> GetCount(OrderPaymentFilter filter = null, CancellationToken cancellationToken = default);

        Task<List<OrderPayment>> GetList(OrderPaymentFilter filter = null, OrderPaymentPaging paging = null, CancellationToken cancellationToken = default);

        Task UpdateApproved(OrderPayment entity, CancellationToken cancellationToken);
    }
}