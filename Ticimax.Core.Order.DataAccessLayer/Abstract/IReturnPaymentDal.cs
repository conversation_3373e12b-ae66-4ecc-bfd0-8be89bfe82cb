using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;

namespace Ticimax.Core.Order.DataAccessLayer.Abstract
{
    public interface IReturnPaymentDal
    {
        Task Add(ReturnPayment entity, CancellationToken cancellationToken);

        Task<int> AddReturnID(ReturnPayment entity, CancellationToken cancellationToken);

        Task CustomerPayDoorRed(int customerID, CancellationToken cancellationToken);

        Task Delete(ReturnPayment entity, CancellationToken cancellationToken);

        Task<int> GetCount(ReturnPaymentFilter filter = null, CancellationToken cancellationToken = default);

        Task<List<ReturnPayment>> GetList(ReturnPaymentFilter filter = null, ReturnPaymentPaging paging = null, CancellationToken cancellationToken = default);

        Task Update(ReturnPayment entity, CancellationToken cancellationToken);

        Task UpdatePaid(ReturnPayment entity, CancellationToken cancellationToken);
    }
}