using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;

namespace Ticimax.Core.Order.DataAccessLayer.Abstract
{
    public interface IBalanceDal
    {
        Task Add(Balance entity, CancellationToken cancellationToken);
        Task<int> GetCount(BalanceFilter filter = null, CancellationToken cancellationToken = default);
        Task<List<Balance>> GetList(BalanceFilter filter = null, BalancePaging paging = null, CancellationToken cancellationToken = default);
        Task ReduceTheRemainingRefundAmount(Balance entity, CancellationToken cancellationToken);
    }
}