using MySqlConnector;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System;
using Ticimax.Core.Abstract;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;

namespace Ticimax.Core.Order.DataAccessLayer.Abstract
{
    public interface IOrderCargoDesiDal
    {
        Task Add(OrderCargoDesi entity, CancellationToken cancellationToken);

        Task<int> GetCount(OrderCargoDesiFilter filter = null, CancellationToken cancellationToken = default);

        Task<List<OrderCargoDesi>> GetList(OrderCargoDesiFilter filter = null, OrderCargoDesiPaging paging = null, CancellationToken cancellationToken = default);
    }
}