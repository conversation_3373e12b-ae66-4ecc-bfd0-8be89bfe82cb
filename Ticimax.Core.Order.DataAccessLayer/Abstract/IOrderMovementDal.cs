using MySqlConnector;
using System.Collections.Generic;
using System;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Abstract;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;

namespace Ticimax.Core.Order.DataAccessLayer.Abstract
{
    public interface IOrderMovementDal
    {
        Task AddAsync(OrderMovement entity, CancellationToken cancellationToken);

        Task DeleteMovementOnOrder(int orderID, CancellationToken cancellationToken);

        Task<int> GetCountAsync(OrderMovementFilter filter = null, CancellationToken cancellationToken = default);

        Task<IList<OrderMovement>> GetListAsync(OrderMovementFilter filter = null, OrderMovementPaging paging = null, CancellationToken cancellationToken = default);
    }
}