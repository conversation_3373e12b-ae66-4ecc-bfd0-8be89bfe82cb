using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Entities.Concrete;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.DataAccessLayer.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Static;
using static iText.StyledXmlParser.Jsoup.Select.Evaluator;

namespace Ticimax.Core.Order.DataAccessLayer.Concrete.MySQL
{
    public class OrderDal : IOrderDal
    {
        private readonly MySqlConnection _cnn;

        public OrderDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<Entities.Concrete.Order>> GetList(OrderFilter filter = null, OrderPaging paging = null, CancellationToken cancellationToken = default)
        {
            if (filter == null)
            {
                return null;
            }

            List<Entities.Concrete.Order> list = new List<Entities.Concrete.Order>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandTimeout = 600;
            cmd.CommandText = $@"SELECT s.ID
                                , s.SIPARISNO
                                , s.UYE_ID
                                , s.UYEADI  
                                , s.UYEMAIL
                                , s.UYECEP
                                , s.TARIH
                                , s.TESLIMATGUNU
				                , s.TESLIMATSAATI
                                , s.SIPARISNOTU
                                , s.SIPARISPANELNOTU
                                , s.URUNYOKDURUM_ID
                                , s.KARGOFIRMA_ID
                                , s.KARGOENTEGRASYON_ID
                                , s.SEPETHAZIRLAYAN_ID
                                , s.KARGOMAGAZAKURYE_ID
                                , s.FATURAADRESI
                                , s.TESLIMATADRESI
                                , s.HEDIYEPAKETIVAR
                                , s.HEDIYEPAKETINOTU
                                , s.HEDIYEPAKETTUTARI
                                , {GetToplamTutarQuery("s", false, false, false)} AS TOPLAMTUTAR
                                , (SELECT SUM(siparis_urun.ADET) FROM siparis_urun WHERE siparis_urun.SIPARIS_ID = s.ID AND siparis_urun.DURUM <> 2 AND siparis_urun.KARGOTIPI = @KARGOTIPI {GetAdminDashboardQueryColumn(filter.IsAdminDashboard)}) AS URUNSAYISI
                                , s.ODEMETIPI
                                , s.EKBILGI
                                , ({GetToplamTutarQuery("s", false, false, false)}
                                        - {GetOdenenTutarQuery("s", false)}) AS BAKIYE
                                , s.INDIRIMTUTARI
                                , s.SEPETKAMPANYASIINDIRIMI
                                , s.HEDIYECEKI
                                , s.HEDIYECEKITUTARI
                                , s.SIPARISKAYNAGI
                                , s.KARGOTUTARI
                                , s.ENTEGRASYONAKTARILDI
                                , s.PARABIRIMI
                                , s.FATURANO
                                , s.FATURATARIHI
                                , s.DURUM
                                , s.SIPARISKODU
                                , s.TESLIMATMAGAZA_ID
                                , s.TUTAR
                                , s.TOPLAMKDV
                                , s.GUMRUKVERGISITUTARI
                                , s.EKVERGITUTARI
                                , s.INDIRIMDAGITILDI
                                , s.PUANKULLANIM
                                , s.HOPIKAMPANYA
                                , s.OZELALAN1
                                , s.OZELALAN2
                                , s.OZELALAN3
                                , IFNULL((SELECT ID FROM siparis_kargo_paket AS skp WHERE skp.Siparis_ID = s.ID ORDER BY skp.ID DESC LIMIT 1), 0) AS PAKETID
                                , IFNULL((SELECT KARGO_PAKET_ID FROM siparis_urun AS sipu WHERE sipu.SIPARIS_ID = s.ID AND sipu.MAGAZA_ID = @MAGAZA_ID ORDER BY sipu.ID DESC LIMIT 1), 0) AS PAKETID2
                                , IFNULL((SELECT Barkod_Bilgisi FROM siparis_kargo_paket AS skp WHERE skp.Siparis_ID = s.ID ORDER BY skp.ID DESC LIMIT 1), '') AS KARGOKODU
                                , IFNULL((SELECT GROUP_CONCAT(skp.Barkod_Bilgisi SEPARATOR ', ') FROM siparis_kargo_paket AS skp WHERE skp.Siparis_ID = s.ID), '') AS KARGOKODUSTR
                                , IFNULL((SELECT Kargo_Takip_Numarasi FROM siparis_kargo_paket AS skp WHERE skp.Siparis_ID = s.ID ORDER BY skp.ID DESC LIMIT 1),'') AS KARGOTAKIPNUMARASI
                                , IFNULL((SELECT u.OZELNOT FROM uyeler AS u WHERE u.ID = s.UYE_ID AND u.ID > 0),'') AS UYEOZELNOT
                                , (SELECT ISLEM FROM paketleme_durum WHERE ID = s.PAKETLEMEDURUM_ID) AS PAKETLEMEDURUM
                            FROM siparis AS s 
                            WHERE 1 
                            AND IFNULL((SELECT 1 FROM siparis_odeme AS so WHERE so.SIPARIS_ID = s.ID AND so.ONAYLANDI = 1 LIMIT 1),0) > 0
                            AND s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID {GetAdminDashboardWhereQuery(filter.IsAdminDashboard)})";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            AppendFilter(ref cmd, filter);

            if (paging != null)
            {
                if (!string.IsNullOrEmpty(paging.SortingValue))
                    cmd.CommandText += $" ORDER BY {paging.SortingValue}";

                if (!string.IsNullOrEmpty(paging.SortingDirection))
                    cmd.CommandText += $" {paging.SortingDirection}";
            }

            PagingExtension.AppendPaging(ref cmd, paging);

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                Entities.Concrete.Order p = new Entities.Concrete.Order();
                p.ID = mdr["ID"].ToInt32();
                p.OrderNo = mdr["SIPARISNO"].ToString();
                p.MemberID = mdr["UYE_ID"].ToInt32();
                p.MemberSpecialNote = mdr["UYEOZELNOT"].ToString();
                p.Customer = mdr["UYEADI"].ToString();
                p.CustomerMail = mdr["UYEMAIL"].ToString();
                p.CustomerTelephone = mdr["UYECEP"].ToString();
                p.DeliveryDate = mdr["TESLIMATGUNU"] != DBNull.Value ? Convert.ToDateTime(mdr["TESLIMATGUNU"]).ToString("dd.MM.yyyy") : "";
                p.DeliveryHour = mdr["TESLIMATSAATI"].ToString();
                p.Date = mdr["TARIH"].ToDateTime();
                p.TotalAmount = mdr["TOPLAMTUTAR"].ToDouble();
                p.Balance = mdr["BAKIYE"].ToDouble();
                p.GiftPackageAmount = mdr["HEDIYEPAKETTUTARI"].ToDouble();
                p.GiftVoucherAmount = mdr["HEDIYECEKITUTARI"].ToDouble();
                p.CargoAmount = mdr["KARGOTUTARI"].ToDouble();
                p.DiscounthAmount = mdr["INDIRIMTUTARI"].ToDouble();
                p.BasketDiscounthAmount = mdr["SEPETKAMPANYASIINDIRIMI"].ToDouble();
                p.ProductCount = mdr["URUNSAYISI"] != DBNull.Value && mdr["URUNSAYISI"] != null ? mdr["URUNSAYISI"].ToInt32() : 0;
                p.OrderNote = mdr["SIPARISNOTU"].ToString();
                p.OrderPanelNote = mdr["SIPARISPANELNOTU"].ToString();
                p.CargoCompanyID = mdr["KARGOFIRMA_ID"].ToInt32();
                p.CargoIntegrationId = mdr["KARGOENTEGRASYON_ID"].ToInt32();
                p.PickerID = mdr["SEPETHAZIRLAYAN_ID"].ToInt32();
                p.CourierID = mdr["KARGOMAGAZAKURYE_ID"].ToInt32();
                p.InvoiceAddress = mdr["FATURAADRESI"] != DBNull.Value ? mdr["FATURAADRESI"].ToJsonDeserialize<MemberAddress>() : null;
                p.DeliveryAddress = mdr["TESLIMATADRESI"] != DBNull.Value ? mdr["TESLIMATADRESI"].ToJsonDeserialize<MemberAddress>() : null;
                p.NoProductStatusID = mdr["URUNYOKDURUM_ID"].ToInt32();
                p.isGiftPackage = mdr["HEDIYEPAKETIVAR"].ToBoolean();
                p.GiftPackageNote = mdr["HEDIYEPAKETINOTU"].ToString();
                p.PaymentTypeID = mdr["ODEMETIPI"].ToInt32();
                p.PaymentType = GetPaymentType(p.PaymentTypeID);

                p.AdditionalInfo = mdr["EKBILGI"].ToJsonDeserialize<OrderAdditionalInfo>();

                p.OrderSource = mdr["SIPARISKAYNAGI"].ToString();
                p.GiftVoucher = mdr["HEDIYECEKI"].ToString();
                p.isIntegrationTransfer = mdr["ENTEGRASYONAKTARILDI"].ToBoolean();
                p.CurrencyCode = mdr["PARABIRIMI"].ToString();
                p.InvoiceNo = mdr["FATURANO"].ToString();
                p.InvoiceDate = mdr["FATURATARIHI"].ToDateTime();
                p.Status = mdr["DURUM"].ToInt32();
                p.CargoTrackingNumber = mdr["KARGOTAKIPNUMARASI"].ToString();
                p.CargoPacketID = mdr["PAKETID"].ToInt32();
                p.CargoPacketID2 = mdr["PAKETID2"].ToInt32();
                var orderStatus = (OrderStatus)p.Status;
                p.StatusDefinition = orderStatus.ToString();
                p.OrderCode = mdr["SIPARISKODU"].ToString();
                p.CargoCode = mdr["KARGOKODU"].ToString();
                p.CargoCode2 = mdr["KARGOKODU"].ToString();
                p.CargoCodeStr = mdr["KARGOKODUSTR"].ToString();
                p.DeliveryStoreID = mdr["TESLIMATMAGAZA_ID"].ToInt32();
                p.PackagingStatusID = mdr["PAKETLEMEDURUM"] != DBNull.Value ? mdr["PAKETLEMEDURUM"].ToInt32() : 0;
                p.Amount = mdr["TUTAR"].ToDouble();
                p.TotalKDVAmount = mdr["TOPLAMKDV"].ToDouble();
                p.AdditionalTaxAmount = mdr["EKVERGITUTARI"].ToDouble();
                p.CustomsTaxAmount = mdr["GUMRUKVERGISITUTARI"].ToDouble();
                p.DiscountDistributed = mdr["INDIRIMDAGITILDI"].ToBoolean();
                p.SpecialAreaOne = mdr["OZELALAN1"].ToString();
                p.SpecialAreaTwo = mdr["OZELALAN2"].ToString();
                p.SpecialAreaThree = mdr["OZELALAN3"].ToString();
                try
                {
                    p.HopiCampaign = mdr["HOPIKAMPANYA"].ToJsonDeserialize<BasketHopiCampaignDto>();
                }
                catch (Exception)
                {
                    p.HopiCampaign = null;
                }

                try
                {
                    p.PointsUsage = mdr["PUANKULLANIM"].ToJsonDeserialize<BasketPointsUsageDto>();
                }
                catch (Exception)
                {
                    p.PointsUsage = null;
                }

                list.Add(p);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();

            return list;
        }
        public async Task<List<OrderInvoiced>> GetInvoicedOrderList(InvoicedOrderFilter filter, OrderPaging paging = null, CancellationToken cancellationToken = default)
        {
            List<OrderInvoiced> list = new List<OrderInvoiced>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandTimeout = 600;
            cmd.CommandText = $@"SELECT s.ID
                                , s.FATURANO                                
                            FROM siparis AS s 
                            WHERE s.FATURANO IS NOT NULL AND FATURANO != ''";

            if (filter != null)
            {
                if (filter.OrderIDList.Count > 0)
                    cmd.CommandText += $" AND s.ID IN ({string.Join(",", filter.OrderIDList)})";
            }

            if (paging != null)
            {
                if (!string.IsNullOrEmpty(paging.SortingValue))
                    cmd.CommandText += $" ORDER BY {paging.SortingValue}";

                if (!string.IsNullOrEmpty(paging.SortingDirection))
                    cmd.CommandText += $" {paging.SortingDirection}";
            }

            PagingExtension.AppendPaging(ref cmd, paging);
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderInvoiced orderInvoiced = new OrderInvoiced();
                orderInvoiced.OrderId = mdr["ID"].ToInt32();
                orderInvoiced.InvoiceNo = mdr["FATURANO"].ToString();
                list.Add(orderInvoiced);
            }
            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();

            return list;
        }

        public async Task<List<OrderStatusResult>> GetOrderStatusList(OrderStatusFilter filter, CancellationToken cancellationToken = default)
        {
            List<OrderStatusResult> list = new List<OrderStatusResult>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandTimeout = 600;
            cmd.CommandText = $@"SELECT s.ID
                                , s.DURUM                                
                            FROM siparis AS s 
                            WHERE 1 ";

            if (filter != null)
            {
                if (filter.OrderIDList.Count > 0)
                    cmd.CommandText += $" AND s.ID IN ({string.Join(",", filter.OrderIDList)})";
            }
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderStatusResult orderStatus = new OrderStatusResult();
                orderStatus.OrderId = mdr["ID"].ToInt32();
                orderStatus.Status = mdr["DURUM"].ToInt32();
                list.Add(orderStatus);
            }
            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            return list;
        }


        public async Task<int> GetCount(OrderFilter filter = null, CancellationToken cancellationToken = default)
        {
            if (filter == null)
                return 0;

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            string sqlInnerWhere = "";
            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                sqlInnerWhere = " su.MAGAZA_ID = @MAGAZA_ID ";
            else
                sqlInnerWhere = " su.MAGAZA_ID = 0 OR su.MAGAZA_ID = @MAGAZA_ID ";

            cmd.CommandText = $@"SELECT COUNT(*)
                                 FROM siparis AS s 
                                 WHERE
                                  s.ID IN (SELECT DISTINCT su.SIPARIS_ID FROM siparis_urun AS su WHERE  {sqlInnerWhere}) AND 
                                  s.ID IN (SELECT DISTINCT so.SIPARIS_ID FROM siparis_odeme AS so WHERE so.ONAYLANDI = 1) ";



            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            AppendFilter(ref cmd, filter);

            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            return count;
        }

        public async Task<IList<int>> GetOneProductOrder(CancellationToken cancellationToken)
        {
            string sqlInnerWhere = "";
            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                sqlInnerWhere = " su.MAGAZA_ID = @MAGAZA_ID ";
            else
                sqlInnerWhere = " su.MAGAZA_ID = 0 OR su.MAGAZA_ID = @MAGAZA_ID ";
            List<int> list = new List<int>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT su.SIPARIS_ID FROM siparis_urun AS su
                                WHERE ({sqlInnerWhere}) AND su.TOPLAMADURUMU = 1 AND su.DURUM <> 2 GROUP BY su.SIPARIS_ID HAVING SUM(su.ADET) = 1;";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                list.Add(mdr["SIPARIS_ID"].ToInt32());
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();
            return list;
        }

        public async Task<OrderDynamicNoteDto> GetOrderDynamicNote(int orderID, CancellationToken cancellationToken)
        {

            OrderDynamicNoteDto model = new OrderDynamicNoteDto();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT s.ID AS SIPARISID
                                          ,s.SIPARISNO
                                          ,s.URUNYOKDURUM_ID
                                          ,suyd.BASLIK
                                          ,suyd.ACIKLAMA
                                          ,suyd.AKTIF FROM siparis s LEFT JOIN siparis_urun_yok_durum suyd ON s.URUNYOKDURUM_ID = suyd.ID WHERE 1 AND s.ID = @SIPARISID";

            cmd.Parameters.Add("@SIPARISID", MySqlDbType.Int32).Value = orderID;
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                model.OrderID = mdr["SIPARISID"].ToInt32();
                model.OrderNumber = mdr["SIPARISNO"].ToString();
                model.NoProductStatusID = mdr["URUNYOKDURUM_ID"].ToInt32();
                model.Header = mdr["BASLIK"].ToString();
                model.Description = mdr["ACIKLAMA"].ToString();
                model.isActive = mdr["AKTIF"] != DBNull.Value ? mdr["AKTIF"].ToBoolean() : false;
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();

            return model;
        }

        public async Task<OrderGiftNoteDto> GetOrderGiftNote(int orderID, CancellationToken cancellationToken)
        {

            OrderGiftNoteDto model = new OrderGiftNoteDto();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT  s.ID
                                       ,s.SIPARISNO
                                       ,s.HEDIYEPAKETINOTU FROM siparis s WHERE 1 AND s.ID = @SIPARISID";

            cmd.Parameters.Add("@SIPARISID", MySqlDbType.Int32).Value = orderID;
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                model.OrderID = mdr["ID"].ToInt32();
                model.OrderNumber = mdr["SIPARISNO"].ToString();
                model.OrderGiftNote = mdr["HEDIYEPAKETINOTU"].ToString();
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();

            return model;
        }

        public async Task<IList<int>> GetALotsOfProductOrder(CancellationToken cancellationToken)
        {

            string sqlInnerWhere = "";
            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                sqlInnerWhere = " su.MAGAZA_ID = @MAGAZA_ID ";
            else
                sqlInnerWhere = " su.MAGAZA_ID = 0 OR su.MAGAZA_ID = @MAGAZA_ID ";
            List<int> list = new List<int>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT su.SIPARIS_ID FROM siparis_urun AS su
                                WHERE ({sqlInnerWhere}) AND su.TOPLAMADURUMU = 1 AND su.DURUM <> 2 GROUP BY su.SIPARIS_ID HAVING SUM(su.ADET) > 1;";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                list.Add(mdr["SIPARIS_ID"].ToInt32());
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();

            return list;
        }

        public async Task MissingProduct(int orderID, int packagingStatus, CancellationToken cancellationToken)
        {

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            string sqlInnerWhere = "";
            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                sqlInnerWhere = " su.MAGAZA_ID = @MAGAZA_ID ";
            else
                sqlInnerWhere = " su.MAGAZA_ID = 0 OR su.MAGAZA_ID = @MAGAZA_ID ";

            cmd.CommandText = $@"UPDATE siparis AS s 
                                 SET s.PAKETLEMEDURUM_ID = (SELECT ID FROM paketleme_durum WHERE ISLEM = @ISLEM LIMIT 1), s.PAKETLEMETARIHI = NOW() 
                                 WHERE s.ID = @ID AND s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND {sqlInnerWhere})";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderID;
            cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = packagingStatus;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();

        }

        public async Task SetInvoiceNo(Entities.Concrete.Order entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE siparis SET FATURANO = @FATURANO, FATURATARIHI = @FATURATARIHI WHERE ID = @ID";
            cmd.Parameters.Add("@FATURANO", MySqlDbType.VarChar).Value = entity.InvoiceNo;
            cmd.Parameters.Add("@FATURATARIHI", MySqlDbType.DateTime).Value = entity.InvoiceDate;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetOrderCompleatedPackageStatus(int orderID, int packagingStatus, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = "UPDATE siparis SET PAKETLEMEDURUM_ID = (SELECT ID FROM paketleme_durum WHERE ISLEM = @ISLEM LIMIT 1), KARGOMAGAZAKURYE_ID = 0, PAKETLEMETARIHI = NOW() WHERE ID = @ID;";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderID;
            cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = packagingStatus;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task SetPackagingStatus(List<int> OrderIDs, PackageStatus status, int statusID, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            if (statusID > 0)
            {
                cmd.CommandText = $"UPDATE siparis SET PAKETLEMEDURUM_ID = @PAKETLEMEDURUM_ID WHERE ID IN ({string.Join(',', OrderIDs)})";
                cmd.Parameters.Add("@PAKETLEMEDURUM_ID", MySqlDbType.Int32).Value = statusID;
            }
            else
            {
                if (status == PackageStatus.Paketleniyor)
                {
                    cmd.CommandText = $"UPDATE siparis SET PAKETLEMEDURUM_ID = (SELECT ID FROM paketleme_durum WHERE ISLEM = @ISLEM LIMIT 1), PAKETLEMEBASLANGICTARIHI = NOW() WHERE ID IN ({string.Join(',', OrderIDs)})";
                }
                else
                {
                    cmd.CommandText = $"UPDATE siparis SET PAKETLEMEDURUM_ID = (SELECT ID FROM paketleme_durum WHERE ISLEM = @ISLEM LIMIT 1) WHERE ID IN ({string.Join(',', OrderIDs)})";
                }

                cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = (int)status;
            }

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateAdditionalInformation(Entities.Concrete.Order entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE siparis
	                                    SET EKBILGI = @EKBILGI
                                    WHERE ID = @ID";

            cmd.Parameters.Add("@EKBILGI", MySqlDbType.Text).Value = entity.AdditionalInfo.ToJsonSerialize();
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateOrderCompleatedAmount(int orderID, OrderProduct orderProduct, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE siparis SET TUTAR = TUTAR + @UrunTutari, TOPLAMKDV = TOPLAMKDV + @KdvTutari, TOPLAMTUTAR = TOPLAMTUTAR + @UrunTutari WHERE ID = @SID";
            cmd.Parameters.Add("@UrunTutari", MySqlDbType.Double).Value = orderProduct.Amount * (orderProduct.OccurrencesPiece - orderProduct.Piece);
            cmd.Parameters.Add("@KdvTutari", MySqlDbType.Double).Value = orderProduct.KDVAmount * (orderProduct.OccurrencesPiece - orderProduct.Piece);
            cmd.Parameters.Add("@SID", MySqlDbType.Int32).Value = orderID;
            await cmd.ExecuteTransactionCommandAsync();
            //cmd.Dispose();
        }

        public async Task<IList<OrderListItemDto>> GetOrderReport(OrderReportFilter filter, CancellationToken cancellationToken)
        {
            var list = new List<OrderListItemDto>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT
	                                s.ID
	                                , s.TUTAR
                                    , s.DURUM
                                    , s.TARIH AS SIPARISTARIHI
                                    , (
	                                        SELECT
		                                        CONCAT(mt.ISIM,' ',mt.SOYISIM)
	                                        FROM
		                                        magaza_temsilci AS mt
	                                        JOIN
		                                        depo_dagitilan_urun AS ddu ON ddu.`HAZIRLAYAN_ID` = mt.`ID` WHERE ddu.DEPO_ID = @DEPO_ID AND ddu.`SIPARIS_ID`=s.ID LIMIT 1) AS HAZIRLAYAN
                                    , {GetToplamTutarQuery("s", false, false, false)} AS TOPLAMTUTAR
                                    , pd.TANIM AS PAKETLEMEDURUM
	                                , (
		                                SELECT
			                                ot.`TANIM`
		                                FROM
			                                siparis_odeme AS so
		                                JOIN
			                                odeme_tab AS ot ON ot.`ODEMETIPI`=so.`ODEMETIPI`
		                                WHERE
			                                so.`SIPARIS_ID`=s.`ID`
		                                ) AS ODEMETIPI
                                FROM
	                                siparis AS s";

            if (filter.FilterByInvoiceDate)
            {
                cmd.CommandText += @"  JOIN
	                                ticimax_fatura AS tf ON tf.SIPARIS_ID = s.ID";
            }

            cmd.CommandText += @" JOIN
                                    paketleme_durum AS pd ON pd.ID = s.PAKETLEMEDURUM_ID
                                WHERE
	                                s.ID IN (SELECT
                                                ddu.SIPARIS_ID
                                            FROM
                                                depo_dagitilan_urun AS ddu
                                            WHERE
                                                ddu.DEPO_ID = @DEPO_ID
                                            GROUP BY
                                                ddu.SIPARIS_ID)";

            if (filter.PackagingStatus.Count > 0)
            {
                cmd.CommandText += $@"  AND s.PAKETLEMEDURUM_ID IN ({string.Join(",", filter.PackagingStatus)})";
            }

            if (filter.FilterByOrderDate)
            {
                if (filter.OrderEndDate == null)
                {
                    cmd.CommandText += @"  AND DATE(s.TARIH) = DATE(@ORDERSTARTDATE)";
                }
                else
                {
                    cmd.CommandText += @"  AND DATE(s.TARIH) >= DATE(@ORDERSTARTDATE)
                                        AND DATE(s.TARIH) <= DATE(@ORDERENDDATE)";

                    cmd.Parameters.Add("@ORDERENDDATE", MySqlDbType.Date).Value = filter.OrderEndDate;
                }

                cmd.Parameters.Add("@ORDERSTARTDATE", MySqlDbType.Date).Value = filter.OrderStartDate;
            }

            if (filter.FilterByInvoiceDate)
            {
                if (filter.InvoiceEndDate == null)
                {
                    cmd.CommandText += @"  AND DATE(tf.TARIH) = DATE(@INVOICESTARTDATE)";
                }
                else
                {
                    cmd.CommandText += @"  AND DATE(tf.TARIH) >= DATE(@INVOICESTARTDATE)
                                        AND DATE(tf.TARIH) <= DATE(@INVOICEENDDATE)";

                    cmd.Parameters.Add("@INVOICEENDDATE", MySqlDbType.Date).Value = filter.InvoiceEndDate;
                }

                cmd.Parameters.Add("@INVOICESTARTDATE", MySqlDbType.Date).Value = filter.InvoiceStartDate;
            }

            if (filter.OrderStatuses.Count > 0)
            {
                cmd.CommandText += $@"  AND s.DURUM IN ({string.Join(",", filter.OrderStatuses)})";
            }


            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                cmd.CommandText += " AND (dsu.MAGAZA_ID = @MAGAZA_ID) ";
            else
                cmd.CommandText += " AND (dsu.MAGAZA_ID = 0 OR dsu.MAGAZA_ID = @MAGAZA_ID) ";

            cmd.CommandText += " ORDER BY s.ID desc ";

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = filter.WarehouseId;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (reader.Read())
            {
                list.Add(new OrderListItemDto
                {
                    InvoicerName = "",
                    OrderId = Convert.ToInt32(reader["ID"]),
                    StatusId = Convert.ToInt32(reader["DURUM"]),
                    OrderDate = reader["SIPARISTARIHI"].ToDateTime(),
                    CollectorName = reader["HAZIRLAYAN"].ToString(),
                    TotalAmount = reader["TOPLAMTUTAR"].ToDouble(),
                    PaymentType = reader["ODEMETIPI"].ToString(),
                    PackagingStatusName = reader["PAKETLEMEDURUM"].ToString()
                });
            }

            reader.Close();
            reader.Dispose();

            return list;
        }

        public async Task<double> GetOrderTotalAmount(OrderReportFilter filter, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = $@"SELECT
                                     SUM({GetToplamTutarQuery("s", false, false, false)}) AS TOPLAMTUTAR
                                FROM
	                                siparis AS s ";

            if (filter.FilterByInvoiceDate)
            {
                cmd.CommandText += @"  JOIN
	                                ticimax_fatura AS tf ON tf.SIPARIS_ID = s.ID";
            }

            cmd.CommandText += @"WHERE
                                    s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = 1) AND
	                                s.ID IN (SELECT
                                                ddu.SIPARIS_ID
                                            FROM
                                                depo_dagitilan_urun AS ddu
                                            WHERE
                                                ddu.DEPO_ID = @DEPO_ID
                                            GROUP BY
                                                ddu.SIPARIS_ID)";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            AppendFilter(ref cmd, filter);

            var totalAmount = await cmd.ExecuteScalarAsync(cancellationToken);
            cmd.Dispose();

            return totalAmount != DBNull.Value ? Convert.ToDouble(totalAmount) : default;
        }

        public async Task<int> GetOrderTotalCount(OrderReportFilter filter, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                     Count(*)
                                FROM
	                                siparis AS s ";

            if (filter.FilterByInvoiceDate)
            {
                cmd.CommandText += @" JOIN
	                                        ticimax_fatura AS tf ON tf.SIPARIS_ID = s.ID";
            }

            cmd.CommandText += @"  WHERE
                                        s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = 1) AND 
	                                    s.ID IN (SELECT
                                                    ddu.SIPARIS_ID
                                                FROM
                                                    depo_dagitilan_urun AS ddu
                                                WHERE
                                                    ddu.DEPO_ID = @DEPO_ID
                                                GROUP BY
                                                    ddu.SIPARIS_ID)";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            AppendFilter(ref cmd, filter);

            var orderCount = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();
            cmd.Dispose();

            return orderCount;
        }

        public async Task<int> GetCanceledOrderTotalCount(OrderCanceledOrderTotalCountFilter filter, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                     Count(*)
                                FROM
	                                siparis AS s ";

            cmd.CommandText += @"JOIN
	                                ticimax_fatura AS tf ON tf.`SIPARIS_ID`=s.`ID`
                                WHERE s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = 1) AND 
                                            s.ID IN (SELECT
                                                ddu.SIPARIS_ID
                                            FROM
                                                depo_dagitilan_urun AS ddu
                                            WHERE
                                                ddu.DEPO_ID = @DEPO_ID
                                            GROUP BY
                                                ddu.SIPARIS_ID)";

            if (filter.EndDate == null)
            {
                cmd.CommandText += @"  AND DATE(tf.TARIH) = DATE(@STARTDATE)";
            }
            else
            {
                cmd.CommandText += @"  AND DATE(tf.TARIH) >= @STARTDATE
                                        AND DATE(tf.TARIH) <= @ENDDATE ";

                cmd.Parameters.Add("@ENDDATE", MySqlDbType.Date).Value = filter.EndDate;
            }

            cmd.CommandText += @"      AND s.DURUM IN (8,9,10,11,12,13,14,15)";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = filter.WarehouseID;
            cmd.Parameters.Add("@STARTDATE", MySqlDbType.Date).Value = filter.StartDate;
            var orderCount = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();

            return orderCount;
        }

        public async Task<int> GetDeliveredOrderTotalCount(OrderDeliveredOrderTotalCountFilter filter, CancellationToken cancellationToken)
        {

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT
                                     Count(*)
                                FROM
	                                siparis AS s ";

            cmd.CommandText += @"JOIN
	                                ticimax_fatura AS tf ON tf.`SIPARIS_ID`=s.`ID`
                                WHERE s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = 1) AND 
	                                s.ID IN (SELECT
                                                ddu.SIPARIS_ID
                                            FROM
                                                depo_dagitilan_urun AS ddu
                                            WHERE
                                                ddu.DEPO_ID = @DEPO_ID
                                            GROUP BY
                                                ddu.SIPARIS_ID)
                                    AND s.PAKETLEMEDURUM_ID = @PAKETLEMEDURUM_ID";

            if (filter.EndDate == null)
            {
                cmd.CommandText += @"  AND DATE(tf.TARIH) = DATE(@STARTDATE)";
            }
            else
            {
                cmd.CommandText += @"  AND DATE(tf.TARIH) >= @STARTDATE
                                        AND DATE(tf.TARIH) <= @ENDDATE";

                cmd.Parameters.Add("@ENDDATE", MySqlDbType.Date).Value = filter.EndDate;
            }

            cmd.Parameters.Add("@PAKETLEMEDURUM_ID", MySqlDbType.Int32).Value = 5;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = filter.WarehouseID;
            cmd.Parameters.Add("@STARTDATE", MySqlDbType.Date).Value = filter.StartDate;
            var orderCount = (await cmd.ExecuteScalarAsync(cancellationToken)).ToInt32();

            return orderCount;
        }

        public async Task UpdateOrderSpecialField3(int orderID, string field, CancellationToken cancellationToken)
        {

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE siparis AS s SET OZELALAN3 = @OZELALAN3 WHERE ID = @SIPARIS_ID AND s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = 1);";
            cmd.Parameters.Add("@OZELALAN3", MySqlDbType.VarChar).Value = field;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();

        }

        public async Task<int> GetOrderPreparedUser(int ID, string Username, int StoreID, int WarehouseID, CancellationToken cancellationToken)
        {
            int UserId = 0;
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT ID FROM magaza_temsilci where ID=@ID and KULLANICIADI=@KULLANICIADI";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = ID;
            cmd.Parameters.Add("@KULLANICIADI", MySqlDbType.VarChar).Value = Username;
            if (StoreID != 0)
            {
                cmd.CommandText += " and MAGAZA_ID=@MAGAZA_ID";
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = StoreID;
            }

            if (WarehouseID != 0)
            {
                cmd.CommandText += " and DEPO_ID=@DEPO_ID";
                cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WarehouseID;
            }

            var dr = await cmd.ExecuteReaderAsync(cancellationToken);
            if (await dr.ReadAsync(cancellationToken))
            {
                UserId = Convert.ToInt32(dr["ID"].ToString());
            }

            await dr.CloseAsync();
            await dr.DisposeAsync();
            await cmd.DisposeAsync();
            return UserId;
        }

        public async Task<int> GetOrderStatusByOrderIdAsync(int orderId, CancellationToken cancellationToken)
        {
            int status = -1;
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT DURUM FROM siparis where ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderId;

            var dr = await cmd.ExecuteReaderAsync(cancellationToken);
            if (await dr.ReadAsync(cancellationToken))
            {
                status = dr["DURUM"] != DBNull.Value ? dr["DURUM"].ToInt32() : 0;
            }

            await dr.CloseAsync();
            await dr.DisposeAsync();
            await cmd.DisposeAsync();
            return status;
        }
        public async Task OrderReset(int OrderID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand("UPDATE siparis_urun SET IADE_ADEDI = 0, IADE_TARIHI = NULL, EKSIK_ADEDI = 0, EKSIK_TARIHI = NULL, BULUNMA_DURUMU = 0, BULUNMA_ADEDI = 0, BULUNMA_TARIHI = NULL, DURUM = 0, ISLEM_ID = 0, IADE_NEDEN_ID = 0, ISLEM_TARIHI = '2000-01-01 00:00:00', HAZIRLAYAN_ID = 0, TOPLAMADURUMU = 0 WHERE SIPARIS_ID = @orderId");
            cmd.Parameters.Add("@orderId", MySqlDbType.Int32).Value = OrderID;
            await cmd.ExecuteTransactionCommandAsync();

            MySqlCommand cmd2 = new MySqlCommand(
                "UPDATE siparis SET PAKETLEMETARIHI='1970-01-01 00:00:00',DURUM = 0, PAKETLEMEDURUM_ID = 1, TUTAR = (SELECT SUM(ADET * TUTAR) FROM siparis_urun WHERE SIPARIS_ID = @orderId), TOPLAMTUTAR = (SELECT SUM(ADET * TUTAR) FROM siparis_urun WHERE SIPARIS_ID = @orderId) + IFNULL(KARGOTUTARI, 0) + IFNULL(KAPIDAODEMETUTARI, 0), TOPLAMKDV = (SELECT SUM(ADET * KDV) FROM siparis_urun WHERE SIPARIS_ID = @orderId), TOPLAMTUTAR_ONAYLANDI = (SELECT SUM(ADET * TUTAR) + SUM(ADET * KDV) + IFNULL(siparis.KARGOTUTARI, 0) + IFNULL(siparis.KAPIDAODEMETUTARI, 0) - SUM(KAMPANYAINDIRIMTUTARI) FROM siparis_urun WHERE SIPARIS_ID = @orderId), TOPLAMTUTAR_ONAYBEKLEYEN = (SELECT SUM(ADET * TUTAR) + SUM(ADET * KDV) + IFNULL(siparis.KARGOTUTARI, 0) + IFNULL(siparis.KAPIDAODEMETUTARI,0) - SUM(KAMPANYAINDIRIMTUTARI) FROM siparis_urun WHERE SIPARIS_ID = @orderId), TOPLAMTUTAR_HAVALE = (SELECT SUM(ADET * TUTAR) + SUM(ADET * KDV) + IFNULL(siparis.KARGOTUTARI, 0) + IFNULL(siparis.KAPIDAODEMETUTARI,0) - SUM(KAMPANYAINDIRIMTUTARI) FROM siparis_urun WHERE SIPARIS_ID = @orderId), TOPLAMTUTAR_HAVALEOB = (SELECT SUM(ADET * TUTAR) + SUM(ADET * KDV) + IFNULL(siparis.KARGOTUTARI, 0) + IFNULL(siparis.KAPIDAODEMETUTARI, 0) - SUM(KAMPANYAINDIRIMTUTARI) FROM siparis_urun WHERE SIPARIS_ID = @orderId) WHERE ID = @orderId;");

            cmd2.Parameters.Add("@orderId", MySqlDbType.Int32).Value = OrderID;
            await cmd2.ExecuteTransactionCommandAsync();

            MySqlCommand cmd3 = new MySqlCommand("DELETE FROM depo_dagitilan_urun AS ddu WHERE SIPARIS_ID = @orderId;");
            cmd3.Parameters.Add("@orderId", MySqlDbType.Int32).Value = OrderID;
            await cmd3.ExecuteTransactionCommandAsync();

            if (!WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
            {
                MySqlCommand cmd4 = new MySqlCommand("DELETE FROM depo_araba_urun AS dau WHERE SIPARIS_ID = @orderId;");
                cmd4.Parameters.Add("@orderId", MySqlDbType.Int32).Value = OrderID;
                await cmd4.ExecuteTransactionCommandAsync();
            }
        }

        public async Task OrderProductReset(int orderProductID, int id, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(@"UPDATE siparis_urun SET IADE_ADEDI = 0, IADE_TARIHI = NULL, EKSIK_ADEDI = 0, EKSIK_TARIHI = NULL, BULUNMA_DURUMU = 0, BULUNMA_ADEDI = 0, BULUNMA_TARIHI = NULL, DURUM = 0, ISLEM_ID = 0, IADE_NEDEN_ID = 0, ISLEM_TARIHI = '2000-01-01 00:00:00', HAZIRLAYAN_ID = 0, TOPLAMADURUMU = 0 WHERE ID = @orderProductId;
                                                 UPDATE depo_dagitilan_urun SET BULUNMA_TARIHI = '2000-01-01 00:00:00', EKSIK_ADET = 0, BULUNAN_ADET = 0  WHERE ID = @Id;");
            cmd.Parameters.Add("@orderProductId", MySqlDbType.Int32).Value = orderProductID;
            cmd.Parameters.Add("@Id", MySqlDbType.Int32).Value = id;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateOrderPreparedID(List<int> OrderIDs, int PreparedID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand("UPDATE siparis SET SEPETHAZIRLAYAN_ID = @SEPETHAZIRLAYAN_ID,TOPLAYICI_ID=@TOPLAYICI_ID WHERE ID IN (" + string.Join(",", OrderIDs) + ") AND  TOPLAYICI_ID=0");
            cmd.Parameters.Add("@SEPETHAZIRLAYAN_ID", MySqlDbType.Int32).Value = PreparedID;
            cmd.Parameters.Add("@TOPLAYICI_ID", MySqlDbType.Int32).Value = PreparedID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateOrderDeliveryHour(int orderID, DateTime date, string deliveryHour, int cargoHourID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE siparis SET TESLIMATGUNU = @TESLIMATGUNU, TESLIMATSAATI = @TESLIMATSAATI, KARGOSAAT_ID = @KARGOSAAT_ID WHERE ID = @ID;";
            cmd.Parameters.Add("@TESLIMATGUNU", MySqlDbType.Date).Value = date;
            cmd.Parameters.Add("@TESLIMATSAATI", MySqlDbType.VarChar).Value = deliveryHour;
            cmd.Parameters.Add("@KARGOSAAT_ID", MySqlDbType.Int32).Value = cargoHourID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();
        }

        public async Task UpdateOrderStore(int orderId, int storeId, bool pickingChange, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"UPDATE siparis_urun SET MAGAZA_ID = @MAGAZA_ID WHERE SIPARIS_ID = @SIPARIS_ID;
                                 UPDATE siparis SET PAKETLEMEDURUM_ID = 1, PAKETLEMEBASLANGICTARIHI = '1970-01-01 00:00:00'";

            if (pickingChange)
            {
                cmd.CommandText += @", SEPETHAZIRLAYAN_ID = IFNULL((SELECT ID FROM magaza_temsilci WHERE 
                                    s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = 1) AND 
                                    AKTIF = 1 AND 
                                    TIP_ID = 1 AND 
                                    KARGOTIPI = (SELECT KARGOTIPI FROM siparis_urun WHERE SIPARIS_ID = @SIPARIS_ID LIMIT 1) LIMIT 1),0) ";
            }
            else
            {
                cmd.CommandText += ", SEPETHAZIRLAYAN_ID = 0 ";
            }

            cmd.CommandText += " WHERE ID = @SIPARIS_ID;";
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = orderId;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = storeId;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> OrdersPerPerson(int storeID, int cargoType, bool storeCondition, CancellationToken cancellationToken)
        {

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            cmd.CommandText = @"SELECT
                                    (SELECT COUNT(ID) FROM siparis AS s ";

            cmd.CommandText += @"WHERE
                                        s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = 1)
                                        AND s.PAKETLEMEDURUM_ID = 1
                                        AND s.DURUM <> 10 ";

            if (storeCondition)
            {
                cmd.CommandText += " AND s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.MAGAZA_ID = @MAGAZA_ID AND su.KARGOTIPI = @KARGOTIPI AND su.SIPARIS_ID = s.ID) ";
            }

            cmd.CommandText += " AND s.ID IN (SELECT so.SIPARIS_ID FROM siparis_odeme AS so WHERE so.ONAYLANDI = 1 AND so.SIPARIS_ID = s.ID))/(SELECT COUNT(ID) FROM magaza_temsilci WHERE TIP_ID = 1 AND AKTIF = 1" + (storeCondition ? " AND MAGAZA_ID = @MAGAZA_ID AND KARGOTIPI = @KARGOTIPI" : "") + ") AS ORAN";
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = storeID;
            cmd.Parameters.Add("@KARGOTIPI", MySqlDbType.Int32).Value = cargoType;
            int ordersPerPerson = Convert.ToInt32(Math.Ceiling(Convert.ToDouble(await cmd.ExecuteReaderAsync(cancellationToken))));
            cmd.Dispose();

            return ordersPerPerson;
        }

        public async Task<List<int>> CollectableOrders(int Limit, bool storeCondition, CancellationToken cancellationToken)
        {

            List<int> orderIDs = new List<int>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT ID FROM siparis AS s ";

            cmd.CommandText += @" WHERE
                                        s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.SIPARIS_ID = s.ID AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = 1)
                                        AND s.DURUM <> 10
                                        AND s.SEPETHAZIRLAYAN_ID = 0
                                        AND PAKETLEMEDURUM_ID = 1 ";

            if (storeCondition)
            {
                cmd.CommandText += " AND s.ID IN (SELECT su.SIPARIS_ID FROM siparis_urun AS su WHERE su.MAGAZA_ID = @MAGAZA_ID AND su.KARGOTIPI = @KARGOTIPI AND su.SIPARIS_ID = s.ID) ";
            }

            cmd.CommandText += " AND s.ID IN (SELECT so.SIPARIS_ID FROM siparis_odeme AS so WHERE so.ONAYLANDI = 1 AND so.SIPARIS_ID = s.ID) ORDER BY ID LIMIT " + Limit;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (mdr.Read())
            {
                orderIDs.Add(Convert.ToInt32(mdr["ID"]));
            }

            mdr.Close();
            mdr.Dispose();
            cmd.Dispose();


            return orderIDs;
        }

        public async Task UpdateCargoStoreCurrier(int orderID, int currierID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "UPDATE siparis SET KARGOMAGAZAKURYE_ID = @KARGOMAGAZAKURYE_ID WHERE ID = @ID";
            cmd.Parameters.Add("@KARGOMAGAZAKURYE_ID", MySqlDbType.Int32).Value = currierID;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();

        }

        public async Task UpdateAmount(OrderUpdateAmountDto request, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE siparis SET
                                        TOPLAMTUTAR_ONAYLANDI = @TOPLAMTUTAR_ONAYLANDI,
                                        TOPLAMTUTAR_HAVALE = @TOPLAMTUTAR_HAVALE,
                                        TOPLAMTUTAR_HAVALEOB=@TOPLAMTUTAR_HAVALEOB,
                                        TOPLAMTUTAR_ONAYBEKLEYEN = @TOPLAMTUTAR_ONAYBEKLEYEN,
                                        ODENENTUTAR = @ODENENTUTAR
                                        WHERE ID = @ID";

            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = request.OrderID;
            cmd.Parameters.Add("@TOPLAMTUTAR_ONAYLANDI", MySqlDbType.Double).Value = request.TotalAmountApproved;
            cmd.Parameters.Add("@TOPLAMTUTAR_HAVALE", MySqlDbType.Double).Value = request.TotalAmountTransfer;
            cmd.Parameters.Add("@TOPLAMTUTAR_HAVALEOB", MySqlDbType.Double).Value = request.TotalAmountTransferPendingApproval;
            cmd.Parameters.Add("@TOPLAMTUTAR_ONAYBEKLEYEN", MySqlDbType.Double).Value = request.TotalAmountPendingApproval;
            cmd.Parameters.Add("@ODENENTUTAR", MySqlDbType.Double).Value = request.AmountPaid;

            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateOrderAmounts(UpdateOrderAmountsDto request, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE siparis SET TUTAR = @TUTAR, TOPLAMTUTAR = @TOPLAMTUTAR, TOPLAMKDV = @TOPLAMKDV WHERE ID = @ID";
            cmd.Parameters.Add("@TUTAR", MySqlDbType.Double).Value = request.Amount;
            cmd.Parameters.Add("@TOPLAMTUTAR", MySqlDbType.Double).Value = request.TotalAmount;
            cmd.Parameters.Add("@TOPLAMKDV", MySqlDbType.Double).Value = request.TotalKDVAmount;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = request.OrderID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<IList<string>> GetOrderSources(OrderFilter filter = null, OrderPaging paging = null, CancellationToken cancellationToken = default)
        {
            List<string> orderResources = new List<string>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = @"SELECT CASE 
                                    WHEN LOWER(s.SIPARISKAYNAGI) = 'nativeios' THEN 'NativeiOS'
                                    ELSE s.SIPARISKAYNAGI
                                    END AS SIPARISKAYNAGI
                                    FROM siparis AS s WHERE 1 ";
            if (filter != null && filter.OrderID > 0)
            {
                cmd.CommandText += " and s.ID=@ID";
                cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.OrderID;
            }

            if (filter != null && !string.IsNullOrEmpty(filter.OrderNo))
            {
                cmd.CommandText += " and s.SIPARISNO=@SIPARISNO";
                cmd.Parameters.Add("@SIPARISNO", MySqlDbType.VarChar).Value = filter.OrderNo;
            }

            cmd.CommandText += " group by s.SIPARISKAYNAGI ";
            PagingExtension.AppendPaging(ref cmd, paging);
            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                orderResources.Add(reader["SIPARISKAYNAGI"].ToString());
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            cmd.Dispose();

            return orderResources;
        }

        public async Task CalculationAmount(int ID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText += @"
                                   UPDATE siparis SET
                                        TUTAR = (SELECT SUM(ADET * TUTAR) FROM siparis_urun WHERE SIPARIS_ID = @SIPARIS_ID AND DURUM <> 2),
                                        TOPLAMTUTAR = (SELECT SUM(ADET * TUTAR) FROM siparis_urun WHERE SIPARIS_ID = @SIPARIS_ID AND DURUM <> 2) + KARGOTUTARI + KAPIDAODEMETUTARI,
                                        TOPLAMKDV = (SELECT SUM(ADET * KDV) FROM siparis_urun WHERE SIPARIS_ID = @SIPARIS_ID AND DURUM <> 2),
                                        TOPLAMTUTAR_ONAYLANDI = (SELECT SUM(ADET * TUTAR) + SUM(ADET * KDV) + siparis.KARGOTUTARI + siparis.KAPIDAODEMETUTARI - SUM(KAMPANYAINDIRIMTUTARI) FROM siparis_urun WHERE SIPARIS_ID = @SIPARIS_ID  AND DURUM <> 2),
                                        TOPLAMTUTAR_ONAYBEKLEYEN = (SELECT SUM(ADET * TUTAR) + SUM(ADET * KDV) + siparis.KARGOTUTARI + siparis.KAPIDAODEMETUTARI - SUM(KAMPANYAINDIRIMTUTARI) FROM siparis_urun WHERE SIPARIS_ID = @SIPARIS_ID AND DURUM <> 2),
                                        TOPLAMTUTAR_HAVALE = (SELECT SUM(ADET * TUTAR) + SUM(ADET * KDV) + siparis.KARGOTUTARI + siparis.KAPIDAODEMETUTARI - SUM(KAMPANYAINDIRIMTUTARI) FROM siparis_urun WHERE SIPARIS_ID = @SIPARIS_ID AND DURUM <> 2),
	                                    TOPLAMTUTAR_HAVALEOB = (SELECT SUM(ADET * TUTAR) + SUM(ADET * KDV) + siparis.KARGOTUTARI + siparis.KAPIDAODEMETUTARI - SUM(KAMPANYAINDIRIMTUTARI) FROM siparis_urun WHERE SIPARIS_ID = @SIPARIS_ID AND DURUM <> 2)
                                    WHERE ID = @SIPARIS_ID
                                ";

            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = ID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> GetCountAsync(OrderFilter filter = null, CancellationToken cancellationToken = default)
        {
            if (filter == null)
                return 0;


            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);

            string sqlInnerWhere = "";
            if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                sqlInnerWhere = " su.MAGAZA_ID = @MAGAZA_ID ";
            else
                sqlInnerWhere = " su.MAGAZA_ID = 0 OR su.MAGAZA_ID = @MAGAZA_ID ";

            cmd.CommandText = $@"SELECT COUNT(*)
                                 FROM siparis AS s 
                                 WHERE
                                  s.ID IN (SELECT DISTINCT su.SIPARIS_ID FROM siparis_urun AS su WHERE {sqlInnerWhere}) AND 
                                  s.ID IN (SELECT DISTINCT so.SIPARIS_ID FROM siparis_odeme AS so WHERE so.ONAYLANDI = 1) ";

            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;
            AppendFilter(ref cmd, filter);

            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            cmd.Dispose();

            return count;
        }


        private void AppendFilter(ref MySqlCommand cmd, OrderFilter filter)
        {
            cmd.Parameters.Add("@KARGOTIPI", MySqlDbType.Int32).Value = filter.CargoType;
            if (!string.IsNullOrEmpty(filter.OrderSource))
            {
                cmd.CommandText += " AND s.SIPARISKAYNAGI = @SIPARISKAYNAGI";
                cmd.Parameters.Add("@SIPARISKAYNAGI", MySqlDbType.VarChar).Value = filter.OrderSource;
            }

            if (filter.OrderSourceList.Count > 0)
            {
                var sourceList = string.Join(",", filter.OrderSourceList.Select(x => $"'{x}'"));
                cmd.CommandText += $" AND s.SIPARISKAYNAGI IN ({sourceList}) ";
            }

            if (filter.PreparedID > -1)
            {
                cmd.CommandText += " AND SEPETHAZIRLAYAN_ID = @SEPETHAZIRLAYAN_ID";
                cmd.Parameters.Add("@SEPETHAZIRLAYAN_ID", MySqlDbType.Int32).Value = filter.PreparedID;
            }

            if (filter.CurrierID > -1)
            {
                cmd.CommandText += " AND KARGOMAGAZAKURYE_ID = @KARGOMAGAZAKURYE_ID";
                cmd.Parameters.Add("@KARGOMAGAZAKURYE_ID", MySqlDbType.Int32).Value = filter.CurrierID;
            }

            if (filter.OrderID > 0)
            {
                cmd.CommandText += " AND s.ID = @ID";
                cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.OrderID;
            }
            else if (filter.OrderIDList.Count > 0)
            {
                cmd.CommandText += $" AND s.ID IN ({string.Join(",", filter.OrderIDList)})";
            }

            if (!string.IsNullOrEmpty(filter.OrderNo))
            {
                cmd.CommandText += " AND s.SIPARISNO = @SIPARISNO";
                cmd.Parameters.Add("@SIPARISNO", MySqlDbType.VarChar).Value = filter.OrderNo;
            }

            if (filter.AvailableOrders)
            {
                cmd.CommandText += " AND s.DURUM < 6";
            }
            else if (filter.OrderStatus.HasValue)
            {
                cmd.CommandText += " AND DURUM = @DURUM";
                cmd.Parameters.Add("@DURUM", MySqlDbType.Int32).Value = (int)filter.OrderStatus.Value;
            }

            if (filter.OrderStatusList.Count > 0)
            {
                cmd.CommandText += $" AND s.DURUM IN ({string.Join(",", filter.OrderStatusList)})";
            }

            else if (filter.isMarketplaceOrder)
            {
                cmd.CommandText += " AND DURUM IN (4, 6)";
            }
            else
            {
                cmd.CommandText += " AND DURUM <> 10";
            }

            if (filter.PackagingStatus.HasValue)
            {
                cmd.CommandText += " AND PAKETLEMEDURUM_ID = (SELECT ID FROM paketleme_durum WHERE ISLEM = @ISLEM LIMIT 1)";
                cmd.Parameters.Add("@ISLEM", MySqlDbType.Int32).Value = (int)filter.PackagingStatus;
            }

            if (filter.DeliveryDateLimit > 0)
            {
                cmd.CommandText += " AND TESLIMATGUNU >= DATE(NOW()) AND TESLIMATGUNU < @TESLIMATGUNU";
                cmd.Parameters.Add("@TESLIMATGUNU", MySqlDbType.Date).Value = DateTime.Now.AddDays(filter.DeliveryDateLimit);
            }

            if (!cmd.Parameters.Contains("@MAGAZA_ID") && filter.StoreID > 0 && !WebSiteInfo.User.Value.IsOneStore)
            {
                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = filter.StoreID;
            }

            if (filter.GroupByOrderSources)
            {
                cmd.CommandText += " group by s.SIPARISKAYNAGI";
            }

            if (filter.isProductAvailable)
            {
                cmd.CommandText += " HAVING URUNSAYISI > 0";
            }

            if (filter.OrderDateStart.HasValue)
            {
                cmd.CommandText += " AND s.TARIH >= @sipbastarih ";
                cmd.Parameters.Add("@sipbastarih", MySqlDbType.DateTime).Value = filter.OrderDateStart;
            }

            if (filter.OrderDateFinish.HasValue)
            {
                cmd.CommandText += " AND s.TARIH <= @sipsontarih ";
                cmd.Parameters.Add("@sipsontarih", MySqlDbType.DateTime).Value = filter.OrderDateFinish.Value.AddDays(1);
            }

            if (filter.isCreateInvoice)
            {
                cmd.CommandText += " AND s.PAKETLEMEDURUM_ID = @PAKETLEMEDURUM_ID";
                cmd.Parameters.Add("@PAKETLEMEDURUM_ID", MySqlDbType.Int32).Value = (int)PackageStatus.FaturaKesildi;
            }

            if (filter.PaymentDateStart.HasValue)
            {
                cmd.CommandText += " AND s.ODEMETARIHI >= @odemebastarih ";
                cmd.Parameters.Add("@odemebastarih", MySqlDbType.DateTime).Value = filter.PaymentDateStart;
            }

            if (filter.PaymentDateFinish.HasValue)
            {
                cmd.CommandText += " AND s.ODEMETARIHI <= @odemesontarih ";
                cmd.Parameters.Add("@odemesontarih", MySqlDbType.DateTime).Value = filter.PaymentDateFinish;
            }

            if (filter.DeliveryDateStart.HasValue)
            {
                cmd.CommandText += " AND s.TESLIMATGUNU >= @TeslimatTarihBas ";
                cmd.Parameters.Add("@TeslimatTarihBas", MySqlDbType.Date).Value = filter.DeliveryDateStart;
            }

            if (filter.DeliveryDateFinish.HasValue)
            {
                cmd.CommandText += " AND s.TESLIMATGUNU <= @TeslimatTarihSon ";
                cmd.Parameters.Add("@TeslimatTarihSon", MySqlDbType.Date).Value = filter.DeliveryDateFinish;
            }

            if (!string.IsNullOrEmpty(filter.SetNo))
            {
                string table = filter.InvoiceCreated.Value ? "depo_dagitilan_urun_rapor" : "depo_dagitilan_urun";

                if (!string.IsNullOrEmpty(filter.ProductBarcode) && filter.InvoiceCreated.HasValue)
                {
                    cmd.CommandText += $" AND s.ID IN (SELECT SIPARIS_ID FROM {table} WHERE SET_NO = @SET_NO AND BARKOD = @BARKOD ";
                    cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
                    cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.ProductBarcode;
                }
                else
                {
                    cmd.CommandText += $" AND s.ID IN (SELECT SIPARIS_ID FROM {table} WHERE SET_NO = @SET_NO AND DEPO_ID = @DEPO_ID ";
                    cmd.Parameters.Add("@SET_NO", MySqlDbType.VarChar).Value = filter.SetNo;
                    cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
                }

                if (filter.WarehouseCarId > 0)
                {
                    cmd.CommandText += " AND ARABA_ID = @ARABA_ID ";
                    cmd.Parameters.Add("@ARABA_ID", MySqlDbType.Int32).Value = filter.WarehouseCarId;
                }

                cmd.CommandText += ")";
            }

            if (filter.ProductIds != null && filter.ProductIds.Count > 0)
                cmd.CommandText += $" AND s.ID IN (SELECT SIPARIS_ID FROM siparis_urun as su WHERE su.URUN_ID IN ({string.Join(',', filter.ProductIds)}))";

            if (!string.IsNullOrEmpty(filter.CargoCode))
            {
                cmd.CommandText += " AND s.ID = (SELECT Siparis_ID FROM siparis_kargo_paket AS skp WHERE skp.Barkod_Bilgisi = @KARGOKODU ORDER BY Siparis_ID DESC LIMIT 1)";
                cmd.Parameters.Add("@KARGOKODU", MySqlDbType.VarChar).Value = filter.CargoCode;
            }

            if (filter.MemberID > 0)
            {
                cmd.CommandText += " AND s.UYE_ID = @UYE_ID ";
                cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = filter.MemberID;
            }

            if (filter.CargoCompanyID > 0)
            {
                cmd.CommandText += " AND s.KARGOFIRMA_ID = @KARGOFIRMA_ID ";
                cmd.Parameters.Add("@KARGOFIRMA_ID", MySqlDbType.Int32).Value = filter.CargoCompanyID;
            }

            if (filter.CargoIDList.Count > 0)
            {
                cmd.CommandText += $" AND s.KARGOFIRMA_ID IN ({string.Join(",", filter.CargoIDList)})";
            }

            if (filter.PaymentTypeList.Count > 0)
            {
                cmd.CommandText += " AND s.ODEMETIPI IN (" + string.Join(",", filter.PaymentTypeList) + ")";
            }

            if (filter.ApprovedPayments)
            {
                cmd.CommandText += " AND s.ID IN (SELECT so.SIPARIS_ID FROM siparis_odeme AS so WHERE so.ONAYLANDI = 1 AND so.SIPARIS_ID = s.ID)";
            }

            if (filter.IsDelivery)
            {
                cmd.CommandText += " AND s.TESLIMATMAGAZA_ID = 0";
            }

            if (filter.IsConsignment.HasValue)
            {
                if (filter.IsConsignment.Value)
                    cmd.CommandText += $" AND s.ID IN (SELECT SIPARIS_ID FROM siparis_urun as su WHERE KONSINYEURUN = 1)";
                else
                    cmd.CommandText += $" AND s.ID IN (SELECT SIPARIS_ID FROM siparis_urun as su WHERE KONSINYEURUN = 0)";
            }

            if (!string.IsNullOrEmpty(filter.CustomerName))
            {
                cmd.CommandText += " AND s.UYEADI = @UYEADI ";
                cmd.Parameters.Add("@UYEADI", MySqlDbType.VarChar).Value = filter.CustomerName;
            }

            //if ((filter.PackagingStatus.HasValue && filter.PackagingStatus.Value == PackageStatus.FaturaBekliyor) || filter.OrderByID)
            //{
            //    cmd.CommandText += " ORDER BY ID DESC";
            //}
            //else if ()
            //else
            //{
            //    cmd.CommandText += " ORDER BY TESLIMATGUNU,TESLIMATSAATI";
            //}
        }

        private void AppendFilter(ref MySqlCommand cmd, OrderReportFilter filter)
        {
            if (filter.PackagingStatus.Count > 0)
            {
                cmd.CommandText += $@"  AND s.PAKETLEMEDURUM_ID IN ({string.Join(",", filter.PackagingStatus)})";
            }

            if (filter.FilterByOrderDate)
            {
                if (filter.OrderEndDate == null)
                {
                    cmd.CommandText += @"  AND DATE(s.TARIH) = DATE(@ORDERSTARTDATE)";
                }
                else
                {
                    cmd.CommandText += @"  AND DATE(s.TARIH) >= DATE(@ORDERSTARTDATE)
                                        AND DATE(s.TARIH) <= DATE(@ORDERENDDATE)";

                    cmd.Parameters.Add("@ORDERENDDATE", MySqlDbType.Date).Value = filter.OrderEndDate;
                }

                cmd.Parameters.Add("@ORDERSTARTDATE", MySqlDbType.Date).Value = filter.OrderStartDate;
            }

            if (filter.FilterByInvoiceDate)
            {
                if (filter.InvoiceEndDate == null)
                {
                    cmd.CommandText += @"  AND DATE(tf.TARIH) = DATE(@INVOICESTARTDATE)";
                }
                else
                {
                    cmd.CommandText += @"  AND DATE(tf.TARIH) >= DATE(@INVOICESTARTDATE)
                                        AND DATE(tf.TARIH) <= DATE(@INVOICEENDDATE)";

                    cmd.Parameters.Add("@INVOICEENDDATE", MySqlDbType.Date).Value = filter.InvoiceEndDate;
                }

                cmd.Parameters.Add("@INVOICESTARTDATE", MySqlDbType.Date).Value = filter.InvoiceStartDate;
            }

            if (filter.OrderStatuses.Count > 0)
            {
                cmd.CommandText += $@"  AND s.DURUM IN ({string.Join(",", filter.OrderStatuses)})";
            }

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = filter.WarehouseId;
        }

        public async Task UpdateOrderCargoIntegration(int orderId, int cargoIntegrationId, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(@"UPDATE siparis SET KARGOENTEGRASYON_ID = @KARGOENTEGRASYON_ID WHERE ID = @ID;");
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderId;
            cmd.Parameters.Add("@KARGOENTEGRASYON_ID", MySqlDbType.Int32).Value = cargoIntegrationId;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateOrderCargoIntegrationAndCargoCompany(int orderId, int cargoIntegrationId, int cargoCompanyId, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(@"UPDATE siparis SET KARGOENTEGRASYON_ID = @KARGOENTEGRASYON_ID, KARGOFIRMA_ID = @KARGOFIRMA_ID WHERE ID = @ID;");
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = orderId;
            cmd.Parameters.Add("@KARGOENTEGRASYON_ID", MySqlDbType.Int32).Value = cargoIntegrationId;
            cmd.Parameters.Add("@KARGOFIRMA_ID", MySqlDbType.Int32).Value = cargoCompanyId;
            await cmd.ExecuteTransactionCommandAsync();
        }


        public string GetToplamTutarQuery(string tableAlias, bool round, bool havaleDahil, bool onayBekleyenDahil)
        {
            string queryString = "";
            if (round)
            {
                queryString += "ROUND(";
            }

            queryString += "IFNULL(";
            if (onayBekleyenDahil && havaleDahil)
            {
                queryString += $"{tableAlias}.TOPLAMTUTAR_HAVALEOB";
            }
            else if (onayBekleyenDahil)
            {
                queryString += $"{tableAlias}.TOPLAMTUTAR_ONAYBEKLEYEN";
            }
            else if (havaleDahil)
            {
                queryString += $"{tableAlias}.TOPLAMTUTAR_HAVALE";
            }
            else
            {
                queryString += $"{tableAlias}.TOPLAMTUTAR_ONAYLANDI";
            }

            queryString += ", 0)";
            if (round)
            {
                queryString += ", 2)";
            }

            return queryString;
        }

        public static string GetOdenenTutarQuery(string tableAlias, bool round)
        {
            string queryString = "";
            if (round)
            {
                queryString += "ROUND(";
            }
            if (true)
            {
                queryString += $"{tableAlias}.ODENENTUTAR";
            }
            else
            {
                queryString += $"(SELECT IFNULL(SUM(TUTAR), 0) FROM siparis_odeme WHERE ODEMETIPI <> 20 AND SIPARIS_ID = {tableAlias}.ID AND ONAYLANDI = 1)";
            }
            if (round)
            {
                queryString += ", 2)";
            }
            return queryString;
        }

        private string GetAdminDashboardQueryColumn(bool isAdminDashboard)
        {
            string queryString = "";
            if (!isAdminDashboard)
            {
                if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                    queryString += "AND (siparis_urun.MAGAZA_ID = @MAGAZA_ID)";
                else
                    queryString += "AND (siparis_urun.MAGAZA_ID = 0 OR siparis_urun.MAGAZA_ID = @MAGAZA_ID)";
            }


            return queryString;
        }

        private string GetAdminDashboardWhereQuery(bool isAdminDashboard)
        {
            string queryString = "";
            if (!isAdminDashboard)
            {
                if (WebSiteInfo.User.Value.Settings.PacketSettings.OmniChannelDistributionActive)
                    queryString += "AND su.MAGAZA_ID = @MAGAZA_ID";
                else
                    queryString += "AND su.MAGAZA_ID = 0 OR su.MAGAZA_ID = @MAGAZA_ID";
            }

            return queryString;
        }

        private string GetPaymentType(int paymentType)
        {
            switch (paymentType)
            {
                case 0: return "Kredi Kartı";
                case 1: return "Havale";
                case 2: return "Kapıda Ödeme Nakit";
                case 3: return "Kapıda Ödeme Kredi Kartı";
                case 4: return "Mobil Ödeme";
                case 5: return "BkmEkspress";
                case 6: return "PayPal";
                case 7: return "Cari";
                case 8: return "MailOrder";
                case 9: return "iPara";
                case 10: return "Nakit";
                case 11: return "PayUOneClick";
                case 12: return "CariKredi";
                case 13: return "GarantiPay";
                case 14: return "PayuBkmEkspress";
                case 15: return "NestPay";
                case 16: return "PayCell";
                case 17: return "IyziPay";
                case 18: return "Hopi";
                case 19: return "PayByMe";
                case 20: return "Hediye Çeki";
                case 21: return "PayGuruMobil";
                case 22: return "Paynet";
                case 23: return "Telr";
                case 24: return "ComPay";
                case 25: return "PayTR";
                case 26: return "MaximumMobil";
                case 27: return "MagazadaOde";
                case 28: return "OzelOdeme";
                case 29: return "Bakiye";
                case 30: return "ParamPos";
                case 31: return "PayWithIyzico";
                case 32: return "Sofort";
                case 33: return "Sipay";
                case 34: return "Lidio";
                case 35: return "Sodexo";
                case 36: return "Ozan";
                case 37: return "Stripe";
                case 38: return "AzerbeycanYKB";
                case 39: return "FastPass";
                case 40: return "YKBWorldPay";
                case 41: return "IyziPayAlisverisKredisi";
                case 48: return "HepsiPayV2";
                default: return "";
            }
        }
    }
}