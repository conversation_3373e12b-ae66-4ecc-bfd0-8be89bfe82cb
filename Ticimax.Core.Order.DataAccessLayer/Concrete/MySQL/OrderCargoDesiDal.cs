using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.DataAccessLayer.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;

namespace Ticimax.Core.Order.DataAccessLayer.Concrete.MySQL
{
    public class OrderCargoDesiDal : IOrderCargoDesiDal
    {
        private readonly MySqlConnection _cnn;

        public OrderCargoDesiDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task Add(OrderCargoDesi entity, CancellationToken cancellationToken)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"INSERT INTO siparis_kargodesi
                                                (SIPARIS_ID
                                                , DESI
                                                , KULLANICI_ID
                                                , ISLEMYERI
                                                , TARIH)
                                    VALUES (@SIPARIS_ID
                                          , @DESI
                                          , @KULLANICI_ID
                                          , @ISLEMYERI
                                          , @TARIH);";

            //TODO : Bu işlem order'da yapılacak ve devamında businessda ayrılacak.
            //if (siparisTablosuGuncelle)
            //{
            //    cmd.CommandText += "UPDATE siparis SET OZELALAN3 = @DESI WHERE ID = @SIPARIS_ID;";
            //}
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@DESI", MySqlDbType.Double).Value = entity.Desi;
            cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = entity.MemberID;
            cmd.Parameters.Add("@ISLEMYERI", MySqlDbType.VarChar).Value = entity.OperationPlace;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = entity.Date;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            //_cnn.Close();
        }

        public async Task<int> GetCount(OrderCargoDesiFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM siparis_kargodesi
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            //_cnn.Close();
            return count;
        }

        public async Task<List<OrderCargoDesi>> GetList(OrderCargoDesiFilter filter = null, OrderCargoDesiPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            List<OrderCargoDesi> list = new List<OrderCargoDesi>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT      ID
                                          , SIPARIS_ID
                                          , DESI
                                          , KULLANICI_ID
                                          , ISLEMYERI
                                          , TARIH
                                    FROM siparis_kargodesi
                                    WHERE 1";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                OrderCargoDesi p = new OrderCargoDesi();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.OrderID = Convert.ToInt32(Reader["SIPARIS_ID"]);
                p.MemberID = Convert.ToInt32(Reader["KULLANICI_ID"]);
                p.Desi = Convert.ToDouble(Reader["DESI"]);
                p.OperationPlace = Reader["ISLEMYERI"].ToString();
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            //_cnn.Close();
            return list;
        }

        private void AppendFilter(ref MySqlCommand cmd, OrderCargoDesiFilter filter)
        {
            if (filter != null)
            {
                if (filter.OrderID.HasValue)
                {
                    cmd.CommandText += " AND SIPARIS_ID = @SIPARIS_ID";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderID.Value;
                }

                if (filter.MemberID.HasValue)
                {
                    cmd.CommandText += " AND KULLANICI_ID = @KULLANICI_ID";
                    cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = filter.MemberID;
                }

                if (!string.IsNullOrEmpty(filter.OperationPlace))
                {
                    cmd.CommandText += " AND ISLEMYERI = @ISLEMYERI";
                    cmd.Parameters.Add("@ISLEMYERI", MySqlDbType.VarChar).Value = filter.OperationPlace;
                }

                if (filter.DateStart.HasValue)
                {
                    cmd.CommandText += " AND TARIH >= @TARIHBAS";
                    cmd.Parameters.Add("@TARIHBAS", MySqlDbType.DateTime).Value = filter.DateStart.Value;
                }

                if (filter.DateFinish.HasValue)
                {
                    cmd.CommandText += " AND TARIH < @TARIHSON";
                    cmd.Parameters.Add("@TARIHSON", MySqlDbType.DateTime).Value = filter.DateFinish.Value;
                }
            }
        }
    }
}