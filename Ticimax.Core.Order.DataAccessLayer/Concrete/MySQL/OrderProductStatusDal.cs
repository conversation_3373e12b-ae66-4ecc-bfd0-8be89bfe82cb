using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.DataAccessLayer.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;

namespace Ticimax.Core.Order.DataAccessLayer.Concrete.MySQL
{
    public class OrderProductStatusDal : IOrderProductStatusDal
    {
        private IDbConnection _cnn;

        public OrderProductStatusDal(IDbConnection cnn)
        {
            _cnn = cnn;
        }

        public async Task Add(OrderProductStatus entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = "INSERT INTO siparis_urun_durumlari (Tanim, Islem, Aktif) VALUES (@Tanim, @Islem, @Aktif)";
            cmd.Parameters.Add("@Tanim", MySqlDbType.VarChar).Value = entity.Definition;
            cmd.Parameters.Add("@Islem", MySqlDbType.Int32).Value = entity.Operation;
            cmd.Parameters.Add("@Aktif", MySqlDbType.Bit).Value = entity.Active;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            _cnn.Close();
            
        }

        public async Task Delete(OrderProductStatus entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = "DELETE FROM siparis_urun_durumlari WHERE ID = @ID";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            _cnn.Close();
            
        }

        public async Task<List<OrderProductStatus>> GetList(OrderProductStatusFilter filter = null, OrderProductStatusPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            List<OrderProductStatus> list = new List<OrderProductStatus>();
            MySqlCommand cmd = new MySqlCommand("", (MySqlConnection)_cnn);
            cmd.CommandText = "SELECT * FROM siparis_urun_durumlari WHERE 1";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                OrderProductStatus obje = new OrderProductStatus();
                obje.ID = Convert.ToInt32(mdr["ID"]);
                obje.Definition = mdr["Tanim"].ToString();
                obje.Operation = Convert.ToInt32(mdr["Islem"]);
                obje.Active = Convert.ToBoolean(mdr["Aktif"]);
                list.Add(obje);
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            _cnn.Close();
            
            return list;
        }

        public async Task Update(OrderProductStatus entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = "UPDATE siparis_urun_durumlari SET Tanim = @Tanim, Islem = @Islem, Aktif = @Aktif WHERE ID = @ID";
            cmd.Parameters.Add("@Tanim", MySqlDbType.Int32).Value = entity.Definition;
            cmd.Parameters.Add("@Islem", MySqlDbType.Int32).Value = entity.Operation;
            cmd.Parameters.Add("@Aktif", MySqlDbType.Int32).Value = entity.Active;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            _cnn.Close();
            
        }

        public async Task<int> GetCount(OrderProductStatusFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM siparis_urun_durumlari
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            return count;
        }

        public async Task AddMultiple(List<OrderProductStatus> list, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            List<string> valueList = new List<string>();
            for (int i = 0; i < list.Count; i++)
            {
                valueList.Add($"(@TANIM{i}, @ISLEM{i}, @AKTIF{i})");
                cmd.Parameters.Add($"@TANIM{i}", MySqlDbType.VarChar).Value = list[i].Definition;
                cmd.Parameters.Add($"@ISLEM{i}", MySqlDbType.Int32).Value = list[i].Operation;
                cmd.Parameters.Add($"@AKTIF{i}", MySqlDbType.Bit).Value = list[i].Active;
            }

            cmd.CommandText += $"INSERT INTO siparis_urun_durumlari (Tanim, Islem, Aktif) VALUES {string.Join(",", valueList)}";

            await cmd.ExecuteTransactionCommandAsync();
        }



        private void AppendFilter(ref MySqlCommand cmd, OrderProductStatusFilter filter)
        {
            if (filter != null)
            {
                if (filter.OrderProductStatusID > 0)
                {
                    cmd.CommandText += " AND ID = @ID ";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.OrderProductStatusID;
                }

                if (filter.Operation > 0)
                {
                    cmd.CommandText += " AND Islem = @Islem ";
                    cmd.Parameters.Add("@Islem", MySqlDbType.Int32).Value = filter.Operation;
                }

                if (filter.Active > -1)
                {
                    cmd.CommandText += " AND Aktif = @Aktif ";
                    cmd.Parameters.Add("@Aktif", MySqlDbType.Int32).Value = filter.Active;
                }
            }
        }
    }
}