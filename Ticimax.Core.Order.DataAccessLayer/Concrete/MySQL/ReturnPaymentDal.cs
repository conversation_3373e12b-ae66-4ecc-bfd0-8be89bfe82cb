using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.DataAccessLayer.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;

namespace Ticimax.Core.Order.DataAccessLayer.Concrete.MySQL
{
    public class ReturnPaymentDal : IReturnPaymentDal
    {
        private IDbConnection _cnn;

        public ReturnPaymentDal(IDbConnection cnn)
        {
            _cnn = cnn;
        }

        public async Task Add(ReturnPayment entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"INSERT INTO iade_odeme_liste (SIPARIS_ID
                                                            , ODEME_ID
                                                            , <PERSON><PERSON><PERSON>ANIC<PERSON>_<PERSON>
                                                            , KULLANICI_ADI
                                                            , WMS
                                                            , TUTAR
                                                            , PARABIRIMI
                                                            , ODEMETIPI
                                                            , IBANADSOYAD
                                                            , IBAN
                                                            , TARIH
                                                            , NOTLAR
                                                            , UYE_ID
                                                            , UYE_ADI
                                                            , ODENDI
                                                            , BAKIYEIADESI
                                                          )
                                                          VALUES
                                                            (@SIPARIS_ID
                                                            , @ODEME_ID
                                                            , @KULLANICI_ID
                                                            , @KULLANICI_ADI
                                                            , @WMS
                                                            , @TUTAR
                                                            , @PARABIRIMI
                                                            , @ODEMETIPI
                                                            , @IBANADSOYAD
                                                            , @IBAN
                                                            , @TARIH
                                                            , @NOTLAR
                                                            , @UYE_ID
                                                            , @UYE_ADI
                                                            , @ODENDI
                                                            , @BAKIYEIADESI
                                                            );";

            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@ODEME_ID", MySqlDbType.Int32).Value = entity.PaymentID;
            cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@KULLANICI_ADI", MySqlDbType.VarChar).Value = entity.PersonName;
            cmd.Parameters.Add("@WMS", MySqlDbType.Int16).Value = 1;
            cmd.Parameters.Add("@TUTAR", MySqlDbType.Double).Value = entity.Amount;
            cmd.Parameters.Add("@PARABIRIMI", MySqlDbType.VarChar).Value = entity.CurrencyCode;
            cmd.Parameters.Add("@ODEMETIPI", MySqlDbType.Int32).Value = entity.PaymentType;
            cmd.Parameters.Add("@IBANADSOYAD", MySqlDbType.VarChar).Value = entity.IBANNameAndLastName;
            cmd.Parameters.Add("@IBAN", MySqlDbType.VarChar).Value = entity.IBAN;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = entity.Date;
            cmd.Parameters.Add("@NOTLAR", MySqlDbType.Text).Value = entity.Notes;
            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = entity.MemberID;
            cmd.Parameters.Add("@UYE_ADI", MySqlDbType.VarChar).Value = entity.MemberName;
            cmd.Parameters.Add("@ODENDI", MySqlDbType.Int16).Value = entity.isPaid;
            cmd.Parameters.Add("@BAKIYEIADESI", MySqlDbType.Int16).Value = entity.isBalanceReturn;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> AddReturnID(ReturnPayment entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"INSERT INTO iade_odeme_liste (SIPARIS_ID
                                                            , ODEME_ID
                                                            , KULLANICI_ID
                                                            , KULLANICI_ADI
                                                            , WMS
                                                            , TUTAR
                                                            , PARABIRIMI
                                                            , ODEMETIPI
                                                            , IBANADSOYAD
                                                            , IBAN
                                                            , TARIH
                                                            , NOTLAR
                                                            , UYE_ID
                                                            , UYE_ADI
                                                            , ODENDI
                                                            , BAKIYEIADESI
                                                          )
                                                          VALUES
                                                            (@SIPARIS_ID
                                                            , @ODEME_ID
                                                            , @KULLANICI_ID
                                                            , @KULLANICI_ADI
                                                            , @WMS
                                                            , @TUTAR
                                                            , @PARABIRIMI
                                                            , @ODEMETIPI
                                                            , @IBANADSOYAD
                                                            , @IBAN
                                                            , @TARIH
                                                            , @NOTLAR
                                                            , @UYE_ID
                                                            , @UYE_ADI
                                                            , @ODENDI
                                                            , @BAKIYEIADESI
                                                            );";

            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@ODEME_ID", MySqlDbType.Int32).Value = entity.PaymentID;
            cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@KULLANICI_ADI", MySqlDbType.VarChar).Value = entity.PersonName;
            cmd.Parameters.Add("@WMS", MySqlDbType.Int16).Value = 1;
            cmd.Parameters.Add("@TUTAR", MySqlDbType.Double).Value = entity.Amount;
            cmd.Parameters.Add("@PARABIRIMI", MySqlDbType.VarChar).Value = entity.CurrencyCode;
            cmd.Parameters.Add("@ODEMETIPI", MySqlDbType.Int32).Value = entity.PaymentType;
            cmd.Parameters.Add("@IBANADSOYAD", MySqlDbType.VarChar).Value = entity.IBANNameAndLastName;
            cmd.Parameters.Add("@IBAN", MySqlDbType.VarChar).Value = entity.IBAN;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = entity.Date;
            cmd.Parameters.Add("@NOTLAR", MySqlDbType.Text).Value = entity.Notes;
            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = entity.MemberID;
            cmd.Parameters.Add("@UYE_ADI", MySqlDbType.VarChar).Value = entity.MemberName;
            cmd.Parameters.Add("@ODENDI", MySqlDbType.Int16).Value = entity.isPaid;
            cmd.Parameters.Add("@BAKIYEIADESI", MySqlDbType.Int16).Value = entity.isBalanceReturn;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            int lastInsertedID = cmd.LastInsertedId.ToInt32();
            await cmd.DisposeAsync();
            _cnn.Close();
            return lastInsertedID;
        }

        public async Task CustomerPayDoorRed(int customerID, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = "UPDATE uyeler SET KAPIDAODEMEYASAKLI = 1 WHERE ID = @UYE_ID";
            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = customerID;
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task Delete(ReturnPayment entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"DELETE FROM iade_odeme_liste WHERE ID = @ID;";
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            _cnn.Close();
        }

        public async Task<int> GetCount(ReturnPaymentFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM iade_odeme_liste
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            _cnn.Close();
            return count;
        }

        public async Task<List<ReturnPayment>> GetList(ReturnPaymentFilter filter = null, ReturnPaymentPaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            List<ReturnPayment> list = new List<ReturnPayment>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT ID
                                      , SIPARIS_ID
                                      , ODEME_ID
                                      , KULLANICI_ID
                                      , KULLANICI_ADI
                                      , TUTAR
                                      , PARABIRIMI
                                      , ODEMETIPI
                                      , IBANADSOYAD
                                      , IBAN
                                      , TARIH
                                      , NOTLAR
                                      , UYE_ID
                                      , UYE_ADI
                                      , ODENDI
                                      , BAKIYEIADESI
                                    FROM
                                      iade_odeme_liste
                                    WHERE 1 ";

            AppendFilter(ref cmd, filter);

            cmd.CommandText += " ORDER BY TARIH DESC";

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                ReturnPayment p = new ReturnPayment();
                p.ID = Reader["ID"].ToInt32();
                p.OrderID = Reader["SIPARIS_ID"].ToInt32();
                p.PaymentID = Reader["ODEME_ID"].ToInt32();
                p.MemberID = Reader["KULLANICI_ID"].ToInt32();
                p.MemberName = Reader["KULLANICI_ADI"].ToString();
                p.Amount = Reader["TUTAR"].ToDouble();
                p.CurrencyCode = Reader["PARABIRIMI"].ToString();
                p.PaymentType = Reader["ODEMETIPI"].ToInt32();
                p.IBANNameAndLastName = Reader["IBANADSOYAD"].ToString();
                p.IBAN = Reader["IBAN"].ToString();
                p.Date = Reader["TARIH"].ToDateTime();
                p.Notes = Reader["NOTLAR"].ToString();
                p.MemberID = Reader["UYE_ID"].ToInt32();
                p.MemberName = Reader["UYE_ADI"].ToString();
                p.isPaid = Reader["ODENDI"].ToBoolean();
                p.isBalanceReturn = Reader["BAKIYEIADESI"].ToBoolean();
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            _cnn.Close();
            return list;
        }

        public async Task Update(ReturnPayment entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"UPDATE
                                  iade_odeme_liste
                                SET SIPARIS_ID = @SIPARIS_ID
                                  , ODEME_ID = @ODEME_ID
                                  , KULLANICI_ID = @KULLANICI_ID
                                  , KULLANICI_ADI = @KULLANICI_ADI
                                  , UYE_ID = @UYE_ID
                                  , UYE_ADI = @UYE_ADI
                                  , TUTAR = @TUTAR
                                  , PARABIRIMI = @PARABIRIMI
                                  , ODEMETIPI = @ODEMETIPI
                                  , IBANADSOYAD = @IBANADSOYAD
                                  , IBAN = @IBAN
                                  , TARIH = @TARIH
                                  , NOTLAR = @NOTLAR
                                  , ODENDI = @ODENDI
                                  , BAKIYEIADESI = @BAKIYEIADESI
                                WHERE ID = @ID;";

            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@ODEME_ID", MySqlDbType.Int32).Value = entity.PaymentID;
            cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@KULLANICI_ADI", MySqlDbType.VarChar).Value = entity.PersonName;
            cmd.Parameters.Add("@TUTAR", MySqlDbType.Double).Value = entity.Amount;
            cmd.Parameters.Add("@PARABIRIMI", MySqlDbType.VarChar).Value = entity.CurrencyCode;
            cmd.Parameters.Add("@ODEMETIPI", MySqlDbType.Int32).Value = entity.PaymentType;
            cmd.Parameters.Add("@IBANADSOYAD", MySqlDbType.VarChar).Value = entity.IBANNameAndLastName;
            cmd.Parameters.Add("@IBAN", MySqlDbType.VarChar).Value = entity.IBAN;
            cmd.Parameters.Add("@TARIH", MySqlDbType.DateTime).Value = entity.Date;
            cmd.Parameters.Add("@NOTLAR", MySqlDbType.Text).Value = entity.Notes;
            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = entity.MemberID;
            cmd.Parameters.Add("@UYE_ADI", MySqlDbType.VarChar).Value = entity.MemberName;
            cmd.Parameters.Add("@ODENDI", MySqlDbType.Int16).Value = entity.isPaid;
            cmd.Parameters.Add("@BAKIYEIADESI", MySqlDbType.Int16).Value = entity.isBalanceReturn;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.DisposeAsync();
            _cnn.Close();
        }

        public async Task UpdatePaid(ReturnPayment entity, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"UPDATE iade_odeme_liste
                                        SET ODENDI = @ODENDI
                                        WHERE ID = @ID";

            cmd.Parameters.Add("@ODENDI", MySqlDbType.Int16).Value = entity.isPaid;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteTransactionCommandAsync();
        }



        private void AppendFilter(ref MySqlCommand cmd, ReturnPaymentFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID.HasValue)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID.Value;
                }

                if (filter.OrderID.HasValue)
                {
                    cmd.CommandText += " AND SIPARIS_ID = @SIPARIS_ID";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderID.Value;
                }

                if (filter.PaymentID.HasValue)
                {
                    cmd.CommandText += " AND ODEME_ID = @ODEME_ID";
                    cmd.Parameters.Add("@ODEME_ID", MySqlDbType.Int32).Value = filter.PaymentID.Value;
                }

                if (filter.PersonID.HasValue)
                {
                    cmd.CommandText += " AND KULLANICI_ID = @KULLANICI_ID";
                    cmd.Parameters.Add("@KULLANICI_ID", MySqlDbType.Int32).Value = filter.PersonID.Value;
                }

                if (filter.PaymentType.HasValue)
                {
                    cmd.CommandText += " AND ODEMETIPI = @ODEMETIPI";
                    cmd.Parameters.Add("@ODEMETIPI", MySqlDbType.Int32).Value = filter.PaymentType.Value;
                }

                if (!string.IsNullOrEmpty(filter.IBANNameAndLastName))
                {
                    cmd.CommandText += " AND IBANADSOYAD LIKE @IBANADSOYAD";
                    cmd.Parameters.Add("@IBANADSOYAD", MySqlDbType.VarChar).Value = "%" + filter.IBANNameAndLastName + "%";
                }

                if (!string.IsNullOrEmpty(filter.IBAN))
                {
                    cmd.CommandText += " AND IBAN LIKE @IBAN";
                    cmd.Parameters.Add("@IBAN", MySqlDbType.VarChar).Value = "%" + filter.IBAN + "%";
                }

                if (filter.DateStart.HasValue)
                {
                    cmd.CommandText += " AND TARIH >= @TARIHBAS";
                    cmd.Parameters.Add("@TARIHBAS", MySqlDbType.DateTime).Value = filter.DateStart.Value;
                }

                if (filter.DateFinish.HasValue)
                {
                    cmd.CommandText += " AND TARIH < @TARIHSON";
                    cmd.Parameters.Add("@TARIHSON", MySqlDbType.DateTime).Value = filter.DateFinish.Value;
                }

                if (filter.MemberID.HasValue)
                {
                    cmd.CommandText += " AND UYE_ID = @UYE_ID";
                    cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = filter.MemberID.Value;
                }

                if (filter.isPaid.HasValue)
                {
                    cmd.CommandText += " AND ODENDI = @ODENDI";
                    cmd.Parameters.Add("@ODENDI", MySqlDbType.Int16).Value = filter.isPaid.Value;
                }

                if (filter.isBalanceReturn.HasValue)
                {
                    cmd.CommandText += " AND BAKIYEIADESI = @BAKIYEIADESI";
                    cmd.Parameters.Add("@BAKIYEIADESI", MySqlDbType.Int16).Value = filter.isBalanceReturn.Value;
                }
            }
        }
    }
}