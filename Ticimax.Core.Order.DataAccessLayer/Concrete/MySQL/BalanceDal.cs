using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using MySqlConnector;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.DataAccessLayer.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;

namespace Ticimax.Core.Order.DataAccessLayer.Concrete.MySQL
{
    public class BalanceDal : IBalanceDal
    {
        private IDbConnection _cnn;

        public BalanceDal(IDbConnection cnn)
        {
            _cnn = cnn;
        }

        public async Task Add(Balance entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"INSERT INTO bakiye
                                                (ACIKLAMA
                                                 , TUTAR
                                                 , DOVIZKODU
                                                 , TIP
                                                 , UYE_ID
                                                 , EKLEMETARIHI
                                                 , EKLEYENKULLANICI
                                                 , EKLEYENKULLANICIADI
                                                 , WMS
                                                 , SIPARIS_ID
                                                 , ODEME_ID
                                                 , ODEMETIPI
                                                 , KALAN_IADE_TUTAR
                                                )
                                        VALUES (@ACIKLAMA
                                                , @TUTAR
                                                , @DOVIZKODU
                                                , @TIP
                                                , @UYE_ID
                                                , @EKLEMETARIHI
                                                , @EKLEYENKULLANICI
                                                , @EKLEYENKULLANICIADI
                                                , @WMS
                                                , @SIPARIS_ID
                                                , @ODEME_ID
                                                , @ODEMETIPI
                                                , @KALAN_IADE_TUTAR
                                                )";

            cmd.Parameters.Add("@ACIKLAMA", MySqlDbType.VarChar).Value = entity.Description;
            cmd.Parameters.Add("@TUTAR", MySqlDbType.Double).Value = entity.Amount;
            cmd.Parameters.Add("@DOVIZKODU", MySqlDbType.VarChar).Value = entity.CurrencyCode;
            cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = entity.Type;
            cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = entity.PersonID;
            cmd.Parameters.Add("@EKLEMETARIHI", MySqlDbType.DateTime).Value = entity.AddingDate;
            cmd.Parameters.Add("@EKLEYENKULLANICI", MySqlDbType.Int32).Value = entity.AddingPersonID;
            cmd.Parameters.Add("@EKLEYENKULLANICIADI", MySqlDbType.VarChar).Value = entity.AddingPerson;
            cmd.Parameters.Add("@WMS", MySqlDbType.Int16).Value = 1;
            cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = entity.OrderID;
            cmd.Parameters.Add("@ODEME_ID", MySqlDbType.Int32).Value = entity.PaymentID;
            cmd.Parameters.Add("@ODEMETIPI", MySqlDbType.Int32).Value = entity.PaymentType;
            cmd.Parameters.Add("@KALAN_IADE_TUTAR", MySqlDbType.Double).Value = entity.RemainingRefundAmount;

            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            _cnn.Close();
        }

        public async Task<int> GetCount(BalanceFilter filter = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT COUNT(*)
                                FROM bakiye
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            _cnn.Close();
            return count;
        }

        public async Task<List<Balance>> GetList(BalanceFilter filter = null, BalancePaging paging = null, CancellationToken cancellationToken = default)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            List<Balance> list = new List<Balance>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT  ID
                                      , UYE_ID
                                      , ACIKLAMA
                                      , TUTAR
                                      , DOVIZKODU
                                      , TIP
                                      , EKLEMETARIHI
                                      , EKLEYENKULLANICI
                                      , EKLEYENKULLANICIADI
                                      , SIPARIS_ID
                                      , ODEME_ID
                                      , ODEMETIPI
                                      , KALAN_IADE_TUTAR
                                    FROM bakiye
                                WHERE 1 ";

            AppendFilter(ref cmd, filter);

            PagingExtension.AppendPaging(ref cmd, paging);

            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await Reader.ReadAsync(cancellationToken))
            {
                Balance p = new Balance();
                p.ID = Convert.ToInt32(Reader["ID"]);
                p.Description = Reader["ACIKLAMA"].ToString();
                p.Amount = Convert.ToDouble(Reader["TUTAR"]);
                p.CurrencyCode = Reader["DOVIZKODU"].ToString();
                p.Type = Convert.ToInt32(Reader["TIP"]);
                p.PersonID = Convert.ToInt32(Reader["UYE_ID"]);
                p.AddingDate = Convert.ToDateTime(Reader["EKLEMETARIHI"]);
                p.AddingPersonID = Convert.ToInt32(Reader["EKLEYENKULLANICI"]);
                p.AddingPerson = Reader["EKLEYENKULLANICIADI"].ToString();
                p.OrderID = Convert.ToInt32(Reader["SIPARIS_ID"]);
                p.PaymentID = Convert.ToInt32(Reader["ODEME_ID"]);
                p.PaymentType = Convert.ToInt32(Reader["ODEMETIPI"]);
                p.RemainingRefundAmount = Convert.ToDouble(Reader["KALAN_IADE_TUTAR"]);
                list.Add(p);
            }

            await Reader.CloseAsync();
            await Reader.DisposeAsync();
            await cmd.DisposeAsync();
            _cnn.Close();

            return list;
        }

        public async Task ReduceTheRemainingRefundAmount(Balance entity, CancellationToken cancellationToken)
        {
            _cnn = new MySqlConnection();
            _cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            _cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"UPDATE bakiye SET KALAN_IADE_TUTAR = KALAN_IADE_TUTAR - @KALAN_IADE_TUTAR WHERE ID = @ID";
            cmd.Parameters.Add("@KALAN_IADE_TUTAR", MySqlDbType.Double).Value = entity.RemainingRefundAmount;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = entity.ID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            await cmd.DisposeAsync();
            _cnn.Close();
        }

        private void AppendFilter(ref MySqlCommand cmd, BalanceFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID.HasValue)
                {
                    cmd.CommandText += " AND ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID.Value;
                }

                if (filter.PersonID.HasValue)
                {
                    cmd.CommandText += " AND UYE_ID = @UYE_ID";
                    cmd.Parameters.Add("@UYE_ID", MySqlDbType.Int32).Value = filter.PersonID.Value;
                }

                if (!string.IsNullOrEmpty(filter.CurrencyCode))
                {
                    cmd.CommandText += " AND DOVIZKODU = @DOVIZKODU";
                    cmd.Parameters.Add("@DOVIZKODU", MySqlDbType.VarChar).Value = filter.CurrencyCode;
                }

                if (filter.Type.HasValue)
                {
                    cmd.CommandText += " AND TIP = @TIP";
                    cmd.Parameters.Add("@TIP", MySqlDbType.Int32).Value = filter.Type.Value;
                }

                if (filter.OrderID.HasValue)
                {
                    cmd.CommandText += " AND SIPARIS_ID = @SIPARIS_ID";
                    cmd.Parameters.Add("@SIPARIS_ID", MySqlDbType.Int32).Value = filter.OrderID.Value;
                }

                if (filter.PaymentID.HasValue)
                {
                    cmd.CommandText += " AND ODEME_ID = @ODEME_ID";
                    cmd.Parameters.Add("@ODEME_ID", MySqlDbType.Int32).Value = filter.PaymentID.Value;
                }

                if (filter.isRemainingRefundAmount.HasValue)
                {
                    if (filter.isRemainingRefundAmount.Value)
                    {
                        cmd.CommandText += " AND KALAN_IADE_TUTAR > 0";
                    }
                    else
                    {
                        cmd.CommandText += " AND KALAN_IADE_TUTAR = 0";
                    }
                }

                if (filter.DateStart.HasValue)
                {
                    cmd.CommandText += " AND EKLEMETARIHI >= @TARIHBAS";
                    cmd.Parameters.Add("@TARIHBAS", MySqlDbType.DateTime).Value = filter.DateStart.Value;
                }

                if (filter.DateFinish.HasValue)
                {
                    cmd.CommandText += " AND EKLEMETARIHI <= @TARIHSON";
                    cmd.Parameters.Add("@TARIHSON", MySqlDbType.DateTime).Value = filter.DateFinish.Value;
                }
            }
        }
    }
}