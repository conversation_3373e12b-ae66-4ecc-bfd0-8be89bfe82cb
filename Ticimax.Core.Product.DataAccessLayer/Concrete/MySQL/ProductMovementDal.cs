using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Product.DataAccessLayer.Abstract;
using Ticimax.Core.Product.Entities.Concrete;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Core.Product.DataAccessLayer.Concrete.MySQL
{
#nullable enable
    public class ProductMovementDal : IProductMovementDal
    {
        private readonly MySqlConnection _cnn;

        public ProductMovementDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<ProductMovementAggregate>> GetList(int productId, int? memberId, List<string>? processTypeList, long? startDate, long? endDate, int pageIndex, int pageSize, CancellationToken cancellationToken)
        {
            if (productId <= 0)
                throw new BusinessException("PRODUCT_IS_REQUIRED");

//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            await _cnn.OpenAsync(cancellationToken);
            List<ProductMovementAggregate> aggregates = new List<ProductMovementAggregate>();

            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "SELECT ID, PRODUCT_ID, MEMBER_ID, MEMBER_NAME, PROCESS_TYPE, PIECE, MESSAGE, CREATED_DATE, LAST_MODIFIED_DATE, NOTE FROM depo_urun_hareket WHERE PRODUCT_ID = @PRODUCT_ID ";

            AppendFilter(ref cmd, productId, memberId, processTypeList, startDate, endDate);
            PagingExtension.AppendPaging(ref cmd, new Paging
            { PageNo = pageIndex, RecordNumber = pageSize });

            var mdr = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await mdr.ReadAsync(cancellationToken))
            {
                aggregates.Add(new ProductMovementAggregate
                {
                    Id = mdr["ID"].ByteArrayToGuid(),
                    ProductId = mdr["PRODUCT_ID"].ToInt32(),
                    MemberId = mdr["MEMBER_ID"].ToInt32(),
                    MemberName = mdr["MEMBER_NAME"].ToString(),
                    ProcessType = mdr["PROCESS_TYPE"].ToString(),
                    Note = mdr["NOTE"].ToString(),
                    Piece = mdr["PIECE"].ToDouble(),
                    Message = mdr["MESSAGE"].ToString(),
                    CreatedDate = mdr["CREATED_DATE"].ToInt64(),
                    LastModifiedDate = mdr["LAST_MODIFIED_DATE"].ToInt64()
                });
            }

            await mdr.CloseAsync();
            await mdr.DisposeAsync();
            await cmd.DisposeAsync();
            //await _cnn.CloseAsync();
            return aggregates;
        }

        public async Task Create(ProductMovementAggregate aggregate, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty, _cnn);
            cmd.CommandText = "INSERT INTO depo_urun_hareket(ID, PRODUCT_ID, MEMBER_ID, MEMBER_NAME, PROCESS_TYPE, PIECE, MESSAGE, CREATED_DATE, LAST_MODIFIED_DATE, NOTE) VALUES(UUID_TO_BIN(@ID), @PRODUCT_ID, @MEMBER_ID, @MEMBER_NAME, @PROCESS_TYPE, @PIECE, @MESSAGE, @CREATED_DATE, @LAST_MODIFIED_DATE, @NOTE)";
            cmd.Parameters.Add("@ID", MySqlDbType.Guid).Value = aggregate.Id;
            cmd.Parameters.Add("@PRODUCT_ID", MySqlDbType.Int32).Value = aggregate.ProductId;
            cmd.Parameters.Add("@MEMBER_ID", MySqlDbType.Int32).Value = aggregate.MemberId;
            cmd.Parameters.Add("@MEMBER_NAME", MySqlDbType.VarChar).Value = aggregate.MemberName;
            cmd.Parameters.Add("@PROCESS_TYPE", MySqlDbType.VarChar).Value = aggregate.ProcessType;
            cmd.Parameters.Add("@PIECE", MySqlDbType.Double).Value = aggregate.Piece;
            cmd.Parameters.Add("@MESSAGE", MySqlDbType.VarChar).Value = aggregate.Message;
            cmd.Parameters.Add("@CREATED_DATE", MySqlDbType.Int64).Value = aggregate.CreatedDate;
            cmd.Parameters.Add("@LAST_MODIFIED_DATE", MySqlDbType.Int64).Value = aggregate.LastModifiedDate;
            cmd.Parameters.Add("@NOTE", MySqlDbType.VarChar).Value = aggregate.Note;
            await cmd.ExecuteTransactionCommandAsync();
        }

        private void AppendFilter(ref MySqlCommand cmd, int productId, int? memberId, List<string>? processTypeList, long? startDate, long? endDate)
        {
            cmd.Parameters.Add("@PRODUCT_ID", MySqlDbType.Int32).Value = productId;

            if (memberId.HasValue)
            {
                cmd.CommandText += " AND MEMBER_ID = @MEMBER_ID";
                cmd.Parameters.Add("@MEMBER_ID", MySqlDbType.Int32).Value = memberId.Value;
            }

            if(processTypeList != null && processTypeList.Count > 0)
            {
                cmd.CommandText += $" AND PROCESS_TYPE IN ({string.Join(",", processTypeList.Select(x => $"'{x}'"))})";
            }
          

            if (startDate.HasValue)
            {
                cmd.CommandText += " AND CREATED_DATE >= @START_DATE";
                cmd.Parameters.Add("@START_DATE", MySqlDbType.Int64).Value = startDate.Value;
            }

            if (endDate.HasValue)
            {
                cmd.CommandText += " AND CREATED_DATE <= @END_DATE";
                cmd.Parameters.Add("@END_DATE", MySqlDbType.Int64).Value = endDate.Value;
            }

            cmd.CommandText += " ORDER BY CREATED_DATE DESC";
        }
    }
}