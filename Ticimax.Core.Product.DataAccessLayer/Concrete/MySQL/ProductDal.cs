using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BarcodeLib;
using iText.StyledXmlParser.Jsoup.Select;
using MySqlConnector;
using StackExchange.Redis;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Product.DataAccessLayer.Abstract;
using Ticimax.Core.Product.Entities.Concrete;
using Ticimax.Core.Product.Entities.Dtos;
using Ticimax.Core.Product.Entities.Filters;
using Ticimax.Core.Product.Entities.Pagings;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Entities.Static;
using static iText.StyledXmlParser.Jsoup.Select.Evaluator;

namespace Ticimax.Core.Product.DataAccessLayer.Concrete.MySQL
{
    public class ProductDal : IProductDal
    {
        private readonly MySqlConnection _cnn;

        public ProductDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<List<Entities.Concrete.Product>> GetListAsync(ProductFilter filter = null, ProductPaging paging = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            string salePriceField = filter == null || string.IsNullOrEmpty(filter.SalePriceField) ? "SATISFIYATI" : filter.SalePriceField;
            List<Entities.Concrete.Product> list = new List<Entities.Concrete.Product>();

            string sqlInnerText = "";
            if (filter.AddSubQueries)
            {
                sqlInnerText = $@" , (SELECT RESIMADI FROM urun_resimleri WHERE URUN_ID = u.ID ORDER BY SIRA ASC LIMIT 1) AS RESIM ";
            }

            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = $@"SELECT    u.ID
                                        , u.URUNKARTI_ID
                                        , uk.URUNADI
                                        , uk.MARKATANIM
                                        , uk.SATISBIRIMI
                                        , uk.URUNADEDIONDALIKLI
                                        , uk.URUNADEDIMINIMUMDEGER
                                        , uk.URUNADEDIKADEMEDEGER
                                        , uk.TEDARIKCI_ID
                                        , uk.TEDARIKCITANIM
                                        , u.ALISFIYATI
                                        , u.STOKKODU
                                        , u.BARKOD
                                        , u.STOKADEDI
                                        , u.SILINDI
                                        , u.KDVORANI
                                        , uk.RESIM1
                                        {sqlInnerText}
                                        , u.TEDARIKCIKODU
                                        , u.TEDARIKCIKODU2
                                        , u.ALISFIYATI
                                        , ROUND((u.{salePriceField} * (u.KDVORANI / 100)), 2) AS KDVFIYATI
                                        , ROUND(u.{salePriceField}, 2) AS SATISFIYATI
                                        , ROUND((u.{salePriceField} + (u.{salePriceField} * (u.KDVORANI / 100))), 2) AS KDVDAHILSATISFIYATI
                                        , u.AKTIF
                                        , uk.KATEGORITANIM
                                        , d.DOVIZKODU
                                        , (SELECT GROUP_CONCAT(ues.EKSECENEK_TANIM SEPARATOR ' ')
	                                        FROM urun_eksecenek AS ues
	                                        WHERE ues.URUN_ID = u.ID ORDER BY ues.EKSECENEK_SIRA) AS VARYASYON
                                        , IFNULL((SELECT SUM(STOK) FROM urun_raf AS ur WHERE ur.URUN_ID = u.ID AND ur.DEPO_ID = @DEPO_ID),0) AS RAFTAKI_ADET
                                        , IF(u.KONSINYESTOKADEDI < 0 , 0 , u.KONSINYESTOKADEDI) AS KONSINYEADEDI
                                    FROM urun_karti AS uk 
                                    INNER JOIN urunler AS u ON uk.ID = u.URUNKARTI_ID
                                    LEFT JOIN doviz AS d ON u.PARABIRIMI_ID = d.ID
                                    WHERE u.SILINDI = 0 ";

            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            AppendFilter(ref cmd, filter);
            PagingExtension.AppendPaging(ref cmd, paging);

            var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (await reader.ReadAsync(cancellationToken))
            {
                Entities.Concrete.Product p = new Entities.Concrete.Product();
                p.ID = reader["ID"].ToInt32();                
                p.ProductCardID = reader["URUNKARTI_ID"].ToInt32();
                p.ProductName = reader["URUNADI"] + " " + reader["VARYASYON"];
                p.StockCode = reader["STOKKODU"].ToString();
                p.StockPiece = reader["STOKADEDI"].ToDouble();
                p.Barcode = reader["BARKOD"].ToString();
                p.ProductCartSupplierDefinition = reader["TEDARIKCITANIM"] != DBNull.Value ? reader["TEDARIKCITANIM"].ToString() : "";
                p.ProductCartSupplierId = reader["TEDARIKCI_ID"] != DBNull.Value ? reader["TEDARIKCI_ID"].ToInt32() : 0;
                p.Image = !string.IsNullOrWhiteSpace(reader["RESIM1"].ToString()) ? WebSiteInfo.User.Value.ImagePath + reader["RESIM1"].ToString() : null;
                if (filter.AddSubQueries)
                {
                    p.Image2 = !string.IsNullOrWhiteSpace(reader["RESIM"].ToString()) ? WebSiteInfo.User.Value.ImagePath + reader["RESIM"].ToString() : null;
                }
                p.SupplierCode = reader["TEDARIKCIKODU"].ToString();
                p.SupplierCode2 = reader["TEDARIKCIKODU2"].ToString();
                p.SalesUnit = reader["SATISBIRIMI"].ToString();
                p.PurchaseAmount = reader["ALISFIYATI"].ToDouble();
                p.isDecimalPiece = reader["URUNADEDIONDALIKLI"].ToBoolean();
                p.DecimalMinimumPiece = reader["URUNADEDIMINIMUMDEGER"].ToDouble();
                p.DecimalIncreasePiece = reader["URUNADEDIKADEMEDEGER"].ToDouble();
                p.Amount = reader["SATISFIYATI"].ToDouble();
                p.PurchasePrice = reader["ALISFIYATI"] != DBNull.Value ? reader["ALISFIYATI"].ToDouble() : 0;
                p.Active = reader["AKTIF"].ToBoolean();
                p.TotalAmount = reader["KDVDAHILSATISFIYATI"].ToDouble();
                p.KDVAmount = reader["KDVFIYATI"].ToDouble();
                p.Brand = reader["MARKATANIM"].ToString();
                p.Category = reader["KATEGORITANIM"].ToString();
                p.Currency = reader["DOVIZKODU"] != DBNull.Value ? reader["DOVIZKODU"].ToString() : "";
                p.VatRate = reader["KDVORANI"].ToInt32();
                p.ShelfPiece = reader["RAFTAKI_ADET"].ToDouble();
                p.ConsignmentStockPiece = reader["KONSINYEADEDI"].ToDouble();
                list.Add(p);
            }

            await reader.CloseAsync();
            await reader.DisposeAsync();
            await cmd.DisposeAsync();
            //_cnn.Close();
            return list;
        }

        public async Task<IList<string>> GetProductBarcode(ProductFilter filter = null, CancellationToken cancellationToken = default)
        {
//            _cnn = new MySqlConnection();
//_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
//            _cnn.Open();
            List<string> list = new List<string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT BARKOD
                                    , URUNKARTI_ID
                                    , URUN_ID
                                FROM urun_barkod WHERE 1 ";

            AppendFilter(ref cmd, filter);
            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (Reader.Read())
            {
                list.Add(Reader["BARKOD"].ToString());
            }

            Reader.Close();
            Reader.Dispose();
            cmd.Dispose();
            //_cnn.Close();
            return list;
        }

        public async Task AddStock(int productID, double piece, int storeID = 0, bool consignmentModuleActive = false, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            if (storeID > 0)
            {
                cmd.CommandText = @"UPDATE magaza_stok
                                    SET STOKADEDI = CASE 
                                       WHEN STOKADEDI + @ADET < 0 THEN 0 
                                       ELSE STOKADEDI + @ADET 
                                    END
                                    WHERE URUN_ID = @ID AND MAGAZA_ID = @MAGAZAID";

                cmd.Parameters.Add("@MAGAZAID", MySqlDbType.Int32).Value = storeID;
            }
            else
            {
                cmd.CommandText = @"UPDATE urunler
                                    SET STOKADEDI = CASE 
                                       WHEN STOKADEDI + @ADET < 0 THEN 0 
                                       ELSE STOKADEDI + @ADET 
                                    END
                                        , STOKGUNCELLEMETARIHI = NOW()
                                    WHERE ID = @ID";
            }

            cmd.Parameters.Add("@ADET", MySqlDbType.Double).Value = piece;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = productID;
            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();

            await UpdateTotalStockPiece(productID: productID);
            await UpdateAdditionalOptionProductField(productID: productID, consingmentModuleActive: consignmentModuleActive);
            await UpdateCategoryProductField(productID: productID);
            await UpdateLabelProductField(productID: productID);
            await ElasticUpdate(productID, cancellationToken);
        }

        public async Task AddConsignmentStock(int productID, double piece, bool consignmentModuleActive, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"UPDATE urunler SET KONSINYESTOKADEDI = KONSINYESTOKADEDI + @KONSINYESTOKADEDI WHERE ID = @ID ";
            cmd.Parameters.Add("@KONSINYESTOKADEDI", MySqlDbType.Double).Value = piece;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = productID;
            await cmd.ExecuteNonQueryAsync(cancellationToken);
            cmd.Dispose();
            //_cnn.Close();
            await UpdateTotalStockPiece(productID: productID);
            await UpdateAdditionalOptionProductField(productID: productID, consingmentModuleActive: consignmentModuleActive);
            await UpdateCategoryProductField(productID: productID);
            await UpdateLabelProductField(productID: productID);
            await ElasticUpdate(productID, cancellationToken);
        }

        public async Task ReduceConsignmentStock(int productId, double piece, bool consignmentModuleActive, CancellationToken cancellationToken)
        {
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE urunler 
SET KONSINYESTOKADEDI = CASE 
                            WHEN (KONSINYESTOKADEDI - @KONSINYESTOKADEDI) < 0 THEN 0 
                            ELSE (KONSINYESTOKADEDI - @KONSINYESTOKADEDI) 
                        END 
WHERE ID = @ID; ";
            cmd.Parameters.Add("@KONSINYESTOKADEDI", MySqlDbType.Double).Value = piece;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = productId;
            await cmd.ExecuteTransactionCommandAsync();
            
            await UpdateTotalStockPiece(productID: productId);
            await UpdateAdditionalOptionProductField(productID: productId, consingmentModuleActive: consignmentModuleActive);
            await UpdateCategoryProductField(productID: productId);
            await UpdateLabelProductField(productID: productId);
            await ElasticUpdate(productId, cancellationToken);
        }

        public async Task UpdateWebStockStock(int productId, double? webStock, bool consignmentModuleActive, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"UPDATE urunler SET
                                        STOKADEDI = @STOKADEDI
                                        WHERE ID = @ID;";
            cmd.Parameters.Add("@STOKADEDI", MySqlDbType.Double).Value = webStock;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = productId;
            await cmd.ExecuteTransactionCommandAsync();
            
            await UpdateTotalStockPiece(productID: productId);
            await UpdateAdditionalOptionProductField(productID: productId, consingmentModuleActive: consignmentModuleActive);
            await UpdateCategoryProductField(productID: productId);
            await UpdateLabelProductField(productID: productId);
            await ElasticUpdate(productId, cancellationToken);
        }

        public async Task UpdateTotalStockPiece(int productCardID = 0, int productID = 0, string supplierCode = "", string strProductCardID = "", CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandTimeout = 600;
            cmd.CommandText = @"UPDATE urun_karti SET TOPLAMSTOKADEDI = IFNULL((SELECT SUM(STOKADEDI + KONSINYESTOKADEDI - EKSISTOKADEDI) FROM urunler WHERE SILINDI = 0 AND AKTIF = 1 AND (STOKADEDI + KONSINYESTOKADEDI - EKSISTOKADEDI) > 0 AND URUNKARTI_ID = urun_karti.ID), 0)
                                                      , EKSECENEKSAYISI = (SELECT COUNT(ID) FROM urunler WHERE URUNKARTI_ID = urun_karti.ID AND SILINDI = 0)
                                                      , STOKGUNCELLEMETARIHI = NOW()
                                WHERE 1 ";

            if (productCardID > 0)
            {
                cmd.CommandText += " AND ID = @URUNKARTIID";
                cmd.Parameters.Add("@URUNKARTIID", MySqlDbType.Int32).Value = productCardID;
            }

            if (productID > 0)
            {
                cmd.CommandText += " AND ID = (SELECT URUNKARTI_ID FROM urunler WHERE ID = @URUNID LIMIT 1)";
                cmd.Parameters.Add("@URUNID", MySqlDbType.Int32).Value = productID;
            }

            if (!string.IsNullOrEmpty(supplierCode))
            {
                cmd.CommandText += " AND TEDARIKCIKODU = @TEDARIKCIKODU";
                cmd.Parameters.Add("@TEDARIKCIKODU", MySqlDbType.VarChar).Value = supplierCode;
            }

            if (!string.IsNullOrEmpty(strProductCardID))
            {
                cmd.CommandText += " AND ID IN (" + strProductCardID + ")";
            }

            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateAdditionalOptionProductField(int productCardID = -1, int productID = -1, int additionalOptionTypeID = -1, int additionalOptionID = -1, List<int> productCardIDs = null, List<string> suppliersCodes = null, bool consingmentModuleActive = false, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            string konsinyeStokVarSorgu = "";
            string konsinyeStokAdediSorgu = "";
            if (consingmentModuleActive)
            {
                konsinyeStokVarSorgu = "IF((u.STOKADEDI + u.KONSINYESTOKADEDI - u.EKSISTOKADEDI) > 0 , 1, 0)";
                konsinyeStokAdediSorgu = "(u.STOKADEDI + u.KONSINYESTOKADEDI - u.EKSISTOKADEDI)";
            }
            else
            {
                konsinyeStokVarSorgu = "IF((u.STOKADEDI - u.EKSISTOKADEDI) > 0 , 1, 0)";
                konsinyeStokAdediSorgu = "(u.STOKADEDI - u.EKSISTOKADEDI)";
            }

            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = $@"UPDATE urun_eksecenek AS ues
                                       LEFT JOIN eksecenek AS es ON ues.EKSECENEK_ID = es.ID
                                       LEFT JOIN eksecenektipi AS est ON ues.EKSECENEKTIPI_ID = est.ID
                                       LEFT JOIN urunler AS u ON ues.URUN_ID = u.ID
                                    SET
                                        ues.EKSECENEK_TANIM = es.TANIM
                                       , ues.EKSECENEK_SIRA = IFNULL(es.SIRA,0)
                                       , ues.EKSECENEK_RESIM = es.RESIM
                                       , ues.EKSECENEK_RENK_KODU = es.RENK_KODU
                                       , ues.EKSECENEKTIPI_TANIM = est.TANIM
                                       , ues.EKSECENEKTIPI_SIRA =  IFNULL(est.SIRA,0)
                                       , ues.URUNLER_AKTIF = u.AKTIF
                                       , ues.URUNLER_SILINDI = u.SILINDI
                                       , ues.URUNLER_STOKVAR = {konsinyeStokVarSorgu}
                                       , ues.URUNLER_STOKADEDI = {konsinyeStokAdediSorgu}
                                       , ues.EKSECENEK_AKTIF =  IFNULL(es.AKTIF,1)
                                       , ues.EKSECENEKTIPI_LISTETIPI = est.LISTETIPI
                                       , ues.URUNLER_STOKKODU = u.STOKKODU
                              WHERE 1 ";

            if (productCardIDs != null && productCardIDs.Count > 0)
            {
                cmd.CommandText += "  AND ues.URUNKART_ID IN (" + string.Join(",", productCardIDs) + ")";
            }

            if (productCardID > -1)
            {
                cmd.CommandText += "  AND ues.URUNKART_ID = " + productCardID;
            }

            if (productID > -1)
            {
                cmd.CommandText += "  AND ues.URUN_ID = " + productID;
            }

            if (additionalOptionTypeID > -1)
            {
                cmd.CommandText += "  AND ues.EKSECENEKTIPI_ID = " + additionalOptionTypeID;
            }

            if (additionalOptionID > -1)
            {
                cmd.CommandText += "  AND ues.EKSECENEK_ID = " + additionalOptionID;
            }

            if (suppliersCodes != null && suppliersCodes.Count > 0)
            {
                cmd.CommandText += $" AND ues.URUN_ID IN(SELECT u.ID FROM urunler AS u WHERE u.TEDARIKCIKODU IN({string.Join(",", suppliersCodes.Select(x => "'" + x + "'"))}) )";
            }

            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateCategoryProductField(List<int> productCardIDs = null, int productID = 0, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE urun_kategori AS ukat
                                                            LEFT JOIN urun_karti AS uk
                                                            ON ukat.URUNKARTI_ID = uk.ID
                                                            SET urunkarti_resimvar = (CASE WHEN (SELECT COUNT(ID) FROM urun_resimleri WHERE URUNKARTI_ID = ukat.URUNKARTI_ID AND SILINDI = 0 AND AKTIF = 1) > 0 THEN 1 ELSE 0 END)
                                                                , ukat.URUNKARTI_SILINDI = uk.SILINDI
                                                                , ukat.URUNKARTI_AKTIF = uk.AKTIF
                                                                , ukat.URUNKARTI_STOKVAR = IF(uk.TOPLAMSTOKADEDI > 0 , 1, 0)
                                                                , ukat.URUNKARTI_MARKAID = uk.MARKA_ID
                                                                , ukat.URUNKARTI_TEDARIKCIID = uk.TEDARIKCI_ID
                                                                , ukat.URUNKARTI_EKLEMETARIHI = uk.EKLEMETARIHI
                                                                , ukat.URUNKARTI_KOMBINURUN = uk.KOMBINURUN
                                                                , ukat.URUNKARTI_LISTEDEGOSTER = uk.LISTEDEGOSTER ";

            cmd.CommandText += " WHERE 1 ";

            if (productCardIDs != null && productCardIDs.Count > 0)
            {
                cmd.CommandText += $@" AND ukat.URUNKARTI_ID IN ({string.Join(",", productCardIDs)})";
            }

            if (productID > 0)
            {
                cmd.CommandText += @" AND ukat.URUNKARTI_ID = (SELECT URUNKARTI_ID FROM urunler WHERE ID = @URUNID LIMIT 1)";
                cmd.Parameters.Add("@URUNID", MySqlDbType.Int32).Value = productID;
            }

            //cmd.ExecuteNonQuery();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task UpdateLabelProductField(List<int> productCardIDs = null, int productID = 0, int labelID = 0, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE etiketurun AS ukat
                                    LEFT JOIN urun_karti AS uk ON ukat.UrunKartiId = uk.ID
                                    LEFT JOIN etiket AS e ON ukat.EtiketId = e.ID
                                    SET urunkarti_resimvar = (CASE WHEN (SELECT COUNT(ID) FROM urun_resimleri WHERE URUNKARTI_ID = ukat.UrunKartiId AND SILINDI = 0 AND AKTIF = 1) > 0 THEN 1 ELSE 0 END)
                                        , ukat.URUNKARTI_SILINDI = uk.SILINDI
                                        , ukat.URUNKARTI_AKTIF = uk.AKTIF
                                        , ukat.URUNKARTI_STOKVAR = IF(uk.TOPLAMSTOKADEDI > 0 , 1, 0)
                                        , ukat.ETIKET_TANIM = e.TANIM
                                        , ukat.ETIKET_BREADCRUMB = e.BREADCRUMB
                                        , ukat.ETIKET_FILTREDEGOSTER = e.FILTREDEGOSTER
                                        , ukat.URUNKARTI_LISTEDEGOSTER = uk.LISTEDEGOSTER
                                    WHERE 1 ";

            if (productCardIDs != null && productCardIDs.Count > 0)
            {
                cmd.CommandText += $@" AND ukat.UrunKartiId IN ({string.Join(",", productCardIDs)})";
            }

            if (productID > 0)
            {
                cmd.CommandText += @" AND ukat.UrunKartiId = (SELECT URUNKARTI_ID FROM urunler WHERE ID = @URUNID LIMIT 1)";
                cmd.Parameters.Add("@URUNID", MySqlDbType.Int32).Value = productID;
            }

            if (labelID > 0)
            {
                cmd.CommandText += $@" AND ukat.EtiketId = {labelID}";
            }

            //cmd.ExecuteNonQuery();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task OptimizeStock(int productID, double piece, int storeID = 0, bool consignmentModuleActive = false, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            if (storeID > 0)
            {
                cmd.CommandText = @"UPDATE magaza_stok
                                    SET STOKADEDI = @ADET
                                    WHERE URUN_ID = @ID AND MAGAZA_ID = @MAGAZA_ID";

                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = storeID;
            }
            else
            {
                cmd.CommandText = @"UPDATE urunler
                                    SET STOKADEDI = @ADET
                                        , STOKGUNCELLEMETARIHI = NOW()
                                    WHERE ID = @ID";
            }

            cmd.Parameters.Add("@ADET", MySqlDbType.Double).Value = piece;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = productID;
            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();

            await UpdateTotalStockPiece(productID: productID);
            await UpdateAdditionalOptionProductField(productID: productID, consingmentModuleActive: consignmentModuleActive);
            await UpdateCategoryProductField(productID: productID);
            await UpdateLabelProductField(productID: productID);
            await ElasticUpdate(productID, cancellationToken);
        }

        public async Task ReduceStock(int productID, double piece, int storeID = 0, bool consignmentModuleActive = false, CancellationToken cancellationToken = default)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            if (storeID > 0)
            {
                cmd.CommandText = @"UPDATE magaza_stok
                                    SET STOKADEDI = STOKADEDI - @ADET
                                    WHERE URUN_ID = @ID AND MAGAZA_ID = @MAGAZA_ID";

                cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = storeID;
            }
            else
            {
                cmd.CommandText = @"UPDATE urunler
                                    SET STOKADEDI = STOKADEDI - @ADET
                                        , STOKGUNCELLEMETARIHI = NOW()
                                    WHERE ID = @ID";
            }

            cmd.Parameters.Add("@ADET", MySqlDbType.Double).Value = piece;
            cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = productID;
            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();

            await UpdateTotalStockPiece(productID: productID);
            await UpdateAdditionalOptionProductField(productID: productID, consingmentModuleActive: consignmentModuleActive);
            await UpdateCategoryProductField(productID: productID);
            await UpdateLabelProductField(productID: productID);
            await ElasticUpdate(productID, cancellationToken);
        }

        public async Task ElasticUpdate(int productId, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty);
            cmd.CommandText = @"UPDATE urun_karti SET ES_GUNCELLEMETARIHI = NOW() WHERE ID IN (SELECT URUNKARTI_ID FROM urunler as u WHERE u.ID = @ProductId);";
            cmd.Parameters.Add("@ProductId", MySqlDbType.Int32).Value = productId;
            //cmd.ExecuteNonQuery();
            //cmd.Dispose();
            //_cnn.Close();
            await cmd.ExecuteTransactionCommandAsync();
        }

        public async Task<int> StoreStockGetCount(StoreStockFilter filter, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<string> list = new List<string>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT COUNT(ms.MAGAZA_ID) FROM magaza_stok ms WHERE 1";
            StoreStockAppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            cmd.Dispose();
            //_cnn.Close();
            return count;
        }

        public async Task<List<StoreStock>> StoreStockGetList(StoreStockFilter filter, CancellationToken cancellationToken)
        {
            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            List<StoreStock> list = new List<StoreStock>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT MAGAZA_ID,
                                       URUN_ID,
                                       URUNKARTI_ID,
                                       STOKADEDI,
                                       EKSISTOKADEDI,
                                       STOKHAREKETTARIHI FROM magaza_stok ms WHERE 1";

            StoreStockAppendFilter(ref cmd, filter);
            var Reader = await cmd.ExecuteReaderAsync(cancellationToken);
            while (Reader.Read())
            {
                StoreStock ss = new StoreStock();
                ss.StoreID = Reader["MAGAZA_ID"].ToInt32();
                ss.ProductID = Reader["URUN_ID"].ToInt32();
                ss.ProductCardID = Reader["URUNKARTI_ID"].ToInt32();
                ss.StockPiece = Reader["STOKADEDI"].ToDouble();
                ss.MissingStockPiece = Reader["EKSISTOKADEDI"].ToDouble();
                ss.StockMovementDate = Reader["STOKHAREKETTARIHI"].ToDateTime();
                list.Add(ss);
            }

            Reader.Close();
            Reader.Dispose();
            cmd.Dispose();
            //_cnn.Close();
            return list;
        }

        public async Task<int> GetCountAsync(ProductFilter filter = null, CancellationToken cancellationToken = default)
        {
            if (filter == null)
            {
                return 0;
            }

            //_cnn = new MySqlConnection();
            //_cnn.ConnectionString = Connection.DevConnectionString(Info.DomainName.Value);
            //_cnn.Open();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);

            string sqlInnerText = "";
            if (filter.NonShelf)
                sqlInnerText = "LEFT JOIN urun_raf AS ur ON ur.URUN_ID = uk.URUN_ID";
            cmd.CommandText = $@"SELECT COUNT(*)
                                    FROM urun_karti AS uk 
                                    INNER JOIN urunler AS u ON uk.ID = u.URUNKARTI_ID
                                    {sqlInnerText}
                                    LEFT JOIN doviz AS d ON u.PARABIRIMI_ID = d.ID
                                    WHERE u.SILINDI = 0 AND 1 ";

            AppendFilter(ref cmd, filter);
            int count = Convert.ToInt32(await cmd.ExecuteScalarAsync(cancellationToken));
            await cmd.DisposeAsync();
            //_cnn.Close();
            return count;
        }

        private void AppendFilter(ref MySqlCommand cmd, ProductFilter filter)
        {
            if (filter != null)
            {
                if (filter.ID > 0)
                {
                    cmd.CommandText += " AND u.ID = @ID";
                    cmd.Parameters.Add("@ID", MySqlDbType.Int32).Value = filter.ID;
                }

                if (filter.IDList != null && filter.IDList.Count > 0)
                {
                    cmd.CommandText += $" AND u.ID IN ({string.Join(',', filter.IDList)})";
                }

                if (filter.ProductCardID > 0)
                {
                    cmd.CommandText += " AND u.URUNKARTI_ID = @URUNKARTI_ID";
                    cmd.Parameters.Add("@URUNKARTI_ID", MySqlDbType.Int32).Value = filter.ProductCardID;
                }
                
                if (!string.IsNullOrEmpty(filter.Barcode))
                {
                    string sqlStr = "";
                    if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.CokluBarkod)
                        sqlStr = " AND (u.BARKOD = @BARKOD OR u.ID IN (SELECT URUN_ID FROM urun_barkod WHERE BARKOD = @BARKOD))";
                    else
                        sqlStr = " AND u.BARKOD = @BARKOD";

                    cmd.CommandText += $" {sqlStr}"; 
                    cmd.Parameters.Add("@BARKOD", MySqlDbType.VarChar).Value = filter.Barcode;
                }

                if (filter.BarcodeList != null && filter.BarcodeList.Count > 0)
                {
                    cmd.CommandText += $" AND (u.BARKOD IN ({string.Join(",", filter.BarcodeList.Select(x => $"'{x}'"))}) ";
                    if (WebSiteInfo.User.Value.Settings.UrunToplamaAyar.CokluBarkod)
                        cmd.CommandText += $" OR u.ID IN (SELECT URUN_ID FROM urun_barkod WHERE BARKOD IN ({string.Join(",", filter.BarcodeList.Select(x => $"'{x}'"))}))";

                    cmd.CommandText += ")";
                }

                if (filter.StockCodeList != null && filter.StockCodeList.Count > 0)
                {
                    cmd.CommandText += $" AND u.STOKKODU IN ({string.Join(",", filter.StockCodeList.Select(x => $"'{x}'"))})";
                }

                if (!string.IsNullOrEmpty(filter.StockCode))
                {
                    cmd.CommandText += " AND u.STOKKODU = @STOKKODU";
                    cmd.Parameters.Add("@STOKKODU", MySqlDbType.VarChar).Value = filter.StockCode;
                }

                if (!string.IsNullOrEmpty(filter.SupplierCode))
                {
                    cmd.CommandText += " AND u.TEDARIKCIKODU = @TEDARIKCIKODU";
                    cmd.Parameters.Add("@TEDARIKCIKODU", MySqlDbType.VarChar).Value = filter.SupplierCode;
                }

                if (!string.IsNullOrEmpty(filter.SupplierCode2))
                {
                    cmd.CommandText += " AND u.TEDARIKCIKODU2 = @TEDARIKCIKODU2";
                    cmd.Parameters.Add("@TEDARIKCIKODU2", MySqlDbType.VarChar).Value = filter.SupplierCode2;
                }

                if (!string.IsNullOrEmpty(filter.ProductName))
                {
                    cmd.CommandText += " AND uk.URUNADI LIKE @URUNADI";
                    cmd.Parameters.Add("@URUNADI", MySqlDbType.VarChar).Value = "%" + filter.ProductName + "%";
                }

                if (filter.NonShelf)
                {
                    cmd.CommandText += " AND u.STOKADEDI > 0 AND IFNULL((SELECT SUM(ur.STOK) FROM urun_raf AS ur WHERE ur.`URUN_ID` = u.`ID`),0) = 0";
                }

                if (filter.IsAvailableStock)
                {
                    cmd.CommandText += " AND u.STOKADEDI > 0";
                }
            }

            if (WebSiteInfo.User.Value.Settings.UrunYerlestirmeAyar.SadeceAktifUrun)
                cmd.CommandText += " AND u.AKTIF = 1 AND uk.AKTIF = 1 ";
        }

        private void StoreStockAppendFilter(ref MySqlCommand cmd, StoreStockFilter filter)
        {
            if (filter != null)
            {
                if (filter.ProductID > 0)
                {
                    cmd.CommandText += " AND ms.URUN_ID = @URUN_ID";
                    cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = filter.ProductID;
                }

                if (filter.StoreID > 0)
                {
                    cmd.CommandText += " AND ms.MAGAZA_ID = @MAGAZAID";
                    cmd.Parameters.Add("@MAGAZAID", MySqlDbType.Int32).Value = filter.StoreID;
                }
            }
        }
    }
}