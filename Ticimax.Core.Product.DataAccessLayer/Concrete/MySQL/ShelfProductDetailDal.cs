using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.DataAccessLayer.Extensions;
using Ticimax.Core.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Product.DataAccessLayer.Abstract;
using Ticimax.Core.Product.Entities.Concrete;
using Ticimax.Core.Product.Entities.Concrete.ProductMovement;
using Ticimax.Core.Utilities.UserInfo;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Core.Product.DataAccessLayer.Concrete.MySQL
{
#nullable enable
    public class ShelfProductDetailDal : IShelfProductDetailDal
    {
        private readonly MySqlConnection _cnn;

        public ShelfProductDetailDal(IDbConnection cnn)
        {
            _cnn = (MySqlConnection)cnn;
        }

        public async Task<double> GetShelfProductAmount(int productId, bool isOpenForSale, bool isOpenForPicking, CancellationToken cancellationToken)
        {
            List<StoreStock> list = new List<StoreStock>();
            MySqlCommand cmd = new MySqlCommand(string.Empty, (MySqlConnection)_cnn);
            cmd.CommandText = @"SELECT SUM(ur.STOK) FROM urun_raf ur 
                                WHERE DEPO_ID = @DEPO_ID AND MAGAZA_ID = @MAGAZA_ID AND URUN_ID = @URUN_ID ";

            cmd.Parameters.Add("@URUN_ID", MySqlDbType.Int32).Value = productId;
            cmd.Parameters.Add("@DEPO_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.WarehouseID;
            cmd.Parameters.Add("@MAGAZA_ID", MySqlDbType.Int32).Value = WebSiteInfo.User.Value.StoreID;


            if (isOpenForSale)
                cmd.CommandText += " AND (SELECT r.SATISA_ACIK FROM raflar as r WHERE r.ID = ur.RAF_ID LIMIT 1) = 1 ";
            else
                cmd.CommandText += " AND (SELECT r.SATISA_ACIK FROM raflar as r WHERE r.ID = ur.RAF_ID LIMIT 1) = 0 ";

            if (isOpenForPicking)
                cmd.CommandText += " AND (SELECT r.TOPLAMAYA_ACIK FROM raflar as r WHERE r.ID = ur.RAF_ID LIMIT 1) = 1 ";
            else
                cmd.CommandText += " AND (SELECT r.TOPLAMAYA_ACIK FROM raflar as r WHERE r.ID = ur.RAF_ID LIMIT 1) = 0 ";

            var totalAmount = await cmd.ExecuteScalarAsync(cancellationToken);
            cmd.Dispose();
            return totalAmount != DBNull.Value ? Convert.ToDouble(totalAmount) : default;
        }
    }
}