using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Product.Entities.Concrete;
using Ticimax.Core.Product.Entities.Filters;
using Ticimax.Core.Product.Entities.Pagings;

namespace Ticimax.Core.Product.DataAccessLayer.Abstract
{
    public interface IConsignmentProductDal
    {
        Task AddAsync(ConsignmentProduct entity, CancellationToken cancellationToken);

        Task DeleteAsync(ConsignmentProduct entity, CancellationToken cancellationToken);

        Task<IList<ConsignmentProduct>> GetListAsync(ConsignmentProductFilter filter = null, ConsignmentProductPaging paging = null, CancellationToken cancellationToken = default);

        Task UpdateAsync(ConsignmentProduct entity, CancellationToken cancellationToken);

        Task UpdateIncomingStockAsync(ConsignmentProduct entity, CancellationToken cancellationToken);

        Task<int> GetCountAsync(ConsignmentProductFilter filter = null, CancellationToken cancellationToken = default);
    }
}