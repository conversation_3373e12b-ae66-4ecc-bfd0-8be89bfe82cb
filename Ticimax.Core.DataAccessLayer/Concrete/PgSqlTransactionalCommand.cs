using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Npgsql;
using Ticimax.Core.DataAccessLayer.Abstract;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Core.DataAccessLayer.Concrete
{
    public class PgSqlTransactionalCommand : IPgSqlTransactionalCommand
    {
        private readonly NpgsqlConnection _cnn;

        public PgSqlTransactionalCommand(IOptions<NpgsqlConnection> cnn)
        {
            _cnn = new NpgsqlConnection(cnn.Value.ConnectionString);
        }

        public async Task PgSqlTransactionalExecuteAsync()
        {
            if (!string.IsNullOrEmpty(_cnn.ConnectionString) && _cnn.State != System.Data.ConnectionState.Open)
                await _cnn.OpenAsync();

            if (WebSiteInfo.User?.Value?.Commands?.Count > 0)
            {
                DbTransaction transaction = await _cnn.BeginTransactionAsync();

                WebSiteInfo.User.Value.Commands.Where(x => x.GetType().Name == typeof(NpgsqlCommand).Name)
                    .ToList().ForEach(x =>
                    {
                        x.Connection = _cnn;
                        x.Transaction = transaction;
                    });

                await transaction.CommitAsync();
            }
        }
    }
}