using System;
using System.Collections.Generic;

namespace Ticimax.Warehouse.Entities.Models.Responses.LatestFeature
{
    public class LatestFeatureModel
    {
        public Guid Id { get; set; }
        public string VersionNumber { get; set; }
        public long VersionDate { get; set; }
        public long CreatedDate { get; set; }
        public long LastModifiedDate { get; set; }
        public List<DescriptionsWithImages> DescriptionsWithImages { get; set; }
    }
    public class DescriptionsWithImages
    {
        public Guid DescriptionId { get; set; }
        public string Description { get; set; }
        public List<string> FileUrls { get; set; }
    }
}
