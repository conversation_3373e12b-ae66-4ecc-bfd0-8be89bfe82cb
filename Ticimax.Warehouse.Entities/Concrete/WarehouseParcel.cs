using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class WarehouseParcel : IEntity
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public string Barcode { get; set; }
        public bool IsParcelShipping { get; set; }
        public int Limit { get; set; }

        public int Rank { get; set; }

        public int TableID { get; set; }

        public int WarehouseID { get; set; }

        public int StoreID { get; set; }

        public double TotalNumberOfProducts { get; set; }

        public double TotalNumberOfPreparedProducts { get; set; }

        public double TotalNumberOfNotOnShelf { get; set; }

        public double TotalNumberOfMissingProducts { get; set; }

        public List<int> OrderIDList { get; set; } = new List<int>();

        public List<OrdersType> OrdersType { get; set; } = new List<OrdersType>();

        public bool isMissingProductPackage { get; set; }

        public bool isCancelOrder { get; set; }

        public bool IsEmpty { get; set; }

        public bool IsWaitingCall { get; set; }

        public bool IsWaitingInvoice { get; set; }

        public string SetNo { get; set; }

        public DateTime AddingDate { get; set; }
    }

    public class OrdersType
    {
        public int OrderID { get; set; }
    }
}