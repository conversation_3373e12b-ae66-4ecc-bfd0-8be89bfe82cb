using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class Shelf : IEntity
    {
        public Shelf()
        {
        }

        public Shelf(int id, int parentId, string definition, string code, string barcode, int warehouseId, int storeId, int rank, bool isMissingProductShelf, bool isEmptyShelf, bool isOpenForSale, bool isOpenForPicking)
        {
            ID = id;
            ParentId = parentId;
            Definition = definition;
            Code = code;
            Barcode = barcode;
            WarehouseID = warehouseId;
            StoreID = storeId;
            Rank = rank;
            IsMissingProductShelf = isMissingProductShelf;
            IsEmptyShelf = isEmptyShelf;
            IsOpenForSale = isOpenForSale;
            IsOpenPicking = isOpenForPicking;
        }

        public int ID { get; set; }

        public int ParentId { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public string Barcode { get; set; }

        public int WarehouseID { get; set; }

        public int StoreID { get; set; }

        public int Rank { get; set; }

        public bool IsMissingProductShelf { get; set; }

        public DateTime AddingDate { get; set; }

        public bool IsEmptyShelf { get; set; }

        public bool IsOpenForSale { get; set; }
        public bool IsOpenPicking { get; set; }
    }
}