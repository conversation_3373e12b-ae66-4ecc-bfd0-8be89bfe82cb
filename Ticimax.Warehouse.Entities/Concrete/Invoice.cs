using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class Invoice : IEntity
    {
        public string Series { get; set; }

        public int Rank { get; set; }

        public int OrderID { get; set; }

        public string Content { get; set; }

        public string Template { get; set; }

        public Ticimax.Invoice.DTO.Models.Invoice.Settings TemplateSettings { get; set; }

        public DateTime Date { get; set; }

        public bool isCancel { get; set; }

    }
}