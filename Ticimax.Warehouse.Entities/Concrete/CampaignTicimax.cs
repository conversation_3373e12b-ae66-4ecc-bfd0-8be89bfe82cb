using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class CampaignTicimax : IEntity
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string Description { get; set; }

        public int Priority { get; set; }

        public int Type { get; set; }

        public double MinAmount { get; set; }

        public int MinProductCount { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime ExpireDate { get; set; }

        public int ProcessType { get; set; }

        public double ProcessValue { get; set; }

        public int RepeatablePiece { get; set; }

        public ApplicationMethods AppMethods { get; set; }

        public Conditions Condition { get; set; }

        public Gifts Gift { get; set; }

        public int MaxOrderCount { get; set; }

        public int OrderCount { get; set; }

        public int MaxCustomerUseCount { get; set; }

        public int BasketDescriptionVisible { get; set; }

        public int VisibleInCampaignPage { get; set; }

        public DateTime CreateDateTime { get; set; }

        public int AddedUserID { get; set; }

        public DateTime EditDateTime { get; set; }

        public int EditUserID { get; set; }

        public string Currency { get; set; }

        public class ApplicationMethods
        {
            public bool SepeteUygula { get; set; }

            public bool SepetteIndirim { get; set; }

            public bool SatirBazliUygula { get; set; }

            public bool KosulaUygunUrunlereUygula { get; set; }

            public int IndirimUrunOncelik { get; set; }

            public int UcretliUrunAdedi { get; set; }

            public List<object> IndirimDegerleri { get; set; }

            public List<object> FiyatAraliklari { get; set; }

            public List<object> AdetAraliklari { get; set; }

            public List<object> UlkeFiyatlari { get; set; }
        }

        public class Conditions
        {
            public bool IndirimliUrunlerdeGecerli { get; set; }

            public List<object> BirlestirilebilirKampanyalar { get; set; }

            public List<object> Kategoriler { get; set; }

            public List<object> Etiketler { get; set; }

            public List<object> Markalar { get; set; }

            public List<object> Tedarikciler { get; set; }

            public List<object> UyeGruplari { get; set; }

            public List<object> Bankalar { get; set; }

            public List<object> OdemeTipleri { get; set; }

            public List<object> BinNumaralari { get; set; }

            public List<object> Urunler { get; set; }

            public List<object> Taksitler { get; set; }

            public int Tip { get; set; }

            public bool HediyeCekiIleKullanilabilir { get; set; }
        }

        public class Gifts
        {
            public Urun Urun { get; set; }

            public HediyeCeki HediyeCeki { get; set; }
        }

        public class Urunler
        {
            public int UrunId { get; set; }

            public string UrunAdi { get; set; }

            public double Adet { get; set; }

            public string Resim { get; set; }

            public int IslemTipi { get; set; }

            public double IslemDegeri { get; set; }
        }

        public class Urun
        {
            public List<Urunler> Urunler { get; set; }

            public bool OtomatikSepeteEkle { get; set; }

            public int UrunAdedi { get; set; }

            public List<object> Kategoriler { get; set; }

            public List<object> Etiketler { get; set; }

            public List<object> Markalar { get; set; }

            public List<object> Tedarikciler { get; set; }
        }

        public class HediyeCeki
        {
            public bool SiparisTarihineGoreOlustur { get; set; }

            public int BaslamaSuresi { get; set; }

            public int GecerliOlduguSure { get; set; }

            public DateTime BaslangicTarihi { get; set; }

            public DateTime BitisTarihi { get; set; }

            public double MinimumSepetTutari { get; set; }

            public List<object> Kategoriler { get; set; }

            public List<object> Markalar { get; set; }

            public bool KargoIndirimi { get; set; }

            public int IslemTipi { get; set; }

            public double IslemDegeri { get; set; }

            public int KullanimSayisi { get; set; }

            public int UyeMaksKullanimSayisi { get; set; }

            public bool IndirimliUrunleKullanilabilir { get; set; }

            public List<object> BirlestirilebilirKampanyalar { get; set; }

            public bool KargoUcretsiz { get; set; }
        }
    }
}