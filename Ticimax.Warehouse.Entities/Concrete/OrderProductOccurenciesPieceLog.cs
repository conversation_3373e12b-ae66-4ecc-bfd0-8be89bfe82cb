using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class OrderProductOccurenciesPieceLog : IEntity
    {
        public int Id { get; set; }

        public int OrderId { get; set; }

        public int ProductId { get; set; }

        public double OldQty { get; set; }

        public double NewQty { get; set; }

        public double OldPrice { get; set; }

        public double NewPrice { get; set; }

        public int UserId { get; set; }

        public string Username { get; set; }

        public DateTime ProcessDate { get; set; }
    }
}