using System;
using Ticimax.Core.Entities;
using Ticimax.Core.Product.Entities.Enums;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class MissingProductReasonLog : IEntity
    {
        public int ID { get; set; }

        public int UserID { get; set; }

        public string UserName { get; set; }

        public DateTime DateTime { get; set; }

        public int TargetID { get; set; }

        public ProductType TargetType { get; set; }

        public int ShelfID { get; set; }

        public double Piece { get; set; }

        public bool StatusOfAfterProcess { get; set; }
    }
}