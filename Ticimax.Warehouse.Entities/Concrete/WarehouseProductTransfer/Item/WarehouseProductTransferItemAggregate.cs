using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Item.Enums;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Item.Events;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.Item
{
    public class WarehouseProductTransferItemAggregate : AggregateRoot
    {
        public WarehouseProductTransferItemAggregate()
        {
        }

        public WarehouseProductTransferItemAggregate(Guid fileId, int productId, double quantity, int shelfId)
        {
            FileId = fileId;
            ProductId = productId;
            Quantity = quantity;
            ShelfId = shelfId;
            SetAsCreated();
            AddEvent(WarehouseProductTransferItemEventName.Created, new WarehouseProductTransferItemEventPayload(this));
            WebSiteInfo.User.Value.Events.AddRange(Events);
        }

        public Guid FileId { get; set; }

        public int ProductId { get; set; }

        public int ShelfId { get; set; }

        public double Quantity { get; set; }

        public double PickQuantity { get; set; }
        public double MissingQuantity { get; set; }

        public double ControlQuantity { get; set; }

        public void Pick(double pickQuantity, bool isMissing)
        {
            if(isMissing)
                MissingQuantity += pickQuantity;
            else
                PickQuantity += pickQuantity;

            SetAsModified();
            AddEvent(WarehouseProductTransferItemEventName.Picked, new WarehouseProductTransferItemEventPayload(this));
            WebSiteInfo.User.Value.Events.AddRange(Events);
        }

        public void Check(double checkQuantity)
        {
            ControlQuantity += checkQuantity;
            SetAsModified();
            AddEvent(WarehouseProductTransferItemEventName.Checked, new WarehouseProductTransferItemEventPayload(this));
            WebSiteInfo.User.Value.Events.AddRange(Events);
        }
    }
}