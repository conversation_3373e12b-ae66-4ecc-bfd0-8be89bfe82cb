using System;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File.Enums;
using Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File.Events;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Entities.Concrete.WarehouseProductTransfer.File
{
    public class WarehouseProductTransferFileAggregate : AggregateRoot
    {
        public WarehouseProductTransferFileAggregate()
        {
        }

        public WarehouseProductTransferFileAggregate(Guid id, string name, string note, double totalProductQuantity, int storeId, int warehouseId, int transferStoreId, int transferWarehouseId, int createdUserId, string createdUserName)
        {
            FileNo = DateTime.Now.ToString("ddMMss") + new Random().Next(0, 500);
            Name = name;
            Note = note;
            TotalProductQuantity = totalProductQuantity;
            Status = WarehouseProductTransferStatus.Created;
            StoreId = storeId;
            WarehouseId = warehouseId;
            TransferStoreId = transferStoreId;
            TransferWarehouseId = transferWarehouseId;
            CreatedUserId = createdUserId;
            CreatedUserName = createdUserName;
            SetAsCreated();
            Id = id;
            AddEvent(WarehouseProductTransferEventName.Created, new WarehouseProductTransferFileEventPayload(this));
            WebSiteInfo.User.Value.Events.AddRange(Events);
        }

        public string FileNo { get; set; }

        public string Name { get; set; }

        public string Note { get; set; }

        public int WarehouseCarId { get; set; }

        public int UserId { get; set; }

        public double TotalProductQuantity { get; set; }

        public string Status { get; set; }

        public int StoreId { get; set; }

        public int WarehouseId { get; set; }

        public int TransferStoreId { get; set; }

        public int TransferWarehouseId { get; set; }
        public int CreatedUserId { get; set; }
        public string CreatedUserName { get; set; }
        public bool IsMissingProductContains { get; set; }

        public void Update(string name, string note)
        {
            Name = name;
            Note = note;
            SetAsModified();
            AddEvent(WarehouseProductTransferEventName.Updated, new WarehouseProductTransferFileEventPayload(this));
            WebSiteInfo.User.Value.Events.AddRange(Events);
        }

        public void ChangeStatus(string status)
        {
            if (!WarehouseProductTransferStatus.IsValid(status))
                throw new BusinessException("WAREHOUSE_PRODUCT_TRANSFER_FILE_STATUS_IS_NOT_VALID");

            if (Status == status)
                return;

            Status = status;
            SetAsModified();
            AddEvent(WarehouseProductTransferEventName.StatusChanged, new WarehouseProductTransferFileEventPayload(this));
            WebSiteInfo.User.Value.Events.AddRange(Events);
        }

        public void Assign(int warehouseCarId, int userId)
        {
            UserId = userId;
            WarehouseCarId = warehouseCarId;
            SetAsModified();
            AddEvent(WarehouseProductTransferEventName.Assigned, new WarehouseProductTransferFileEventPayload(this));
            WebSiteInfo.User.Value.Events.AddRange(Events);
        }
    }
}