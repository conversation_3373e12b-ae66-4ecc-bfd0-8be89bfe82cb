using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class DynamicFormObject : IEntity
    {
        public string Name { get; set; }

        public bool Required { get; set; }

        public int Order { get; set; }

        public string Type { get; set; }

        public string Placeholder { get; set; }

        public int? Maxlength { get; set; }

        public List<DynamicFormOption> Options { get; set; }

        public string PriceOperator { get; set; }

        public string Value { get; set; }

        public double? OptionalPrice { get; set; }

        public double? OptionalPreviewPrice { get; set; }

        public string OptionalPriceDesc { get; set; }

        public List<string> Accept { get; set; }

        public string PriceFormula { get; set; }
    }
}