using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Enums;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class EInvoceResult : IEntity
    {
        public int ID { get; set; }

        public EInvoiceType EInvoiceType { get; set; }

        public int OrderID { get; set; }

        public bool isCancel { get; set; }

        public string Detail { get; set; }

        public string Result { get; set; }

        public string FileName { get; set; }

        public string InvoiceNumber { get; set; }

        public string Sha256Hash { get; set; }

        public string Code { get; set; }

        public string Description { get; set; }

        public string UUID { get; set; }

        public string VKN { get; set; }

        public string FilePath { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime CancelDate { get; set; }

        public double TotalVatExcludes { get; set; }

        public string EnvUUID { get; set; }

        public string CustInvID { get; set; }

        public int EInvoiceAcceptedRed { get; set; }

        public string EInvoiceApplicationAnswer { get; set; }

        public DateTime OrderDate { get; set; }

        public string ShippingCompany { get; set; }

        public string PaymentType { get; set; }

        public int? Integrator { get; set; }

        public EInvoiceSort EInvoiceSort { get; set; }
    }
}