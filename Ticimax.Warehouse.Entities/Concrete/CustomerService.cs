using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class CustomerService : IEntity
    {
        public int ID { get; set; }

        public int Type { get; set; }

        public int OrderID { get; set; }

        public string OrderNo { get; set; }

        public int PaymentID { get; set; }

        public int StatusID { get; set; }

        public string StatusStr { get; set; }

        public int CallingMemberId { get; set; }

        public int CallByMemberID { get; set; }

        public string CallingMemberName { get; set; }

        public string CallByMemberName { get; set; }
        public DateTime OrderDate { get; set; }

        public string CallByPhoneNumber { get; set; }

        public int NumberOfCalls { get; set; }

        public DateTime AddingDate { get; set; }

        public DateTime? FirstCallDate { get; set; } = null;

        public DateTime? LastCallDate { get; set; } = null;

        public string OrderDeliveryPhone { get; set; }

        public double ReturnAmount { get; set; }

        public int PaymentType { get; set; }

        public double OrderTotalAmount { get; set; }

        public string OrderTotalAmountStr { get; set; }
    }
}