using System.Collections.Generic;
using System.Linq;

namespace Ticimax.Warehouse.Entities.Concrete.ShelfCounting.Item.Enums
{
    public static class ShelfCountingItemStatus
    {
        public static string Created => "Created";

        public static string Processing => "Processing";
        
        public static string ShelfCompleted => "ShelfCompleted";

        public static string Completed => "Completed";

        public static string PartiallyCompleted => "PartiallyCompleted";

        public static string OverCompleted => "OverCompleted";

        private static IEnumerable<string> ValidSources => new List<string>
        {
            Created,
            Processing,
            ShelfCompleted,
            Completed,
            PartiallyCompleted,
            OverCompleted
        };

        public static bool IsValid(string source)
        {
            return ValidSources.Any(x => x.Contains(source));
        }
    }
}