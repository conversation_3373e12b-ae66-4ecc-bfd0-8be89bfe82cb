using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete.ShelfCounting.Item.Events
{
    public class ShelfCountingItemEventPayload : BaseDomainEvent
    {
        public ShelfCountingItemEventPayload()
        {
        }

        public ShelfCountingItemEventPayload(ShelfCountingItemAggregate aggregate)
        {
            FileId = aggregate.FileId;
            ShelfId = aggregate.ShelfId;
            ProductId = aggregate.ProductId;
            AgentId = aggregate.AgentId;
            ProductOldCount = aggregate.ProductOldCount;
            ProductCount = aggregate.ProductCount;
        }

        public Guid FileId { get; set; }

        public int ShelfId { get; set; }

        public int ProductId { get; set; }

        public int AgentId { get; set; }

        public double ProductOldCount { get; set; }

        public double ProductCount { get; set; }
    }
}