using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class WarehouseCustomer : IEntity
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string Mail { get; set; }

        public string Telephone { get; set; }

        public CustomerContactUserDto ContactUser { get; set; }

        public bool isActive { get; set; }

        public bool isDeleted { get; set; }

        public DateTime CreateDate { get; set; }

        public int CreateUserID { get; set; }

        public DateTime ModifiedDate { get; set; }

        public int ModifiedUserID { get; set; }
    }
}