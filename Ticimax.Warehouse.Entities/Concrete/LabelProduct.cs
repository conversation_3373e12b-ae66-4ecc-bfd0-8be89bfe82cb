using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class LabelProduct : IEntity
    {
        public LabelProduct Clone()
        {
            var product = (LabelProduct)MemberwiseClone();
            return product;
        }

        public int ProductID { get; set; }

        //public string Urun<PERSON>di { get; set; }
        public string Barcode { get; set; }

        public double Piece { get; set; }

        public int ShelfID { get; set; }

        public string ShelfName { get; set; } = "";

        public DateTime Date { get; set; }

        public int TableID { get; set; }

        public string Table { get; set; } = "";

        public int BoxID { get; set; }

        public string Box { get; set; } = "";

        public int OrderID { get; set; }

        public int OrderProductID { get; set; }

        public bool isSingleProduct { get; set; }
    }
}