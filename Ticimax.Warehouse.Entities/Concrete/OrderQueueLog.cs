using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class OrderQueueLog : IEntity
    {
        public int ID { get; set; }

        public DateTime DateTime { get; set; }

        public int UserID { get; set; }

        public string UserName { get; set; }

        public int OrderID { get; set; }

        public string Detail { get; set; }

        public OrderQueueProcess Process { get; set; }

        public enum OrderQueueProcess
        {
            ProductFind = 1,
            QCOrderCompleted = 2,
            QCBoxCompleted = 3,
            DistributorOrderAssign = 4,
            DistributorOrderCompleted = 5,
            OrderCancel = 6,
        }
    }
}