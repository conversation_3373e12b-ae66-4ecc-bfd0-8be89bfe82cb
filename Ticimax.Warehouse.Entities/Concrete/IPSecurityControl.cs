using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Dtos.LoginControlDto;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class IPSecurityControl : IEntity
    {
        public int ID { get; set; }

        public string PhysicalAddress { get; set; }

        public string NumberOfAttempts { get; set; }

        public string DateOfUpdate { get; set; }

        public SecurityControlDto ToEntity()
        {
            return new SecurityControlDto
                { ID = ID, PhysicalAddress = PhysicalAddress, NumberOfAttempts = NumberOfAttempts.ToInt32(), DateOfUpdate = DateOfUpdate };
        }
    }
}