using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Enums;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class WarehouseOperationRules : IEntity
    {
        public int ID { get; set; }
        public int WarehouseId { get; set; }
        public string Definition { get; set; }
        public string Process { get; set; }
        public int Active { get; set; }
        public WarehousePackagingOperationRules? WarehousePackagingOperationRules { get; set; }
        public ReturnOrderOperationRules? ReturnOrderOperationRules { get; set; }
        public WarehouseOperationRules(int id, int warehouseId, string definition, string process, int active, WarehousePackagingOperationRules? warehousePackagingOperationRules, ReturnOrderOperationRules? returnOrderOperationRules)
        {
            ID = id;
            WarehouseId = warehouseId;
            Definition = definition;
            Process = process;
            Active = active;
            if (Process == WarehouseOperationRuleEnums.PackagingOperation)
                WarehousePackagingOperationRules = warehousePackagingOperationRules ?? new WarehousePackagingOperationRules();
            if (Process == WarehouseOperationRuleEnums.ReturnOrderOperation)
                ReturnOrderOperationRules = returnOrderOperationRules ?? new ReturnOrderOperationRules();
        }
        public WarehouseOperationRules()
        {
            
        }
    }

    public class WarehousePackagingOperationRules
    {
        public double Amount { get; set; }
        public int PaymentType { get; set; } = -1;
        public List<int> BreadCrumbsCategories { get; set; }
    }

    public class ReturnOrderOperationRules
    {
        // Boş -> 0  Dolu -> 1  Custom -> 2
        public int EntireOrderRefundShippingFeeReturn { get; set; }
        public int EntireOrderCashOnDeliveryReturn { get; set; }
        public int EntireOrderFixedRefundAmountReturn { get; set; }
        public int EntireOrderRefundBankCommissionReturn { get; set; }
        public int OrderPartialReturnRefundShippingFeeReturn { get; set; }
        public int OrderPartialReturnCashOnDeliveryReturn { get; set; }
        public int OrderPartialReturnRefundBankCommissionReturn { get; set; }
        public int OrderNotReceivedReturnRefundShippingFeeReturn { get; set; }
        public int OrderNotReceivedReturnCashOnDeliveryReturn { get; set; }
        public int OrderNotReceivedReturnFixedRefundAmountReturn { get; set; }
        public int OrderNotReceivedReturnRefundBankCommissionReturn { get; set; }
        public bool WorkReasonForReturn { get; set; }
        public List<int> ReturnOrderReasonIds { get; set; }
    }
}