using MySqlConnector;
using System;
using System.Collections.Generic;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class SerializableMySqlCommand
    {
        public string CommandText { get; set; }
        public List<SerializableMySqlParameter> Parameters { get; set; }
    }

    public class SerializableMySqlParameter
    {
        public string Name { get; set; }
        public object Value { get; set; }
        public MySqlDbType DbType { get; set; }
    }

}

