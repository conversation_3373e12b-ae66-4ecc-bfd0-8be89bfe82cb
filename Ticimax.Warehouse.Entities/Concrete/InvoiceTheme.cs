using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class InvoiceTheme : IEntity
    {
        public int ID { get; set; }

        public string Name { get; set; }

        public string Html { get; set; }

        public string Javascript { get; set; }

        public string Css { get; set; }

        public DateTime CreatedDate { get; set; }

        public bool IsDefault { get; set; }

        public string BackgroundImage { get; set; }

        public InvoiceThemeSettings Settings { get; set; }

        public List<InvoiceThemeCustomPrinterMatch> CustomPrinterMatches { get; set; } = new List<InvoiceThemeCustomPrinterMatch>();

        public string Type { get; set; }
    }
}