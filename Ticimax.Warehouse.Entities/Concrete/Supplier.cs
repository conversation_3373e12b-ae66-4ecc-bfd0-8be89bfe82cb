using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class Supplier : IEntity
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string MailAddress { get; set; }

        public string TelephoneNumber { get; set; }

        public SupplierContactUserDto ContactUser { get; set; }

        public DateTime CreateDate { get; set; }

        public DateTime ModifiedDate { get; set; }

        public int CreateUserID { get; set; }

        public int ModifiedUserID { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }
        public string SupplierCode { get; set; }

        public SimpleSupplierDto SimpleSupplierEntity()
        {
            return new SimpleSupplierDto
                { ID = ID, Definition = Definition };
        }
    }
}