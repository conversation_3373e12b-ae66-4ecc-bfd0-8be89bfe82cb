using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class CargoIntegration : IEntity
    {
        public int ID { get; set; }

        public string Description { get; set; }

        public string CargoCode { get; set; }

        public int CargoCompanyID { get; set; }

        public string PersonName { get; set; }

        public string Password { get; set; }

        public string CustomerNo { get; set; }

        public string SpecialArea { get; set; }

        public Boolean Active { get; set; }

        public Boolean CargoTracking { get; set; }

        public Boolean CargoSending { get; set; }

        public bool CargoReturn { get; set; }

        public string CargoReturnMessage { get; set; }

        public bool ReturnManagerOk { get; set; }

        public string ApiUsername { get; set; }

        public string ApiPassword { get; set; }

        public int AutoIntegrationCargoCompanyId { get; set; }

        public CargoWarehouseSettingsDto WarehouseSettings { get; set; }
    }
}