using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class DynamicFormData : IEntity
    {
        public int ID { get; set; }

        public int PersonID { get; set; }

        public string PersonName { get; set; }

        public int FormID { get; set; }

        public int ObjectID { get; set; }

        public string ObjectType { get; set; }

        public string FormOriginalData { get; set; }

        public DateTime AddingDate { get; set; }

        public List<DynamicFormObject> FormObjects { get; set; }
    }
}