using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using System;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class ReturnOrderRequest
    {
        public int Id { get; set; }
        public int MemberId { get; set; }

        public int OrderId { get; set; }
        public string OrderNo { get; set; }
        public DateTime OrderDate { get; set; }

        public string Note { get; set; }

        public string IbanName { get; set; }

        public string Iban { get; set; }
        public int PaymentTypeId { get; set; }
        public string PaymentType { get; set; }
        public int RefundType { get; set; }
        public int StatusId { get; set; }
        public DateTime CreatedDate { get; set; }
        public string Answer { get; set; }
        public string AnsweredById { get; set; }
        public DateTime AnswerDate { get; set; }
        public int CargoCompanyId { get; set; }
        public int CargoIntegrationId { get; set; }
        public string CargoIntegrationDefination { get; set; }
        public string CargoIntegrationCode { get; set; }
        public string CargoCode { get; set; }
        public string MemberName { get; set; }
        public string Status { get; set; }
        public string AnsweredBy { get; set; }
        public int ReasonForCancellationId { get; set; }
        public string ReasonForCancellationDefination { get; set; }
        public int ReasonForCancellationTypeId { get; set; }
        public string ReasonForCancellationTypeDefination { get; set; }
        public string CargoCallCode { get; set; }
        public int GiftCertificateId { get; set; }
        public string GiftCertificateCode { get; set; }
        public string Description { get; set; }
        public double Amount { get; set; }
        public string AmountStr { get; set; }
        public string Currency { get; set; }
        public double ApprovedProductNumber { get; set; }
        public double RejectedProductNumber { get; set; }
        public double WaitingProductNumber { get; set; }
        public int  StoreId { get; set; }
        public string  StoreName { get; set; }
        public string  StoreReturnCode { get; set; }
        public string  StoreCode { get; set; }
        public bool  MarketplaceTransferred { get; set; }
        public string  MarketplaceReturnId { get; set; }
        public string OrderSource { get; set; }
    }
}