using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.BusinessEntities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class WarehouseSettings : IEntity
    {
        public int Id { get; set; }

        public int ChangerUser { get; set; }

        public DateTime ChangeDate { get; set; }

        public string ChangerIpAddress { get; set; }

        public int WarehouseId { get; set; }

        public int StoreId { get; set; }

        public BLDepoAyar Settings { get; set; }
    }
}