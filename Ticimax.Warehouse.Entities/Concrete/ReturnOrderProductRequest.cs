using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class ReturnOrderProductRequest : IEntity
    {
        public int Id { get; set; }

        public int ReturnRequestId { get; set; }

        public int OrderId { get; set; }

        public int OrderProductId { get; set; }

        public int ProductCardId { get; set; }

        public int ProductId { get; set; }

        public double Quantity { get; set; }

        public int CancelReturnRequestId { get; set; }

        public string CancelReturnRequestName { get; set; }
        public string CancelReturnReasonTypeName { get; set; }

        public int CancelReturnTypeId { get; set; }

        public int StatusId { get; set; }

        public string Reply { get; set; }
        public string Answer { get; set; }

        public DateTime ReplyDate { get; set; }
    }
}