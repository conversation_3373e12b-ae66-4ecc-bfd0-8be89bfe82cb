using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Concrete
{
    public class DynamicFormOption : IEntity
    {
        public int Order { get; set; }

        public string Name { get; set; }

        public string Value { get; set; }

        public double? OptionalPrice { get; set; }

        public double? OptionalPreviewPrice { get; set; }

        public string OptionalPriceDesc { get; set; }

        public string ImagePath { get; set; }

        public string Placeholder { get; set; }

        //HESAPLAMA
        public string PriceOperator { get; set; }

        public string OutPriceOperator { get; set; }

        public string OutValue { get; set; }
    }
}