using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Enums;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class CustomerServiceFilter : IFilter
    {
        public int? ID { get; set; }

        public CustomerServiceType? Type { get; set; }

        public List<int> StatusIDs { get; set; } = new List<int>();

        public int? OrderID { get; set; }

        public string OrderNo { get; set; }

        public int? PaymentID { get; set; }

        public CustomerServiceStatus? StatusID { get; set; }

        public int? CallingMemberId { get; set; }

        public int? CallByMemberID { get; set; }

        public int? PaymentType { get; set; }

        public string CallingMemberName { get; set; }

        public string CallByMemberName { get; set; }

        public string CallByPhoneNumber { get; set; }

        public string OrderDeliveryPhone { get; set; }

        public DateTime? AddingDateStart { get; set; }

        public DateTime? AddingDateFinish { get; set; }

        public DateTime? FirstCallDateStart { get; set; }

        public DateTime? FirstCallDateFinish { get; set; }

        public DateTime? LastCallDateStart { get; set; }

        public DateTime? LastCallDateFinish { get; set; }
    }
}