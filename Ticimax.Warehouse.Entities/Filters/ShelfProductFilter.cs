using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class ShelfProductFilter : IFilter
    {
        private string urunAdiAlani = "URUNADI";

        public int ID { get; set; }

        public int ProductID { get; set; }

        public string ProductBarcode { get; set; }

        public string ProductStockCode { get; set; }

        public int ShelfID { get; set; }

        public int WarehouseID { get; set; }
        public bool NotIncludingGoodsReceivingShelf { get; set; }

        public double Stock { get; set; }

        public bool IsMultipleBarcode { get; set; }

        public List<int> ProductIDList { get; set; }

        public bool? IsStockAvailable { get; set; }

        public bool? IsSaleOpened { get; set; }
        public bool? IsPickingOpened { get; set; }

        public string ProductNameField
        {
            get => !string.IsNullOrEmpty(urunAdiAlani) ? urunAdiAlani : "URUNADI";
            set => urunAdiAlani = value;
        }

        public List<int> WarehouseIDList { get; set; }

        public bool? IsEmptyShelf { get; set; }

        public bool IsShelfProdutControlPiece { get; set; }

        public List<int> ShelfIds { get; set; }
    }
}