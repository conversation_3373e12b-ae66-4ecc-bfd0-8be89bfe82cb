using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class StoreAgentFilter : IFilter
    {
        public int ID { get; set; }

        public List<int> IDs { get; set; }

        public int? StoreID { get; set; }

        public int? WarehouseID { get; set; }
        public int? AuthGroupID { get; set; }

        public int TypeID { get; set; }

        public int Active { get; set; } = -1;

        public string Username { get; set; }
        public string UserNameContains { get; set; }

        public string Password { get; set; }

        public string SetNo { get; set; }

        public int? ShadowUserID { get; set; }
        public int? IsCustomPrinter { get; set; }
        public bool WareHouseIdControl { get; set; } = false;
    }
}