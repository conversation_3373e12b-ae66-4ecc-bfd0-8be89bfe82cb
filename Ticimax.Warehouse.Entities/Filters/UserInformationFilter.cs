using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class UserInformationFilter : IFilter
    {
        public int ID { get; set; }

        public List<int> IDs { get; set; }

        public int UserID { get; set; }

        public string Token { get; set; }

        public string DomainName { get; set; }

        public bool isGetCount { get; set; }
    }
}