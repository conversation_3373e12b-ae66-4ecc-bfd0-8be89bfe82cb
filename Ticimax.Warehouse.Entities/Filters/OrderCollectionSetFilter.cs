using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    [System.Diagnostics.DebuggerStepThrough]
    public class OrderCollectionSetFilter : IFilter
    {
        private string urunAdiAlani = "URUNADI";

        public string SetNo { get; set; }

        public string ProductBarcode { get; set; }

        public int? ID { get; set; }

        public int? PreparedID { get; set; } = null;

        public int? PreparationStatus { get; set; } = null;

        public int? OrderID { get; set; } = null;

        public int? OrderProductID { get; set; } = null;

        public List<int> OrderIDList { get; set; } = new List<int>();

        public int? TableID { get; set; }

        public int? WarehouseID { get; set; }

        public int WarehouseCarId { get; set; }

        public int? BoxID { get; set; }

        public int? CarID { get; set; }

        public bool? FindStatus { get; set; }

        public bool? MissingProduct { get; set; }

        public bool? BoxAssign { get; set; }

        public bool isGrouping { get; set; } = true;

        public bool MultipleBarcode { get; set; } = false;
        public bool OrderCombinesProductFound { get; set; }

        public int DataLimit { get; set; } = 0;

        public List<int> PreparedStatusList { get; set; }
        public List<int> CarIDList { get; set; } = new List<int>();
        public List<int> BoxIDs { get; set; } = new List<int>();

        public double? OrderProductPiece { get; set; }

        public DateTime? DateStart { get; set; }

        public DateTime? DateFinish { get; set; }

        public string ProductNameFields
        {
            get => !string.IsNullOrEmpty(urunAdiAlani) ? urunAdiAlani : "URUNADI";
            set => urunAdiAlani = value;
        }

        public int? ShelfID { get; set; }

        public string ShelfName { get; set; }

        public int ProductID { get; set; }

        public List<int> ProductIds { get; set; }

        public bool InvoiceCreated { get; set; }

        public int? Status { get; set; }

        public bool? IsEmptyShelf { get; set; } = null;

        public bool BoxControl { get; set; }

        public int? WarehouseBoxID { get; set; }
        public string? ProductIdsStr { get; set; }

        public string WarehouseBoxBarcode { get; set; }

        public bool IsAvailableQualityControl { get; set; }
    }
}