using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class InvoiceFilter : IFilter
    {
        public string Series { get; set; }

        public int? Rank { get; set; }

        public int? OrderID { get; set; }

        public List<int> OrderIDList { get; set; }

        public DateTime? DateStart { get; set; }

        public DateTime? DateFinish { get; set; }

        public bool? isCancel { get; set; }

    }
}