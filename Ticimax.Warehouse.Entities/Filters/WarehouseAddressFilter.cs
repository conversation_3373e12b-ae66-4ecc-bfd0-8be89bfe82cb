using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class WarehouseAddressFilter : IFilter
    {
        public int ID { get; set; }

        public int TargetID { get; set; }

        public int Type { get; set; }

        /// <summary>
        /// 1 - Fatura Adresi
        /// 2 - Teslimat Adresi
        /// </summary>
        public int AddressType { get; set; }

        public bool isGetCount { get; set; }
    }
}