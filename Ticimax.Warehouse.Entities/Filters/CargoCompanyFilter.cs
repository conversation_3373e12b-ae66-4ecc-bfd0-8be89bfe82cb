using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class CargoCompanyFilter : IFilter
    {
        public int? ID { get; set; }

        public bool? Integration { get; set; }

        public bool? Active { get; set; }

        public string CurrencyCode { get; set; }

        public int? Type { get; set; }

        public List<int> Ids { get; set; }
    }
}