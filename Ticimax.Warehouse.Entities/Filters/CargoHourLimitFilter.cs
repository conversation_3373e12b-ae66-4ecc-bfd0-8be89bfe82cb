using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class CargoHourLimitFilter : IFilter
    {
        public int ID { get; set; }

        public int CargoID { get; set; }

        public int CountryID { get; set; }

        public int ProvinceID { get; set; }

        public int DistrictID { get; set; }

        public int SemtID { get; set; }

        public int Day { get; set; } = -1;

        public DateTime Date { get; set; }
    }
}