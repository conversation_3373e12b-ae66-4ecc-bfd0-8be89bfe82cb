using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class WarehouseParcelFilter : IFilter
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public int Limit { get; set; } = -1;

        public int TableID { get; set; }

        public int WarehouseID { get; set; }

        public int StoreID { get; set; }

        public string Barcode { get; set; }

        public bool? isMissingProductParcel { get; set; }

        public List<int> IDList { get; set; }

        public List<int> TableIDList { get; set; }

        public bool? IsEmpty { get; set; }

        public bool? IsWaitingCall { get; set; }
        
        public bool? IsWaitingInvoice { get; set; }
    }
}