using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class ProductPlacementLogFilter : IFilter
    {
        public int? ID { get; set; }

        public List<int> IDs { get; set; }

        public int? UserID { get; set; }

        public List<int> UserIDs { get; set; }

        public int? ProductID { get; set; }

        public List<int> ProductIDs { get; set; }

        public int? ShelfID { get; set; }

        public List<int> ShelfIDs { get; set; }

        public int? WarehouseID { get; set; }

        public List<int> WarehouseIDs { get; set; }

        public DateTime? FilterDate { get; set; }

        public string StartDate { get; set; }

        public string EndDate { get; set; }

        public string TableDate { get; set; }

        public int? OrderID { get; set; }

        public List<int> OrderIDs { get; set; }

        public bool IsOnlyExtraction { get; set; } = false;
    }
}