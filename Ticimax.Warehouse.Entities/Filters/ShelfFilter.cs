using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class ShelfFilter : IFilter
    {
        public ShelfFilter()
        {
        }

        public ShelfFilter(int id, int? parentId, List<int> idList, string definition, string code, string barcode, int? warehouseId, int? storeId, int productId, bool isMissingProductShelf, bool isEmptyShelf, List<int> parentIds, bool? isOpenForSale, List<string> barcodes)
        {
            ID = id;
            ParentId = parentId;
            IDList = idList;
            Definition = definition;
            Code = code;
            Barcode = barcode;
            WarehouseId = warehouseId;
            StoreId = storeId;
            ProductID = productId;
            IsMissingProductShelf = isMissingProductShelf;
            IsEmptyShelf = isEmptyShelf;
            ParentIds = parentIds;
            IsOpenForSale = isOpenForSale;
            Barcodes = barcodes;
        }

        public int ID { get; set; }

        public int? ParentId { get; set; }

        public List<int> IDList { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public string Barcode { get; set; }

        public int? WarehouseId { get; set; }

        public int? StoreId { get; set; }

        public int ProductID { get; set; }

        public bool IsMissingProductShelf { get; set; }

        public bool IsEmptyShelf { get; set; }

        public List<int> ParentIds { get; set; }

        public bool? IsOpenForSale { get; set; }

        public List<string> Barcodes { get; set; }
    }
}