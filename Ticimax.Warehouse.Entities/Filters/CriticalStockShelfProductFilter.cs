using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class CriticalStockShelfProductFilter : IFilter
    {
        public int WarehouseId { get; set; }
        public double CriticStok { get; set; }
        public CriticalStockShelfProductFilter(int warehouseId, double criticStock)
        {
            WarehouseId = warehouseId;
            CriticStok = criticStock;
        }
    }
}