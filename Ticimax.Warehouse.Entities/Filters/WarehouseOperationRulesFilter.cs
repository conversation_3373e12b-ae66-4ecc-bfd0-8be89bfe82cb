using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class WarehouseOperationRulesFilter : IFilter
    {
        public int? ID { get; set; }

        public string? Process { get; set; }
        public int? Active { get; set; }
        public WarehouseOperationRulesFilter(int? id, string? process, int? active)
        {
            ID = id;
            Process = process;
            Active = active;
        }
    }
}