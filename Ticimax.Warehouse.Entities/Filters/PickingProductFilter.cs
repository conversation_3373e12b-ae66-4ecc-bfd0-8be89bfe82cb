using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Enums;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class PickingProductFilter : IFilter
    {
        public int OrderID { get; set; }

        public List<int> OrderIDList { get; set; } = new List<int>();

        public OrderStatus? OrderStatus { get; set; }

        public List<OrderStatus> OrderStatusList { get; set; } = new List<OrderStatus>();
        public PackageStatus? PackageStatus { get; set; }

        public int? PriorityPackageStatus { get; set; }

        public int PreparedID { get; set; } = -1;

        public int CurrierID { get; set; } = -1;

        public int DeliveryDateLimit { get; set; }

        public int CargoType { get; set; }
        public bool IsPickingProductScreen { get; set; }

        public int StoreID { get; set; }

        public string OrderNo { get; set; }

        public bool AvailableOrders { get; set; }
        public bool SetManagementOrders { get; set; }

        public bool ProductAvailable { get; set; } = true;

        public int ProductCount { get; set; }

        public int ProductCountLarge { get; set; }

        public string OrderSource { get; set; }
        public string MarketPlaceOrderType { get; set; }

        public bool ShelfStockDistibutionControl { get; set; } = true;
        public bool AllOrderFromAllStores { get; set; }

        public int CargoCompanyId { get; set; }
        public string CargoCompanyOrderType { get; set; }
        public int CargoIntegrationId { get; set; }
        public int CountryId { get; set; }

        public int? PaymentType { get; set; }
        public int? ProductId { get; set; }
        public int? Monthly { get; set; }
        public List<int> ProductIds { get; set; }

        public bool? IsConsignmentProduct { get; set; }
        public bool IsReportRequest { get; set; } = false;
        public string Barcode { get; set; }
        public string StockCode { get; set; }
        public bool IgnoreTheRemainingAmount { get; set; }
    }
}