using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class DynamicFormFileFilter : IFilter
    {
        public int ID { get; set; } = -1;

        public int FormID { get; set; } = -1;

        public int FormDataID { get; set; } = -1;

        public string Dosya<PERSON>di { get; set; }

        public List<int> FormDataIDs { get; set; }
    }
}