using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class WarehouseCarProductFilter : IFilter
    {
        public int Id { get; set; }
        public int WarehouseCarID { get; set; }

        public string ProductBarcode { get; set; }

        public int ProductID { get; set; }

        public bool isGroupBy { get; set; } = true;

        public List<int> TargetIDs { get; set; }

        public int OrderID { get; set; }
        public string FileId { get; set; }
        public bool IsWarehouseTransfer { get; set; }
        public int? IsGetNotApproval { get; set; }
    }
}