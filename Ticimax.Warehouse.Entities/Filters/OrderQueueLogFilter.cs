using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Filters
{
    public class OrderQueueLogFilter : IFilter
    {
        public int? ID { get; set; }

        public int? UserID { get; set; }

        public int? OrderID { get; set; }

        public DateTime? FilterDate { get; set; }

        public string TableDate { get; set; }

        public int? Process { get; set; }

        public bool isGetCount { get; set; }
    }
}