using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;

namespace Ticimax.Warehouse.Entities.BusinessEntities
{
    public class User
    {
        public int ID { get; set; }

        [Required(ErrorMessage = "Alan adı girmelisiniz.")]
        [Display(Name = "Alan Adı")]
        public string DomainName { get; set; }

        [Required(ErrorMessage = "Kullanıcı adı girmelisiniz.")]
        [Display(Name = "Kullanıcı Adı")]
        public string Username { get; set; }

        [Required(ErrorMessage = "<PERSON><PERSON>re girmelisiniz.")]
        [DataType(DataType.Password)]
        [Display(Name = "Şifre")]
        public string Password { get; set; }
        public string OutSourceUserCacheKey { get; set; }

        public string Name { get; set; }

        public string Email { get; set; }

        public int Type { get; set; }

        public string Telephone { get; set; }

        public string InternalNumber { get; set; }

        public int StoreID { get; set; }

        public string StoreName { get; set; }

        public int CargoType { get; set; }

        public int WarehouseID { get; set; }

        public string Warehouse { get; set; }

        public int TableID { get; set; }

        public string Table { get; set; }

        public string SetNo { get; set; }

        public string Image { get; set; }

        public string AuthGroupName { get; set; }

        public List<string> Authority { get; set; }

        public BLDepoAyar Settings { get; set; }

        public BLSiteYonetimAyar SiteSettings { get; set; }

        public BLFirmaBilgileri CompanyInformation { get; set; }

        public BLIadeKargoAyar ReturnCargoSettings { get; set; } = new BLIadeKargoAyar();

        public BLMagazaModulAyar StoreModuleSettings { get; set; }

        public BLSiparisAyarlari OrderSettings { get; set; }

        public BLOdemeAyar PaymentSettings { get; set; }

        public WarehouseCar WarehouseCar { get; set; }

        public MinioSetting MinioSettings { get; set; }

        public string CdnAddress { get; set; }

        public string ImagePath { get; set; }

        public BLEArsivFaturaAyarlari EArchiveEInvoiceSettings { get; set; }

        public bool PayDoorCheckoutSmsApprovalActive { get; set; }

        public bool ShowDeliveryDate { get; set; }

        public string WarehouseToken { get; set; }

        public DateTime WarehouseTokenExpire { get; set; }

        public string WarehouseTokenServer { get; set; }

        public List<QuickMenu> QuickMenus { get; set; }

        public bool CheckUser { get; set; }

        public string DomainBasedLockerKey { get; set; }

        public bool isTicimaxUser { get; set; } = false;

        public string Version { get; set; }

        public bool IsOneStore { get; set; } = false;

        public string IpAddress { get; set; }

        public string Url { get; set; }

        public int OrderCancelLimit { get; set; } = 0;

        public List<IDbCommand> Commands { get; set; } = new List<IDbCommand>();

        public List<DomainEvent> Events { get; set; } = new List<DomainEvent>();

        public RateLimitCheck RateLimit { get; set; } = new RateLimitCheck();

        public bool CustomPrinter { get; set; }
    }
}