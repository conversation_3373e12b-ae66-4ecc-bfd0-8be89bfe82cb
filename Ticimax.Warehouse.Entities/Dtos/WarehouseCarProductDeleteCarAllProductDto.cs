using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseCarProductDeleteCarAllProductDto : IDto
    {
        public int WarehouseCarID { get; set; }

        public int OrderID { get; set; }

        public List<int> OrderIDs { get; set; }

        public WarehouseCarProduct ToEntity()
        {
            return new WarehouseCarProduct
                { WarehouseCarID = WarehouseCarID, OrderID = OrderID, OrderIDs = OrderIDs };
        }
    }
}