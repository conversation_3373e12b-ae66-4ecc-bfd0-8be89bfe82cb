using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class CategoryGetListDto : IDto
    {
        public int? ID { get; set; }
        public int? PID { get; set; }

        public string? Code { get; set; }

        public bool? Active { get; set; }

        public List<int> Ids { get; set; }

        public CategoryFilter ToFilter()
        {
            return new CategoryFilter
            { ID = ID, Code = Code, Active = Active,  Ids = Ids, PID = PID };
        }
    }
}