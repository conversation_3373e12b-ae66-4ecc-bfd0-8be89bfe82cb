using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class CustomerServiceUpdateCallingMemberDto : IDto
    {
        public int CallByMemberID { get; set; }

        public string CallingMemberName { get; set; }

        public DateTime? FirstCallDate { get; set; } = null;

        public DateTime? LastCallDate { get; set; } = null;

        public CustomerService ToEntity(int id)
        {
            return new CustomerService
                { ID = id, CallByMemberID = CallByMemberID, CallingMemberName = CallingMemberName, FirstCallDate = FirstCallDate, LastCallDate = LastCallDate };
        }
    }
}