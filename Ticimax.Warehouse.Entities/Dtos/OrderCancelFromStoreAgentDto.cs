using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class OrderCancelFromStoreAgentDto : IDto
    {
        public OrderCancelFromStoreAgentDto()
        {
        }

        public OrderCancelFromStoreAgentDto(StoreAgentCancelOrderLog log)
        {
            ID = log.ID;
            Date = log.Date;
            UserID = log.UserID;
            UserName = log.UserName;
            OrderID = log.OrderID;
            ReasonID = log.ReasonID;
        }

        public int ID { get; set; }

        public DateTime Date { get; set; }

        public int UserID { get; set; }

        public string UserName { get; set; }

        public int OrderID { get; set; }

        public int ReasonID { get; set; }
    }
}