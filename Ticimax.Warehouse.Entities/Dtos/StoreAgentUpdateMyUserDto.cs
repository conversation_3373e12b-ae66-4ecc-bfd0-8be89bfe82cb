using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class StoreAgentUpdateMyUserDto : IDto
    {
        public string Name { get; set; }

        public string LastName { get; set; }

        public string Email { get; set; }

        public string Image { get; set; }

        public string Username { get; set; }

        public string Password { get; set; }

        public string Telephone { get; set; }
        public bool CustomPrinter { get; set; }
    }
}