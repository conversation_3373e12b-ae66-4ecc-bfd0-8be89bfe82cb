using Ticimax.Core.Order.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseTableProduct
    {
        public WarehouseTableProduct()
        {

        }

        public WarehouseTableProduct(OrderProduct orderProduct)
        {
            TableId = orderProduct.TableID;
            TableName = orderProduct.TableName;
            ParcelId = orderProduct.BoxID;
            ParcelName = orderProduct.BoxName;
            Piece = orderProduct.OccurrencesPiece + orderProduct.MissingPiece;
        }

        public int TableId { get; set; }
        public string TableName { get; set; }
        public int ParcelId { get; set; }
        public string ParcelName { get; set; }
        public double Piece { get; set; }
    }
}
