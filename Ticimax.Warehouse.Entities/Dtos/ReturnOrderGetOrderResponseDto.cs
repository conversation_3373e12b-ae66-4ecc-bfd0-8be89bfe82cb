using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ReturnOrderGetOrderResponseDto : IDto
    {
        public Order Order { get; set; }

        public List<OrderProductReturnCause> ReturnCausies { get; set; }

        public Dictionary<int, string> ReturnType { get; set; }

        public bool isCampaignOrder { get; set; }

        public ReturnOrderRequest ReturnOrderRequest { get; set; }
    }
}