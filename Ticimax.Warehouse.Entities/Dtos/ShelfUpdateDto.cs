using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ShelfUpdateDto : IDto
    {
        public int ParentId { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public string Barcode { get; set; }

        public int Rank { get; set; }

        public bool IsMissingProductShelf { get; set; }

        public bool IsOpenForSale { get; set; }
        public bool IsOpenPicking { get; set; }

        public bool IsEmptyShelf { get; set; }

        public Shelf ToEntity(int id)
        {
            return new Shelf(id, ParentId, Definition, Code, Barcode, WebSiteInfo.User.Value.WarehouseID, WebSiteInfo.User.Value.StoreID, Rank, IsMissingProductShelf, IsEmptyShelf, IsOpenForSale, IsOpenPicking);
        }
    }
}