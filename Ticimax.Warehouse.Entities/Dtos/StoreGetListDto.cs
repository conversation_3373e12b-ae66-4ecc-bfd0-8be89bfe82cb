using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class StoreGetListDto : IDto
    {
        public int StoreID { get; set; }

        public List<int> StoreIds { get; set; }

        public bool isGetCount { get; set; }

        public StoreFilter ToFilter()
        {
            return new StoreFilter
                { StoreID = StoreID, StoreIds = StoreIds };
        }
    }
}