using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class PickedProductsDto : IDto
    {
        public int WarehouseId { get; set; }
        public int PersonId { get; set; }
        public string PersonName { get; set; }

        public int ProductID { get; set; }

        public string ProductName { get; set; }

        public string Barcode { get; set; }

        public double Piece { get; set; }

        public int ShelfId { get; set; }
        public string Shelf { get; set; }

        public DateTime DateTime { get; set; }

        public string StockCode { get; set; }
    }
}