using System;
using Ticimax.Core.Entities;
using Ticimax.Core.Product.Entities.Enums;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class MissingProductReasonLogDto : IDto
    {
        public int ID { get; set; }

        public int UserID { get; set; }

        public string UserName { get; set; }

        public DateTime DateTime { get; set; }

        public int TargetID { get; set; }

        public ProductType TargetType { get; set; }

        public int ShelfID { get; set; }

        public double Piece { get; set; }

        public bool StatusOfAfterProcess { get; set; }

        public MissingProductReasonLog ToEntity()
        {
            return new MissingProductReasonLog
                { ID = ID, UserID = UserID, UserName = UserName, DateTime = DateTime, TargetID = TargetID, TargetType = TargetType, ShelfID = ShelfID, Piece = Piece, StatusOfAfterProcess = StatusOfAfterProcess };
        }
    }
}