using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseOperationRulesGetListDto : IDto
    {
        public int? ID { get; set; }

        public string? Process { get; set; }
        public int? Active { get; set; }

        public WarehouseOperationRulesFilter ToFilter()
        {
            return new WarehouseOperationRulesFilter(ID, Process, Active);
        }
    }
}