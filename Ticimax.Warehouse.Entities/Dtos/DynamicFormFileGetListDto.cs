using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class DynamicFormFileGetListDto : IDto
    {
        public int ID { get; set; } = -1;

        public int FormID { get; set; } = -1;

        public int FormDataID { get; set; } = -1;

        public string DosyaAdi { get; set; }

        public List<int> FormDataIDs { get; set; }

        public bool isGetCount { get; set; } = false;

        public DynamicFormFileFilter ToFilter()
        {
            return new DynamicFormFileFilter { ID = ID, FormID = FormID, FormDataID = FormDataID, DosyaAdi = DosyaAdi, FormDataIDs = FormDataIDs };
        }
    }
}