using System;
using System.Collections.Generic;
using System.Linq;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Warehouse.Entities.Enums;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class OrderStatusQueueDto : IDto
    {
        public OrderStatusQueueDto()
        {
        }

        public OrderStatusQueueDto(Order order)
        {
            OrderID = order.ID;
            OrderNo = order.OrderNo;
            OrderSource = order.OrderSource;
            OrderStatus = order.Status;
            OrderStatusDefinition = order.StatusDefinition;
            TotalDesi = order.Products.Where(x => x.Status != 2).Sum(x => x.ProductInfo.Varyasyon.KargoAgirligi * x.Piece);
            TotalAmount = order.TotalAmount;
            CurrencyCode = order.CurrencyCode;
            DateTime = order.Date;
            OrderStatusDefinition = order.StatusDefinition;
            PackagingStatus = order.PackagingStatusID;
            PackagingStatusDefinition = ((PackageStatus)Enum.Parse(typeof(PackageStatus), order.PackagingStatusID.ToString())).ToString();
            OrderProducts = order.Products.Select(x => new OrderProductStatusQueueDto
                { Amount = x.Amount, ProductID = x.ProductID, StoreID = x.StoreID, ProductCardID = x.ProductCardID, SalesUnit = x.SalesUnit, Status = x.Status, StockCode = x.StockCode, Piece = x.Piece, ReturnPiece = x.ReturnPiece, KDVAmount = x.KDVAmount, ProductBarcode = x.Barcode, ProductVariationID = x.ProductInfo.Varyasyon.ID, StatusDefinition = x.StatusStr }).ToList();

            SpecialAreas = new List<SpecialAreaDto>
                { new SpecialAreaDto { ID = 1, Value = !string.IsNullOrEmpty(order.SpecialAreaOne) ? order.SpecialAreaOne : null }, new SpecialAreaDto { ID = 2, Value = !string.IsNullOrEmpty(order.SpecialAreaTwo) ? order.SpecialAreaTwo : null }, new SpecialAreaDto { ID = 3, Value = !string.IsNullOrEmpty(order.SpecialAreaThree) ? order.SpecialAreaThree : null } };
        }

        public int OrderID { get; set; }

        public string OrderNo { get; set; }

        public List<SpecialAreaDto> SpecialAreas { get; set; }

        public string OrderSource { get; set; }

        public DateTime DateTime { get; set; }

        public int OrderStatus { get; set; }

        public string OrderStatusDefinition { get; set; }

        public int PackagingStatus { get; set; }

        public string PackagingStatusDefinition { get; set; }

        public double TotalDesi { get; set; }

        public double TotalAmount { get; set; }

        public string CurrencyCode { get; set; }

        public List<OrderProductStatusQueueDto> OrderProducts { get; set; }
    }

    public class OrderProductStatusQueueDto : IDto
    {
        public int ProductID { get; set; }

        public int StoreID { get; set; }

        public int ProductVariationID { get; set; }

        public int ProductCardID { get; set; }

        public string SalesUnit { get; set; }

        public int Status { get; set; }

        public string StatusDefinition { get; set; }

        public string ProductBarcode { get; set; }

        public string StockCode { get; set; }

        public double Piece { get; set; }

        public double ReturnPiece { get; set; }

        public double Amount { get; set; }

        public double KDVAmount { get; set; }

        public bool IsAlternativeProduct { get; set; }

        public OldOrderProductDto OldOrderProduct { get; set; }
    }

    public class SpecialAreaDto : IDto
    {
        public int ID { get; set; }

        public string Value { get; set; }
    }

    public class OldOrderProductDto : IDto
    {
        public int OrderProductID { get; set; }

        public int ProductID { get; set; }

        public int ProductCardID { get; set; }

        public string ProductBarcode { get; set; }

        public string StockCode { get; set; }

        public double Piece { get; set; }

        public string SalesUnit { get; set; }

        public int Status { get; set; }

        public string StatusDefinition { get; set; }
    }
}