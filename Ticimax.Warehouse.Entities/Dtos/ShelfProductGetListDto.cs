using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ShelfProductGetListDto : IDto
    {
        public int ID { get; set; }

        public int ProductID { get; set; }

        public string ProductBarcode { get; set; }

        public string ProductStockCode { get; set; }

        public int ShelfID { get; set; }

        public int WarehouseID { get; set; }
        public bool NotIncludingGoodsReceivingShelf { get; set; }

        public List<int> WarehouseIDList { get; set; }

        public double Stock { get; set; }

        public bool isMultipleBarcode { get; set; }

        public List<int> ProductIDList { get; set; }

        public bool? IsStockAvailable { get; set; }
        
        public bool? IsSaleOpened { get; set; }

        public bool IsGetCount { get; set; } = false;

        public bool? IsEmptyShelf { get; set; }
        public bool? IsPickingOpened { get; set; }

        public List<int> ShelfIds { get; set; }

        public ShelfProductFilter ToFilter()
        {
            return new ShelfProductFilter
            {
                ID = ID,
                ProductID = ProductID,
                ProductBarcode = ProductBarcode,
                ProductStockCode = ProductStockCode,
                ShelfID = ShelfID,
                WarehouseID = WarehouseID,
                WarehouseIDList = WarehouseIDList,
                Stock = Stock,
                IsMultipleBarcode = isMultipleBarcode,
                ProductIDList = ProductIDList,
                IsStockAvailable = IsStockAvailable,
                IsEmptyShelf = IsEmptyShelf,
                ShelfIds = ShelfIds,
                IsSaleOpened = IsSaleOpened
            };
        }
    }
}