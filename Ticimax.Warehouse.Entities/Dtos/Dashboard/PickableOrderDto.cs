using System.Collections.Generic;

namespace Ticimax.Warehouse.Entities.Dtos.Dashboard;

public class PickableOrderDto
{
    public PickableOrderDto()
    {
        
    }
    public PickableOrderDto(int orderCount)
    {
        OrderCount = orderCount;
    }
    
    public int OrderCount { get; set; }

    public List<PickableOrderItemDto> Items { get; set; } = new List<PickableOrderItemDto>();
}

public class PickableOrderItemDto
{
    public PickableOrderItemDto(string type, int orderCount, double productCount)
    {
        Type = type;
        OrderCount = orderCount;
        ProductCount = productCount;
    }
    
    public string Type { get; set; }

    public int OrderCount { get; set; }

    public double ProductCount { get; set; }
}

public static class PickableOrderItemType
{
    public static string OneProductOrder => "OneProductOrder";

    public static string ALotOfProductOrder => "ALotOfProductOrder";

    public static string MixedProductOrder => "MixedProductOrder";
}