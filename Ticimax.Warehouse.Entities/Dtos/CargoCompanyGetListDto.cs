using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class CargoCompanyGetListDto : IDto
    {
        public int? ID { get; set; }

        public bool? Integration { get; set; }

        public bool? Active { get; set; }

        public string CurrencyCode { get; set; }

        public int? Type { get; set; }

        public List<int> Ids { get; set; }

        public CargoCompanyFilter ToFilter()
        {
            return new CargoCompanyFilter
                { ID = ID, Integration = Integration, Active = Active, CurrencyCode = CurrencyCode, Type = Type, Ids = Ids };
        }
    }
}