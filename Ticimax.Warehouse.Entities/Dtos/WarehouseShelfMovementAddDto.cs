using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseShelfMovementAddDto : IDto
    {
        public int ShelfId { get; set; }
        public int MemberId { get; set; }
        public string MemberName { get; set; }
        public string ProcessType { get; set; }
        public DateTime CreatedDate { get; set; }


        public WarehouseShelfMovement ToEntity()
        {
            return new WarehouseShelfMovement(0, ShelfId, MemberId, MemberName, ProcessType, CreatedDate);
        }
    }
}