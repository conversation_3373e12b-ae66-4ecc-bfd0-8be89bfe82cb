using System;
using Ticimax.Core.Entities;
using Ticimax.Core.Product.Entities.Dtos;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ConsignmentProductControllerAddDto : IDto
    {
        public int ProductCardID { get; set; }

        public int ProductID { get; set; }

        public double Stock { get; set; }

        public DateTime SupplyDate { get; set; }

        public ConsignmentProductAddDto ToEntity()
        {
            return new ConsignmentProductAddDto { ProductCardID = ProductCardID, ProductID = ProductID, Stock = Stock, SupplyDate = SupplyDate };
        }
    }
}