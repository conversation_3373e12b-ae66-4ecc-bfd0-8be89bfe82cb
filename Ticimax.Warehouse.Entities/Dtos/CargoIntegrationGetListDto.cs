using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class CargoIntegrationGetListDto : IDto
    {
        public int ID { get; set; }
        public List<int> IDList { get; set; }

        public int Active { get; set; } = -1;

        public string CargoCode { get; set; }

        public int CargoCompanyID { get; set; }

        public int Return { get; set; } = -1;

        public bool isGetCount { get; set; } = false;

        public CargoIntegrationFilter ToFilter()
        {
            return new CargoIntegrationFilter
                { ID = ID, Active = Active, CargoCode = CargoCode, CargoCompanyID = CargoCompanyID, Return = Return, IDList = IDList };
        }
    }
}