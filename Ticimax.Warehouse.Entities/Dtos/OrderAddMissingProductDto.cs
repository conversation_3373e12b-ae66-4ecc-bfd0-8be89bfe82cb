using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class OrderAddMissingProductDto : IDto
    {
        public OrderAddMissingProductDto()
        {
        }

        public OrderAddMissingProductDto(int orderId, int orderProductId, double missingPiece, int returnCauseId)
        {
            OrderID = orderId;
            OrderProductID = orderProductId;
            MissingPiece = missingPiece;
            ReturnCauseID = returnCauseId;
        }

        public int OrderID { get; set; }

        public int OrderProductID { get; set; }

        public double MissingPiece { get; set; }

        public int ReturnCauseID { get; set; }
    }
}