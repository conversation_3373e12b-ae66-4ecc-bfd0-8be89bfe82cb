namespace Ticimax.Warehouse.Entities.Dtos
{
    public class CustomerContactUserDto
    {
        /* -- NOTES --
         *
         * Supplier ve Customer'a göre Yetkili Kişi modeli bölündü.
         * Müşteri Yetkili Kişide parametreler zorunlu değil.
         *
         */

        public string Name { get; set; }

        public string Surname { get; set; }

        public string Role { get; set; }

        //[Phone]
        //[Required(AllowEmptyStrings = true)]
        //[StringLength(30, MinimumLength = 10)]
        public string Telephone { get; set; }

        //[EmailAddress]
        //[Required(AllowEmptyStrings = true)]
        public string Mail { get; set; }
    }
}