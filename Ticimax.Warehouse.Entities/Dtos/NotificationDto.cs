using System;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class NotificationDto
    {
        public Guid Id { get; set; }
        public string DomainName { get; set; }
        public int UserId { get; set; }
        public NotificationTypes NotificationType { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Icon { get; set; }
        public bool IsConfirmationRequied { get; set; }
        public bool IsConfirmed { get; set; }
        public bool IsRead { get; set; }
        public long CreatedDate { get; set; }
        public long LastModifiedDate { get; set; }
    }
    public enum NotificationTypes
    {
        ExcelJob,
        CreateCollection,
        MissingProductApprovalStatus
    }
}
