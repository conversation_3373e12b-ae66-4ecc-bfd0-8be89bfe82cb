using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class MissingProductReasonGetListDto : IDto
    {
        public int? ID { get; set; }

        public string Definiton { get; set; }

        public bool? isActive { get; set; } = null;

        public int? Operation { get; set; }

        public bool isGetCount { get; set; }

        public MissingProductReasonFilter ToFilter()
        {
            return new MissingProductReasonFilter
                { ID = ID, Definiton = Definiton, isActive = isActive, Operation = Operation };
        }
    }
}