using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseCarProductUpdateDto : IDto
    {
        public WarehouseCarProductUpdateDto(WarehouseCarProduct carProduct)
        {
            ID = carProduct.ID;
            WarehouseCarID = carProduct.WarehouseCarID;
            OrderID = carProduct.OrderID;
            ProductID = carProduct.ProductID;
            Piece = carProduct.Piece;
            Settings = carProduct.Settings;
        }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int ID { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int WarehouseCarID { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int OrderID { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int ProductID { get; set; }

        [Range(0.0000, 999999999.9999, ErrorMessage = "{0} is not equals 0")]
        public double Piece { get; set; }

        public WarehouseCarProductSettings Settings { get; set; }

        public WarehouseCarProduct ToEntity()
        {
            return new WarehouseCarProduct
                { ID = ID, WarehouseCarID = WarehouseCarID, OrderID = OrderID, ProductID = ProductID, Piece = Piece, Settings = Settings };
        }
    }
}