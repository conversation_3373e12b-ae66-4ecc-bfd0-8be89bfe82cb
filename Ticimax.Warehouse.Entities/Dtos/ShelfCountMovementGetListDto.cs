using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ShelfCountMovementGetListDto : IDto
    {
        public int ID { get; set; }

        public string CountNo { get; set; }

        public int ShelfID { get; set; }

        public int ProductID { get; set; }

        public int PersonID { get; set; }

        public string CountStartDate { get; set; }

        public string CountFinishDate { get; set; }
    }
}