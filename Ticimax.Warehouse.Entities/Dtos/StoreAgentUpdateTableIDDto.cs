using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class StoreAgentUpdateTableIDDto : IDto
    {
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int PersonID { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int TableID { get; set; }
    }
}