using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ShelfProductDeleteByProductIDAndShelfIDDto : IDto
    {
        public int ProductID { get; set; }

        public int ShelfID { get; set; }

        public ShelfProduct ToEntity()
        {
            return new ShelfProduct
                { ProductID = ProductID, ShelfID = ShelfID };
        }
    }
}