using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class OrderGetOrderListDto : IDto
    {
        public int OrderID { get; set; }

        public string OrderNo { get; set; }

        public int ProductCount { get; set; }

        public DateTime? OrderDateStart { get; set; }

        public DateTime? OrderDateFinish { get; set; }

        public DateTime? PaymentDateStart { get; set; }

        public DateTime? PaymentDateFinish { get; set; }

        public DateTime? DeliveryDateStart { get; set; }

        public DateTime? DeliveryDateFinish { get; set; }

        public int? PackagingStatus { get; set; }

        public bool isGetCount { get; set; } = false;

        public bool OrderByID { get; set; }

        public int? OrderStatus { get; set; }
        public string OrderStatusStrList { get; set; }
        public string OrderSourceList { get; set; }

        public bool AvailableOrders { get; set; } = false;

        public bool isCreateInvoice { get; set; } = false;

        public string OrderSource { get; set; }

        public int CargoID { get; set; }
        public string CargoIDStrList { get; set; }

        public string ProductIds { get; set; }
        public string CustomerName { get; set; }
        public bool? IsConsignment { get; set; }
        public string PaymentTypeList { get; set; }

    }
}