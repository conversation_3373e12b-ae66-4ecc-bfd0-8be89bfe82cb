using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseCarEditDto : IDto
    {
        public WarehouseCarEditDto()
        {
        }

        public WarehouseCarEditDto(int id, string barcode, string definition, bool isActive)
        {
            ID = id;
            Barcode = barcode;
            Definition = definition;
            this.isActive = isActive;
        }

        public int ID { get; set; }

        public string Barcode { get; set; }

        public string Definition { get; set; }

        public bool isActive { get; set; }

        public WarehouseCar ToEntity()
        {
            return new WarehouseCar
                { ID = ID, Barcode = Barcode, Definition = Definition, isActive = isActive };
        }
    }
}