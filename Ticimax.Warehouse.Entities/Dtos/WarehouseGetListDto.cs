using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseGetListDto : IDto
    {
        public int ID { get; set; }

        public int StoreID { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public bool? Active { get; set; }

        public WarehouseFilter ToFilter()
        {
            return new WarehouseFilter
                { ID = ID, StoreID = StoreID, Definition = Definition, Code = Code, Active = Active };
        }
    }
}