using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ShelfProductDeleteByShelfIDDto : IDto
    {
        public int ShelfID { get; set; }
        public bool ReduceStockWebSite { get; set; }

        public ShelfProduct ToEntity()
        {
            return new ShelfProduct
                { ShelfID = ShelfID };
        }
    }
}