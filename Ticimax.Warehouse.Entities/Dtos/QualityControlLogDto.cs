using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class QualityControlLogDto : IDto
    {
        public int OrderID { get; set; }

        public int PersonID { get; set; }

        public string PersonName { get; set; }

        public int PersonTableID { get; set; }

        public string PersonTable { get; set; }

        public double Desi { get; set; }

        public bool isGiftWaybill { get; set; }

        public bool isInvoiceCreate { get; set; }

        public string InvoiceNo { get; set; }

        public string BEVersion { get; set; }

        public string UIVersion { get; set; }

        public bool isInvoicePrinted { get; set; }

        public bool isRabbitTransfer { get; set; }

        public DateTime Date { get; set; }

    }
}