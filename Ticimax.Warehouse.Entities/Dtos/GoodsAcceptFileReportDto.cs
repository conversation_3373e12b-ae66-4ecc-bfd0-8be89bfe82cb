using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class GoodsAcceptFileReportDto : IDto
    {
        public int GoodsAcceptID { get; set; }
        public string Definition { get; set; }
        public int Barcode { get; set; }
        public string ProductName { get; set; }
        public string Variation { get; set; }
        public double Price { get; set; }
        public int Piece { get; set; }
        public int QuantityFound { get; set; }
        public double TotalPurchasePrice { get; set; }
    }
}
