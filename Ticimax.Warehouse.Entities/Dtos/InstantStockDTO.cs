using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class InstantStockDTO:IDto
    {
        public int ProductID { get; set; }
        public string ProductName { get; set; }
        public string AdditionalOptions { get; set; }
        public string Barcode { get; set; }
        public string StockCode { get; set; }
        public int ShelfStock { get; set; }
        public int StockPiece { get; set; }
        public string Supplier { get; set; }
        public int ConsignmentStockQuantity { get; set; }
        public double PurchasePrice { get; set; }
        public double SalePrice { get; set; }
        public double MarketPrice { get; set; }
        public DateTime ReleaseDate { get; set; }
        public DateTime UploadDate { get; set; }
    }
}
