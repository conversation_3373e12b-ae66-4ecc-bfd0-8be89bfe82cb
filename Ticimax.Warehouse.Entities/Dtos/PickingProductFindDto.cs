using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class PickingProductFindDto : IDto
    {
        public int OrderID { get; set; }

        public int OrderProductID { get; set; }

        public string ProductBarcode { get; set; }

        public int ProductID { get; set; }

        public int ShelfID { get; set; }

        public int? WarehouseBoxID { get; set; }

        public double Piece { get; set; }

        public string SetNo { get; set; }

        public bool isMissingProduct { get; set; }
        public bool IsOrderCombinesRequest { get; set; }

        public int MissingProductReturnCauseID { get; set; }

        public string ApiVersion { get; set; }

        public string UIVersion { get; set; }
    }
}