using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class GetToBePickOrderFilterDto : IDto
    {
        public bool? isSingleOrder { get; set; }
        public string OrderSource { get; set; }
        public string Barcode { get; set; }
        public string StockCode { get; set; }
        public int CargoCompanyId { get; set; }
        public int OrderId { get; set; }
        public int? PaymentType { get; set; }

        public List<int> ProductIds { get; set; }
        public string ProductIdsStr { get; set; }

        public bool? IsConsignmentProduct { get; set; }
        public bool IsReportRequest { get; set; } = false;
        public bool AllOrderFromAllStores { get; set; }
    }
}