using Newtonsoft.Json;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class AgentProductAndOrderTotalDto
    {
        [JsonIgnore]
        public double ProductTotal { get; set; }

        [JsonIgnore]
        public int OrderTotal { get; set; }

        public double TodayProductTotal { get; set; }

        public int TodayOrderTotal { get; set; }

        public double YesterdayProductTotal { get; set; }

        public int YesterdayOrderTotal { get; set; }
    }
}