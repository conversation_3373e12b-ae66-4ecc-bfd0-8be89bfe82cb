using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseAddressSetDefaultDto : IDto
    {
        public int ID { get; set; }

        public int TargetID { get; set; }

        public int Type { get; set; }

        public bool isInvoiceAddress { get; set; }

        public WarehouseAddress ToEntity()
        {
            return new WarehouseAddress
                { ID = ID, TargetID = TargetID, Type = Type, isInvoiceAddress = isInvoiceAddress };
        }
    }
}