using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class OrderCollectionPageDto : IDto
    {
        public string SetNo { get; set; }

        public int OrderPiece { get; set; }

        public double ProductPiece { get; set; }

        public int PickerPiece { get; set; }

        public int Status { get; set; }

        public string StatusDefinition { get; set; }

        public string TotalPicker { get; set; }

        public double OccurrencesPiece { get; set; }

        public double MissingPiece { get; set; }

        public DateTime CreateDate { get; set; }

        public int PreparedId { get; set; }
        public int CarId { get; set; }
        public string CarName { get; set; }
        public string PreparedName { get; set; }

    }
}