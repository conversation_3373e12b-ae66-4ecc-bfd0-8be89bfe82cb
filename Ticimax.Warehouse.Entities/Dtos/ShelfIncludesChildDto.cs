using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ShelfIncludesChildDto : IDto
    {
        public ShelfIncludesChildDto()
        {
        }

        public ShelfIncludesChildDto(Shelf shelf, List<Shelf> childShelfs)
        {
            Id = shelf.ID;
            ParentId = shelf.ParentId;
            Definition = shelf.Definition;
            Code = shelf.Code;
            Barcode = shelf.Barcode;
            WarehouseId = shelf.WarehouseID;
            StoreId = shelf.StoreID;
            Rank = shelf.Rank;
            IsMissingProductShelf = shelf.IsMissingProductShelf;
            AddingDate = shelf.AddingDate;
            IsEmptyShelf = shelf.IsEmptyShelf;
            IsOpenForSale = shelf.IsOpenForSale;
            IsOpenPicking = shelf.IsOpenPicking;
            ChildShelfs = childShelfs;
        }

        public int Id { get; set; }

        public int ParentId { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public string Barcode { get; set; }

        public int WarehouseId { get; set; }

        public int StoreId { get; set; }

        public int Rank { get; set; }

        public bool IsMissingProductShelf { get; set; }

        public DateTime AddingDate { get; set; }

        public bool IsEmptyShelf { get; set; }

        public bool IsOpenForSale { get; set; }
        public bool IsOpenPicking { get; set; }

        public List<Shelf> ChildShelfs { get; set; }
    }
}