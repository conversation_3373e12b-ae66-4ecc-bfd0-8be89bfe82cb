using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseCarAddDto : IDto
    {
        public string Definition { get; set; }

        public string Barcode { get; set; }

        public bool isActive { get; set; }

        public WarehouseCar ToEntity()
        {
            return new WarehouseCar
                { Definition = Definition, Barcode = Barcode, isActive = isActive };
        }
    }
}