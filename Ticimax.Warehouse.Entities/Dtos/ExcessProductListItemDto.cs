using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ExcessProductListItemDto : IDto
    {
        public int ExcessProductID { get; set; }

        public int ProductID { get; set; }

        public int ProductCardID { get; set; }

        public string Barcode { get; set; }

        public string ProductName { get; set; }

        public string Image { get; set; }

        public int PersonID { get; set; }

        public string PersonName { get; set; }

        public string StockCode { get; set; }

        public DateTime EntryDate { get; set; }

        public string EntryDateStr
        {
            get { return EntryDate.ToString("dd/MM/yyyy HH:mm:ss"); }
        }

        public string EntryDescription { get; set; }

        public DateTime? ExitDate { get; set; }

        public string ExitDescription { get; set; }

        public bool Status { get; set; } // 0 = girdi , 1 = çıktı

        public double? Piece { get; set; }
    }
}