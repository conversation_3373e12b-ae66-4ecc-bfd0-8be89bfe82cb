using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class AddSavePendentUserLogDto : IDto
    {
        public DateTime DateTime { get; set; }

        public int UserID { get; set; }

        public string UserName { get; set; }

        public string SetNo { get; set; }

        public int ClearedID { get; set; }

        public string ClearedName { get; set; }

        public DetailDto AffectedDetails { get; set; } = new DetailDto();

        public SavePendentUserLog ToEntity()
        {
            return new SavePendentUserLog
                { DateTime = DateTime, UserID = UserID, UserName = UserName, SetNo = SetNo, ClearedID = ClearedID, ClearedName = ClearedName };
        }

        public class DetailDto
        {
            public List<int> UserIDs { get; set; } = new List<int>();

            public List<int> CarIDs { get; set; } = new List<int>();

            public List<int> TableIDs { get; set; } = new List<int>();

            public List<int> BoxIDs { get; set; } = new List<int>();

            public List<UpdatedStatusOrderDto> AffectedOrders { get; set; } = new List<UpdatedStatusOrderDto>();

            public List<ShelfProductDetailDto> ShelfProductDetails { get; set; } = new List<ShelfProductDetailDto>();

            public class UpdatedStatusOrderDto
            {
                public int OrderID { get; set; }

            }

            public class ShelfProductDetailDto
            {
                public int ShelfID { get; set; }

                public int ProductID { get; set; }

                public double Piece { get; set; }
            }
        }
    }
}