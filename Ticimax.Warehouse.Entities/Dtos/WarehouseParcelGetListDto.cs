using System.Collections.Generic;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseParcelGetListDto
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public int Limit { get; set; } = -1;

        public int TableID { get; set; }

        public int WarehouseID { get; set; }

        public int StoreID { get; set; }

        public string Barcode { get; set; }

        public bool? isMissingProductParcel { get; set; }

        public List<int> IDList { get; set; }

        public List<int> TableIDList { get; set; }

        public bool isGetCount { get; set; } = false;

        public bool FillParcel { get; set; } = true;

        public bool? IsEmpty { get; set; }

        public bool? IsWaitingCall { get; set; }

        public bool? IsWaitingInvoice { get; set; }

        public WarehouseParcelFilter ToFilter()
        {
            return new WarehouseParcelFilter
                { ID = ID, Definition = Definition, Code = Code, Limit = Limit, TableID = TableID, WarehouseID = WarehouseID, StoreID = StoreID, Barcode = Barcode, isMissingProductParcel = isMissingProductParcel, IDList = IDList, TableIDList = TableIDList, IsEmpty = IsEmpty, IsWaitingCall = IsWaitingCall, IsWaitingInvoice = IsWaitingInvoice };
        }
    }
}