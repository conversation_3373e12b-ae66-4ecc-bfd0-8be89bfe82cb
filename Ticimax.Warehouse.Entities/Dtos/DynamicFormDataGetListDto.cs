using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class DynamicFormDataGetListDto : IDto
    {
        public int ItemID { get; set; } = -1;

        public int PersonID { get; set; } = -1;

        public int FormID { get; set; } = -1;

        public int Type { get; set; } = -1;

        public int ObjectID { get; set; } = -1;

        public string ObjectType { get; set; } = string.Empty;

        public bool DeserializeObject { get; set; } = false;

        public DateTime? AddingDate1 { get; set; }

        public DateTime? AddingDate2 { get; set; }

        public List<int> FormDataIDs { get; set; } = new List<int>();

        public bool isGetCount { get; set; } = false;

        public DynamicFormDataFilter ToFilter()
        {
            return new DynamicFormDataFilter
                { ItemID = ItemID, PersonID = PersonID, FormID = FormID, Type = Type, ObjectID = ObjectID, ObjectType = ObjectType, DeserializeObject = DeserializeObject, AddingDate1 = AddingDate1, AddingDate2 = AddingDate2, FormDataIDs = FormDataIDs };
        }
    }
}