using System;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class OrderAddProductLogDto : IDto
    {
        public int ID { get; set; }

        public int UserID { get; set; }

        public int OrderID { get; set; }

        public DateTime DateTime { get; set; }

        public OrderAddProductReason ReasonID { get; set; }

        public string Message { get; set; }

        public OrderAddProductLog ToEntity()
        {
            return new OrderAddProductLog
                { ID = ID, UserID = UserID, OrderID = OrderID, DateTime = DateTime, ReasonID = ReasonID, Message = Message };
        }
    }
}