using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseOperationRulesAddDto : IDto
    {
        public string Definition { get; set; }

        public string Process { get; set; }

        public int Active { get; set; }
        public WarehousePackagingOperationRules? WarehousePackagingOperationRules { get; set; }
        public ReturnOrderOperationRules? ReturnOrderOperationRules { get; set; }

        public WarehouseOperationRules ToEntity()
        {
            return new WarehouseOperationRules(0, WebSiteInfo.User.Value.WarehouseID, Definition, Process, Active, WarehousePackagingOperationRules, ReturnOrderOperationRules);
        }
    }
}