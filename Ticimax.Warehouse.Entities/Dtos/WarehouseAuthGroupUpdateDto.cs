using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseAuthGroupUpdateDto : IDto
    {
        public int ID { get; set; }

        public string Defination { get; set; }

        public WarehouseAuthGroup ToEntity()
        {
            return new WarehouseAuthGroup
            {
                ID = ID,
                Defination = Defination,
            };
        }
    }
}