using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class CancelReturnReasonGetListDto : IDto
    {
        public int? ID { get; set; }

        public bool? Active { get; set; }

        public CancelReturnReasonFilter ToFilter()
        {
            return new CancelReturnReasonFilter
            { ID = ID, Active = Active};
        }
    }
}