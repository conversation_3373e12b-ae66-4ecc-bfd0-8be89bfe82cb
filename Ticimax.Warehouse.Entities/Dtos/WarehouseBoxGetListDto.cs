using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseBoxGetListDto : IDto
    {
        public int? WarehouseBoxID { get; set; }

        public string Barcode { get; set; }

        public int ShelfID { get; set; }

        public bool isShelf { get; set; }

        public bool isGetCount { get; set; } = false;

        public List<int> ProductIDs { get; set; } = new List<int>();

        public WarehouseBoxFilter ToFilter()
        {
            return new WarehouseBoxFilter
                { WarehouseBoxID = WarehouseBoxID, Barcode = Barcode, ShelfID = ShelfID, isShelf = isShelf, ProductIDs = ProductIDs };
        }
    }
}