using System;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class StockLocationReportFilter : IFilter
    {
        public int? ProductID { get; set; }
        public string ProductIds { get; set; }

        public int? ShelfID { get; set; }
        public string? ShelfIDs { get; set; }
        public string? ShelfDefinition { get; set; }
        public double? MinShelfStock { get; set; }
        public double? MaxShelfStock { get; set; }
        public double? MinConsigmentStock { get; set; }
        public double? MaxConsigmentStock { get; set; }
        public double? MinStock { get; set; }
        public double? MaxStock { get; set; }
    }
}