using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseSettingsDto : IDto
    {
        public WarehouseSettingDto GeneralSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto PickingProductSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto OrderCombineSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto ProductPlacementSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto QualityControlSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto ReturnSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto OrderSelectSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto MarketplaceSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto DistributorSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto TelephoneCentralSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto TicimaxAdminSettings { get; set; } = new WarehouseSettingDto();

        public WarehouseSettingDto BarcodeSettings { get; set; } = new WarehouseSettingDto();
        
        public WarehouseSettingDto OmnichannelSettings { get; set; } = new WarehouseSettingDto();
    }
}