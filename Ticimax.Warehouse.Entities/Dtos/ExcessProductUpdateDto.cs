using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ExcessProductUpdateDto : IDto
    {
        public int ExcessProductID { get; set; }

        public string EntryDescription { get; set; }

        public DateTime? ExitDate { get; set; }

        public string ExitDescription { get; set; }

        public ExcessProduct ToEntity()
        {
            return new ExcessProduct
                { ExcessProductID = ExcessProductID, EntryDescription = EntryDescription, ExitDate = ExitDate, ExitDescription = ExitDescription };
        }
    }
}