using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ShelfProductReduceDto : IDto
    {
        public int ShelfID { get; set; }

        public string ShelfBarcode { get; set; }

        public int ProductID { get; set; }

        public string ProductBarcode { get; set; }

        public double StockPiece { get; set; }

        public bool ReduceStockWebSite { get; set; }

        public int? OrderID { get; set; }
        public string Type { get; set; }
        public string ObjectId { get; set; }
        public string? Note { get; set; }

    }
}