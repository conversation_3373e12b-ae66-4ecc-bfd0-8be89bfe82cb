using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class InstantStockFilter:IFilter
    {
        public int ProductID { get; set; }
        public string ProductIds { get; set; }
        public double? WebStockQuantityMin { get; set; }
        public double? WebStockQuantityMax { get; set; }
        public double? ConsigmentStockQuantityMin { get; set; }
        public double? ConsigmentStockQuantityMax { get; set; }
        public double? ShelfStockQuantityMin { get; set; }
        public double? ShelfStockQuantityMax { get; set; }
    }
}
