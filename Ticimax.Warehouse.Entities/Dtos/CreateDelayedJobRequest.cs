using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class CreateDelayedJobRequest : IDto
    {
        public string Source { get; set; }

        public string JobType { get; set; }

        public string ApiMethodType { get; set; }

        public string ApiAddress { get; set; }

        public string ApiSendData { get; set; }

        public string ApiHeaders { get; set; }

        public int DelayInMinute { get; set; }

        public string QueueName { get; set; }

        public ResultOption ResultOption { get; set; }
    }

    public class ResultOption
    {
        public int RepeatCount { get; set; }

        public bool RepeatUntilSuccessful { get; set; }

        public int NextControlInMinute { get; set; }
    }
}