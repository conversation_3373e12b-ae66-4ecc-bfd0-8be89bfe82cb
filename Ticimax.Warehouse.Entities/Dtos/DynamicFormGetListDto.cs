using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class DynamicFormGetListDto : IDto
    {
        public int ID { get; set; }

        public int Active { get; set; } = -1;

        public int Type { get; set; } = -1;

        public bool isGetCount { get; set; } = false;

        public DynamicFormFilter ToFilter()
        {
            return new DynamicFormFilter
                { ID = ID, Active = Active, Type = Type };
        }
    }
}