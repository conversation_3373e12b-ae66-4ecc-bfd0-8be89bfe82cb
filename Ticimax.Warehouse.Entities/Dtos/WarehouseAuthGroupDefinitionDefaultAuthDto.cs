using Newtonsoft.Json;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseAuthGroupDefinitionDefaultAuthDto : IDto
    {
        public int ID { get; set; }

        public int PID { get; set; }

        public string AuthCode { get; set; }

        public string Information { get; set; }

        public int SubAuthorityCount { get; set; }

        [JsonIgnore]
        public WarehousePickingType PickingType { get; set; }

        [JsonIgnore]
        public WarehouseDefaultAuthGroup AuthGroups { get; set; }

        [JsonIgnore]
        public bool Flag { get; set; }
    }

    public class WarehousePickingType
    {
        [JsonProperty(PropertyName = "UrunBazli")]
        public bool UrunBazli { get; set; }

        [JsonProperty(PropertyName = "SiparisBazli")]
        public bool SiparisBazli { get; set; }
    }

    public class WarehouseDefaultAuthGroup
    {
        [JsonProperty(PropertyName = "Yonetici")]
        public bool Yonetici { get; set; }

        [JsonProperty(PropertyName = "Toplayici")]
        public bool Toplayici { get; set; }

        [JsonProperty(PropertyName = "Birlestirme")]
        public bool Birlestirme { get; set; }

        [JsonProperty(PropertyName = "KaliteKontrol")]
        public bool KaliteKontrol { get; set; }

        [JsonProperty(PropertyName = "MalKabulveUrunYerlestirme")]
        public bool MalKabulveUrunYerlestirme { get; set; }

        [JsonProperty(PropertyName = "Sayim")]
        public bool Sayim { get; set; }

        [JsonProperty(PropertyName = "Iade")]
        public bool Iade { get; set; }
    }

    public class WarehouseAuthServicesEntity
    {
        [JsonProperty(PropertyName = "id")]
        public int Id { get; set; }

        [JsonProperty(PropertyName = "pId")]
        public int Pid { get; set; }

        [JsonProperty(PropertyName = "authCode")]
        public string YetkiKodu { get; set; }

        [JsonProperty(PropertyName = "itemOrder")]
        public int Sira { get; set; }

        [JsonProperty(PropertyName = "childDefinitionCount")]
        public int AltYetkiSayisi { get; set; }

        [JsonProperty(PropertyName = "collectionTypeSetting")]
        public string ToplamaTipi { get; set; }

        [JsonProperty(PropertyName = "groupSetting")]
        public string YetkiGrubu { get; set; }
    }
}