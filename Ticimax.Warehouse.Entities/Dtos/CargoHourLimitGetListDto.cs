using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class CargoHourLimitGetListDto : IDto
    {
        public int ID { get; set; }

        public int CargoID { get; set; }

        public int CountryID { get; set; }

        public int ProvinceID { get; set; }

        public int DistrictID { get; set; }

        public int SemtID { get; set; }

        public int Day { get; set; } = -1;

        public DateTime Date { get; set; }

        public bool isGetCount { get; set; } = false;

        public CargoHourLimitFilter ToFilter()
        {
            return new CargoHourLimitFilter
                { ID = ID, CargoID = CargoID, CountryID = CountryID, ProvinceID = ProvinceID, DistrictID = DistrictID, SemtID = SemtID, Day = Day, Date = Date };
        }
    }
}