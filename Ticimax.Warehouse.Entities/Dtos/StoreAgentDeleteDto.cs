using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class StoreAgentDeleteDto : IDto
    {
        public int ID { get; set; }

        public int StoreID { get; set; }

        public int WarehouseID { get; set; }

        public StoreAgent ToEntity()
        {
            return new StoreAgent
                { ID = ID, StoreID = StoreID, WarehouseID = WarehouseID };
        }
    }
}