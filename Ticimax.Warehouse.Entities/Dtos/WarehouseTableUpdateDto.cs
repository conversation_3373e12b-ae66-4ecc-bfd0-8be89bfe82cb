using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseTableUpdateDto : IDto
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string Code { get; set; }

        public string Barcode { get; set; }

        public WarehouseTable ToEntity()
        {
            return new WarehouseTable
                { ID = ID, Definition = Definition, Code = Code, Barcode = Barcode };
        }
    }
}