using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class ProductShelfChangeDto : IDto
    {
        public int ShelfID { get; set; }

        public string ShelfBarcode { get; set; }

        public int TransferShelfID { get; set; }

        public string TransferShelfBarcode { get; set; }

        public List<ShelfProductReduceProductDto> Products { get; set; }

        public bool ReduceStockWebSite { get; set; }
    }
}