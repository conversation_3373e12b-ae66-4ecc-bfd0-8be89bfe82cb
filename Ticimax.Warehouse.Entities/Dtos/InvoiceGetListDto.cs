using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class InvoiceGetListDto : IDto
    {
        public string Series { get; set; }

        public int? Rank { get; set; }

        public int? OrderID { get; set; }

        public List<int> OrderIDList { get; set; }

        public DateTime? DateStart { get; set; }

        public DateTime? DateFinish { get; set; }

        public bool? isCancel { get; set; }

        public bool isGetCount { get; set; }

        public InvoiceFilter ToFilter()
        {
            return new InvoiceFilter
                { Series = Series, Rank = Rank, OrderID = OrderID, OrderIDList = OrderIDList, DateStart = DateStart, DateFinish = DateFinish, isCancel = isCancel };
        }
    }
}