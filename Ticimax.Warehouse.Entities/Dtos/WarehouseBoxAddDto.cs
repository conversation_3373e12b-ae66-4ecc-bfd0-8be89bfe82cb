using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Core.Product.Entities.Enums;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseBoxAddDto : IDto
    {
        public string Barcode { get; set; }

        public ProductType TargetType { get; set; }

        public List<string> ProductBarcodes { get; set; }

        public WarehouseBox ToEntity()
        {
            return new WarehouseBox
                { Barcode = Barcode };
        }
    }
}