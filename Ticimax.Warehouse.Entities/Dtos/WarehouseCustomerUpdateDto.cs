using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseCustomerUpdateDto : IDto
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string Mail { get; set; }

        public string Telephone { get; set; }

        public CustomerContactUserDto ContactUser { get; set; }

        public bool isActive { get; set; }

        public WarehouseCustomer ToEntity()
        {
            return new WarehouseCustomer
                { ID = ID, Definition = Definition, Mail = Mail, Telephone = Telephone, ContactUser = ContactUser, isActive = isActive };
        }
    }
}