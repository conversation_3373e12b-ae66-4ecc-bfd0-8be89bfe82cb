using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete;

namespace Ticimax.Warehouse.Entities.Dtos
{
    public class WarehouseAddressUpdateDto : IDto
    {
        public int ID { get; set; }

        public string Definition { get; set; }

        public string Telephone { get; set; }

        public string RecieverName { get; set; }

        public string Mail { get; set; }

        public string Address { get; set; }

        public int CountryID { get; set; }

        public int ProvinceID { get; set; }

        public int DistrictID { get; set; }

        public string PostalCode { get; set; }

        public string TaxNumber { get; set; }

        public string TaxAdministration { get; set; }

        public bool isInvoiceAddress { get; set; }

        public bool isActive { get; set; }

        public WarehouseAddress ToEntity()
        {
            return new WarehouseAddress
            {
                ID = ID,
                Definition = Definition,
                Telephone = Telephone,
                RecieverName = RecieverName,
                Mail = Mail,
                Address = Address,
                CountryID = CountryID,
                ProvinceID = ProvinceID,
                DistrictID = DistrictID,
                PostalCode = PostalCode,
                TaxNumber = TaxNumber,
                TaxAdministration = TaxAdministration,
                isInvoiceAddress = isInvoiceAddress,
                isActive = isActive
            };
        }
    }
}