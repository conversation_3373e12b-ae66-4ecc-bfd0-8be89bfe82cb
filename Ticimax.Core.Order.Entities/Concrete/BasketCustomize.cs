using System.Collections.Generic;
using Newtonsoft.Json;
using Ticimax.Core.Entities;
using Ticimax.Core.Entities.Concrete;

namespace Ticimax.Core.Order.Entities.Concrete
{
    public class BasketCustomize : IEntity
    {
        [JsonProperty("FormId")]
        public int FormID { get; set; }

        [JsonProperty("Customization")]
        public List<CustomizationItem> CustomizationItem { get; set; }
    }
}