using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Core.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Invoice.DTO;

namespace Ticimax.Core.Order.Entities.Concrete
{
    [System.Diagnostics.DebuggerStepThrough]
    [DesignItemClass]
    public class Order : IEntity
    {
        [DesignItemProperty(Name = "Sipariş ID", Group = "Sipariş")]
        public int ID { get; set; }
        
        [DesignItemProperty(Name = "Sipariş Id Barkodu", Group = "Sipariş")]
        public string OrderIdBarcode { get; set; }

        [DesignItemProperty(Name = "Sipariş Numarası", Group = "Sipariş")]
        public string OrderNo { get; set; }

        [DesignItemProperty(Name = "Sipariş Numarası Barkodu", Group = "Sipariş")]
        public string OrderNoBarcode { get; set; }

        [DesignItemProperty(Name = "Üye Id", Group = "Müşteri")]
        public int MemberID { get; set; }

        public string MemberSpecialNote { get; set; }

        public string Customer { get; set; }

        public string CustomerMail { get; set; }

        public string CustomerTelephone { get; set; }

        [DesignItemProperty(Name = "Vergiler Dahil Toplam Tutar", HandlebarHelper = "currencyFormat", Group = "Tutarlar")]
        public double TotalAmount { get; set; }
        public double Balance { get; set; }

        public double DiscounthAmount { get; set; }

        public double BasketDiscounthAmount { get; set; }

        public double CargoAmount { get; set; }

        [DesignItemProperty(Name = "Hediye Çeki Tutarı", HandlebarHelper = "currencyFormat", Group = "Tutarlar")]
        public double GiftVoucherAmount { get; set; }

        [DesignItemProperty(Name = "Hediye Paketi Tutarı", HandlebarHelper = "currencyFormat", Group = "Tutarlar")]
        public double GiftPackageAmount { get; set; }
        public double QualityControlDesi { get; set; }

        public DateTime Date { get; set; }

        [DesignItemProperty(Name = "Teslimat Saati")]
        public string DeliveryHour { get; set; }

        public string DeliveryDate { get; set; }

        public int ProductCount { get; set; }

        [DesignItemProperty(Name = "Sipariş Notu", Group = "Sipariş")]
        public string OrderNote { get; set; }

        [DesignItemProperty(Name = "Sipariş Panel Notu", Group = "Sipariş")]
        public string OrderPanelNote { get; set; }

        public int NoProductStatusID { get; set; }

        public int CargoCompanyID { get; set; }

        public int CargoIntegrationId { get; set; }

        [DesignItemProperty(Name = "Kargo Firma", Group = "Kargo")]
        public string CargoCompany { get; set; }

        public int PickerID { get; set; }

        public int CourierID { get; set; }

        [DesignItemProperty(Name = "Fatura Adresi")]
        public MemberAddress InvoiceAddress { get; set; }

        [DesignItemProperty(Name = "Teslimat Adresi")]
        public MemberAddress DeliveryAddress { get; set; }

        public List<OrderProduct> Products { get; set; }

        public List<OrderPayment> Payments { get; set; }

        public bool isGiftPackage { get; set; }

        [DesignItemProperty(Name = "Hediye Paketi Notu", Group = "Sipariş")]
        public string GiftPackageNote { get; set; }

        public Store Store { get; set; }

        public int DeliveryStoreID { get; set; }

        [DesignItemProperty(Name = "Tahsilat Tipi", Group = "Fatura")]
        public string PaymentType { get; set; }

        public int PaymentTypeID { get; set; }

        public OrderAdditionalInfo AdditionalInfo { get; set; } = new OrderAdditionalInfo();

        public string GiftVoucher { get; set; }

        [DesignItemProperty(Name = "Sipariş Kaynağı", Group = "Sipariş")]
        public string OrderSource { get; set; }

        public bool isIntegrationTransfer { get; set; }

        public string CurrencyCode { get; set; }

        public string InvoiceNo { get; set; }

        [DesignItemProperty(Name = "Fatura Tarihi", HandlebarHelper = "dateFormat", Group = "Fatura")]
        public DateTime InvoiceDate { get; set; }

        public int Status { get; set; }

        public string StatusDefinition { get; set; }

        public string OrderCode { get; set; }

        public double TotalDesi { get; set; }

        [DesignItemProperty(Name = "Kargo Kodu", Group = "Kargo")]
        public string CargoCode { get; set; }
        public string CargoCode2 { get; set; }
        public string CargoCodeStr { get; set; }

        [DesignItemProperty(Name = "Kargo Kodu Barkodu", Group = "Kargo", ItemType = DesignItemType.Image)]
        public string CargoCodeBarcode { get; set; }
        public string CargoCodeBarcode2 { get; set; }

        [DesignItemProperty(Name = "Kargo Takip Numarası Barkodu", Group = "Kargo", ItemType = DesignItemType.Image)]
        public string CargoTrackingNumberBarcode { get; set; }

        [DesignItemProperty(Name = "Marketplace Kampanya Kodu Barkod", Group = "Sipariş", ItemType = DesignItemType.Image)]
        public string MarketplaceCampaignCodeBarcode { get; set; }

        public int PackagingStatusID { get; set; }
        public bool IsParcelShipping { get; set; }

        public double Amount { get; set; }

        public double TotalKDVAmount { get; set; }

        public double AdditionalTaxAmount { get; set; }

        public double CustomsTaxAmount { get; set; }

        public bool DiscountDistributed { get; set; }

        public BasketPointsUsageDto PointsUsage { get; set; }

        public BasketHopiCampaignDto HopiCampaign { get; set; }

        [DesignItemProperty(Name = "Kargo Takip Numarası", Group = "Kargo")]
        public string CargoTrackingNumber { get; set; }

        public int CargoPacketID { get; set; }
        public int CargoPacketID2 { get; set; }

        public string SpecialAreaOne { get; set; }

        public string SpecialAreaTwo { get; set; }

        public string SpecialAreaThree { get; set; }

        public string ProcessUserName { get; set; }
        public string CargoIntegrationName { get; set; }

        public WaitingOrderDto WaitingOrder { get; set; }
        public bool WaitingCustomerService { get; set; }
    }
}