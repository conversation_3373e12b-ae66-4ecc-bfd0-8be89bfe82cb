using System;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Order.Entities.Concrete
{
    public class OrderPayment : IEntity
    {
        public int ID { get; set; }

        public int OrderID { get; set; }

        public int MemberID { get; set; }

        public double Amount { get; set; }

        public int Installments { get; set; }

        public int PaymentOptionID { get; set; }

        public double BankCommission { get; set; }

        public double PaymentDiscount { get; set; }

        public double PayDoorAmount { get; set; }

        public int RemittanceAccountID { get; set; }

        public int PaymentType { get; set; }

        public int Approved { get; set; }

        public DateTime Date { get; set; }

        public string CheckSum { get; set; }

        public string PaymentNote { get; set; }

        public int PayDoorPosBankID { get; set; }

        public int RemittanceBankID { get; set; }

        public string PosReferenceID { get; set; }

        public string Currency { get; set; }

        public bool PaymentQueryApproved { get; set; }
    }
}