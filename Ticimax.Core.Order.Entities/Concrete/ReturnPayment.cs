using System;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Order.Entities.Concrete
{
    public class ReturnPayment : IEntity
    {
        public int ID { get; set; }

        public int OrderID { get; set; }

        public int PaymentID { get; set; }

        public int PersonID { get; set; }

        public string PersonName { get; set; }

        public double Amount { get; set; }

        public string CurrencyCode { get; set; } = "TRY";

        public int PaymentType { get; set; }

        public string IBANNameAndLastName { get; set; }

        public string IBAN { get; set; }

        public DateTime Date { get; set; }

        public string Notes { get; set; }

        public int MemberID { get; set; }

        public string MemberName { get; set; }

        public bool isPaid { get; set; }

        public bool isBalanceReturn { get; set; }
    }
}