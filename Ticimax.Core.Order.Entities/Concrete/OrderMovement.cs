using System;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Order.Entities.Concrete
{
    public class OrderMovement : IEntity
    {
        public int ID { get; set; }

        public int OrderID { get; set; }

        public int MemberID { get; set; }

        public int AgentID { get; set; }
        public int WarehouseID { get; set; }

        public string Name { get; set; }

        public string Message { get; set; }

        public bool isSystem { get; set; }

        public DateTime AddingDate { get; set; }
    }
}