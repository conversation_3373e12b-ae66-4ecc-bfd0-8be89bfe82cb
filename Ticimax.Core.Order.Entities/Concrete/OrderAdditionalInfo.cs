using System.Collections.Generic;
using System.Text.Json.Serialization;
using Newtonsoft.Json;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Order.Entities.Concrete
{
    public class OrderAdditionalInfo : IEntity
    {
        public bool isBagAvailable { get; set; } = true;

        public string ReturnReferanceNo { get; set; }

        [JsonProperty("MarketplaceKampanyaKodu")]
        [JsonPropertyName("MarketplaceKampanyaKodu")]
        public string MarketplaceCampaignCode { get; set; }

        public OrderIban IBAN { get; set; } = new OrderIban();

        [JsonProperty("UyeBakiye")]
        [JsonPropertyName("UyeBakiye")]
        public List<OrderMemberBalance> MemberBalance = new List<OrderMemberBalance>();

        public bool CargoReturn { get; set; }

        public bool PayDoorReturn { get; set; }

        public bool BankCommissionReturn { get; set; }
    }
}