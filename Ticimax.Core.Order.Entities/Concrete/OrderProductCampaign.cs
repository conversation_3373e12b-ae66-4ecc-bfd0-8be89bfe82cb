using Ticimax.Core.Entities;

namespace Ticimax.Core.Order.Entities.Concrete
{
    public class OrderProductCampaign : IEntity
    {
        public int CampaignID { get; set; }

        public string CampaignDefinition { get; set; }

        public int CampaignOperationType { get; set; }

        public double CampaignOperationValue { get; set; }

        public bool CombineCampaign { get; set; }

        public bool SquadCampaign { get; set; }
    }
}