using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Filters;

namespace Ticimax.Core.Order.Entities.Dtos
{
    public class OrderGetOrderTotalCountDto : IDto
    {
        public int WarehouseId { get; set; }

        public bool FilterByInvoiceDate { get; set; }

        public DateTime? InvoiceStartDate { get; set; }

        public DateTime? InvoiceEndDate { get; set; }

        public bool FilterByOrderDate { get; set; }

        public DateTime? OrderStartDate { get; set; }

        public DateTime? OrderEndDate { get; set; }

        public List<int> OrderStatuses { get; set; } = new List<int>();

        public List<int> PackagingStatus { get; set; } = new List<int>();

        public OrderReportFilter ToFilter()
        {
            return new OrderReportFilter
            {
                WarehouseId = WarehouseId,
                FilterByInvoiceDate = FilterByInvoiceDate,
                InvoiceStartDate = InvoiceStartDate,
                InvoiceEndDate = InvoiceEndDate,
                FilterByOrderDate = FilterByOrderDate,
                OrderStartDate = OrderStartDate,
                OrderEndDate = OrderEndDate,
                OrderStatuses = OrderStatuses,
                PackagingStatus = PackagingStatus
            };
        }
    }
}