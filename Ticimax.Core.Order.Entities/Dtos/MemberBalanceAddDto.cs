using System;
using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Concrete;

namespace Ticimax.Core.Order.Entities.Dtos
{
    public class MemberBalanceAddDto : IDto
    {
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int MemberID { get; set; }

        [Range(0.0000, 99999999999.9999, ErrorMessage = "{0} is not equals 0")]
        public double Amount { get; set; }

        public string CurrencyCode { get; set; } = "TRY";

        public bool isRemittance { get; set; }

        public DateTime Date { get; set; }

        public MemberBalance ToEntity()
        {
            return new MemberBalance
                { MemberID = MemberID, Amount = Amount, CurrencyCode = CurrencyCode, isRemittance = isRemittance, Date = Date };
        }
    }
}