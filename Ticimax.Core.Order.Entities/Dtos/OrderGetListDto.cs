using System;
using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Enums;
using Ticimax.Core.Order.Entities.Filters;

namespace Ticimax.Core.Order.Entities.Dtos
{
    [System.Diagnostics.DebuggerStepThrough]
    public class OrderGetListDto : IDto
    {
        public int OrderID { get; set; }

        public List<int> OrderIDList { get; set; } = new List<int>();

        public OrderStatus? OrderStatus { get; set; }

        public PackageStatus? PackagingStatus { get; set; }

        public int? PriorityOrderPackagingStatus { get; set; }

        public int PreparedID { get; set; } = -1;

        public int CurrierID { get; set; } = -1;

        public int DeliveryDateLimit { get; set; }

        public int CargoType { get; set; }

        public int StoreID { get; set; }

        public string OrderNo { get; set; }

        public bool AvailableOrders { get; set; }

        public bool isProductAvailable { get; set; }

        public int ProductCount { get; set; }

        public int ProductCountLarge { get; set; }

        public string SetNo { get; set; }

        public bool InvoiceCreated { get; set; }

        public string ProductBarcode { get; set; }

        public int WarehouseCarId { get; set; }

        public string CargoCode { get; set; }

        public int CargoCompanyId { get; set; }

        public int MemberID { get; set; }
        public bool GetProcessUser { get; set; }

        public bool ApprovedPayments { get; set; }
        public bool IsAdminDashboard { get; set; }

        public List<int> PaymentTypeList { get; set; } = new List<int>();
        public List<int> OrderStatusList { get; set; } = new List<int>();
        public List<int> CargoIDList { get; set; } = new List<int>();
        public List<string> OrderSourceList { get; set; } = new List<string>();

        public DateTime? OrderDateStart { get; set; }

        public DateTime? OrderDateFinish { get; set; }

        public DateTime? PaymentDateStart { get; set; }

        public DateTime? PaymentDateFinish { get; set; }

        public DateTime? DeliveryDateStart { get; set; }

        public DateTime? DeliveryDateFinish { get; set; }

        public bool isGetCount { get; set; } = false;

        public bool OrderByID { get; set; }

        public string OrderSource { get; set; }

        public bool isCreateInvoice { get; set; } = false;

        public int? WarehouseID { get; set; }

        public bool isMarketplaceOrder { get; set; }

        public bool IsDelivery { get; set; }
        public string? CustomerName { get; set; }

        public List<int> ProductIds { get; set; }
        public bool? IsConsignment { get; set; }

        public OrderFilter ToFilter()
        {
            return new OrderFilter
            {
                OrderID = OrderID,
                OrderIDList = OrderIDList,
                OrderStatus = OrderStatus,
                PackagingStatus = PackagingStatus,
                PriorityOrderPackagingStatus = PriorityOrderPackagingStatus,
                PreparedID = PreparedID,
                CurrierID = CurrierID,
                DeliveryDateLimit = DeliveryDateLimit,
                CargoType = CargoType,
                StoreID = StoreID,
                OrderNo = OrderNo,
                AvailableOrders = AvailableOrders,
                isProductAvailable = isProductAvailable,
                ProductCount = ProductCount,
                ProductCountLarge = ProductCountLarge,
                SetNo = SetNo,
                InvoiceCreated = InvoiceCreated,
                ProductBarcode = ProductBarcode,
                CargoCode = CargoCode,
                CargoCompanyID = CargoCompanyId,
                MemberID = MemberID,
                ApprovedPayments = ApprovedPayments,
                PaymentTypeList = PaymentTypeList,
                OrderDateStart = OrderDateStart,
                OrderDateFinish = OrderDateFinish,
                PaymentDateStart = PaymentDateStart,
                PaymentDateFinish = PaymentDateFinish,
                DeliveryDateStart = DeliveryDateStart,
                DeliveryDateFinish = DeliveryDateFinish,
                OrderByID = OrderByID,
                OrderSource = OrderSource,
                isCreateInvoice = isCreateInvoice,
                WarehouseID = WarehouseID,
                isMarketplaceOrder = isMarketplaceOrder,
                IsDelivery = IsDelivery,
                ProductIds = ProductIds,
                WarehouseCarId = WarehouseCarId,
                IsConsignment = IsConsignment,
                IsAdminDashboard = IsAdminDashboard,
                OrderStatusList = OrderStatusList,
                OrderSourceList = OrderSourceList,
                CargoIDList = CargoIDList,
                CustomerName = CustomerName
            };
        }
    }
}