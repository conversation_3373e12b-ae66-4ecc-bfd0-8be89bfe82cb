using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Concrete;

namespace Ticimax.Core.Order.Entities.Dtos
{
    public class OrderPaymentAddDto : IDto
    {
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int OrderID { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int MemberID { get; set; }

        [Range(0.0000, ***********.9999, ErrorMessage = "{0} is not equals 0")]
        public double Amount { get; set; }

        public int Installments { get; set; }

        public int PaymentOptionID { get; set; }

        public double BankCommission { get; set; }

        public double PaymentDiscount { get; set; }

        public double PayDoorAmount { get; set; }

        public int RemittanceAccountID { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int PaymentType { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int Approved { get; set; }

        public string CheckSum { get; set; }

        [StringLength(65535, MinimumLength = 1, ErrorMessage = "{0} length must be smaller than entered characters")]
        public string PaymentNote { get; set; }

        [StringLength(3, ErrorMessage = "{0} length must be smaller than 3 characters")]
        public string Currency { get; set; }

        public bool PaymentQueryApproved { get; set; }

        public OrderPayment ToEntity()
        {
            return new OrderPayment
            {
                OrderID = OrderID,
                MemberID = MemberID,
                Amount = Amount,
                Installments = Installments,
                PaymentOptionID = PaymentOptionID,
                BankCommission = BankCommission,
                PaymentDiscount = PaymentDiscount,
                PayDoorAmount = PayDoorAmount,
                RemittanceAccountID = RemittanceAccountID,
                PaymentType = PaymentType,
                Approved = Approved,
                CheckSum = CheckSum,
                PaymentNote = PaymentNote,
                Currency = Currency,
                PaymentQueryApproved = PaymentQueryApproved
            };
        }
    }
}