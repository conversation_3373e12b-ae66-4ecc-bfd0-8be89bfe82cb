using System;
using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Order.Entities.Concrete;

namespace Ticimax.Core.Order.Entities.Dtos
{
    public class OrderCargoDesiAddDto
    {
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int OrderID { get; set; }

        [Range(0.00, 99999999.99, ErrorMessage = "{0} is not equals 0")]
        public double Desi { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int MemberID { get; set; }

        [StringLength(50, MinimumLength = 1, ErrorMessage = "{0} length must be smaller than 50 characters")]
        public string OperationPlace { get; set; }

        public DateTime Date { get; set; }

        public bool OrderTableUpdate { get; set; }

        public OrderCargoDesi ToEntity()
        {
            return new OrderCargoDesi
                { OrderID = OrderID, Desi = Desi, MemberID = MemberID, OperationPlace = OperationPlace, Date = Date };
        }
    }
}