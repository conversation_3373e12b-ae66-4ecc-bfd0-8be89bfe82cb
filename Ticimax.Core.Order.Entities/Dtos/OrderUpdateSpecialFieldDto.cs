using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Order.Entities.Dtos
{
    public class OrderUpdateSpecialFieldDto : IDto
    {
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int OrderID { get; set; }

        [Required]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "{0} length must be smaller than 255 characters")]
        public string Field { get; set; }
    }
}