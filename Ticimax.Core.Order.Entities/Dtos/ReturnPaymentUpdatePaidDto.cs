using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Concrete;

namespace Ticimax.Core.Order.Entities.Dtos
{
    public class ReturnPaymentUpdatePaidDto : IDto
    {
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int ID { get; set; }

        [Required]
        public bool isPaid { get; set; }

        public ReturnPayment ToEntity()
        {
            return new ReturnPayment
                { ID = ID, isPaid = isPaid };
        }
    }
}