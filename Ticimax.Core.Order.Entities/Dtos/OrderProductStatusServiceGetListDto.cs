using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Filters;

namespace Ticimax.Core.Order.Entities.Dtos
{
    public class OrderProductStatusServiceGetListDto : IDto
    {
        public int OrderProductStatusID { get; set; }

        public int Operation { get; set; }

        public int Active { get; set; } = -1;

        public bool isGetCount { get; set; } = false;

        public OrderProductStatusFilter ToFilter()
        {
            return new OrderProductStatusFilter
                { OrderProductStatusID = OrderProductStatusID, Operation = Operation, Active = Active };
        }
    }
}