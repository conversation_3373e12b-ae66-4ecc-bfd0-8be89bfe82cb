using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;
using Ticimax.Core.Order.Entities.Filters;

namespace Ticimax.Core.Order.Entities.Dtos
{
    public class OrderMovementGetListDto : IDto
    {
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} not equals 0")]
        public int OrderID { get; set; }
        public int WarehouseID { get; set; }
        public List<int> OrderIDs { get; set; }

        public bool isGetCount { get; set; } = false;

        public OrderMovementFilter ToFilter()
        {
            return new OrderMovementFilter
                { OrderID = OrderID, OrderIDs = OrderIDs, WarehouseID = WarehouseID };
        }
    }
}