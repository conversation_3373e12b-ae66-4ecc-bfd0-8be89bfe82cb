using System.Collections.Generic;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Product.Entities.Filters
{
    public class ProductFilter : IFilter
    {
        public int ID { get; set; }

        public List<int> IDList { get; set; }

        public int ProductCardID { get; set; }

        public string ProductName { get; set; }

        public string Barcode { get; set; }

        public List<string> BarcodeList { get; set; }
        public List<string> StockCodeList { get; set; }

        public string StockCode { get; set; }

        public string SupplierCode { get; set; }

        public string SupplierCode2 { get; set; }

        public bool MultipleBarcode { get; set; }

        public bool NonShelf { get; set; } = false;

        public bool IsAvailableStock { get; set; }
        public bool AddSubQueries { get; set; } = true;

        public string SalePriceField { get; set; }
    }
}