using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Product.Entities.Dtos
{
    public class ConsignmentProductUpdateInComingStockDto : IDto
    {
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int ID { get; set; }

        [Range(0.0, 99999999999.99, ErrorMessage = "{0} is not equals 0")]
        public double InComingStock { get; set; }
        public int ProductId { get; set; }
    }
}