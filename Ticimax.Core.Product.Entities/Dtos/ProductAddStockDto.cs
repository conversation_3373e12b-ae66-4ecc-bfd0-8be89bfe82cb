using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Product.Entities.Dtos
{
    public class ProductAddStockDto : IDto
    {
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int ProductID { get; set; }

        [Range(0.00, 99999999.99, ErrorMessage = "{0} is not equals 0")]
        public double Piece { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int StoreID { get; set; }

        public bool ConsignmentModuleActive { get; set; }

    }
}