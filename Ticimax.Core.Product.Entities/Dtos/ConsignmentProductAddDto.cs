using System;
using System.ComponentModel.DataAnnotations;
using Ticimax.Core.Entities;
using Ticimax.Core.Product.Entities.Concrete;

namespace Ticimax.Core.Product.Entities.Dtos
{
    public class ConsignmentProductAddDto : IDto
    {
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int PersonID { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "{0} length must be smaller than 100 characters")]
        public string PersonName { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int ProductCardID { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "{0} is not equals 0")]
        public int ProductID { get; set; }

        [Required]
        [Range(0.0, 99999999999.99, ErrorMessage = "{0} is not equals 0")]
        public double Stock { get; set; }

        [DataType(DataType.DateTime)]
        [DisplayFormat(ApplyFormatInEditMode = true, DataFormatString = "{dd-MM-yyyy  HH:mm:ss}")]
        public DateTime SupplyDate { get; set; }

        public ConsignmentProduct ToEntity()
        {
            return new ConsignmentProduct
                { PersonID = PersonID, PersonName = PersonName, ProductCardID = ProductCardID, ProductID = ProductID, Stock = Stock, SupplyDate = SupplyDate };
        }
    }
}