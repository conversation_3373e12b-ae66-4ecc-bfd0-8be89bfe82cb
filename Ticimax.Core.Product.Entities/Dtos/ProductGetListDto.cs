using System.Collections.Generic;
using Ticimax.Core.Entities;
using Ticimax.Core.Product.Entities.Filters;

namespace Ticimax.Core.Product.Entities.Dtos
{
    public class ProductGetListDto : IDto
    {
        public int ID { get; set; }

        public List<int> IDList { get; set; } = new List<int>();
        public string IDListStr { get; set; }

        public int ProductCardID { get; set; }
        public string BarcodeListStr { get; set; }

        public string ProductName { get; set; }

        public string Barcode { get; set; }

        public List<string> BarcodeList { get; set; }
        public List<string> StockCodeList { get; set; }
        public string StockCodeListStr { get; set; }

        public string StockCode { get; set; }

        public string SupplierCode { get; set; }

        public string SupplierCode2 { get; set; }

        public bool MultipleBarcode { get; set; }

        public bool isGetCount { get; set; } = false;
        public bool AddSubQueries { get; set; } = true;

        public bool NonShelf { get; set; } = false;

        public bool IsAvailableStock { get; set; }

        public string SalePriceField { get; set; }

        public ProductFilter ToFilter()
        {
            return new ProductFilter
            {
                ID = ID,
                IDList = IDList,
                ProductCardID = ProductCardID,
                ProductName = ProductName,
                Barcode = Barcode,
                BarcodeList = BarcodeList,
                StockCode = StockCode,
                SupplierCode = SupplierCode,
                SupplierCode2 = SupplierCode2,
                MultipleBarcode = MultipleBarcode,
                NonShelf = NonShelf,
                SalePriceField = SalePriceField,
                IsAvailableStock = IsAvailableStock,
                StockCodeList = StockCodeList,
                AddSubQueries = AddSubQueries
            };
        }
    }
}