using System;
using Ticimax.Core.Entities;

namespace Ticimax.Core.Product.Entities.Concrete
{
    public class ConsignmentProduct : IEntity
    {
        public int ID { get; set; }

        public int ProductCardID { get; set; }

        public int ProductID { get; set; }

        public double Stock { get; set; }

        public DateTime SupplyDate { get; set; }

        public double InComingStock { get; set; }

        public DateTime StockDateStart { get; set; }

        public int PersonID { get; set; }

        public string PersonName { get; set; }

        public DateTime AddingDate { get; set; }

        public string ProductName { get; set; }

        public int SupplierID { get; set; }

        public string Supplier { get; set; }
    }
}