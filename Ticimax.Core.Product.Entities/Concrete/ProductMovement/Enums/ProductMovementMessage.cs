namespace Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums
{
    public static class ProductMovementMessage
    {
        public static string ProductPlacementToShelf(string name) => $"{name} isimli rafa ürün yerleştirildi.";

        public static string RemoveProductFromTheShelf(string name) => $"{name} isimli raftan ürün çıkarıldı.";

        public static string Picked(string name) => $"{name} isimli raftan toplandı.";

        public static string Allocated(string shelfName, int orderId) => $"{shelfName} isimli raftan <a href='/order-management/order-detail?id={orderId}&ref=allOrders'>{orderId}</a> sipariş için alokasyon yapıldı.";
        public static string AllocatedForWarehouseTransfer(string shelfName, string transferNo) => $"{shelfName} isimli raftan {transferNo} talep için alokasyon yapıldı.";

        public static string QualityControlCompleted(int orderId) => $"<a href='/order-management/order-detail?id={orderId}&ref=allOrders'>{orderId}</a> sipariş için kalite kontrol tamamlandı.";
        
        public static string ProductPlacementToWarehouseCar(string name) => $"{name} isimli arabaya ürün yerleştirildi.";

        public static string RemoveProductFromTheWarehouseCar(string name) => $"{name} isimli arabadan ürün çıkarıldı.";
        
        public static string ReturnProductFromReturnOrder(int id) => $"<a href='/order-management/order-detail?id={id}&ref=allOrders'>{id}</a> id'li siparişten iade olarak alındı.";

        public static string PickedFromGoodsAccept(int id) => $"{id} id'li mal kabul dosyası için okutuldu.";

        public static string ShelfCountingRegulation(string shelfName) => $"{shelfName} lokasyonuna sayım yapıldı.";
        public static string ConsigmentStockUpdate() => $"Konsinye adedi düzenlenmiştir.";
    }
}