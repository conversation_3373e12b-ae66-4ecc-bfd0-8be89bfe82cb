using System.Collections.Generic;
using System.Linq;

namespace Ticimax.Core.Product.Entities.Concrete.ProductMovement.Enums
{
    public static class ProductMovementProcessType
    {
        public static string ProductPlacementToShelf => "ProductPlacementToShelf";

        public static string RemoveProductFromTheShelf => "RemoveProductFromTheShelf";

        public static string Allocated => "Allocated";
        public static string ConsigmentStockUpdate => "ConsigmentStockUpdate";
        
        public static string Picked => "Picked";

        public static string QualityControlCompleted => "QualityControlCompleted";

        public static string ProductPlacementToWarehouseCar => "ProductPlacementToWarehouseCar";

        public static string RemoveProductFromTheWarehouseCar => "RemoveProductFromTheWarehouseCar";

        public static string ReturnProductFromReturnOrder => "ReturnProductFromReturnOrder";
        
        public static string PickedFromGoodsAccept => "PickedFromGoodsAccept";
        
        public static string ShelfCountingRegulation => "ShelfCountingRegulation";

        private static List<string> ValidTypes => new List<string>()
        {
            ProductPlacementToShelf,
            RemoveProductFromTheShelf,
            Allocated,
            Picked,
            QualityControlCompleted,
            ProductPlacementToWarehouseCar,
            RemoveProductFromTheWarehouseCar,
            ReturnProductFromReturnOrder,
            PickedFromGoodsAccept,
            ShelfCountingRegulation
        };

        public static bool IsValid(string val)
        {
            return ValidTypes.Any(x=> x == val);
        }
    }
}