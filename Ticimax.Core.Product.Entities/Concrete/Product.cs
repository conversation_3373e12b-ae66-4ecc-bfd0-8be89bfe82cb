using Ticimax.Core.Entities;

namespace Ticimax.Core.Product.Entities.Concrete
{
    public class Product : IEntity
    {
        public int ID { get; set; }

        public int ProductCardID { get; set; }

        public string ProductName { get; set; }

        public string Barcode { get; set; }

        public string StockCode { get; set; }

        public double StockPiece { get; set; }

        public string AdditionalOptions { get; set; }

        public string Image { get; set; }
        public string Image2 { get; set; }

        public bool isDecimalPiece { get; set; }

        public double DecimalMinimumPiece { get; set; }

        public double DecimalIncreasePiece { get; set; }

        public double Piece { get; set; }

        public double ShelfPiece { get; set; }

        public string SupplierCode { get; set; }

        public string SupplierCode2 { get; set; }
        public string ProductCartSupplierDefinition { get; set; }
        public int ProductCartSupplierId { get; set; }

        public double Amount { get; set; }
        public double PurchasePrice { get; set; }

        public double TotalAmount { get; set; }
        public double PurchaseAmount { get; set; }

        public double KDVAmount { get; set; }

        public int VatRate { get; set; }

        public bool Active { get; set; }

        public string SalesUnit { get; set; }

        public string Currency { get; set; }

        public string Brand { get; set; }
        public string Category { get; set; }
        public double ConsignmentStockPiece { get; set; }

    }
}