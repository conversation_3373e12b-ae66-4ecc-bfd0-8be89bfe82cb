using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.DataAccessLayer.Abstract;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Core.Order.Entities.Pagings;
using Ticimax.Core.Utilities.Service;

namespace Ticimax.Core.Order.Business.Concrete
{
    public class OrderCampaignService : BaseService, IOrderCampaignService
    {
        private readonly IOrderCampaignDal _orderCampaignDal;

        public OrderCampaignService(IOrderCampaignDal orderCampaignDal)
        {
            _orderCampaignDal = orderCampaignDal;
        }

        public async Task<DataResult<List<OrderCampaign>>> GetList(OrderCampaignFilter filter, Paging paging = null, CancellationToken cancellationToken = default)
        {
            DataResult<List<OrderCampaign>> response = new DataResult<List<OrderCampaign>>();

            response.Model = (await _orderCampaignDal.GetList(filter, paging != null ? new OrderCampaignPaging(paging.PageNo, paging.RecordNumber) : null, cancellationToken)).ToList();

            return response;
        }

        public async Task<DataResult<bool>> IsCheckReturnVouchers(int orderID, CancellationToken cancellationToken)
        {
            DataResult<bool> response = new DataResult<bool>();

            response.Model = await _orderCampaignDal.IsCheckReturnVoucher(orderID, cancellationToken);

            return response;
        }
    }
}