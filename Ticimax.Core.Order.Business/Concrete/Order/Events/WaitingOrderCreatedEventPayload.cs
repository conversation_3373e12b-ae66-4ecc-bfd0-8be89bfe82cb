using System;
using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Concrete.OrderWaiting;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Core.Order.Business.Concrete.Order.Events
{
    public class WaitingOrderCreatedEventPayload : BaseDomainEvent
    {
        public WaitingOrderCreatedEventPayload(WaitingOrderAggregate aggregate)
        {
            DomainName = WebSiteInfo.User.Value.DomainName;
            UserId = WebSiteInfo.User.Value.ID;
            Id = aggregate.Id;
            OrderId = aggregate.OrderId;
            Message = aggregate.Message;
            Status = aggregate.Status;
        }

        public string DomainName { get; set; }
        public int UserId { get; set; }
        public Guid Id { get; set; }
        public int OrderId { get; set; }
        public string Message { get; set; }
        public string Status { get; set; }
    }
}
