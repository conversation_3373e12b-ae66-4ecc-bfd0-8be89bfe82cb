using Ticimax.Core.Entities;
using Ticimax.Warehouse.Entities.Static;

namespace Ticimax.Core.Order.Business.Concrete.Order.Events
{
    public class OrderStatusChangeEvent : BaseDomainEvent
    {
        public OrderStatusChangeEvent(int orderId, int statusId)
        {
            OrderId = orderId;
            StatusId = statusId;
            DomainName = WebSiteInfo.User.Value.DomainName;
            UserId = WebSiteInfo.User.Value.ID;
            Username = WebSiteInfo.User.Value.Username;
            Token = WebSiteInfo.User.Value.WarehouseToken;
            SendSms = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.SmsBilgilendir;
            SendMail = WebSiteInfo.User.Value.Settings.UrunToplamaAyar.MailBilgilendir;
        }

        public int OrderId { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }

        public int StatusId { get; set; }

        public string DomainName { get; set; }

        public string Token { get; set; }

        public bool SendSms { get; set; }

        public bool SendMail { get; set; }
    }
}
