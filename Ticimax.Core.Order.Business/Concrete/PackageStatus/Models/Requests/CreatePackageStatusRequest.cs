using Ticimax.Core.Order.Entities.Concrete;

namespace Ticimax.Core.Order.Business.Concrete.PackageStatus.Models.Requests
{
    public class CreatePackageStatusRequest
    {
        public string Defination { get; set; }

        public int Operation { get; set; }

        public PackagingStatus ToEntity()
        {
            return new PackagingStatus
                { Definition = Defination, Operation = Operation };
        }
    }
}