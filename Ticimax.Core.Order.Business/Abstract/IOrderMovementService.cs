using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;

namespace Ticimax.Core.Order.Business.Abstract
{
    public interface IOrderMovementService
    {
        Task<DataResult<List<OrderMovement>>> GetList(OrderMovementGetListDto request, Paging paging = null, CancellationToken cancellationToken = default);

        Task AddAsync(OrderMovementAddDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> GetCount(OrderMovementGetListDto request, CancellationToken cancellationToken);
    }
}