using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Ticimax.Core.Entities;
using Ticimax.Core.Exceptions.Common.Application.Exceptions;
using Ticimax.Core.Extensions;
using Ticimax.Core.Order.Business.Concrete;
using Ticimax.Core.Order.Entities.Concrete;
using Ticimax.Core.Order.Entities.Dtos;
using Ticimax.Core.Order.Entities.Filters;
using Ticimax.Warehouse.Entities.Concrete;
using Ticimax.Warehouse.Entities.Dtos;
using Ticimax.Warehouse.Entities.Dtos.PrintService;
using Ticimax.Warehouse.Entities.Filters;

namespace Ticimax.Core.Order.Business.Abstract
{
    public interface IOrderService
    {
        Task<ErrorResponse> OrderCompleted(int orderID, int cargoType, bool muchScanning, CancellationToken cancellationToken);

        Task<ErrorResponse> MissingProduct(int orderID, CancellationToken cancellationToken);

        Task<DataResult<List<int>>> GetOneProductOrder(CancellationToken cancellationToken);

        Task<DataResult<List<int>>> GetALotsOfProductOrder(CancellationToken cancellationToken);

        Task<DataResult<List<Entities.Concrete.Order>>> GetList(OrderGetListDto request, Paging paging = null, CancellationToken cancellationToken = default);
        
        Task<Nextable<Entities.Concrete.Order>> GetListByManagementPage(OrderGetListDto request, Paging paging, CancellationToken cancellationToken = default);

        Task<DataResult<List<Entities.Concrete.Order>>> GetListDto(OrderGetListDto request, Paging paging = null, CancellationToken cancellationToken = default);

        Task<ErrorResponse> SetInvoiceNo(OrderSetInvoiceNoDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> SetPackagingStatus(OrderSetPackagingStatusDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> SetAdditionalInformation(OrderSetAdditionalInformationDto request, CancellationToken cancellationToken);

        Task<DataResult<List<OrderListItemDto>>> GetOrderReport(OrderGetOrderReportDto request, CancellationToken cancellationToken);

        Task<DataResult<double>> GetOrderTotalAmount(OrderGetOrderTotalAmountDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> GetOrderTotalCount(OrderGetOrderTotalCountDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> GetCanceledOrderTotalCount(OrderGetCanceledOrderTotalCountDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> GetDeliveredOrderTotalCount(OrderGetDeliveredOrderTotalCountDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> UpdateSpecialField3(OrderUpdateSpecialFieldDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> GetCount(OrderGetListDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> UpdateOrderPreparedID(UpdateOrderPreparedIDDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> UpdateOrderDeliveryHour(UpdateOrderDeliveryHourDto request, CancellationToken cancellationToken);

        Task UpdateOrderStore(UpdateOrderStoreDto request, CancellationToken cancellationToken);

        Task<DataResult<int>> OrdersPerPerson(OrdersPerPersonDto request, CancellationToken cancellationToken);

        Task<DataResult<List<int>>> CollectableOrders(CollectableOrdersDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> UpdateCargoStoreCurrier(UpdateCargoStoreCurrierDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> UpdateAmount(OrderServiceUpdateAmountDto request, CancellationToken cancellationToken);

        Task<DataResult<OrderDynamicNoteDto>> GetOrderDynamicNote(OrderDynamicNoteRequestDto request, CancellationToken cancellationToken);

        Task<DataResult<OrderGiftNoteDto>> GetOrderGiftNote(OrderDynamicNoteRequestDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> SetOrderStatus(OrderSetStatusDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> UpdateOrderAmounts(UpdateOrderAmountsDto request, CancellationToken cancellationToken);

        Task<ErrorResponse> AddOrderFilesAsync(OrderFileDetailsDto request, CancellationToken cancellationToken);

        Task<DataResult<List<string>>> GetOrderResources(GetOrderSourceDto request = null, Paging paging = null, CancellationToken cancellationToken = default);

        Task<ErrorResponse> CalculationAmount(int ID, CancellationToken cancellationToken);

        Task<int> GetUserId(int ID, string Username, int StoreID, int WarehouseID, CancellationToken cancellationToken);

        Task<ErrorResponse> OrderReset(OrderResetRequestDto request, CancellationToken cancellationToken);

        Task<List<string>> GetOrderSources(CancellationToken cancellationToken);

        Task<ErrorResponse> OrderProductReset(int orderId, int orderProductId, OrderProductResetRequestDto request, CancellationToken cancellationToken);
        Task<DataResult<List<OrderInvoiced>>> GetInvoicedOrderList(InvoicedOrderFilter request, Paging paging = null, CancellationToken cancellationToken = default);
        Task UpdateOrderCargoIntegration(int orderId, int cargoIntegrationId, CancellationToken cancellationToken);
        Task UpdateOrderCargo(UpdateOrderCargoDto request, CancellationToken cancellationToken);
        Task<int> GetOrderStatusByOrderId(int orderId, CancellationToken cancellationToken);
        Task<List<OrderStatusResult>> GetOrderStatusList(OrderStatusFilter filter, CancellationToken cancellationToken);
    }
}