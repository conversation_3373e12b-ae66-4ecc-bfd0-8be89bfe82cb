using Autofac;
using Autofac.Extras.DynamicProxy;
using Castle.DynamicProxy;
using Ticimax.Core.Business.Abstract;
using Ticimax.Core.Business.Concrete;
using Ticimax.Core.DataAccessLayer.Abstract;
using Ticimax.Core.DataAccessLayer.Concrete;
using Ticimax.Core.Order.Business.Abstract;
using Ticimax.Core.Order.Business.Concrete;
using Ticimax.Core.Order.DataAccessLayer.Abstract;
using Ticimax.Core.Order.DataAccessLayer.Concrete.MySQL;
using Ticimax.Core.Utilities.Interceptors;

namespace Ticimax.Core.Order.Business.DependencyResolvers.Autofac
{
    public class AutofacBusinessModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterType<OrderService>().As<IOrderService>();
            builder.RegisterType<OrderPaymentService>().As<IOrderPaymentService>();
            builder.RegisterType<OrderProductService>().As<IOrderProductService>();
            builder.RegisterType<OrderProductReturnCauseService>().As<IOrderProductReturnCauseService>();
            builder.RegisterType<OrderProductStatusService>().As<IOrderProductStatusService>();
            builder.RegisterType<MemberBalanceService>().As<IMemberBalanceService>();
            builder.RegisterType<ReturnPaymentService>().As<IReturnPaymentService>();
            builder.RegisterType<BalanceService>().As<IBalanceService>();
            builder.RegisterType<OrderMovementService>().As<IOrderMovementService>();
            builder.RegisterType<OrderCampaignService>().As<IOrderCampaignService>();
            builder.RegisterType<PackagingStatusService>().As<IPackagingStatusService>();
            builder.RegisterType<OrderConversationService>().As<IOrderConversationService>();

            builder.RegisterType<OrderDal>().As<IOrderDal>();
            builder.RegisterType<OrderPaymentDal>().As<IOrderPaymentDal>();
            builder.RegisterType<OrderProductDal>().As<IOrderProductDal>();
            builder.RegisterType<OrderProductReturnCauseDal>().As<IOrderProductReturnCauseDal>();
            builder.RegisterType<OrderProductStatusDal>().As<IOrderProductStatusDal>();
            builder.RegisterType<MemberBalanceDal>().As<IMemberBalanceDal>();
            builder.RegisterType<BalanceDal>().As<IBalanceDal>();
            builder.RegisterType<ReturnPaymentDal>().As<IReturnPaymentDal>();
            builder.RegisterType<OrderMovementDal>().As<IOrderMovementDal>();
            builder.RegisterType<OrderCampaignDal>().As<IOrderCampaignDal>();
            builder.RegisterType<PackagingStatusDal>().As<IPackagingStatusDal>();

            builder.RegisterType<TransactionalCommandDal>().As<ITransactionalCommandDal>();
            builder.RegisterType<TransactionalCommandService>().As<ITransactionalCommandService>();

            //Aspect
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            builder.RegisterAssemblyTypes(assembly).AsImplementedInterfaces()
                .EnableInterfaceInterceptors(new ProxyGenerationOptions
                {
                    Selector = new AspectInterceptorSelector()
                }).InstancePerLifetimeScope();
        }
    }
}